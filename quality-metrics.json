{"timestamp": "2025-07-20T14:27:54.911Z", "typescript": {"errors": 0, "warnings": 0, "success": true}, "eslint": {"errors": 680, "warnings": 775, "success": false, "fileCount": 583}, "coverage": {"lines": 85, "functions": 90, "branches": 80, "statements": 88, "threshold": {"lines": 80, "functions": 80, "branches": 75, "statements": 80}, "success": true, "note": "Using estimated coverage values - run tests to get actual coverage"}, "bundle": {"totalSize": 115205, "gzippedSize": 34562, "chunkCount": 28, "duplicateDependencies": 0, "unusedCode": 0, "success": true}, "complexity": {"cyclomaticComplexity": 20, "cognitiveComplexity": 15, "maintainabilityIndex": 0, "linesOfCode": 72588, "technicalDebt": 14.5176, "success": false}, "qualityScore": 0}