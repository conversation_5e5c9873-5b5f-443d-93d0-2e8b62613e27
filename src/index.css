/* Linear-Inspired Theme Update for Risk Management App */

/* Import Inter and Inter Display fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Inter+Tight:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Linear-inspired color system - minimal, desaturated */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%; /* Much darker text for better contrast */

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    /* Linear's subtle desaturated blue - much more muted */
    --primary: 210 11% 71%; /* Desaturated blue-gray */
    --primary-foreground: 0 0% 98%;

    /* Minimal secondary colors */
    --secondary: 0 0% 96.1%; /* Very light gray */
    --secondary-foreground: 0 0% 9%;

    /* Subtle accent - barely there */
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;

    /* Muted colors for less important elements */
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%; /* Medium gray for secondary text */

    /* Destructive stays functional but muted */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    /* Borders - very subtle */
    --border: 0 0% 89.8%; /* Lighter border */
    --input: 0 0% 89.8%;
    --ring: 210 11% 71%; /* Matches primary */

    --radius: 0.375rem; /* Slightly smaller radius like Linear */

    /* Sidebar - keep existing colors but make more subtle */
    --sidebar-background: 0 0% 98%; /* Almost white */
    --sidebar-foreground: 0 0% 3.9%;
    --sidebar-primary: 210 11% 71%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 0 0% 96.1%;
    --sidebar-accent-foreground: 0 0% 9%;
    --sidebar-border: 0 0% 89.8%;
    --sidebar-ring: 210 11% 71%;

    /* Success/Warning colors - muted versions */
    --success: 142 76% 36%;
    --warning: 38 92% 50%;
    --info: 210 11% 71%;
  }

  .dark {
    /* Dark mode with Linear's approach - high contrast */
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%; /* Much lighter text */

    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;

    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;

    --primary: 210 11% 71%;
    --primary-foreground: 0 0% 9%;

    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;

    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;

    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 212.7 26.8% 83.9%;

    --sidebar-background: 0 0% 3.9%;
    --sidebar-foreground: 0 0% 98%;
    --sidebar-primary: 210 11% 71%;
    --sidebar-primary-foreground: 0 0% 9%;
    --sidebar-accent: 0 0% 14.9%;
    --sidebar-accent-foreground: 0 0% 98%;
    --sidebar-border: 0 0% 14.9%;
    --sidebar-ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    /* Smoother font rendering like Linear */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  /* Typography system inspired by Linear */
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter Tight', 'Inter', sans-serif;
    font-weight: 600;
    letter-spacing: -0.025em; /* Tighter letter spacing */
    line-height: 1.2;
  }

  h1 {
    @apply text-2xl font-semibold;
    letter-spacing: -0.03em;
  }

  h2 {
    @apply text-xl font-semibold;
  }

  h3 {
    @apply text-lg font-semibold;
  }

  h4 {
    @apply text-base font-semibold;
  }

  /* Body text */
  p, div, span {
    font-family: 'Inter', sans-serif;
    line-height: 1.5;
  }

  /* Removed problematic color-scheme override that was breaking dark mode tooltips */

  /* Ensure input fields use proper theme styling */
  input, textarea, select {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    border: 1px solid hsl(var(--border));
    /* Linear-style focus states */
    transition: border-color 0.15s ease, box-shadow 0.15s ease;
  }

  input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: hsl(var(--primary));
    box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
  }

  /* Override any browser dark mode selection styling */
  ::selection {
    background-color: hsl(var(--primary) / 0.15) !important;
    color: hsl(var(--foreground)) !important;
  }

  ::-moz-selection {
    background-color: hsl(var(--primary) / 0.15) !important;
    color: hsl(var(--foreground)) !important;
  }
}

/* Linear-inspired component styles */
@layer components {
  /* Buttons - Linear style */
  .btn-primary {
    @apply bg-foreground text-background hover:bg-foreground/90;
    @apply rounded-md px-4 py-2 text-sm font-medium;
    @apply transition-all duration-150;
    @apply border-0;
  }

  .btn-secondary {
    @apply bg-transparent text-foreground border border-border hover:bg-muted;
    @apply rounded-md px-4 py-2 text-sm font-medium;
    @apply transition-all duration-150;
  }

  .btn-ghost {
    @apply bg-transparent text-muted-foreground hover:text-foreground hover:bg-muted;
    @apply rounded-md px-3 py-2 text-sm font-medium;
    @apply transition-all duration-150;
    @apply border-0;
  }

  /* Cards - Linear style */
  .card-linear {
    @apply bg-card border border-border rounded-lg;
    @apply shadow-sm;
    /* Very subtle shadow like Linear */
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  }

  /* Tables - Linear style */
  .table-linear {
    @apply w-full border-collapse;
  }

  .table-linear th {
    @apply text-left text-xs font-medium text-muted-foreground uppercase tracking-wide;
    @apply py-3 px-4 border-b border-border;
  }

  .table-linear td {
    @apply py-3 px-4 text-sm border-b border-border;
    @apply transition-colors duration-150;
  }

  .table-linear tr:hover td {
    @apply bg-muted/50;
  }

  /* Sidebar - Linear style */
  .sidebar-linear {
    @apply bg-sidebar-background border-r border-sidebar-border;
    /* Very minimal, clean sidebar */
  }

  .sidebar-item {
    @apply flex items-center gap-3 px-3 py-2 text-sm;
    @apply text-sidebar-foreground/70 hover:text-sidebar-foreground;
    @apply hover:bg-sidebar-accent rounded-md;
    @apply transition-all duration-150;
  }

  .sidebar-item.active {
    @apply text-sidebar-foreground bg-sidebar-accent;
  }

  .sidebar-item svg {
    @apply w-4 h-4 flex-shrink-0;
  }

  /* Status badges - Linear style with better contrast */
  .status-badge {
    @apply inline-flex items-center rounded-full px-2 py-1 text-xs font-medium;
    @apply ring-1 ring-inset;
  }

  .status-badge.low {
    @apply bg-green-50 text-green-800 ring-green-600/20;
  }

  .status-badge.medium {
    @apply bg-yellow-50 text-yellow-900 ring-yellow-600/20;
  }

  .status-badge.high {
    @apply bg-orange-50 text-orange-900 ring-orange-600/20;
  }

  .status-badge.critical {
    @apply bg-red-50 text-red-900 ring-red-600/20;
  }

  /* Form elements - Linear style */
  .form-input-linear {
    @apply block w-full rounded-md border border-border;
    @apply px-3 py-2 text-sm placeholder-muted-foreground;
    @apply bg-background text-foreground;
    @apply focus:border-primary focus:ring-primary focus:ring-1 focus:ring-offset-0;
    @apply transition-colors duration-150;
  }

  .form-label-linear {
    @apply block text-sm font-medium text-foreground mb-1;
  }

  /* Navigation - Linear style */
  .nav-link {
    @apply text-muted-foreground hover:text-foreground;
    @apply transition-colors duration-150;
  }

  .nav-link.active {
    @apply text-foreground;
  }

  /* Loading states - Linear style */
  .loading-skeleton {
    @apply animate-pulse bg-muted rounded;
  }

  /* Minimal scrollbars like Linear */
  .custom-scrollbar::-webkit-scrollbar {
    @apply w-1;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    @apply bg-transparent;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    @apply bg-border rounded-full;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/30;
  }
}

/* Auth page overrides to ensure proper centering */
.auth-page {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  margin: 0 !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.auth-page > div {
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Linear-inspired animations */
@keyframes fade-in {
  from { opacity: 0; transform: translateY(4px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slide-in {
  from { opacity: 0; transform: translateX(-4px); }
  to { opacity: 1; transform: translateX(0); }
}

.animate-fade-in {
  animation: fade-in 0.2s ease-out;
}

.animate-slide-in {
  animation: slide-in 0.15s ease-out;
}

/* Enhanced Mobile-First Design */
@layer components {
  /* Mobile-optimized header */
  .header-mobile {
    @apply h-14 px-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60;
    /* Sticky header for mobile */
    position: sticky;
    top: 0;
    z-index: 40;
  }

  /* Mobile sidebar improvements */
  .sidebar-mobile {
    @apply w-full max-w-none;
    /* Better mobile sidebar width */
  }

  /* Mobile-friendly card spacing */
  .card-mobile {
    @apply p-4 rounded-lg border bg-card shadow-sm;
    /* Smaller padding on mobile */
  }

  /* Mobile button improvements */
  .btn-mobile-primary {
    @apply w-full h-12 text-base font-medium rounded-lg;
    @apply bg-foreground text-background;
    @apply flex items-center justify-center gap-2;
    @apply touch-manipulation; /* Better touch response */
    /* Larger touch targets for mobile */
    min-height: 48px;
  }

  .btn-mobile-secondary {
    @apply w-full h-12 text-base font-medium rounded-lg;
    @apply bg-transparent text-foreground border border-border;
    @apply flex items-center justify-center gap-2;
    @apply touch-manipulation;
    min-height: 48px;
  }

  .btn-mobile-ghost {
    @apply w-full h-10 text-sm font-medium rounded-md;
    @apply bg-transparent text-muted-foreground;
    @apply flex items-center justify-center gap-2;
    @apply touch-manipulation;
    min-height: 44px;
  }

  /* Mobile form improvements */
  .form-mobile {
    @apply space-y-4;
  }

  .form-input-mobile {
    @apply w-full h-12 px-4 text-base rounded-lg border border-border;
    @apply bg-background text-foreground placeholder-muted-foreground;
    @apply focus:border-primary focus:ring-1 focus:ring-primary;
    @apply touch-manipulation;
    /* Larger inputs for easier mobile interaction */
    min-height: 48px;
  }

  .form-label-mobile {
    @apply block text-sm font-medium text-foreground mb-2;
  }

  /* Mobile table improvements */
  .table-mobile {
    @apply w-full text-sm;
  }

  .table-mobile th {
    @apply text-left text-xs font-medium text-muted-foreground uppercase tracking-wide;
    @apply py-3 px-3 border-b border-border;
    /* Smaller padding on mobile */
  }

  .table-mobile td {
    @apply py-4 px-3 border-b border-border;
    @apply min-h-[52px]; /* Ensure adequate touch targets */
  }

  /* Mobile navigation improvements */
  .nav-mobile {
    @apply flex flex-col space-y-1 p-4;
  }

  .nav-item-mobile {
    @apply flex items-center gap-3 px-3 py-3 text-base;
    @apply text-sidebar-foreground/70 hover:text-sidebar-foreground;
    @apply hover:bg-sidebar-accent rounded-lg;
    @apply transition-all duration-150;
    @apply touch-manipulation;
    /* Larger touch targets */
    min-height: 48px;
  }

  .nav-item-mobile.active {
    @apply text-sidebar-foreground bg-sidebar-accent;
  }

  /* Mobile status badges */
  .status-badge-mobile {
    @apply inline-flex items-center rounded-full px-3 py-1.5 text-sm font-medium;
    @apply ring-1 ring-inset;
    /* Slightly larger for mobile */
  }

  /* Mobile spacing utilities */
  .mobile-container {
    @apply px-4 py-4;
  }

  .mobile-section {
    @apply space-y-4;
  }

  .mobile-grid {
    @apply grid grid-cols-1 gap-4;
  }

  /* Mobile-specific layouts */
  .mobile-stack {
    @apply flex flex-col space-y-4;
  }

  .mobile-header-actions {
    @apply flex flex-col space-y-2 w-full;
  }

  /* Bottom sheet style for mobile modals */
  .mobile-sheet {
    @apply fixed inset-x-0 bottom-0 z-50;
    @apply bg-background border-t border-border;
    @apply rounded-t-xl shadow-lg;
    @apply max-h-[90vh] overflow-y-auto;
  }

  .mobile-sheet-handle {
    @apply w-12 h-1 bg-muted-foreground/30 rounded-full mx-auto my-3;
  }

  /* Improved mobile scrolling */
  .mobile-scroll {
    @apply overflow-y-auto;
    /* Smooth scrolling on iOS */
    -webkit-overflow-scrolling: touch;
  }

  /* Mobile-optimized loading states */
  .loading-mobile {
    @apply flex items-center justify-center p-8;
  }

  .skeleton-mobile {
    @apply animate-pulse bg-muted rounded-lg;
  }

  /* Mobile floating action button */
  .fab-mobile {
    @apply fixed bottom-6 right-6 z-50;
    @apply w-14 h-14 rounded-full;
    @apply bg-foreground text-background;
    @apply flex items-center justify-center;
    @apply shadow-lg hover:shadow-xl;
    @apply transition-all duration-200;
    @apply touch-manipulation;
  }

  /* Mobile-safe areas (for notched phones) */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }
}

/* Mobile-specific responsive improvements */
@media (max-width: 768px) {
  /* Ensure proper mobile viewport */
  html {
    /* Prevent zoom on iOS when rotating */
    -webkit-text-size-adjust: 100%;
    /* Better font rendering on mobile */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    /* Prevent horizontal scroll */
    overflow-x: hidden;
    /* Better mobile scrolling */
    -webkit-overflow-scrolling: touch;
  }

  /* Improve touch targets */
  button, [role="button"], input[type="submit"], input[type="button"] {
    /* Minimum 44px touch target */
    min-height: 44px;
    min-width: 44px;
    /* Better touch response */
    touch-action: manipulation;
  }

  /* Mobile navigation improvements */
  .sidebar-content {
    @apply p-0;
  }

  .sidebar-group {
    @apply p-4;
  }

  /* Better mobile table handling */
  .table-container {
    @apply overflow-x-auto;
    /* Smooth horizontal scrolling */
    -webkit-overflow-scrolling: touch;
  }

  .table-responsive {
    @apply min-w-full;
    /* Prevent table from becoming too narrow */
    min-width: 600px;
  }

  /* Mobile card improvements */
  .card {
    @apply rounded-lg; /* Consistent border radius */
  }

  /* Mobile form spacing */
  .form-group {
    @apply space-y-3; /* Better mobile form spacing */
  }

  /* Mobile dialog improvements */
  .dialog-mobile {
    @apply m-4 max-w-none;
    /* Full width minus margin on mobile */
    width: calc(100vw - 2rem);
  }

  /* Mobile sheet improvements */
  .sheet-mobile {
    @apply max-h-[85vh];
  }

  /* Hide desktop-only elements */
  .desktop-only {
    @apply hidden;
  }

  /* Show mobile-only elements */
  .mobile-only {
    @apply block;
  }

  /* Mobile-optimized spacing */
  .mobile-px {
    @apply px-4;
  }

  .mobile-py {
    @apply py-4;
  }

  .mobile-gap {
    @apply gap-4;
  }

  /* Mobile typography adjustments */
  h1 {
    @apply text-xl; /* Smaller headings on mobile */
  }

  h2 {
    @apply text-lg;
  }

  h3 {
    @apply text-base;
  }

  /* Mobile button improvements */
  .btn-group-mobile {
    @apply flex flex-col space-y-3;
  }

  .btn-group-mobile .btn {
    @apply w-full;
  }

  /* Mobile alert improvements */
  .alert-mobile {
    @apply p-4 rounded-lg border text-sm;
  }

  /* Mobile badge improvements */
  .badge-mobile {
    @apply text-xs px-2 py-1;
  }

  /* Mobile progress improvements */
  .progress-mobile {
    @apply h-2 rounded-full;
  }

  /* Mobile tooltip improvements */
  .tooltip-mobile {
    @apply text-sm p-2 max-w-xs;
  }

  /* Force tooltip visibility in dark mode with maximum specificity */
  [data-radix-tooltip-content],
  [data-radix-popper-content-wrapper] [data-radix-tooltip-content],
  [data-radix-tooltip-content] *,
  [data-radix-popper-content-wrapper] [data-radix-tooltip-content] * {
    background-color: white !important;
    color: black !important;
    border-color: #d1d5db !important;
  }

  .dark [data-radix-tooltip-content],
  .dark [data-radix-popper-content-wrapper] [data-radix-tooltip-content],
  .dark [data-radix-tooltip-content] *,
  .dark [data-radix-popper-content-wrapper] [data-radix-tooltip-content] * {
    background-color: #1f2937 !important;
    color: white !important;
    border-color: #4b5563 !important;
  }

  /* Additional targeting for nested elements */
  .dark [data-radix-tooltip-content] p,
  .dark [data-radix-tooltip-content] span,
  .dark [data-radix-tooltip-content] div {
    color: white !important;
  }

  [data-radix-tooltip-content] p,
  [data-radix-tooltip-content] span,
  [data-radix-tooltip-content] div {
    color: black !important;
  }

  /* Universal Chart Tooltip Styles - Apply to ALL Recharts tooltips */

  /* Target all possible Recharts tooltip containers and custom tooltips */
  .recharts-tooltip-wrapper,
  .recharts-tooltip-wrapper > div,
  .recharts-tooltip-wrapper > div > div,
  .recharts-default-tooltip,
  .chart-tooltip,
  [class*="recharts-tooltip"],
  div[style*="background-color"][style*="border"],
  div[style*="background"][style*="color"] {
    background-color: white !important;
    color: black !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
    z-index: 1000 !important;
  }

  /* Dark mode styles for all tooltip containers */
  .dark .recharts-tooltip-wrapper,
  .dark .recharts-tooltip-wrapper > div,
  .dark .recharts-tooltip-wrapper > div > div,
  .dark .recharts-default-tooltip,
  .dark .chart-tooltip,
  .dark [class*="recharts-tooltip"],
  .dark div[style*="background-color"][style*="border"],
  .dark div[style*="background"][style*="color"] {
    background-color: #1f2937 !important;
    color: white !important;
    border: 1px solid #4b5563 !important;
  }

  /* Ensure ALL text elements in ANY chart tooltip inherit the correct color */
  .recharts-tooltip-wrapper *,
  .recharts-tooltip-wrapper p,
  .recharts-tooltip-wrapper span,
  .recharts-tooltip-wrapper div,
  .recharts-tooltip-wrapper td,
  .recharts-tooltip-wrapper th,
  .recharts-default-tooltip *,
  .recharts-default-tooltip p,
  .recharts-default-tooltip span,
  .recharts-default-tooltip div,
  .chart-tooltip *,
  .chart-tooltip p,
  .chart-tooltip span,
  .chart-tooltip div,
  [class*="recharts-tooltip"] *,
  [class*="recharts-tooltip"] p,
  [class*="recharts-tooltip"] span,
  [class*="recharts-tooltip"] div,
  div[style*="background-color"][style*="border"] *,
  div[style*="background-color"][style*="border"] p,
  div[style*="background-color"][style*="border"] span,
  div[style*="background-color"][style*="border"] div {
    color: inherit !important;
  }

  /* Dark mode text color inheritance - force white text */
  .dark .recharts-tooltip-wrapper *,
  .dark .recharts-tooltip-wrapper p,
  .dark .recharts-tooltip-wrapper span,
  .dark .recharts-tooltip-wrapper div,
  .dark .recharts-default-tooltip *,
  .dark .recharts-default-tooltip p,
  .dark .recharts-default-tooltip span,
  .dark .recharts-default-tooltip div,
  .dark .chart-tooltip *,
  .dark .chart-tooltip p,
  .dark .chart-tooltip span,
  .dark .chart-tooltip div,
  .dark [class*="recharts-tooltip"] *,
  .dark [class*="recharts-tooltip"] p,
  .dark [class*="recharts-tooltip"] span,
  .dark [class*="recharts-tooltip"] div,
  .dark div[style*="background-color"][style*="border"] *,
  .dark div[style*="background-color"][style*="border"] p,
  .dark div[style*="background-color"][style*="border"] span,
  .dark div[style*="background-color"][style*="border"] div,
  .dark div[style*="background"][style*="color"] *,
  .dark div[style*="background"][style*="color"] p,
  .dark div[style*="background"][style*="color"] span,
  .dark div[style*="background"][style*="color"] div {
    color: white !important;
  }

  /* Additional aggressive overrides for stubborn tooltips */
  .recharts-tooltip-wrapper div[style] {
    background-color: white !important;
    color: black !important;
  }

  .dark .recharts-tooltip-wrapper div[style] {
    background-color: #1f2937 !important;
    color: white !important;
  }

  /* Override any Recharts internal styling */
  .recharts-tooltip-content {
    background-color: white !important;
    color: black !important;
    border: 1px solid #d1d5db !important;
  }

  .dark .recharts-tooltip-content {
    background-color: #1f2937 !important;
    color: white !important;
    border: 1px solid #4b5563 !important;
  }

  /* Make tooltip cursor transparent to remove grey bar on hover - v2.0 */
  .recharts-tooltip-cursor,
  .recharts-rectangle.recharts-tooltip-cursor,
  .recharts-tooltip-cursor rect,
  .recharts-wrapper .recharts-tooltip-cursor {
    fill: transparent !important;
    opacity: 0 !important;
    visibility: hidden !important;
    background: transparent !important;
  }

  /* Mobile menu improvements */
  .dropdown-mobile {
    @apply w-screen max-w-sm;
  }

  /* Mobile search improvements */
  .search-mobile {
    @apply h-12 text-base rounded-lg;
  }

  /* Mobile loading improvements */
  .spinner-mobile {
    @apply w-6 h-6;
  }

  /* Mobile empty state improvements */
  .empty-state-mobile {
    @apply p-8 text-center;
  }

  .empty-state-mobile .icon {
    @apply w-12 h-12 mx-auto mb-4;
  }

  .empty-state-mobile .title {
    @apply text-lg font-semibold mb-2;
  }

  .empty-state-mobile .description {
    @apply text-sm text-muted-foreground mb-6;
  }
}

/* Tablet-specific improvements */
@media (min-width: 769px) and (max-width: 1024px) {
  /* Show mobile-only on tablets too for some elements */
  .mobile-tablet-only {
    @apply block;
  }

  /* Tablet-specific sidebar */
  .sidebar-tablet {
    @apply w-64; /* Slightly wider sidebar on tablet */
  }

  /* Tablet-specific grid */
  .tablet-grid {
    @apply grid-cols-2;
  }

  /* Tablet button groups */
  .btn-group-tablet {
    @apply flex flex-row space-x-3 space-y-0;
  }
}

/* High DPI display improvements */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  /* Sharper borders on high DPI displays */
  .border-sharp {
    border-width: 0.5px;
  }

  /* Better icon rendering */
  .icon-sharp {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark mode mobile improvements */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
  /* Better dark mode contrast on mobile */
  .dark-mobile-improvement {
    @apply bg-background text-foreground;
  }

  /* Dark mode status bar */
  meta[name="theme-color"] {
    content: hsl(var(--background));
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .mobile-animation {
    @apply transition-none animate-none;
  }
}

/* Print styles for mobile */
@media print {
  .mobile-print-hide {
    @apply hidden;
  }

  .mobile-print-show {
    @apply block;
  }
}
