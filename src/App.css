#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  /* text-align: center;*/
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;

  /* Add this to your main CSS file or create src/styles/pdf-charts.css */

  /* Ensure proper rendering for PDF chart generation */
  .recharts-wrapper {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif !important;
  }

  .recharts-cartesian-axis-tick-value {
    font-size: 12px !important;
  }

  .recharts-legend-item-text {
    font-size: 14px !important;
  }

  .recharts-tooltip-wrapper {
    font-size: 12px !important;
  }

  /* Improve text rendering in charts */
  .recharts-text {
    font-family: inherit !important;
  }

  /* Ensure proper colors for PDF */
  .recharts-pie-sector {
    stroke: white;
    stroke-width: 1px;
  }

  .recharts-bar {
    stroke: none;
  }

  .recharts-line {
    stroke-width: 2px;
  }

  .recharts-area {
    fill-opacity: 0.8;
  }
}
