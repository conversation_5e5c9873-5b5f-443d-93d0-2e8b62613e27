// Type mappers for converting between different data formats
import { Risk, RiskSeverity, RiskStatus, ControlMeasure, MitigationAction } from "@/types";
import {
  DbRisk,
  DbControlMeasure,
  DbMitigationAction,
  DbRiskHistory,
  DbRiskCategory,
  DbProfile,
} from "@/types/db";

/**
 * Map database risk data to domain Risk object
 */

export const mapDatabaseRiskToRisk = (
  dbRisk: DbRisk & {
    categories?: { name: string };
    profiles?: { name: string };
  }
): Risk => {
  return {
    id: dbRisk.id,
    title: dbRisk.title,
    description: dbRisk.description,
    category: dbRisk.categories?.name ?? "Uncategorized",
    categoryId: dbRisk.category_id,
    ownerId: dbRisk.owner_id ?? "",
    ownerName: dbRisk.profiles?.name ?? "Unassigned",
    organizationId: dbRisk.organization_id, // ADD THIS LINE
    createdAt: new Date(dbRisk.created_at),
    updatedAt: new Date(dbRisk.updated_at),

    // Inherent risk properties (risk without controls)
    inherentLikelihood: dbRisk.inherent_likelihood || dbRisk.likelihood,
    inherentImpact: dbRisk.inherent_impact || dbRisk.impact,
    inherentSeverity:
      (dbRisk.inherent_severity as RiskSeverity) || (dbRisk.severity as RiskSeverity),

    // Residual risk properties (risk after controls are applied)
    likelihood: dbRisk.likelihood,
    impact: dbRisk.impact,
    severity: dbRisk.severity as RiskSeverity,

    status: dbRisk.status as RiskStatus,
    currentControls: dbRisk.current_controls,
    controlMeasures: [], // Will be populated separately if needed
    mitigationApproach: dbRisk.mitigation_approach,
    mitigationActions: [], // Will be populated separately if needed
    dueDate: dbRisk.due_date ? new Date(dbRisk.due_date) : undefined,
    relatedIncidents: [], // Will be populated separately if needed
  };
};

/**
 * Map Risk object to database insert format
 */

export const mapRiskToDatabase = (risk: Partial<Risk>) => {
  return {
    title: risk.title,
    description: risk.description,
    category_id: risk.categoryId,
    owner_id: risk.ownerId,
    organization_id: risk.organizationId, // ADD THIS LINE
    inherent_likelihood: risk.inherentLikelihood,
    inherent_impact: risk.inherentImpact,
    inherent_severity: risk.inherentSeverity,
    likelihood: risk.likelihood,
    impact: risk.impact,
    severity: risk.severity,
    status: risk.status,
    current_controls: risk.currentControls,
    mitigation_approach: risk.mitigationApproach,
    due_date: risk.dueDate?.toISOString(),
  };
};

/**
 * Map database risk to domain Risk object (alias for compatibility)
 */

export const mapDbRiskToDomain = (
  dbRisk: DbRisk,
  additionalData?: {
    ownerName?: string;
    category?: string;
    controlMeasures?: ControlMeasure[];
    mitigationActions?: MitigationAction[];
  }
): Risk => {
  return {
    id: dbRisk.id,
    title: dbRisk.title,
    description: dbRisk.description,
    category: additionalData?.category ?? "Uncategorized",
    categoryId: dbRisk.category_id,
    ownerId: dbRisk.owner_id ?? "",
    ownerName: additionalData?.ownerName ?? "Unassigned",
    organizationId: dbRisk.organization_id, // ADD THIS LINE
    createdAt: new Date(dbRisk.created_at),
    updatedAt: new Date(dbRisk.updated_at),

    // Inherent risk properties
    inherentLikelihood: dbRisk.inherent_likelihood || dbRisk.likelihood,
    inherentImpact: dbRisk.inherent_impact || dbRisk.impact,
    inherentSeverity:
      (dbRisk.inherent_severity as RiskSeverity) || (dbRisk.severity as RiskSeverity),

    // Residual risk properties
    likelihood: dbRisk.likelihood,
    impact: dbRisk.impact,
    severity: dbRisk.severity as RiskSeverity,

    status: dbRisk.status as RiskStatus,
    currentControls: dbRisk.current_controls,
    controlMeasures: additionalData?.controlMeasures ?? [],
    mitigationApproach: dbRisk.mitigation_approach,
    mitigationActions: additionalData?.mitigationActions ?? [],
    dueDate: dbRisk.due_date ? new Date(dbRisk.due_date) : undefined,
    relatedIncidents: [],
  };
};

/**
 * Map domain Risk to database format
 */

export const mapDomainRiskToDb = (risk: Partial<Risk>) => {
  return {
    title: risk.title,
    description: risk.description,
    category_id: risk.categoryId,
    owner_id: risk.ownerId,
    organization_id: risk.organizationId, // ADD THIS LINE
    inherent_likelihood: risk.inherentLikelihood,
    inherent_impact: risk.inherentImpact,
    inherent_severity: risk.inherentSeverity,
    likelihood: risk.likelihood,
    impact: risk.impact,
    severity: risk.severity,
    status: risk.status,
    current_controls: risk.currentControls,
    mitigation_approach: risk.mitigationApproach,
    due_date: risk.dueDate?.toISOString(),
  };
};

/**
 * Map database control measure to domain object
 */

export const mapDbControlMeasureToDomain = (dbControl: DbControlMeasure): ControlMeasure => {
  return {
    id: dbControl.id,
    riskId: dbControl.risk_id,
    organizationId: dbControl.organization_id, // ADD THIS LINE
    description: dbControl.description,
    effectiveness: dbControl.effectiveness as "High" | "Medium" | "Low" | undefined,
    implemented: dbControl.implemented,
    createdAt: new Date(dbControl.created_at),
    updatedAt: new Date(dbControl.updated_at),
  };
};

/**
 * Map domain control measure to database format
 */

export const mapDomainControlMeasureToDb = (control: Partial<ControlMeasure>) => {
  return {
    risk_id: control.riskId,
    organization_id: control.organizationId, // ADD THIS LINE
    description: control.description,
    effectiveness: control.effectiveness,
    implemented: control.implemented,
    created_at: control.createdAt?.toISOString(),
    updated_at: control.updatedAt?.toISOString(),
  };
};

/**
 * Map database mitigation action to domain object
 */

export const mapDbMitigationActionToDomain = (dbAction: DbMitigationAction): MitigationAction => {
  return {
    id: dbAction.id,
    riskId: dbAction.risk_id,
    organizationId: dbAction.organization_id, // ADD THIS LINE
    description: dbAction.description,
    completed: dbAction.completed,
    createdAt: new Date(dbAction.created_at),
    updatedAt: new Date(dbAction.updated_at),
  };
};

/**
 * Map domain mitigation action to database format
 */

export const mapDomainMitigationActionToDb = (action: Partial<MitigationAction>) => {
  return {
    risk_id: action.riskId,
    organization_id: action.organizationId, // ADD THIS LINE
    description: action.description,
    completed: action.completed,
    created_at: action.createdAt?.toISOString(),
    updated_at: action.updatedAt?.toISOString(),
  };
};

/**
 * Map database risk history to domain object
 */

export const mapDbRiskHistoryToDomain = (dbHistory: DbRiskHistory) => {
  return {
    id: dbHistory.id,
    risk_id: dbHistory.risk_id,
    organizationId: dbHistory.organization_id, // ADD THIS LINE
    recorded_at: new Date(dbHistory.recorded_at),
    likelihood: dbHistory.likelihood,
    impact: dbHistory.impact,
    severity: dbHistory.severity as RiskSeverity,
    status: dbHistory.status as RiskStatus,
    created_at: dbHistory.created_by ? new Date() : undefined,
  };
};

/**
 * Map database risk category to domain object
 */

export const mapDbRiskCategoryToDomain = (dbCategory: DbRiskCategory) => {
  return {
    id: dbCategory.id,
    name: dbCategory.name,
    description: dbCategory.description,
    organizationId: dbCategory.organization_id, // ADD THIS LINE
    createdAt: new Date(dbCategory.created_at),
    updatedAt: new Date(dbCategory.updated_at),
  };
};

/**
 * Map database profile to domain object
 */

export const mapDbProfileToDomain = (dbProfile: DbProfile) => {
  return {
    id: dbProfile.id,
    name: dbProfile.name,
    email: dbProfile.email,
    role: dbProfile.role,
    department: dbProfile.department,
    organizationId: dbProfile.organization_id, // ADD THIS LINE
    avatar_url: dbProfile.avatar_url,
    createdAt: new Date(dbProfile.created_at),
    updatedAt: new Date(dbProfile.updated_at),
  };
};
