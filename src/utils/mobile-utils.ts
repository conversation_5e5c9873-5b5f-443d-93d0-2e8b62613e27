/**
 * Mobile-specific utility functions
 * Provides device detection, performance optimization, and mobile UX helpers
 */
export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isIOS: boolean;
  isAndroid: boolean;
  isLowEndDevice: boolean;
  hasTouch: boolean;
  screenSize: "small" | "medium" | "large" | "xlarge";
  orientation: "portrait" | "landscape";
  pixelRatio: number;
  connectionType?: string;
  deviceMemory?: number;
  hardwareConcurrency?: number;
}
/**
 * Get comprehensive device information
 */
interface NavigatorWithMemory extends Navigator {
  deviceMemory?: number;
  hardwareConcurrency?: number;
  connection?: {
    effectiveType?: string;
  };
}
export function getDeviceInfo(): DeviceInfo {
  const userAgent = navigator.userAgent;
  const nav = navigator as NavigatorWithMemory;
  // Basic device detection
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  const isTablet = /iPad|Android(?=.*\bMobile\b)(?=.*\bTablet\b)|Android(?=.*\bTablet\b)/i.test(
    userAgent
  );
  const isIOS = /iPad|iPhone|iPod/.test(userAgent);
  const isAndroid = /Android/i.test(userAgent);
  // Screen size detection
  const width = window.innerWidth;
  let screenSize: DeviceInfo["screenSize"] = "medium";
  if (width < 640) screenSize = "small";
  else if (width < 1024) screenSize = "medium";
  else if (width < 1280) screenSize = "large";
  else screenSize = "xlarge";
  // Performance indicators
  const deviceMemory = nav.deviceMemory ?? 4;
  const hardwareConcurrency = nav.hardwareConcurrency ?? 2;
  const isLowEndDevice = deviceMemory <= 2 || hardwareConcurrency <= 2;
  return {
    isMobile: isMobile && !isTablet,
    isTablet,
    isDesktop: !isMobile && !isTablet,
    isIOS,
    isAndroid,
    isLowEndDevice,
    hasTouch: "ontouchstart" in window,
    screenSize,
    orientation: window.innerHeight > window.innerWidth ? "portrait" : "landscape",
    pixelRatio: window.devicePixelRatio ?? 1,
    connectionType: nav.connection?.effectiveType,
    deviceMemory,
    hardwareConcurrency,
  };
}
/**
 * Optimize images based on device capabilities
 */
export function getOptimalImageSettings(deviceInfo: DeviceInfo) {
  let quality = 85;
  let format = "webp";
  if (deviceInfo.isLowEndDevice) {
    quality = 60;
  } else if (deviceInfo.isMobile) {
    quality = 75;
  }
  // Use AVIF for modern browsers, WebP for others
  if (supportsImageFormat("avif")) {
    format = "avif";
  } else if (!supportsImageFormat("webp")) {
    format = "jpeg";
  }
  return { quality, format };
}
/**
 * Check if browser supports image format
 */
export function supportsImageFormat(format: "webp" | "avif"): boolean {
  const canvas = document.createElement("canvas");
  canvas.width = 1;
  canvas.height = 1;
  try {
    return canvas.toDataURL(`image/${format}`).indexOf(`data:image/${format}`) === 0;
  } catch {
    return false;
  }
}
/**
 * Debounce function optimized for mobile
 */
export function mobileDebounce<T extends (...args: unknown[]) => any>(
  func: T,
  delay: number,
  immediate = false
): T {
  let timeoutId: NodeJS.Timeout | null = null;
  return ((...args: Parameters<T>) => {
    const callNow = immediate && !timeoutId;
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    timeoutId = setTimeout(() => {
      timeoutId = null;
      if (!immediate) func(...args);
    }, delay);
    if (callNow) func(...args);
  }) as T;
}
/**
 * Throttle function optimized for mobile scrolling
 */
export function mobileThrottle<T extends (...args: unknown[]) => any>(func: T, delay: number): T {
  let lastCall = 0;
  return ((...args: Parameters<T>) => {
    const now = Date.now();
    if (now - lastCall >= delay) {
      lastCall = now;
      func(...args);
    }
  }) as T;
}
/**
 * Safe area utilities for mobile devices
 */
export function getSafeAreaInsets() {
  const style = getComputedStyle(document.documentElement);
  return {
    top: parseInt(style.getPropertyValue("--safe-area-inset-top") ?? "0"),
    right: parseInt(style.getPropertyValue("--safe-area-inset-right") ?? "0"),
    bottom: parseInt(style.getPropertyValue("--safe-area-inset-bottom") ?? "0"),
    left: parseInt(style.getPropertyValue("--safe-area-inset-left") ?? "0"),
  };
}
/**
 * Haptic feedback utilities
 */
export class HapticFeedback {
  static isSupported(): boolean {
    return "vibrate" in navigator;
  }
  static light(): void {
    if (this.isSupported()) {
      navigator.vibrate(10);
    }
  }
  static medium(): void {
    if (this.isSupported()) {
      navigator.vibrate(20);
    }
  }
  static heavy(): void {
    if (this.isSupported()) {
      navigator.vibrate([30, 10, 30]);
    }
  }
  static success(): void {
    if (this.isSupported()) {
      navigator.vibrate([10, 5, 10]);
    }
  }
  static error(): void {
    if (this.isSupported()) {
      navigator.vibrate([50, 25, 50, 25, 50]);
    }
  }
  static selection(): void {
    if (this.isSupported()) {
      navigator.vibrate(5);
    }
  }
}
/**
 * Mobile-specific event utilities
 */
export function addMobileEventListener(
  element: Element,
  eventType: "tap" | "swipe" | "pinch",
  handler: (event: React.SyntheticEvent) => void,
  options: {
    threshold?: number;
    direction?: "left" | "right" | "up" | "down";
    preventDefault?: boolean;
  } = {
    // Implementation needed
  }
) {
  const { threshold = 50, direction, preventDefault = false } = options;
  let startX = 0;
  let startY = 0;
  let startTime = 0;
  const handleTouchStart = (e: TouchEvent) => {
    if (preventDefault) e.preventDefault();
    const touch = e.touches[0];
    startX = touch.clientX;
    startY = touch.clientY;
    startTime = Date.now();
  };
  const handleTouchEnd = (e: TouchEvent) => {
    if (preventDefault) e.preventDefault();
    const touch = e.changedTouches[0];
    const deltaX = touch.clientX - startX;
    const deltaY = touch.clientY - startY;
    const deltaTime = Date.now() - startTime;
    const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
    if (eventType === "tap" && distance < 10 && deltaTime < 300) {
      handler({ type: "tap", x: touch.clientX, y: touch.clientY });
    } else if (eventType === "swipe" && distance > threshold) {
      let swipeDirection: string;
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        swipeDirection = deltaX > 0 ? "right" : "left";
      } else {
        swipeDirection = deltaY > 0 ? "down" : "up";
      }
      if (!direction || direction === swipeDirection) {
        handler({
          type: "swipe",
          direction: swipeDirection,
          distance,
          deltaX,
          deltaY,
          velocity: distance / deltaTime,
        });
      }
    }
  };
  element.addEventListener("touchstart", handleTouchStart, { passive: !preventDefault });
  element.addEventListener("touchend", handleTouchEnd, { passive: !preventDefault });
  return () => {
    element.removeEventListener("touchstart", handleTouchStart);
    element.removeEventListener("touchend", handleTouchEnd);
  };
}
/**
 * Mobile performance monitoring
 */
export class MobilePerformanceMonitor {
  private static instance: MobilePerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();
  static getInstance(): MobilePerformanceMonitor {
    if (!this.instance) {
      this.instance = new MobilePerformanceMonitor();
    }
    return this.instance;
  }
  startTiming(label: string): void {
    performance.mark(`${label}-start`);
  }
  endTiming(label: string): number {
    performance.mark(`${label}-end`);
    performance.measure(label, `${label}-start`, `${label}-end`);
    const measure = performance.getEntriesByName(label, "measure")[0];
    const duration = measure.duration;
    if (!this.metrics.has(label)) {
      this.metrics.set(label, []);
    }
    const measurements = this.metrics.get(label)!;
    measurements.push(duration);
    // Keep only last 100 measurements
    if (measurements.length > 100) {
      measurements.shift();
    }
    return duration;
  }
  getAverageTime(label: string): number {
    const measurements = this.metrics.get(label);
    if (!measurements || measurements.length === 0) return 0;
    return measurements.reduce((sum, time) => sum + time, 0) / measurements.length;
  }
  getMetrics(): Record<string, { average: number; count: number; latest: number }> {
    const result: Record<string, { average: number; count: number; latest: number }> = {};
    this.metrics.forEach((measurements, label) => {
      result[label] = {
        average: this.getAverageTime(label),
        count: measurements.length,
        latest: measurements[measurements.length - 1] || 0,
      };
    });
    return result;
  }
  logSlowOperations(threshold = 100): void {
    const metrics = this.getMetrics();
    Object.entries(metrics).forEach(([label, data]) => {
      if (data.average > threshold) {
        // Condition handled
      }
    });
  }
}
/**
 * Mobile-specific CSS utilities
 */
export function addMobileCSS(): void {
  const style = document.createElement("style");
  style.textContent = `
    /* Mobile-specific optimizations */
    * {
      -webkit-tap-highlight-color: transparent;
      -webkit-touch-callout: none;
    }
    /* Smooth scrolling on mobile */
    html {
      -webkit-overflow-scrolling: touch;
      scroll-behavior: smooth;
    }
    /* Prevent zoom on input focus */
    input, select, textarea {
      font-size: 16px !important;
    }
    /* Safe area support */
    .safe-area-top { padding-top: env(safe-area-inset-top); }
    .safe-area-bottom { padding-bottom: env(safe-area-inset-bottom); }
    .safe-area-left { padding-left: env(safe-area-inset-left); }
    .safe-area-right { padding-right: env(safe-area-inset-right); }
    /* Mobile-optimized scrollbars */
    @media (max-width: 768px) {
      ::-webkit-scrollbar {
        width: 2px;
      }
      ::-webkit-scrollbar-track {
        background: transparent;
      }
      ::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 1px;
      }
    }
  `;
  document.head.appendChild(style);
}
// Initialize mobile optimizations
if (typeof window !== "undefined") {
  // Add mobile CSS optimizations
  addMobileCSS();
  // Prevent zoom on double tap
  let lastTouchEnd = 0;
  document.addEventListener(
    "touchend",
    event => {
      const now = Date.now();
      if (now - lastTouchEnd <= 300) {
        event.preventDefault();
      }
      lastTouchEnd = now;
    },
    false
  );
}
