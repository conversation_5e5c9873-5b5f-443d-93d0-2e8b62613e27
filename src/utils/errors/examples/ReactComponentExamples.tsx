/**
 * React Component Examples for Centralized Error Handling System
 * 
 * This file demonstrates how to integrate the centralized error handling system
 * with React components in the RiskCompass application.
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ValidationError,
  NetworkError,
  BusinessLogicError,
  errorHandler,
  logger
} from '@/utils/errors';
import {
  RiskErrorBoundary,
  FormErrorBoundary,
  PageErrorBoundary
} from '@/components/error-boundaries';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';

// ============================================================================
// Example 1: Form Component with Error Handling
// ============================================================================

export const RiskFormWithErrorHandling: React.FC = () => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    likelihood: 1,
    impact: 1
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate form data
      await validateFormData(formData);
      
      // Submit form
      const result = await submitRiskForm(formData);
      
      toast({
        title: "Success",
        description: "Risk created successfully",
        variant: "default"
      });
      
      navigate(`/risks/${result.id}`);

    } catch (error) {
      // Handle errors through centralized system
      await errorHandler.handle(error, {
        component: 'risk_form',
        action: 'form_submission',
        additionalData: { formData }
      });

      // The error handler will show appropriate user notifications
      // We can also add component-specific handling here if needed
      if (error instanceof ValidationError) {
        // Focus on the invalid field
        const field = document.querySelector(`[name="${error.field}"]`) as HTMLElement;
        if (field) {
          field.focus();
        }
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const validateFormData = async (data: typeof formData) => {
    if (!data.title || data.title.trim().length < 3) {
      throw new ValidationError(
        'Title must be at least 3 characters long',
        'title',
        'minLength',
        {
          component: 'risk_form',
          action: 'validation'
        }
      );
    }

    if (!data.description || data.description.trim().length < 10) {
      throw new ValidationError(
        'Description must be at least 10 characters long',
        'description',
        'minLength',
        {
          component: 'risk_form',
          action: 'validation'
        }
      );
    }
  };

  const submitRiskForm = async (data: typeof formData) => {
    const response = await fetch('/api/risks', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      throw new NetworkError(
        `Failed to create risk: ${response.statusText}`,
        response.status,
        '/api/risks',
        'POST',
        {
          component: 'risk_form',
          action: 'api_submission'
        }
      );
    }

    return response.json();
  };

  return (
    <FormErrorBoundary formName="risk-creation">
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label htmlFor="title">Risk Title</label>
          <Input
            id="title"
            name="title"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            required
          />
        </div>

        <div>
          <label htmlFor="description">Description</label>
          <textarea
            id="description"
            name="description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            className="w-full p-2 border rounded"
            rows={4}
            required
          />
        </div>

        <Button type="submit" disabled={isSubmitting}>
          {isSubmitting ? 'Creating...' : 'Create Risk'}
        </Button>
      </form>
    </FormErrorBoundary>
  );
};

// ============================================================================
// Example 2: Data Fetching Component with Error Handling
// ============================================================================

export const RiskDetailsWithErrorHandling: React.FC<{ riskId: string }> = ({ riskId }) => {
  const [risk, setRisk] = useState<unknown>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<unknown>(null);

  const fetchRiskDetails = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/risks/${riskId}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          throw new BusinessLogicError(
            'Risk not found',
            'risk_not_found',
            'risk',
            riskId,
            {
              component: 'risk_details',
              action: 'fetch_risk'
            }
          );
        }

        throw new NetworkError(
          `Failed to fetch risk: ${response.statusText}`,
          response.status,
          `/api/risks/${riskId}`,
          'GET',
          {
            component: 'risk_details',
            action: 'fetch_risk'
          }
        );
      }

      const riskData = await response.json();
      setRisk(riskData);
      
      logger.info('Risk details loaded successfully', {
        component: 'risk_details',
        action: 'fetch_risk',
        additionalData: { riskId }
      });

    } catch (error) {
      setError(error);
      
      // Handle through centralized system
      await errorHandler.handle(error, {
        component: 'risk_details',
        action: 'fetch_risk',
        additionalData: { riskId }
      });
    } finally {
      setLoading(false);
    }
  }, [riskId]);

  useEffect(() => {
    fetchRiskDetails();
  }, [fetchRiskDetails]);

  const handleRetry = () => {
    fetchRiskDetails();
  };

  if (loading) {
    return <div>Loading risk details...</div>;
  }

  if (error) {
    return (
      <div className="p-4 border border-red-200 rounded bg-red-50">
        <h3 className="font-semibold text-red-800">Error Loading Risk</h3>
        <p className="text-red-600 mb-3">
          {error instanceof BusinessLogicError && error.businessRule === 'risk_not_found'
            ? 'The requested risk could not be found.'
            : 'There was an error loading the risk details.'
          }
        </p>
        <Button onClick={handleRetry} variant="outline" size="sm">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <RiskErrorBoundary>
      <div className="space-y-4">
        <h1 className="text-2xl font-bold">{risk.title}</h1>
        <p>{risk.description}</p>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <strong>Likelihood:</strong> {risk.likelihood}
          </div>
          <div>
            <strong>Impact:</strong> {risk.impact}
          </div>
        </div>
      </div>
    </RiskErrorBoundary>
  );
};

// ============================================================================
// Example 3: Page Component with Error Boundary
// ============================================================================

export const RiskManagementPage: React.FC = () => {
  return (
    <PageErrorBoundary pageName="risk-management">
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-8">Risk Management</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <h2 className="text-xl font-semibold mb-4">Create New Risk</h2>
            <RiskFormWithErrorHandling />
          </div>
          
          <div>
            <h2 className="text-xl font-semibold mb-4">Recent Risks</h2>
            <RiskListWithErrorHandling />
          </div>
        </div>
      </div>
    </PageErrorBoundary>
  );
};

// ============================================================================
// Example 4: List Component with Error Handling
// ============================================================================

const RiskListWithErrorHandling: React.FC = () => {
  const [risks, setRisks] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchRisks = useCallback(async () => {
    try {
      setLoading(true);

      // Use error handler's retry mechanism for network requests
      const data = await errorHandler.handleWithRetry(
        async () => {
          const response = await fetch('/api/risks');
          if (!response.ok) {
            throw new NetworkError(
              `Failed to fetch risks: ${response.statusText}`,
              response.status,
              '/api/risks',
              'GET'
            );
          }
          return response.json();
        },
        {
          component: 'risk_list',
          action: 'fetch_risks'
        },
        3 // max retry attempts
      );

      setRisks(data);

    } catch (error) {
      // Error is already handled by handleWithRetry
      // Component can show fallback UI or empty state
      setRisks([]);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchRisks();
  }, [fetchRisks]);

  if (loading) {
    return <div>Loading risks...</div>;
  }

  return (
    <RiskErrorBoundary>
      <div className="space-y-2">
        {risks.length === 0 ? (
          <p className="text-muted-foreground">No risks found.</p>
        ) : (
          risks.map((risk) => (
            <div key={risk.id} className="p-3 border rounded">
              <h3 className="font-medium">{risk.title}</h3>
              <p className="text-sm text-muted-foreground">{risk.description}</p>
            </div>
          ))
        )}
      </div>
    </RiskErrorBoundary>
  );
};
