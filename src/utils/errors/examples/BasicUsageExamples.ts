/**
 * Basic Usage Examples for Centralized Error Handling System
 *
 * This file demonstrates how to use the centralized error handling system
 * in various scenarios throughout the RiskCompass application.
 */
import {
  AppError,
  ValidationError,
  NetworkError,
  AuthenticationError,
  BusinessLogicError,
  SystemError,
  DataProcessingError,
  errorHandler,
  logger,
} from "@/utils/errors";
import { ErrorSeverity, ErrorCategory } from "@/types";
// ============================================================================
// Example 1: Form Validation with Custom Error Handling
// ============================================================================
export const handleFormValidation = async (formData: Record<string, unknown>) => {
  try {
    // Validate required fields
    if (!formData.title || formData.title.trim().length < 3) {
      throw new ValidationError("Title must be at least 3 characters long", "title", "minLength", {
        component: "risk_form",
        action: "form_validation",
        userId: "current-user-id",
      });
    }
    if (!formData.description || formData.description.trim().length < 10) {
      throw new ValidationError(
        "Description must be at least 10 characters long",
        "description",
        "minLength",
        {
          component: "risk_form",
          action: "form_validation",
        }
      );
    }
    // If validation passes, continue with form submission
    return { success: true };
  } catch (error) {
    // Handle validation errors through centralized system
    await errorHandler.handle(error, {
      component: "risk_form",
      action: "form_validation",
    });
    return { success: false, error };
  }
};
// ============================================================================
// Example 2: API Call with Network Error Handling
// ============================================================================
export const fetchRiskData = async (riskId: string) => {
  try {
    const response = await fetch(`/api/risks/${riskId}`);
    if (!response.ok) {
      throw new NetworkError(
        `Failed to fetch risk data: ${response.statusText}`,
        response.status,
        `/api/risks/${riskId}`,
        "GET",
        {
          component: "risk_details",
          action: "fetch_risk_data",
          additionalData: { riskId },
        }
      );
    }
    const data = await response.json();
    logger.info("Risk data fetched successfully", {
      component: "risk_details",
      action: "fetch_risk_data",
      additionalData: { riskId },
    });
    return data;
  } catch (error) {
    // Use handleWithRetry for automatic retry on network errors
    return await errorHandler.handleWithRetry(
      () => fetch(`/api/risks/${riskId}`).then(r => r.json()),
      {
        component: "risk_details",
        action: "fetch_risk_data",
        additionalData: { riskId },
      },
      3 // max retry attempts
    );
  }
};
// ============================================================================
// Example 3: Authentication Error Handling
// ============================================================================
export const handleAuthenticatedRequest = async (requestFn: () => Promise<unknown>) => {
  try {
    return await requestFn();
  } catch (error: unknown) {
    if (error.status === 401) {
      throw new AuthenticationError("Authentication token expired", "token", {
        component: "api_client",
        action: "authenticated_request",
      });
    }
    if (error.status === 403) {
      throw new AuthenticationError("Insufficient permissions", "permission", {
        component: "api_client",
        action: "authenticated_request",
      });
    }
    // Re-throw other errors to be handled elsewhere
    throw error;
  }
};
// ============================================================================
// Example 4: Business Logic Error Handling
// ============================================================================
export const validateRiskBusinessRules = async (riskData: Record<string, unknown>) => {
  try {
    // Check if risk can be closed
    if (
      riskData.status === "CLOSED" &&
      riskData.mitigationActions?.some((action: Record<string, unknown>) => !action.completed)
    ) {
      throw new BusinessLogicError(
        "Cannot close risk with incomplete mitigation actions",
        "risk_closure_validation",
        "risk",
        riskData.id,
        {
          component: "risk_management",
          action: "risk_status_change",
          additionalData: {
            riskId: riskData.id,
            currentStatus: riskData.status,
            incompleteMitigations: riskData.mitigationActions?.filter(
              (a: Record<string, unknown>) => !a.completed
            ).length,
          },
        }
      );
    }
    // Check severity vs impact/likelihood consistency
    const calculatedSeverity = calculateSeverity(riskData.likelihood, riskData.impact);
    if (riskData.severity !== calculatedSeverity) {
      throw new BusinessLogicError(
        "Risk severity does not match calculated severity based on likelihood and impact",
        "severity_consistency_check",
        "risk",
        riskData.id,
        {
          component: "risk_management",
          action: "risk_validation",
          additionalData: {
            providedSeverity: riskData.severity,
            calculatedSeverity,
            likelihood: riskData.likelihood,
            impact: riskData.impact,
          },
        }
      );
    }
    return { valid: true };
  } catch (error) {
    await errorHandler.handle(error);
    return { valid: false, error };
  }
};
// Helper function for severity calculation
const calculateSeverity = (likelihood: number, impact: number): string => {
  const score = likelihood * impact;
  if (score >= 20) return "CRITICAL";
  if (score >= 15) return "HIGH";
  if (score >= 10) return "MEDIUM";
  return "LOW";
};
// ============================================================================
// Example 5: Data Processing Error Handling
// ============================================================================
export const processRiskImportData = async (csvData: unknown[]) => {
  const results = {
    processed: 0,
    errors: [] as unknown[],
    warnings: [] as unknown[],
  };
  for (let i = 0; i < csvData.length; i++) {
    const rowNumber = i + 2; // +2 because row 1 is headers
    try {
      const row = csvData[i];
      // Validate row data
      if (!row.title || !row.description) {
        throw new DataProcessingError(
          `Missing required fields in row ${rowNumber}`,
          "csv_import",
          "row_validation",
          1,
          {
            component: "risk_import",
            action: "csv_processing",
            additionalData: { rowNumber, missingFields: [] },
          }
        );
      }
      // Process the row
      await processRiskRow(row);
      results.processed++;
    } catch (error) {
      if (error instanceof DataProcessingError) {
        results.errors.push({
          row: rowNumber,
          message: error.userMessage,
          technical: error.technicalMessage,
        });
        // Log but don't stop processing
        logger.error(`Row ${rowNumber} processing failed`, error);
      } else {
        // Handle unexpected errors
        await errorHandler.handle(error, {
          component: "risk_import",
          action: "csv_processing",
          additionalData: { rowNumber },
        });
        results.errors.push({
          row: rowNumber,
          message: "Unexpected error occurred",
          technical: error instanceof Error ? error.message : String(error),
        });
      }
    }
  }
  return results;
};
const processRiskRow = async (row: Record<string, unknown>) => {
  // Simulate row processing
  await new Promise(resolve => setTimeout(resolve, 10));
};
// ============================================================================
// Example 6: System Error Handling with Recovery
// ============================================================================
export const handleDatabaseOperation = async (operation: () => Promise<unknown>) => {
  try {
    return await operation();
  } catch (error: unknown) {
    if (error.code === "CONNECTION_LOST") {
      throw new SystemError(
        "Database connection lost",
        "database",
        "CONNECTION_LOST",
        {
          component: "database_client",
          action: "database_operation",
        },
        ErrorSeverity.HIGH
      );
    }
    if (error.code === "TIMEOUT") {
      throw new SystemError(
        "Database operation timed out",
        "database",
        "TIMEOUT",
        {
          component: "database_client",
          action: "database_operation",
        },
        ErrorSeverity.MEDIUM
      );
    }
    // Handle as generic system error
    throw new SystemError(`Database operation failed: ${error.message}`, "database", error.code, {
      component: "database_client",
      action: "database_operation",
    });
  }
};
