/**
 * Integration Examples for Centralized Error Handling System
 *
 * This file demonstrates how to integrate and configure the centralized error handling system
 * in your RiskCompass application, including setup, configuration, and advanced usage patterns.
 */

import {
  errorHandler,
  logger,
  errorReporting,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>gger,
  <PERSON><PERSON><PERSON>Reporting,
} from "@/utils/errors";
import { LogLevel } from "@/utils/errors/Logger";

// ============================================================================
// Example 1: Application Initialization and Setup
// ============================================================================

export const initializeErrorHandling = () => {
  // Get environment info from Vite
  const isProduction = import.meta.env.MODE === "production";

  // Configure logger - disable console in production
  logger.updateConfig({
    minLevel: isProduction ? LogLevel.ERROR : LogLevel.DEBUG,
    enableConsole: !isProduction, // Disable console logging in production
    enableRemote: isProduction,
    remoteEndpoint: import.meta.env.VITE_LOG_ENDPOINT,
    maxLogEntries: isProduction ? 100 : 1000, // Reduce memory usage in production
    enableStackTrace: !isProduction, // Disable stack traces in production
  });

  // Configure error handler - disable console logging in production
  errorHandler.updateConfig({
    enableToastNotifications: true,
    enableConsoleLogging: !isProduction, // Disable console logging in production
    enableRemoteReporting: isProduction,
    autoRetry: true,
    maxRetryAttempts: 3,
    retryDelay: 1000,
  });

  // Configure error reporting
  errorReporting.updateConfig({
    enabled: isProduction,
    endpoint: import.meta.env.VITE_ERROR_REPORTING_ENDPOINT,
    apiKey: import.meta.env.VITE_ERROR_REPORTING_API_KEY,
    environment: import.meta.env.MODE as "development" | "staging" | "production",
    buildVersion: import.meta.env.VITE_BUILD_VERSION,
    maxReportsPerSession: 50,
    enableUserFeedback: true,
    enableScreenshots: false,
    enableConsoleCapture: true,
  });

  // Setup global error handling
  errorHandler.setupGlobalErrorHandling();

  logger.info("Error handling system initialized", {
    component: "app_initialization",
    action: "error_system_setup",
  });
};

// ============================================================================
// Example 2: Custom Error Handler for Specific Services
// ============================================================================

export class RiskServiceErrorHandler {
  private static instance: RiskServiceErrorHandler;

  private constructor() {
    // Implementation needed
  }
  public static getInstance(): RiskServiceErrorHandler {
    if (!RiskServiceErrorHandler.instance) {
      RiskServiceErrorHandler.instance = new RiskServiceErrorHandler();
    }
    return RiskServiceErrorHandler.instance;
  }

  /**
   * Handle risk-specific operations with custom error handling
   */
  public async handleRiskOperation<T>(
    operation: () => Promise<T>,
    operationType: "create" | "update" | "delete" | "fetch",
    riskId?: string
  ): Promise<T> {
    try {
      logger.debug(`Starting risk ${operationType} operation`, {
        component: "risk_service",
        action: `risk_${operationType}`,
        additionalData: { riskId },
      });

      const result = await operation();

      logger.info(`Risk ${operationType} operation completed successfully`, {
        component: "risk_service",
        action: `risk_${operationType}`,
        additionalData: { riskId },
      });

      return result;
    } catch (error) {
      // Add risk-specific context to error
      const context = {
        component: "risk_service",
        action: `risk_${operationType}`,
        additionalData: {
          riskId,
          operationType,
          timestamp: new Date().toISOString(),
        },
      };

      // Handle through centralized system with custom recovery actions
      await errorHandler.handle(error, context, [
        {
          label: "Retry Operation",
          action: async () => {
            try {
              return await operation();
            } catch (retryError) {
              await errorHandler.handle(retryError, context);
            }
          },
          primary: true,
        },
        {
          label: "Go to Risk List",
          action: () => {
            window.location.href = "/risks";
          },
        },
      ]);

      throw error;
    }
  }

  /**
   * Handle bulk risk operations with progress tracking
   */
  public async handleBulkRiskOperation<T>(
    items: unknown[],
    operation: (item: unknown, index: number) => Promise<T>,
    operationType: string
  ): Promise<{ results: T[]; errors: unknown[] }> {
    const results: T[] = [];
    const errors: unknown[] = [];

    logger.info(`Starting bulk ${operationType} operation`, {
      component: "risk_service",
      action: `bulk_${operationType}`,
      additionalData: { itemCount: items.length },
    });

    for (let i = 0; i < items.length; i++) {
      try {
        const result = await operation(items[i], i);
        results.push(result);
      } catch (error) {
        errors.push({
          index: i,
          item: items[i],
          error: error instanceof Error ? error.message : String(error),
        });

        // Log individual errors but don't stop the bulk operation
        logger.error(`Bulk operation failed for item ${i}`, error as Error, {
          component: "risk_service",
          action: `bulk_${operationType}`,
          additionalData: { itemIndex: i, totalItems: items.length },
        });
      }
    }

    logger.info(`Bulk ${operationType} operation completed`, {
      component: "risk_service",
      action: `bulk_${operationType}`,
      additionalData: {
        totalItems: items.length,
        successCount: results.length,
        errorCount: errors.length,
      },
    });

    return { results, errors };
  }
}

// ============================================================================
// Example 3: API Client with Centralized Error Handling
// ============================================================================

export class ApiClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseUrl: string = "/api") {
    this.baseUrl = baseUrl;
    this.defaultHeaders = {
      "Content-Type": "application/json",
    };
  }

  /**
   * Make API request with centralized error handling
   */
  public async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;

    return await errorHandler.handleWithRetry(
      async () => {
        const response = await fetch(url, {
          ...options,
          headers: {
            ...this.defaultHeaders,
            ...options.headers,
          },
        });

        if (!response.ok) {
          const { NetworkError, AuthenticationError } = await import("@/utils/errors/exports");

          if (response.status === 401) {
            throw new AuthenticationError("Authentication required", "token", {
              component: "api_client",
              action: "api_request",
              additionalData: { endpoint, method: options.method ?? "GET" },
            });
          }

          throw new NetworkError(
            `API request failed: ${response.statusText}`,
            response.status,
            endpoint,
            (options.method as string) ?? "GET",
            {
              component: "api_client",
              action: "api_request",
            }
          );
        }

        return response.json();
      },
      {
        component: "api_client",
        action: "api_request",
        additionalData: { endpoint, method: options.method ?? "GET" },
      },
      3 // max retry attempts
    );
  }

  // Convenience methods
  public async get<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: "GET" });
  }

  public async post<T>(endpoint: string, data: unknown): Promise<T> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: JSON.stringify(data),
    });
  }

  public async put<T>(endpoint: string, data: unknown): Promise<T> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: JSON.stringify(data),
    });
  }

  public async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: "DELETE" });
  }
}

// ============================================================================
// Example 4: React Hook for Error Handling
// ============================================================================

import { useState, useCallback } from "react";

export const useErrorHandler = () => {
  const [error, setError] = useState<unknown>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleAsync = useCallback(
    async <T>(
      asyncOperation: () => Promise<T>,
      context?: Partial<import("@/types").ErrorContext>
    ): Promise<T | null> => {
      try {
        setIsLoading(true);
        setError(null);

        const result = await asyncOperation();
        return result;
      } catch (error) {
        setError(error);

        // Handle through centralized system
        await errorHandler.handle(error, context);

        return null;
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    error,
    isLoading,
    handleAsync,
    clearError,
  };
};

// ============================================================================
// Example 5: Environment-Specific Configuration
// ============================================================================

export const getErrorHandlingConfig = () => {
  const isDevelopment = import.meta.env.MODE === "development";
  const isProduction = import.meta.env.MODE === "production";

  return {
    logger: {
      minLevel: isDevelopment ? LogLevel.DEBUG : LogLevel.WARN,
      enableConsole: true,
      enableRemote: isProduction,
      remoteEndpoint: import.meta.env.VITE_LOG_ENDPOINT,
      maxLogEntries: isDevelopment ? 500 : 1000,
      enableStackTrace: true,
    },
    errorHandler: {
      enableToastNotifications: true,
      enableConsoleLogging: true,
      enableRemoteReporting: isProduction,
      autoRetry: true,
      maxRetryAttempts: isDevelopment ? 1 : 3,
      retryDelay: isDevelopment ? 500 : 1000,
    },
    errorReporting: {
      enabled: isProduction,
      endpoint: import.meta.env.VITE_ERROR_REPORTING_ENDPOINT,
      apiKey: import.meta.env.VITE_ERROR_REPORTING_API_KEY,
      environment: import.meta.env.MODE as "development" | "staging" | "production",
      buildVersion: import.meta.env.VITE_BUILD_VERSION ?? "dev",
      maxReportsPerSession: isDevelopment ? 10 : 50,
      enableUserFeedback: true,
      enableScreenshots: isProduction,
      enableConsoleCapture: true,
    },
  };
};

// Export singleton instances

export const riskServiceErrorHandler = RiskServiceErrorHandler.getInstance();

export const apiClient = new ApiClient();
