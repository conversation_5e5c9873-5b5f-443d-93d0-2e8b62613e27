import { ErrorMetadata, ErrorSeverity, ErrorContext } from "@/types";
import { AppError } from "./AppError";
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  CRITICAL = 4,
}
export interface LogEntry {
  id: string;
  correlationId: string;
  timestamp: Date;
  level: LogLevel;
  message: string;
  context?: ErrorContext | undefined;
  metadata?: Record<string, any> | undefined;
  error?: ErrorMetadata | undefined;
  stack?: string | undefined;
  environment: string;
  sessionId?: string | undefined;
  userId?: string | undefined;
  component?: string | undefined;
  action?: string | undefined;
}
export interface LoggerConfig {
  minLevel: LogLevel;
  enableConsole: boolean;
  enableRemote: boolean;
  remoteEndpoint?: string;
  maxLogEntries: number;
  enableStackTrace: boolean;
  environment: string;
  enableCorrelationIds: boolean;
  batchSize: number;
  flushInterval: number;
}
/**
 * Centralized logging service with structured logging capabilities
 */
export class Logger {
  private static instance: Logger;
  private config: LoggerConfig;
  private logEntries: LogEntry[] = [];
  private logBatch: LogEntry[] = [];
  private logId = 0;
  private correlationIdCounter = 0;
  private currentCorrelationId: string | null = null;
  private sessionId: string;
  private flushTimer: NodeJS.Timeout | null = null;
  private constructor(config: Partial<LoggerConfig> = {}) {
    const environment = this.detectEnvironment();
    this.config = {
      minLevel: environment === "production" ? LogLevel.ERROR : LogLevel.DEBUG,
      enableConsole: environment !== "production",
      enableRemote: environment === "production",
      maxLogEntries: 1000,
      enableStackTrace: true,
      environment,
      enableCorrelationIds: true,
      batchSize: 10,
      flushInterval: 5000, // 5 seconds
      ...config,
    };
    this.sessionId = this.generateSessionId();
    this.setupPeriodicFlush();
  }
  /**
   * Detect current environment
   */
  private detectEnvironment(): string {
    if (typeof window !== "undefined") {
      return import.meta.env["MODE"] === "production" ? "production" : "development";
    }
    return process.env["NODE_ENV"] === "production" ? "production" : "development";
  }
  /**
   * Generate session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }
  /**
   * Setup periodic flush for batched logging
   */
  private setupPeriodicFlush(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flushTimer = setInterval(() => {
      this.flushBatch();
    }, this.config.flushInterval);
  }
  /**
   * Get singleton instance of Logger
   */
  public static getInstance(config?: Partial<LoggerConfig>): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger(config);
    } else if (config) {
      // Update existing instance with new config
      Logger.instance.updateConfig(config);
    }
    return Logger.instance;
  }
  /**
   * Reset singleton instance (for testing)
   */
  public static resetInstance(): void {
    if (Logger.instance) {
      Logger.instance.destroy();
      Logger.instance = null as any;
    }
  }
  /**
   * Generate unique log ID
   */
  private generateLogId(): string {
    return `log_${Date.now()}_${++this.logId}`;
  }
  /**
   * Generate correlation ID for request tracking
   */
  private generateCorrelationId(): string {
    return `corr_${Date.now()}_${++this.correlationIdCounter}`;
  }
  /**
   * Start a new correlation context
   */
  public startCorrelation(correlationId?: string): string {
    this.currentCorrelationId = correlationId || this.generateCorrelationId();
    return this.currentCorrelationId;
  }
  /**
   * End current correlation context
   */
  public endCorrelation(): void {
    this.currentCorrelationId = null;
  }
  /**
   * Get current correlation ID
   */
  public getCurrentCorrelationId(): string | null {
    return this.currentCorrelationId;
  }
  /**
   * Extract user ID from context or session
   */
  private extractUserId(context?: ErrorContext): string | undefined {
    if (context?.userId) return context.userId;
    // Try to get from session storage or other sources
    if (typeof window !== "undefined") {
      try {
        const userData = sessionStorage.getItem("user");
        if (userData) {
          const user = JSON.parse(userData);
          return user.id;
        }
      } catch {
        // Ignore parsing errors
      }
    }
    return undefined;
  }
  /**
   * Create log entry
   */
  private createLogEntry(
    level: LogLevel,
    message: string,
    context?: ErrorContext,
    metadata?: Record<string, any>,
    error?: AppError | Error
  ): LogEntry {
    const correlationId = this.config.enableCorrelationIds
      ? this.currentCorrelationId || this.generateCorrelationId()
      : "";
    const entry: LogEntry = {
      id: this.generateLogId(),
      correlationId,
      timestamp: new Date(),
      level,
      message,
      context,
      metadata,
      environment: this.config.environment,
      sessionId: this.sessionId,
      userId: this.extractUserId(context),
      component: context?.component,
      action: context?.action,
    };
    if (error) {
      if (error instanceof AppError) {
        entry.error = error.toMetadata();
      } else {
        entry.stack = error.stack;
      }
    }
    return entry;
  }
  /**
   * Add log entry to internal storage and batch
   */
  private addLogEntry(entry: LogEntry): void {
    this.logEntries.push(entry);
    this.logBatch.push(entry);
    // Maintain max log entries limit
    if (this.logEntries.length > this.config.maxLogEntries) {
      this.logEntries = this.logEntries.slice(-this.config.maxLogEntries);
    }
    // Auto-flush if batch is full
    if (this.logBatch.length >= this.config.batchSize) {
      this.flushBatch();
    }
  }
  /**
   * Output to console based on log level (only in development)
   */
  private outputToConsole(entry: LogEntry): void {
    if (!this.config.enableConsole) return;
    const timestamp = entry.timestamp.toISOString();
    const correlationInfo = entry.correlationId ? `[${entry.correlationId}]` : "";
    const componentInfo = entry.component ? `[${entry.component}]` : "";
    const prefix = `[${timestamp}] [${LogLevel[entry.level]}]${correlationInfo}${componentInfo}`;
    const message = `${prefix} ${entry.message}`;
    const logData = {
      ...entry.context,
      ...entry.metadata,
      ...(entry.userId && { userId: entry.userId }),
      ...(entry.action && { action: entry.action }),
    };
    switch (entry.level) {
      case LogLevel.DEBUG:
        break;
      case LogLevel.INFO:
        break;
      case LogLevel.WARN:
        break;
      case LogLevel.ERROR:
      case LogLevel.CRITICAL:
        if (entry.error?.stack ?? entry.stack) {
          // Condition handled
        }
        break;
    }
  }
  /**
   * Flush batch of logs to remote endpoint
   */
  private async flushBatch(): Promise<void> {
    if (this.logBatch.length === 0) return;
    const batchToSend = [...this.logBatch];
    this.logBatch = [];
    if (this.config.enableRemote && this.config.remoteEndpoint) {
      try {
        await fetch(this.config.remoteEndpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            logs: batchToSend,
            sessionId: this.sessionId,
            environment: this.config.environment,
            timestamp: new Date().toISOString(),
          }),
        });
      } catch (error) {
        // In production, we might want to queue failed batches for retry
        if (this.config.environment === "development") {
          // Condition handled
        }
        // Re-add failed logs to batch for retry (with limit to prevent memory issues)
        if (this.logBatch.length < this.config.maxLogEntries / 2) {
          this.logBatch.unshift(...batchToSend);
        }
      }
    }
  }
  /**
   * Log a message with specified level
   */
  private log(
    level: LogLevel,
    message: string,
    context?: ErrorContext,
    metadata?: Record<string, any>,
    error?: AppError | Error
  ): void {
    if (level < this.config.minLevel) return;
    const entry = this.createLogEntry(level, message, context, metadata, error);
    this.addLogEntry(entry);
    this.outputToConsole(entry);
  }
  /**
   * Debug level logging
   */
  public debug(message: string, context?: ErrorContext, metadata?: Record<string, any>): void {
    this.log(LogLevel.DEBUG, message, context, metadata);
  }
  /**
   * Info level logging
   */
  public info(message: string, context?: ErrorContext, metadata?: Record<string, any>): void {
    this.log(LogLevel.INFO, message, context, metadata);
  }
  /**
   * Warning level logging
   */
  public warn(message: string, context?: ErrorContext, metadata?: Record<string, any>): void {
    this.log(LogLevel.WARN, message, context, metadata);
  }
  /**
   * Error level logging
   */
  public error(
    message: string,
    error?: AppError | Error,
    context?: ErrorContext,
    metadata?: Record<string, any>
  ): void {
    this.log(LogLevel.ERROR, message, context, metadata, error);
  }
  /**
   * Critical level logging
   */
  public critical(
    message: string,
    error?: AppError | Error,
    context?: ErrorContext,
    metadata?: Record<string, any>
  ): void {
    this.log(LogLevel.CRITICAL, message, context, metadata, error);
  }
  /**
   * Log an AppError with appropriate level
   */
  public logError(error: AppError, additionalContext?: Partial<ErrorContext>): void {
    const context = { ...error.context, ...additionalContext };
    const level = this.mapSeverityToLogLevel(error.severity);
    this.log(level, error.technicalMessage, context, undefined, error);
  }
  /**
   * Map error severity to log level
   */
  private mapSeverityToLogLevel(severity: ErrorSeverity): LogLevel {
    switch (severity) {
      case ErrorSeverity.LOW:
        return LogLevel.WARN;
      case ErrorSeverity.MEDIUM:
        return LogLevel.ERROR;
      case ErrorSeverity.HIGH:
        return LogLevel.ERROR;
      case ErrorSeverity.CRITICAL:
        return LogLevel.CRITICAL;
      default:
        return LogLevel.ERROR;
    }
  }
  /**
   * Get recent log entries
   */
  public getRecentLogs(count: number = 50): LogEntry[] {
    return this.logEntries.slice(-count);
  }
  /**
   * Get logs by level
   */
  public getLogsByLevel(level: LogLevel): LogEntry[] {
    return this.logEntries.filter(entry => entry.level === level);
  }
  /**
   * Get logs by correlation ID
   */
  public getLogsByCorrelationId(correlationId: string): LogEntry[] {
    return this.logEntries.filter(entry => entry.correlationId === correlationId);
  }
  /**
   * Get logs by component
   */
  public getLogsByComponent(component: string): LogEntry[] {
    return this.logEntries.filter(entry => entry.component === component);
  }
  /**
   * Clear all log entries
   */
  public clearLogs(): void {
    this.logEntries = [];
    this.logBatch = [];
  }
  /**
   * Force flush current batch
   */
  public async flush(): Promise<void> {
    await this.flushBatch();
  }
  /**
   * Update logger configuration
   */
  public updateConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
    // Restart flush timer if interval changed
    if (config.flushInterval) {
      this.setupPeriodicFlush();
    }
  }
  /**
   * Get current configuration
   */
  public getConfig(): LoggerConfig {
    return { ...this.config };
  }
  /**
   * Get logging statistics
   */
  public getStats(): {
    totalLogs: number;
    logsByLevel: Record<string, number>;
    batchSize: number;
    sessionId: string;
    environment: string;
  } {
    const logsByLevel: Record<string, number> = {};
    Object.values(LogLevel).forEach(level => {
      if (typeof level === "string") {
        logsByLevel[level] = this.logEntries.filter(
          entry => LogLevel[entry.level] === level
        ).length;
      }
    });
    return {
      totalLogs: this.logEntries.length,
      logsByLevel,
      batchSize: this.logBatch.length,
      sessionId: this.sessionId,
      environment: this.config.environment,
    };
  }
  /**
   * Cleanup resources
   */
  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = null;
    }
    // Flush any remaining logs
    this.flushBatch();
  }
}
// Export singleton instance with environment-specific configuration
export const logger = Logger.getInstance({
  remoteEndpoint: import.meta.env["VITE_LOGGING_ENDPOINT"] || "/api/logs",
});
