import { ErrorCategory, ErrorSeverity, ErrorContext } from "@/types";
import { AppError } from "./AppError";

/**
 * Validation Error - for form validation and input validation issues
 */
export class ValidationError extends AppError {
  public readonly field?: string;
  public readonly validationRule?: string;

  constructor(
    message: string,
    field?: string,
    validationRule?: string,
    context: Partial<ErrorContext> = {
      // Implementation needed
    }
  ) {
    super(message, ErrorCategory.VALIDATION, ErrorSeverity.LOW, context, {
      userMessage: `Please check the ${field ?? "input"} field: ${message}`,
      suggestedActions: [
        "Review the form fields",
        "Ensure all required fields are filled",
        "Check data formats and requirements",
      ],
    });

    this.field = field;
    this.validationRule = validationRule;
  }
}

/**
 * Network Error - for API calls, network connectivity issues
 */
export class NetworkError extends AppError {
  public readonly statusCode?: number;
  public readonly endpoint?: string;
  public readonly method?: string;

  constructor(
    message: string,
    statusCode?: number,
    endpoint?: string,
    method?: string,
    context: Partial<ErrorContext> = {
      // Implementation needed
    }
  ) {
    const severity = statusCode && statusCode >= 500 ? ErrorSeverity.HIGH : ErrorSeverity.MEDIUM;

    super(message, ErrorCategory.NETWORK, severity, context, {
      userMessage:
        statusCode && statusCode >= 500
          ? "Server error occurred. Please try again later."
          : "Network connection issue. Please check your internet connection.",
      suggestedActions: [
        "Check your internet connection",
        "Try refreshing the page",
        "Wait a moment and try again",
        "Contact support if the issue persists",
      ],
    });

    this.statusCode = statusCode;
    this.endpoint = endpoint;
    this.method = method;
  }
}

/**
 * Authentication Error - for login, token, and auth issues
 */
export class AuthenticationError extends AppError {
  public readonly authType?: "login" | "token" | "session" | "permission";

  constructor(
    message: string,
    authType?: "login" | "token" | "session" | "permission",
    context: Partial<ErrorContext> = {
      // Implementation needed
    }
  ) {
    super(message, ErrorCategory.AUTHENTICATION, ErrorSeverity.MEDIUM, context, {
      recoverable: true,
      userMessage: "Authentication failed. Please log in again.",
      suggestedActions: [
        "Log out and log back in",
        "Clear browser cache and cookies",
        "Check your credentials",
        "Contact support if the issue persists",
      ],
    });

    this.authType = authType;
  }
}

/**
 * Authorization Error - for permission and access control issues
 */
export class AuthorizationError extends AppError {
  public readonly requiredPermission?: string;
  public readonly userRole?: string;

  constructor(
    message: string,
    requiredPermission?: string,
    userRole?: string,
    context: Partial<ErrorContext> = {
      // Implementation needed
    }
  ) {
    super(message, ErrorCategory.AUTHORIZATION, ErrorSeverity.MEDIUM, context, {
      recoverable: false,
      userMessage: "You do not have permission to perform this action.",
      suggestedActions: [
        "Contact your administrator",
        "Check your user permissions",
        "Request access if needed",
      ],
    });

    this.requiredPermission = requiredPermission;
    this.userRole = userRole;
  }
}

/**
 * Business Logic Error - for domain-specific business rule violations
 */
export class BusinessLogicError extends AppError {
  public readonly businessRule?: string;
  public readonly entityType?: string;
  public readonly entityId?: string;

  constructor(
    message: string,
    businessRule?: string,
    entityType?: string,
    entityId?: string,
    context: Partial<ErrorContext> = {
      // Implementation needed
    }
  ) {
    super(message, ErrorCategory.BUSINESS_LOGIC, ErrorSeverity.MEDIUM, context, {
      userMessage: "Unable to complete the operation due to business rules.",
      suggestedActions: [
        "Review the operation requirements",
        "Check related data and dependencies",
        "Contact support for clarification",
      ],
    });

    this.businessRule = businessRule;
    this.entityType = entityType;
    this.entityId = entityId;
  }
}

/**
 * System Error - for internal system errors, database issues, etc.
 */
export class SystemError extends AppError {
  public readonly systemComponent?: string;
  public readonly errorCode?: string;

  constructor(
    message: string,
    systemComponent?: string,
    errorCode?: string,
    context: Partial<ErrorContext> = {},
    severity: ErrorSeverity = ErrorSeverity.HIGH
  ) {
    super(message, ErrorCategory.SYSTEM, severity, context, {
      recoverable: severity !== ErrorSeverity.CRITICAL,
      userMessage: "A system error occurred. Please try again later.",
      suggestedActions: [
        "Try again in a few minutes",
        "Refresh the page",
        "Contact support if the issue persists",
      ],
    });

    this.systemComponent = systemComponent;
    this.errorCode = errorCode;
  }
}

/**
 * Data Processing Error - for data import, export, transformation issues
 */
export class DataProcessingError extends AppError {
  public readonly dataType?: string;
  public readonly processingStep?: string;
  public readonly recordCount?: number;

  constructor(
    message: string,
    dataType?: string,
    processingStep?: string,
    recordCount?: number,
    context: Partial<ErrorContext> = {
      // Implementation needed
    }
  ) {
    super(message, ErrorCategory.DATA_PROCESSING, ErrorSeverity.MEDIUM, context, {
      userMessage: "Error processing data. Please check your data format and try again.",
      suggestedActions: [
        "Verify your data format",
        "Check for invalid or missing data",
        "Try with a smaller dataset",
        "Contact support if needed",
      ],
    });

    this.dataType = dataType;
    this.processingStep = processingStep;
    this.recordCount = recordCount;
  }
}

/**
 * External Service Error - for third-party service integration issues
 */
export class ExternalServiceError extends AppError {
  public readonly serviceName?: string;
  public readonly serviceEndpoint?: string;
  public readonly serviceStatusCode?: number;

  constructor(
    message: string,
    serviceName?: string,
    serviceEndpoint?: string,
    serviceStatusCode?: number,
    context: Partial<ErrorContext> = {
      // Implementation needed
    }
  ) {
    const severity =
      serviceStatusCode && serviceStatusCode >= 500 ? ErrorSeverity.HIGH : ErrorSeverity.MEDIUM;

    super(message, ErrorCategory.EXTERNAL_SERVICE, severity, context, {
      userMessage: "External service is temporarily unavailable. Please try again later.",
      suggestedActions: [
        "Try again later",
        "Check service status",
        "Contact support if the issue persists",
      ],
    });

    this.serviceName = serviceName;
    this.serviceEndpoint = serviceEndpoint;
    this.serviceStatusCode = serviceStatusCode;
  }
}
