// Export base error class
export { AppError } from "./AppError";

// Export specific error classes
export {
  ValidationError,
  NetworkError,
  AuthenticationError,
  AuthorizationError,
  BusinessLogicError,
  SystemError,
  DataProcessingError,
  ExternalServiceError,
} from "./exports";

// Export error utilities
export { <PERSON><PERSON>r<PERSON>and<PERSON>, errorHandler } from "./ErrorHandler";
export { Logger, logger } from "./Logger";
export { ErrorReporting, errorReporting } from "./ErrorReporting";
export { ErrorMessages } from "./ErrorMessages";

// Re-export error types from main types
export type { ErrorCategory, ErrorSeverity, ErrorContext, ErrorMetadata } from "@/types";

/**
 * Utility function to check if an error is an AppError instance
 */

export const isAppError = (error: unknown): error is AppError => {
  return error instanceof AppError;
};

/**
 * Utility function to convert any error to AppError
 */

export const toAppError = (
  error: unknown,
  context?: Partial<import("@/types").ErrorContext>
): AppError => {
  if (isAppError(error)) {
    return error;
  }

  if (error instanceof Error) {
    // Import here to avoid circular dependency
    const { SystemError } = require("./exports");
    return new SystemError(
      error.message,
      "unknown",
      undefined,
      context,
      import("@/types").ErrorSeverity.MEDIUM
    );
  }

  // Import here to avoid circular dependency
  const { SystemError } = require("./exports");
  return new SystemError(
    typeof error === "string" ? error : "Unknown error occurred",
    "unknown",
    undefined,
    context,
    import("@/types").ErrorSeverity.MEDIUM
  );
};

/**
 * Utility function to extract error message safely
 */

export const getErrorMessage = (error: unknown): string => {
  if (isAppError(error)) {
    return error.userMessage;
  }

  if (error instanceof Error) {
    return error.message;
  }

  if (typeof error === "string") {
    return error;
  }

  return "An unexpected error occurred";
};

/**
 * Utility function to check if error is recoverable
 */

export const isRecoverableError = (error: unknown): boolean => {
  if (isAppError(error)) {
    return error.isRecoverable();
  }

  // Assume non-AppErrors are recoverable by default
  return true;
};

/**
 * Utility function to check if error is critical
 */

export const isCriticalError = (error: unknown): boolean => {
  if (isAppError(error)) {
    return error.isCritical();
  }

  return false;
};
