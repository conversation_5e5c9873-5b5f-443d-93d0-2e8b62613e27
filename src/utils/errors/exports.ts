/**
 * Static exports for error classes to resolve code splitting conflicts
 * This file provides static imports for error classes that are commonly used
 * across the application without causing chunking issues.
 */

// Re-export commonly used error classes that need static imports
export {
  ValidationError,
  NetworkError,
  AuthenticationError,
  AuthorizationError,
  BusinessLogicError,
  SystemError,
  DataProcessingError,
  ExternalServiceError
} from './SpecificErrors';

// Re-export the base error class
export { AppError } from './AppError';
