import { ErrorMetadata, ErrorSeverity, ErrorContext } from "@/types";
import { AppError } from "./AppError";
import { logger } from "./Logger";

export interface ErrorReport {
  id: string;
  timestamp: Date;
  error: ErrorMetadata;
  userAgent: string;
  url: string;
  userId?: string;
  organizationId?: string;
  sessionId: string;
  buildVersion?: string;
  environment: "development" | "staging" | "production";
}

export interface ErrorReportingConfig {
  enabled: boolean;
  endpoint?: string;
  apiKey?: string;
  environment: "development" | "staging" | "production";
  buildVersion?: string;
  maxReportsPerSession: number;
  enableUserFeedback: boolean;
  enableScreenshots: boolean;
  enableConsoleCapture: boolean;
}

/**
 * Error reporting service for monitoring and analytics
 */
export class ErrorReporting {
  private static instance: ErrorReporting;
  private config: ErrorReportingConfig;
  private sessionId: string;
  private reportCount = 0;
  private consoleHistory: Array<{ level: string; message: string; timestamp: Date }> = [];

  private constructor(config: Partial<ErrorReportingConfig> = {}) {
    this.config = {
      enabled: false,
      environment: "development",
      maxReportsPerSession: 50,
      enableUserFeedback: true,
      enableScreenshots: false,
      enableConsoleCapture: true,
      ...config,
    };

    this.sessionId = this.generateSessionId();

    if (this.config.enableConsoleCapture) {
      this.setupConsoleCapture();
    }
  }

  /**
   * Get singleton instance
   */
  public static getInstance(config?: Partial<ErrorReportingConfig>): ErrorReporting {
    if (!ErrorReporting.instance) {
      ErrorReporting.instance = new ErrorReporting(config);
    }
    return ErrorReporting.instance;
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  /**
   * Setup console capture for debugging context
   */
  private setupConsoleCapture(): void {
    const originalConsole = {
      log: console.log,
      warn: console.warn,
      error: console.error,
      info: console.info,
    };

    const captureConsole = (level: string, originalMethod: (...args: unknown[]) => void) => {
      return (...args: unknown[]) => {
        // Call original method
        originalMethod.apply(console, args);

        // Capture for error reporting
        this.consoleHistory.push({
          level,
          message: args
            .map(arg => (typeof arg === "object" ? JSON.stringify(arg) : String(arg)))
            .join(" "),
          timestamp: new Date(),
        });

        // Keep only last 50 console entries
        if (this.consoleHistory.length > 50) {
          this.consoleHistory = this.consoleHistory.slice(-50);
        }
      };
    };

    console.log = captureConsole("log", originalConsole.log);
    console.warn = captureConsole("warn", originalConsole.warn);
    console.error = captureConsole("error", originalConsole.error);
    console.info = captureConsole("info", originalConsole.info);
  }

  /**
   * Report an error
   */
  public async reportError(
    error: AppError,
    additionalContext?: Partial<ErrorContext>
  ): Promise<boolean> {
    if (!this.config.enabled || this.reportCount >= this.config.maxReportsPerSession) {
      return false;
    }

    try {
      const report = await this.createErrorReport(error, additionalContext);
      const success = await this.sendReport(report);

      if (success) {
        this.reportCount++;
        logger.info("Error report sent successfully", { reportId: report.id });
      }

      return success;
    } catch (reportingError) {
      logger.error("Failed to report error", reportingError as Error);
      return false;
    }
  }

  /**
   * Create error report with all context
   */
  private async createErrorReport(
    error: AppError,
    additionalContext?: Partial<ErrorContext>
  ): Promise<ErrorReport> {
    const report: ErrorReport = {
      id: `report_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`,
      timestamp: new Date(),
      error: error.toMetadata(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      userId: error.context.userId || additionalContext?.userId,
      organizationId: error.context.organizationId || additionalContext?.organizationId,
      sessionId: this.sessionId,
      buildVersion: this.config.buildVersion,
      environment: this.config.environment,
    };

    return report;
  }

  /**
   * Send report to remote endpoint
   */
  private async sendReport(report: ErrorReport): Promise<boolean> {
    if (!this.config.endpoint) {
      logger.warn("Error reporting endpoint not configured");
      return false;
    }

    try {
      const response = await fetch(this.config.endpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...(this.config.apiKey && { Authorization: `Bearer ${this.config.apiKey}` }),
        },
        body: JSON.stringify({
          ...report,
          consoleHistory: this.config.enableConsoleCapture ? this.consoleHistory : undefined,
        }),
      });

      return response.ok;
    } catch (error) {
      logger.error("Failed to send error report", error as Error);
      return false;
    }
  }

  /**
   * Collect user feedback for an error
   */
  public async collectUserFeedback(
    errorId: string,
    feedback: {
      description?: string;
      reproductionSteps?: string;
      expectedBehavior?: string;
      userEmail?: string;
    }
  ): Promise<boolean> {
    if (!this.config.enableUserFeedback || !this.config.endpoint) {
      return false;
    }

    try {
      const response = await fetch(`${this.config.endpoint}/feedback`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...(this.config.apiKey && { Authorization: `Bearer ${this.config.apiKey}` }),
        },
        body: JSON.stringify({
          errorId,
          feedback,
          sessionId: this.sessionId,
          timestamp: new Date().toISOString(),
        }),
      });

      return response.ok;
    } catch (error) {
      logger.error("Failed to send user feedback", error as Error);
      return false;
    }
  }

  /**
   * Get error statistics for monitoring
   */
  public getErrorStats(): {
    reportsThisSession: number;
    maxReports: number;
    sessionId: string;
    consoleHistorySize: number;
  } {
    return {
      reportsThisSession: this.reportCount,
      maxReports: this.config.maxReportsPerSession,
      sessionId: this.sessionId,
      consoleHistorySize: this.consoleHistory.length,
    };
  }

  /**
   * Update configuration
   */
  public updateConfig(config: Partial<ErrorReportingConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Get current configuration
   */
  public getConfig(): ErrorReportingConfig {
    return { ...this.config };
  }

  /**
   * Reset session (useful for testing or after user login)
   */
  public resetSession(): void {
    this.sessionId = this.generateSessionId();
    this.reportCount = 0;
    this.consoleHistory = [];
  }
}

// Export singleton instance

export const errorReporting = ErrorReporting.getInstance();
