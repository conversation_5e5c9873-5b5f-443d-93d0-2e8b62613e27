import { ErrorCategory, ErrorSeverity } from "@/types";
import { AppError } from "./AppError";

export interface UserFriendlyMessage {
  title: string;
  description: string;
  actions: Array<{
    label: string;
    action: () => void;
    primary?: boolean;
    variant?: "default" | "destructive" | "outline";
  }>;
  severity: "info" | "warning" | "error" | "critical";
}

/**
 * Service for generating user-friendly error messages
 */
export class ErrorMessages {
  private static instance: ErrorMessages;

  private constructor() {
    // Implementation needed
  }
  public static getInstance(): ErrorMessages {
    if (!ErrorMessages.instance) {
      ErrorMessages.instance = new ErrorMessages();
    }
    return ErrorMessages.instance;
  }

  /**
   * Generate user-friendly message from AppError
   */
  public generateMessage(error: AppError): UserFriendlyMessage {
    const baseMessage = this.getBaseMessage(error.category, error.severity);

    return {
      ...baseMessage,
      description: error.userMessage || baseMessage.description,
      actions: this.generateActions(error),
      severity: this.mapSeverityToUserSeverity(error.severity),
    };
  }

  /**
   * Get base message template based on category and severity
   */
  private getBaseMessage(
    category: ErrorCategory,
    severity: ErrorSeverity
  ): Omit<UserFriendlyMessage, "severity"> {
    const templates = {
      [ErrorCategory.VALIDATION]: {
        title: "Input Validation Error",
        description: "Please check your input and try again.",
        actions: [],
      },
      [ErrorCategory.NETWORK]: {
        title: severity === ErrorSeverity.HIGH ? "Server Error" : "Connection Issue",
        description:
          severity === ErrorSeverity.HIGH
            ? "Our servers are experiencing issues. Please try again later."
            : "Please check your internet connection and try again.",
        actions: [],
      },
      [ErrorCategory.AUTHENTICATION]: {
        title: "Authentication Required",
        description: "Please log in to continue.",
        actions: [],
      },
      [ErrorCategory.AUTHORIZATION]: {
        title: "Access Denied",
        description: "You do not have permission to perform this action.",
        actions: [],
      },
      [ErrorCategory.BUSINESS_LOGIC]: {
        title: "Operation Not Allowed",
        description: "This operation cannot be completed due to business rules.",
        actions: [],
      },
      [ErrorCategory.SYSTEM]: {
        title: severity === ErrorSeverity.CRITICAL ? "Critical System Error" : "System Error",
        description: "A system error occurred. Please try again later.",
        actions: [],
      },
      [ErrorCategory.USER_INPUT]: {
        title: "Invalid Input",
        description: "Please check your input and try again.",
        actions: [],
      },
      [ErrorCategory.DATA_PROCESSING]: {
        title: "Data Processing Error",
        description: "There was an error processing your data.",
        actions: [],
      },
      [ErrorCategory.EXTERNAL_SERVICE]: {
        title: "Service Unavailable",
        description: "An external service is temporarily unavailable.",
        actions: [],
      },
    };

    return (
      templates[category] || {
        title: "Unexpected Error",
        description: "An unexpected error occurred.",
        actions: [],
      }
    );
  }

  /**
   * Generate contextual actions based on error
   */
  private generateActions(error: AppError): UserFriendlyMessage["actions"] {
    const actions: UserFriendlyMessage["actions"] = [];

    // Add retry action for recoverable errors
    if (error.isRecoverable()) {
      actions.push({
        label: "Try Again",
        action: () => window.location.reload(),
        primary: true,
      });
    }

    // Add category-specific actions
    switch (error.category) {
      case ErrorCategory.AUTHENTICATION:
        actions.push({
          label: "Log In",
          action: () => (window.location.href = "/login"),
          primary: true,
        });
        break;

      case ErrorCategory.NETWORK:
        actions.push({
          label: "Check Connection",
          action: () => {
            if (navigator.onLine) {
              alert("Your connection appears to be working. The issue may be with our servers.");
            } else {
              alert("You appear to be offline. Please check your internet connection.");
            }
          },
          variant: "outline" as const,
        });
        break;

      case ErrorCategory.VALIDATION:
      case ErrorCategory.USER_INPUT:
        actions.push({
          label: "Review Input",
          action: () => {
            // Focus on first invalid input if available
            const firstInvalid = document.querySelector('[aria-invalid="true"]') as HTMLElement;
            if (firstInvalid) {
              firstInvalid.focus();
            }
          },
          variant: "outline" as const,
        });
        break;

      case ErrorCategory.AUTHORIZATION:
        actions.push({
          label: "Contact Admin",
          action: () => {
            // This could open a support modal or redirect to help
            alert("Please contact your administrator for access.");
          },
          variant: "outline" as const,
        });
        break;
    }

    // Add help action for all errors
    actions.push({
      label: "Get Help",
      action: () => {
        // This could open a help modal, chat widget, or support page
        window.open("/help", "_blank");
      },
      variant: "outline" as const,
    });

    return actions;
  }

  /**
   * Map error severity to user-friendly severity
   */
  private mapSeverityToUserSeverity(severity: ErrorSeverity): UserFriendlyMessage["severity"] {
    switch (severity) {
      case ErrorSeverity.LOW:
        return "info";
      case ErrorSeverity.MEDIUM:
        return "warning";
      case ErrorSeverity.HIGH:
        return "error";
      case ErrorSeverity.CRITICAL:
        return "critical";
      default:
        return "error";
    }
  }

  /**
   * Generate contextual help text based on error
   */
  public generateHelpText(error: AppError): string {
    const helpTexts = {
      [ErrorCategory.VALIDATION]:
        "Validation errors occur when the information you entered doesn't meet the required format or criteria. Please check the highlighted fields and ensure all required information is provided correctly.",

      [ErrorCategory.NETWORK]:
        "Network errors can occur due to internet connectivity issues or server problems. If you're connected to the internet, the issue may be temporary. Try again in a few minutes.",

      [ErrorCategory.AUTHENTICATION]:
        "Authentication errors occur when your login session has expired or your credentials are invalid. Please log in again to continue.",

      [ErrorCategory.AUTHORIZATION]:
        "You don't have the necessary permissions to perform this action. Contact your administrator if you believe you should have access.",

      [ErrorCategory.BUSINESS_LOGIC]:
        "This operation violates business rules or constraints. Review the requirements and ensure all conditions are met before trying again.",

      [ErrorCategory.SYSTEM]:
        "System errors are usually temporary and resolve themselves. If the problem persists, please contact support.",

      [ErrorCategory.USER_INPUT]:
        "Please check that all information is entered correctly and in the expected format. Required fields must be filled out.",

      [ErrorCategory.DATA_PROCESSING]:
        "There was an issue processing your data. This could be due to file format, size, or content issues. Please verify your data and try again.",

      [ErrorCategory.EXTERNAL_SERVICE]:
        "An external service we depend on is currently unavailable. This is usually temporary. Please try again later.",
    };

    return (
      helpTexts[error.category] ||
      "An unexpected error occurred. If this problem continues, please contact support with the error details."
    );
  }

  /**
   * Generate recovery suggestions based on error context
   */
  public generateRecoverySuggestions(error: AppError): string[] {
    const suggestions: string[] = [];

    // Add general suggestions based on category
    switch (error.category) {
      case ErrorCategory.VALIDATION:
      case ErrorCategory.USER_INPUT:
        suggestions.push(
          "Double-check all required fields are filled out",
          "Verify data formats (dates, emails, phone numbers)",
          "Remove any special characters that might not be allowed"
        );
        break;

      case ErrorCategory.NETWORK:
        suggestions.push(
          "Check your internet connection",
          "Try refreshing the page",
          "Disable any VPN or proxy temporarily",
          "Try again in a few minutes"
        );
        break;

      case ErrorCategory.AUTHENTICATION:
        suggestions.push(
          "Log out and log back in",
          "Clear your browser cache and cookies",
          "Try using an incognito/private browser window"
        );
        break;

      case ErrorCategory.SYSTEM:
        suggestions.push(
          "Wait a few minutes and try again",
          "Refresh the page",
          "Try using a different browser",
          "Clear your browser cache"
        );
        break;
    }

    // Add context-specific suggestions
    if (error.context.component) {
      suggestions.push(`Try navigating away from this ${error.context.component} and back`);
    }

    // Add severity-based suggestions
    if (error.severity === ErrorSeverity.CRITICAL) {
      suggestions.push("Contact support immediately if this error persists");
    }

    return suggestions;
  }
}

// Export singleton instance

export const errorMessages = ErrorMessages.getInstance();
