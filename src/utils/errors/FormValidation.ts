import { z } from "zod";
import { ValidationError } from "./exports";
import { ErrorContext } from "@/types";

/**
 * Validate form data using Zod schema and throw ValidationError for failures
 */

export const validateFormData = <T>(
  schema: z.Zod<PERSON>chema<T>,
  data: unknown,
  context?: Partial<ErrorContext>
): T => {
  try {
    return schema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      // Get the first validation error
      const firstError = error.errors[0];
      const field = firstError.path.join(".");

      throw new ValidationError(firstError.message, field, firstError.code, context);
    }

    // Re-throw non-Zod errors
    throw error;
  }
};

/**
 * Validate form data and return validation result without throwing
 */

export const validateFormDataSafe = <T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; errors: z.ZodError } => {
  const result = schema.safeParse(data);

  if (result.success) {
    return { success: true, data: result.data };
  } else {
    return { success: false, errors: result.error };
  }
};

/**
 * Convert Zod errors to ValidationError instances
 */

export const zodErrorsToValidationErrors = (
  zodError: z.ZodError,
  context?: Partial<ErrorContext>
): ValidationError[] => {
  return zodError.errors.map(error => {
    const field = error.path.join(".");
    return new ValidationError(error.message, field, error.code, context);
  });
};

/**
 * Get all validation errors from Zod error
 */

export const getAllValidationErrors = (
  zodError: z.ZodError,
  context?: Partial<ErrorContext>
): { field: string; message: string; code: string }[] => {
  return zodError.errors.map(error => ({
    field: error.path.join("."),
    message: error.message,
    code: error.code,
  }));
};

/**
 * Custom validation for risk form business rules
 */

export const validateRiskBusinessRules = (data: unknown, context?: Partial<ErrorContext>) => {
  // Validate that residual risk is not higher than inherent risk
  if (data.likelihood > data.inherentLikelihood) {
    throw new ValidationError(
      "Residual likelihood cannot be higher than inherent likelihood",
      "likelihood",
      "business_rule_violation",
      context
    );
  }

  if (data.impact > data.inherentImpact) {
    throw new ValidationError(
      "Residual impact cannot be higher than inherent impact",
      "impact",
      "business_rule_violation",
      context
    );
  }

  // Validate that if control measures exist, they should reduce risk
  if (data.controlMeasures && data.controlMeasures.length > 0) {
    const riskScore = data.likelihood * data.impact;
    const inherentRiskScore = data.inherentLikelihood * data.inherentImpact;

    if (riskScore >= inherentRiskScore) {
      throw new ValidationError(
        "With control measures in place, residual risk should be lower than inherent risk",
        "controlMeasures",
        "business_rule_violation",
        context
      );
    }
  }

  // Validate due date is in the future for active risks
  if (data.dueDate && data.status !== "CLOSED") {
    const now = new Date();
    const dueDate = new Date(data.dueDate);

    if (dueDate <= now) {
      throw new ValidationError(
        "Due date must be in the future for active risks",
        "dueDate",
        "business_rule_violation",
        context
      );
    }
  }
};

/**
 * Validate required fields based on risk status
 */

export const validateRequiredFieldsByStatus = (data: unknown, context?: Partial<ErrorContext>) => {
  switch (data.status) {
    case "IDENTIFIED":
      // Basic fields required
      if (!data.title || data.title.trim().length < 3) {
        throw new ValidationError(
          "Title is required and must be at least 3 characters",
          "title",
          "required",
          context
        );
      }
      break;

    case "ASSESSED":
      // Risk assessment fields required
      if (!data.inherentLikelihood || data.inherentLikelihood < 1 || data.inherentLikelihood > 5) {
        throw new ValidationError(
          "Inherent likelihood must be between 1 and 5",
          "inherentLikelihood",
          "required",
          context
        );
      }
      if (!data.inherentImpact || data.inherentImpact < 1 || data.inherentImpact > 5) {
        throw new ValidationError(
          "Inherent impact must be between 1 and 5",
          "inherentImpact",
          "required",
          context
        );
      }
      break;

    case "MITIGATED":
      // Mitigation fields required
      if (!data.mitigationApproach || data.mitigationApproach.trim().length === 0) {
        throw new ValidationError(
          "Mitigation approach is required for mitigated risks",
          "mitigationApproach",
          "required",
          context
        );
      }
      break;

    case "CLOSED":
      // All mitigation actions should be completed
      if (data.mitigationActions?.some((action: Record<string, unknown>) => !action.completed)) {
        throw new ValidationError(
          "All mitigation actions must be completed before closing a risk",
          "mitigationActions",
          "business_rule_violation",
          context
        );
      }
      break;
  }
};

/**
 * Comprehensive risk form validation
 */

export const validateRiskForm = (data: unknown, context?: Partial<ErrorContext>) => {
  // First validate required fields by status
  validateRequiredFieldsByStatus(data, context);

  // Then validate business rules
  validateRiskBusinessRules(data, context);

  return true;
};
