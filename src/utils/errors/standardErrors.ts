/**
 * Standardized error handling utilities for utility functions
 * Provides consistent error types and handling patterns across the application
 */

/**
 * Base error class for utility function errors
 */
export class UtilityError extends Error {
  public readonly code: string;
  public readonly context?: Record<string, unknown>;

  constructor(message: string, code: string, context?: Record<string, unknown>) {
    super(message);
    this.name = 'UtilityError';
    this.code = code;
    this.context = context;
  }
}

/**
 * Validation error for invalid input parameters
 */
export class ValidationError extends UtilityError {
  constructor(message: string, context?: Record<string, unknown>) {
    super(message, 'VALIDATION_ERROR', context);
    this.name = 'ValidationError';
  }
}

/**
 * Parsing error for data transformation failures
 */
export class ParseError extends UtilityError {
  constructor(message: string, context?: Record<string, unknown>) {
    super(message, 'PARSE_ERROR', context);
    this.name = 'ParseError';
  }
}

/**
 * Configuration error for invalid utility configuration
 */
export class ConfigurationError extends UtilityError {
  constructor(message: string, context?: Record<string, unknown>) {
    super(message, 'CONFIGURATION_ERROR', context);
    this.name = 'ConfigurationError';
  }
}

/**
 * Standard result type for utility functions
 */
export type UtilityResult<T> = {
  success: true;
  data: T;
} | {
  success: false;
  error: UtilityError;
};

/**
 * Creates a successful result
 */
export function createSuccess<T>(data: T): UtilityResult<T> {
  return { success: true, data };
}

/**
 * Creates an error result
 */
export function createError<T>(error: UtilityError): UtilityResult<T> {
  return { success: false, error };
}

/**
 * Safe wrapper for utility functions that may throw
 */
export function safeExecute<T>(
  fn: () => T,
  errorMessage: string,
  context?: Record<string, unknown>
): UtilityResult<T> {
  try {
    const result = fn();
    return createSuccess(result);
  } catch (error) {
    const utilityError = error instanceof UtilityError 
      ? error 
      : new UtilityError(
          `${errorMessage}: ${error instanceof Error ? error.message : 'Unknown error'}`,
          'EXECUTION_ERROR',
          { ...context, originalError: error }
        );
    return createError(utilityError);
  }
}

/**
 * Async safe wrapper for utility functions that may throw
 */
export async function safeExecuteAsync<T>(
  fn: () => Promise<T>,
  errorMessage: string,
  context?: Record<string, unknown>
): Promise<UtilityResult<T>> {
  try {
    const result = await fn();
    return createSuccess(result);
  } catch (error) {
    const utilityError = error instanceof UtilityError 
      ? error 
      : new UtilityError(
          `${errorMessage}: ${error instanceof Error ? error.message : 'Unknown error'}`,
          'EXECUTION_ERROR',
          { ...context, originalError: error }
        );
    return createError(utilityError);
  }
}

/**
 * Type guard to check if a result is successful
 */
export function isSuccess<T>(result: UtilityResult<T>): result is { success: true; data: T } {
  return result.success;
}

/**
 * Type guard to check if a result is an error
 */
export function isError<T>(result: UtilityResult<T>): result is { success: false; error: UtilityError } {
  return !result.success;
}

/**
 * Unwraps a result, throwing the error if it's not successful
 */
export function unwrapResult<T>(result: UtilityResult<T>): T {
  if (isSuccess(result)) {
    return result.data;
  }
  throw result.error;
}

/**
 * Gets the data from a result or returns a default value if error
 */
export function getResultOrDefault<T>(result: UtilityResult<T>, defaultValue: T): T {
  return isSuccess(result) ? result.data : defaultValue;
}