import { Error<PERSON>ategory, <PERSON><PERSON>r<PERSON>everity, Error<PERSON>ontext, ErrorMetadata } from "@/types";

/**
 * Generate a unique ID for errors
 */
const generateErrorId = (): string => {
  return `err_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
};

/**
 * Base application error class that all custom errors should extend
 */
export abstract class AppError extends Error {
  public readonly id: string;
  public readonly category: ErrorCategory;
  public readonly severity: ErrorSeverity;
  public readonly context: ErrorContext;
  public readonly recoverable: boolean;
  public readonly userMessage: string;
  public readonly technicalMessage: string;
  public readonly suggestedActions: string[];
  public readonly timestamp: Date;

  constructor(
    message: string,
    category: ErrorCategory,
    severity: ErrorSeverity,
    context: Partial<ErrorContext> = {},
    options: {
      recoverable?: boolean;
      userMessage?: string;
      suggestedActions?: string[];
      cause?: Error;
    } = {
      // Implementation needed
    }
  ) {
    super(message);

    this.name = this.constructor.name;
    this.id = generateErrorId();
    this.category = category;
    this.severity = severity;
    this.timestamp = new Date();
    this.recoverable = options.recoverable ?? true;
    this.technicalMessage = message;
    this.userMessage = options.userMessage || this.getDefaultUserMessage();
    this.suggestedActions = options.suggestedActions || this.getDefaultSuggestedActions();

    this.context = {
      timestamp: this.timestamp,
      url: typeof window !== "undefined" ? window.location.href : undefined,
      userAgent: typeof navigator !== "undefined" ? navigator.userAgent : undefined,
      ...context,
    };

    // Maintain proper stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }

    // Set the cause if provided
    if (options.cause) {
      this.cause = options.cause;
    }
  }

  /**
   * Get default user-friendly message based on error category
   */
  protected getDefaultUserMessage(): string {
    switch (this.category) {
      case ErrorCategory.VALIDATION:
        return "Please check your input and try again.";
      case ErrorCategory.NETWORK:
        return "Network connection issue. Please check your internet connection and try again.";
      case ErrorCategory.AUTHENTICATION:
        return "Authentication failed. Please log in again.";
      case ErrorCategory.AUTHORIZATION:
        return "You do not have permission to perform this action.";
      case ErrorCategory.BUSINESS_LOGIC:
        return "Unable to complete the operation due to business rules.";
      case ErrorCategory.SYSTEM:
        return "A system error occurred. Please try again later.";
      case ErrorCategory.USER_INPUT:
        return "Invalid input provided. Please check your data and try again.";
      case ErrorCategory.DATA_PROCESSING:
        return "Error processing data. Please try again.";
      case ErrorCategory.EXTERNAL_SERVICE:
        return "External service is temporarily unavailable. Please try again later.";
      default:
        return "An unexpected error occurred. Please try again.";
    }
  }

  /**
   * Get default suggested actions based on error category
   */
  protected getDefaultSuggestedActions(): string[] {
    switch (this.category) {
      case ErrorCategory.VALIDATION:
        return [
          "Review the form fields",
          "Ensure all required fields are filled",
          "Check data formats",
        ];
      case ErrorCategory.NETWORK:
        return [
          "Check your internet connection",
          "Try refreshing the page",
          "Contact support if the issue persists",
        ];
      case ErrorCategory.AUTHENTICATION:
        return ["Log out and log back in", "Clear browser cache", "Contact support if needed"];
      case ErrorCategory.AUTHORIZATION:
        return ["Contact your administrator", "Check your user permissions"];
      case ErrorCategory.BUSINESS_LOGIC:
        return ["Review the operation requirements", "Contact support for clarification"];
      case ErrorCategory.SYSTEM:
        return ["Try again in a few minutes", "Contact support if the issue persists"];
      case ErrorCategory.USER_INPUT:
        return ["Check your input data", "Refer to the help documentation"];
      case ErrorCategory.DATA_PROCESSING:
        return [
          "Verify your data format",
          "Try with a smaller dataset",
          "Contact support if needed",
        ];
      case ErrorCategory.EXTERNAL_SERVICE:
        return ["Try again later", "Check service status", "Contact support if the issue persists"];
      default:
        return ["Try again", "Contact support if the issue persists"];
    }
  }

  /**
   * Convert error to metadata object for logging and reporting
   */
  public toMetadata(): ErrorMetadata {
    return {
      id: this.id,
      category: this.category,
      severity: this.severity,
      context: this.context,
      stack: this.stack,
      recoverable: this.recoverable,
      userMessage: this.userMessage,
      technicalMessage: this.technicalMessage,
      suggestedActions: this.suggestedActions,
    };
  }

  /**
   * Convert error to JSON for serialization
   */
  public toJSON(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      id: this.id,
      category: this.category,
      severity: this.severity,
      context: this.context,
      recoverable: this.recoverable,
      userMessage: this.userMessage,
      technicalMessage: this.technicalMessage,
      suggestedActions: this.suggestedActions,
      timestamp: this.timestamp.toISOString(),
      stack: this.stack,
    };
  }

  /**
   * Check if error is recoverable
   */
  public isRecoverable(): boolean {
    return this.recoverable;
  }

  /**
   * Check if error is critical
   */
  public isCritical(): boolean {
    return this.severity === ErrorSeverity.CRITICAL;
  }
}
