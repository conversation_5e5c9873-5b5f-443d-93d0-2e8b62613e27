import { ErrorContext, ErrorSeverity } from "@/types";
import { AppError } from "./AppError";
import { logger } from "./Logger";
import { toast } from "@/hooks/use-toast";
export interface ErrorHandlerConfig {
  enableToastNotifications: boolean;
  enableConsoleLogging: boolean;
  enableRemoteReporting: boolean;
  autoRetry: boolean;
  maxRetryAttempts: number;
  retryDelay: number;
}
export interface ErrorRecoveryAction {
  label: string;
  action: () => void | Promise<void>;
  primary?: boolean;
}
export interface ErrorHandlingResult {
  handled: boolean;
  recovered: boolean;
  retryable: boolean;
  userNotified: boolean;
  loggedRemotely: boolean;
}
/**
 * Centralized error handling service
 */
export class ErrorHandler {
  private static instance: ErrorHandler;
  private config: ErrorHandlerConfig;
  private constructor(config: Partial<ErrorHandlerConfig> = {}) {
    this.config = {
      enableToastNotifications: true,
      enableConsoleLogging: true,
      enableRemoteReporting: false,
      autoRetry: false,
      maxRetryAttempts: 3,
      retryDelay: 1000,
      ...config,
    };
  }
  /**
   * Get singleton instance of ErrorHandler
   */
  public static getInstance(config?: Partial<ErrorHandlerConfig>): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler(config);
    }
    return ErrorHandler.instance;
  }
  /**
   * Handle any error with comprehensive error processing
   */
  public async handle(
    error: unknown,
    context?: Partial<ErrorContext>,
    recoveryActions?: ErrorRecoveryAction[]
  ): Promise<ErrorHandlingResult> {
    const appError = await this.normalizeError(error, context);
    // Log the error
    if (this.config.enableConsoleLogging) {
      logger.logError(appError, context);
    }
    // Show user notification
    const userNotified = this.showUserNotification(appError, recoveryActions);
    // Report to remote service (placeholder)
    const loggedRemotely = await this.reportToRemote(appError);
    return {
      handled: true,
      recovered: false, // Recovery would be handled by recovery actions
      retryable: appError.isRecoverable(),
      userNotified,
      loggedRemotely,
    };
  }
  /**
   * Handle errors with automatic retry capability
   */
  public async handleWithRetry<T>(
    operation: () => Promise<T>,
    context?: Partial<ErrorContext>,
    maxAttempts?: number
  ): Promise<T> {
    const attempts = maxAttempts || this.config.maxRetryAttempts;
    let lastError: AppError | undefined;
    for (let attempt = 1; attempt <= attempts; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = await this.normalizeError(error, {
          ...context,
          additionalData: { attempt, maxAttempts: attempts },
        });
        // Don't retry if error is not recoverable
        if (!lastError.isRecoverable()) {
          break;
        }
        // Don't retry on last attempt
        if (attempt === attempts) {
          break;
        }
        // Wait before retry
        await this.delay(this.config.retryDelay * attempt);
        logger.warn(`Retrying operation (attempt ${attempt + 1}/${attempts})`, lastError.context);
      }
    }
    // Handle the final error
    await this.handle(lastError!, context);
    throw lastError!;
  }
  /**
   * Convert any error to AppError
   */
  private async normalizeError(error: unknown, context?: Partial<ErrorContext>): Promise<AppError> {
    if (error instanceof AppError) {
      return error;
    }
    // Dynamic import to avoid circular dependency
    const { SystemError } = await import("./exports");
    if (error instanceof Error) {
      return new SystemError(error.message, "unknown", undefined, context, ErrorSeverity.MEDIUM);
    }
    return new SystemError(
      typeof error === "string" ? error : "Unknown error occurred",
      "unknown",
      undefined,
      context,
      ErrorSeverity.MEDIUM
    );
  }
  /**
   * Show user-friendly notification
   */
  private showUserNotification(error: AppError, recoveryActions?: ErrorRecoveryAction[]): boolean {
    if (!this.config.enableToastNotifications) {
      return false;
    }
    try {
      const variant = this.getToastVariant(error.severity);
      toast({
        title: this.getErrorTitle(error.severity),
        description: error.userMessage,
        variant,
        action:
          recoveryActions?.length > 0
            ? this.createRecoveryActionElement(recoveryActions[0])
            : undefined,
      });
      return true;
    } catch (toastError) {
      // Fallback to console if toast fails
      return false;
    }
  }
  /**
   * Get toast variant based on error severity
   */
  private getToastVariant(severity: ErrorSeverity): "default" | "destructive" {
    switch (severity) {
      case ErrorSeverity.LOW:
        return "default";
      case ErrorSeverity.MEDIUM:
      case ErrorSeverity.HIGH:
      case ErrorSeverity.CRITICAL:
        return "destructive";
      default:
        return "destructive";
    }
  }
  /**
   * Get error title based on severity
   */
  private getErrorTitle(severity: ErrorSeverity): string {
    switch (severity) {
      case ErrorSeverity.LOW:
        return "Notice";
      case ErrorSeverity.MEDIUM:
        return "Warning";
      case ErrorSeverity.HIGH:
        return "Error";
      case ErrorSeverity.CRITICAL:
        return "Critical Error";
      default:
        return "Error";
    }
  }
  /**
   * Create recovery action element for toast
   */
  private createRecoveryActionElement(recoveryAction: ErrorRecoveryAction): {
    altText: string;
    onClick: () => void;
  } {
    // This would need to be implemented based on your toast component's action API
    return {
      altText: recoveryAction.label,
      onClick: recoveryAction.action,
    };
  }
  /**
   * Report error to remote service (placeholder)
   */
  private async reportToRemote(error: AppError): Promise<boolean> {
    if (!this.config.enableRemoteReporting) {
      return false;
    }
    try {
      // Placeholder for remote error reporting
      // This could integrate with services like Sentry, LogRocket, etc.
      return true;
    } catch (reportError) {
      logger.error("Failed to report error to remote service", reportError as Error);
      return false;
    }
  }
  /**
   * Delay utility for retry logic
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  /**
   * Handle global unhandled errors
   */
  public setupGlobalErrorHandling(): void {
    // Handle unhandled promise rejections
    window.addEventListener("unhandledrejection", event => {
      event.preventDefault();
      this.handle(event.reason, {
        component: "global",
        action: "unhandled_promise_rejection",
      });
    });
    // Handle global JavaScript errors
    window.addEventListener("error", event => {
      this.handle(event.error || event.message, {
        component: "global",
        action: "javascript_error",
        additionalData: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno,
        },
      });
    });
  }
  /**
   * Update configuration
   */
  public updateConfig(config: Partial<ErrorHandlerConfig>): void {
    this.config = { ...this.config, ...config };
  }
  /**
   * Get current configuration
   */
  public getConfig(): ErrorHandlerConfig {
    return { ...this.config };
  }
}
// Export singleton instance
export const errorHandler = ErrorHandler.getInstance();
