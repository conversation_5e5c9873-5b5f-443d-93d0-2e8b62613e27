# Centralized Error Handling System

A comprehensive error handling system for the RiskCompass application that provides structured error management, logging, reporting, and user-friendly error boundaries.

## Overview

This system replaces scattered error handling throughout the application with a centralized, consistent approach that includes:

- **Custom Error Types**: Specific error classes for different scenarios
- **Structured Logging**: Centralized logging with context and metadata
- **Error Boundaries**: React error boundaries for different application sections
- **User-Friendly Messages**: Contextual error messages and recovery suggestions
- **Error Reporting**: Optional remote error reporting and monitoring
- **Automatic Retry**: Built-in retry mechanisms for recoverable errors

## Quick Start

### 1. Initialize the System

```typescript
import { initializeErrorHandling } from '@/utils/errors/examples/IntegrationExamples';

// In your main App component or index file
initializeErrorHandling();
```

### 2. Use Error Boundaries

```tsx
import { PageErrorBoundary, RiskErrorBoundary, FormErrorBoundary } from '@/components/error-boundaries';

// Wrap entire pages
<PageErrorBoundary pageName="risk-management">
  <RiskManagementPage />
</PageErrorBoundary>

// Wrap specific sections
<RiskErrorBoundary>
  <RiskDetailsComponent />
</RiskErrorBoundary>

// Wrap forms
<FormErrorBoundary formName="risk-creation">
  <RiskForm />
</FormErrorBoundary>
```

### 3. Handle Errors in Components

```typescript
import { ValidationError, NetworkError, errorHandler } from '@/utils/errors';

const handleFormSubmit = async (formData: any) => {
  try {
    // Validate data
    if (!formData.title) {
      throw new ValidationError('Title is required', 'title');
    }

    // Make API call
    const response = await fetch('/api/risks', {
      method: 'POST',
      body: JSON.stringify(formData)
    });

    if (!response.ok) {
      throw new NetworkError('Failed to create risk', response.status);
    }

  } catch (error) {
    // Handle through centralized system
    await errorHandler.handle(error, {
      component: 'risk_form',
      action: 'form_submission'
    });
  }
};
```

## Error Types

### Base Error Class

All custom errors extend `AppError`:

```typescript
import { AppError, ErrorCategory, ErrorSeverity } from '@/utils/errors';

class CustomError extends AppError {
  constructor(message: string) {
    super(
      message,
      ErrorCategory.BUSINESS_LOGIC,
      ErrorSeverity.MEDIUM,
      { component: 'my_component' }
    );
  }
}
```

### Specific Error Types

- **ValidationError**: Form validation and input validation issues
- **NetworkError**: API calls and network connectivity issues
- **AuthenticationError**: Login, token, and auth issues
- **AuthorizationError**: Permission and access control issues
- **BusinessLogicError**: Domain-specific business rule violations
- **SystemError**: Internal system errors, database issues
- **DataProcessingError**: Data import, export, transformation issues
- **ExternalServiceError**: Third-party service integration issues

## Error Boundaries

### Available Boundaries

- **BaseErrorBoundary**: Generic error boundary with customizable fallback
- **PageErrorBoundary**: Full-page error boundary with navigation options
- **RiskErrorBoundary**: Risk-specific error boundary with contextual actions
- **FormErrorBoundary**: Form-specific error boundary with retry options

### Custom Error Boundaries

```tsx
import { BaseErrorBoundary, ErrorFallbackProps } from '@/components/error-boundaries';

const CustomErrorFallback: React.FC<ErrorFallbackProps> = ({ error, resetErrorBoundary }) => (
  <div>
    <h2>Custom Error UI</h2>
    <p>{error.message}</p>
    <button onClick={resetErrorBoundary}>Try Again</button>
  </div>
);

<BaseErrorBoundary fallback={CustomErrorFallback}>
  <MyComponent />
</BaseErrorBoundary>
```

## Logging

### Basic Logging

```typescript
import { logger } from '@/utils/errors';

logger.debug('Debug message', { component: 'my_component' });
logger.info('Info message', { component: 'my_component' });
logger.warn('Warning message', { component: 'my_component' });
logger.error('Error message', error, { component: 'my_component' });
logger.critical('Critical error', error, { component: 'my_component' });
```

### Structured Logging

```typescript
logger.info('User action completed', {
  component: 'risk_management',
  action: 'risk_created',
  userId: 'user-123',
  organizationId: 'org-456',
  additionalData: {
    riskId: 'risk-789',
    riskTitle: 'New Risk'
  }
});
```

## Error Handling Patterns

### API Calls with Retry

```typescript
import { errorHandler } from '@/utils/errors';

const fetchData = async () => {
  return await errorHandler.handleWithRetry(
    async () => {
      const response = await fetch('/api/data');
      if (!response.ok) throw new Error('API call failed');
      return response.json();
    },
    { component: 'data_fetcher', action: 'fetch_data' },
    3 // max retry attempts
  );
};
```

### Form Validation

```typescript
import { ValidationError } from '@/utils/errors';

const validateForm = (data: FormData) => {
  if (!data.title || data.title.length < 3) {
    throw new ValidationError(
      'Title must be at least 3 characters',
      'title',
      'minLength'
    );
  }
};
```

### Business Logic Validation

```typescript
import { BusinessLogicError } from '@/utils/errors';

const validateRiskClosure = (risk: Risk) => {
  if (risk.status === 'CLOSED' && risk.openMitigations > 0) {
    throw new BusinessLogicError(
      'Cannot close risk with open mitigations',
      'risk_closure_validation',
      'risk',
      risk.id
    );
  }
};
```

## Configuration

### Environment-Based Configuration

```typescript
// Development
{
  logger: { minLevel: LogLevel.DEBUG, enableConsole: true },
  errorHandler: { maxRetryAttempts: 1, enableToastNotifications: true },
  errorReporting: { enabled: false }
}

// Production
{
  logger: { minLevel: LogLevel.WARN, enableRemote: true },
  errorHandler: { maxRetryAttempts: 3, enableRemoteReporting: true },
  errorReporting: { enabled: true, endpoint: 'https://api.example.com/errors' }
}
```

## Best Practices

### 1. Use Specific Error Types

```typescript
// ❌ Generic error
throw new Error('Something went wrong');

// ✅ Specific error with context
throw new ValidationError('Email format is invalid', 'email', 'format');
```

### 2. Provide Context

```typescript
// ❌ No context
await errorHandler.handle(error);

// ✅ Rich context
await errorHandler.handle(error, {
  component: 'risk_form',
  action: 'form_submission',
  userId: currentUser.id,
  additionalData: { formData }
});
```

### 3. Use Error Boundaries

```tsx
// ❌ No error boundary
<RiskForm />

// ✅ Wrapped in appropriate boundary
<FormErrorBoundary formName="risk-creation">
  <RiskForm />
</FormErrorBoundary>
```

### 4. Log Important Events

```typescript
// ✅ Log successful operations too
logger.info('Risk created successfully', {
  component: 'risk_service',
  action: 'risk_creation',
  additionalData: { riskId: newRisk.id }
});
```

## Migration Guide

### Replacing Existing Error Handling

1. **Replace console.error calls**:
   ```typescript
   // Before
   console.error('Error:', error);
   
   // After
   logger.error('Operation failed', error, { component: 'my_component' });
   ```

2. **Replace try-catch blocks**:
   ```typescript
   // Before
   try {
     await operation();
   } catch (error) {
     console.error(error);
     toast({ title: 'Error', description: 'Something went wrong' });
   }
   
   // After
   try {
     await operation();
   } catch (error) {
     await errorHandler.handle(error, { component: 'my_component' });
   }
   ```

3. **Add error boundaries**:
   ```tsx
   // Before
   <Component />
   
   // After
   <ErrorBoundary>
     <Component />
   </ErrorBoundary>
   ```

## Examples

See the `examples/` directory for comprehensive usage examples:

- `BasicUsageExamples.ts`: Core error handling patterns
- `ReactComponentExamples.tsx`: React component integration
- `IntegrationExamples.ts`: System setup and configuration
