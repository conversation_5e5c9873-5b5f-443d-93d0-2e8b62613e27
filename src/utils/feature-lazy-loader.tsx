import React, { Suspense } from 'react';
import { createLazyComponent } from './component-lazy-loader';
import { Skeleton } from '@/components/ui/skeleton';

/**
 * Feature-based code splitting for large application modules
 * This approach groups related functionality together for better caching
 */

// Loading fallbacks for different feature types
const ReportLoadingFallback = () => (
  <div className="space-y-4 p-4">
    <Skeleton className="h-8 w-48" />
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <Skeleton className="h-32 w-full" />
      <Skeleton className="h-32 w-full" />
      <Skeleton className="h-32 w-full" />
    </div>
    <Skeleton className="h-64 w-full" />
  </div>
);

const FormLoadingFallback = () => (
  <div className="space-y-4 p-4">
    <Skeleton className="h-8 w-64" />
    <div className="space-y-3">
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-24 w-full" />
      <div className="flex gap-2">
        <Skeleton className="h-10 w-24" />
        <Skeleton className="h-10 w-24" />
      </div>
    </div>
  </div>
);

const ChartLoadingFallback = () => (
  <div className="space-y-4 p-4">
    <Skeleton className="h-6 w-32" />
    <Skeleton className="h-64 w-full" />
    <div className="flex justify-center gap-4">
      <Skeleton className="h-4 w-16" />
      <Skeleton className="h-4 w-16" />
      <Skeleton className="h-4 w-16" />
    </div>
  </div>
);

const TableLoadingFallback = () => (
  <div className="space-y-4 p-4">
    <div className="flex justify-between items-center">
      <Skeleton className="h-8 w-48" />
      <Skeleton className="h-8 w-32" />
    </div>
    <div className="space-y-2">
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
    </div>
  </div>
);

/**
 * Risk Management Feature Bundle
 */
export const LazyRiskManagement = {
  // Risk Register with advanced filtering and sorting
  RiskRegister: createLazyComponent(
    () => import('@/features/risk-management/RiskRegister'),
    { fallback: TableLoadingFallback }
  ),
  
  // Risk Assessment Forms
  RiskAssessmentForm: createLazyComponent(
    () => import('@/features/risk-management/RiskAssessmentForm'),
    { fallback: FormLoadingFallback }
  ),
  
  // Risk Analytics Dashboard
  RiskAnalytics: createLazyComponent(
    () => import('@/features/risk-management/RiskAnalytics'),
    { fallback: ChartLoadingFallback }
  ),
  
  // Risk Matrix Visualization
  RiskMatrix: createLazyComponent(
    () => import('@/features/risk-management/RiskMatrix'),
    { fallback: ChartLoadingFallback }
  ),
  
  // Risk Templates Management
  RiskTemplates: createLazyComponent(
    () => import('@/features/risk-management/RiskTemplates'),
    { fallback: TableLoadingFallback }
  ),
};

/**
 * Incident Management Feature Bundle
 */
export const LazyIncidentManagement = {
  // Incident Tracking Table
  IncidentTracker: createLazyComponent(
    () => import('@/features/incident-management/IncidentTracker'),
    { fallback: TableLoadingFallback }
  ),
  
  // Incident Reporting Form
  IncidentReportForm: createLazyComponent(
    () => import('@/features/incident-management/IncidentReportForm'),
    { fallback: FormLoadingFallback }
  ),
  
  // Incident Analytics
  IncidentAnalytics: createLazyComponent(
    () => import('@/features/incident-management/IncidentAnalytics'),
    { fallback: ChartLoadingFallback }
  ),
  
  // Incident Timeline
  IncidentTimeline: createLazyComponent(
    () => import('@/features/incident-management/IncidentTimeline'),
    { fallback: () => <Skeleton className="h-96 w-full" /> }
  ),
};

/**
 * Reporting Feature Bundle
 */
export const LazyReporting = {
  // Report Builder
  ReportBuilder: createLazyComponent(
    () => import('@/features/reporting/ReportBuilder'),
    { fallback: ReportLoadingFallback }
  ),
  
  // Dashboard Analytics
  DashboardAnalytics: createLazyComponent(
    () => import('@/features/reporting/DashboardAnalytics'),
    { fallback: ChartLoadingFallback }
  ),
  
  // Export Manager
  ExportManager: createLazyComponent(
    () => import('@/features/reporting/ExportManager'),
    { fallback: () => <Skeleton className="h-48 w-full" /> }
  ),
  
  // Custom Reports
  CustomReports: createLazyComponent(
    () => import('@/features/reporting/CustomReports'),
    { fallback: ReportLoadingFallback }
  ),
};

/**
 * Administration Feature Bundle
 */
export const LazyAdministration = {
  // User Management
  UserManagement: createLazyComponent(
    () => import('@/features/administration/UserManagement'),
    { fallback: TableLoadingFallback }
  ),
  
  // Organization Settings
  OrganizationSettings: createLazyComponent(
    () => import('@/features/administration/OrganizationSettings'),
    { fallback: FormLoadingFallback }
  ),
  
  // Policy Management
  PolicyManagement: createLazyComponent(
    () => import('@/features/administration/PolicyManagement'),
    { fallback: TableLoadingFallback }
  ),
  
  // System Configuration
  SystemConfiguration: createLazyComponent(
    () => import('@/features/administration/SystemConfiguration'),
    { fallback: FormLoadingFallback }
  ),
  
  // Audit Logs
  AuditLogs: createLazyComponent(
    () => import('@/features/administration/AuditLogs'),
    { fallback: TableLoadingFallback }
  ),
};

/**
 * Data Visualization Feature Bundle
 */
export const LazyDataVisualization = {
  // Advanced Charts
  AdvancedCharts: createLazyComponent(
    () => import('@/features/visualization/AdvancedCharts'),
    { fallback: ChartLoadingFallback }
  ),
  
  // Interactive Dashboards
  InteractiveDashboard: createLazyComponent(
    () => import('@/features/visualization/InteractiveDashboard'),
    { fallback: ReportLoadingFallback }
  ),
  
  // Data Explorer
  DataExplorer: createLazyComponent(
    () => import('@/features/visualization/DataExplorer'),
    { fallback: TableLoadingFallback }
  ),
  
  // Trend Analysis
  TrendAnalysis: createLazyComponent(
    () => import('@/features/visualization/TrendAnalysis'),
    { fallback: ChartLoadingFallback }
  ),
};

/**
 * Integration Feature Bundle
 */
export const LazyIntegrations = {
  // API Integrations
  APIIntegrations: createLazyComponent(
    () => import('@/features/integrations/APIIntegrations'),
    { fallback: FormLoadingFallback }
  ),
  
  // Data Import/Export
  DataImportExport: createLazyComponent(
    () => import('@/features/integrations/DataImportExport'),
    { fallback: () => <Skeleton className="h-64 w-full" /> }
  ),
  
  // Webhook Management
  WebhookManagement: createLazyComponent(
    () => import('@/features/integrations/WebhookManagement'),
    { fallback: TableLoadingFallback }
  ),
  
  // Third-party Connectors
  ThirdPartyConnectors: createLazyComponent(
    () => import('@/features/integrations/ThirdPartyConnectors'),
    { fallback: FormLoadingFallback }
  ),
};

/**
 * Module-level variable to track preloaded features
 */
const preloadedFeatures = new Set<string>();

/**
 * Feature preloader functions based on user role and navigation patterns
 */

/**
 * Preload features based on user role
 */
export function preloadForRole(role: string) {
  switch (role) {
    case 'admin':
      preloadAdminFeatures();
      break;
    case 'manager':
      preloadManagerFeatures();
      break;
    case 'analyst':
      preloadAnalystFeatures();
      break;
    default:
      preloadBasicFeatures();
  }
}

/**
 * Preload administration features
 */
function preloadAdminFeatures() {
  if (!preloadedFeatures.has('admin')) {
    // Preload administration features
    LazyAdministration.UserManagement.preload?.();
    LazyAdministration.OrganizationSettings.preload?.();
    LazyAdministration.PolicyManagement.preload?.();
    preloadedFeatures.add('admin');
  }
}

/**
 * Preload manager-specific features
 */
function preloadManagerFeatures() {
  if (!preloadedFeatures.has('manager')) {
    // Preload reporting and analytics
    LazyReporting.ReportBuilder.preload?.();
    LazyReporting.DashboardAnalytics.preload?.();
    LazyDataVisualization.InteractiveDashboard.preload?.();
    preloadedFeatures.add('manager');
  }
}

/**
 * Preload analyst-specific features
 */
function preloadAnalystFeatures() {
  if (!preloadedFeatures.has('analyst')) {
    // Preload risk and incident analysis
    LazyRiskManagement.RiskAnalytics.preload?.();
    LazyIncidentManagement.IncidentAnalytics.preload?.();
    LazyDataVisualization.TrendAnalysis.preload?.();
    preloadedFeatures.add('analyst');
  }
}

/**
 * Preload basic features for all users
 */
function preloadBasicFeatures() {
  if (!preloadedFeatures.has('basic')) {
    // Preload core features
    LazyRiskManagement.RiskRegister.preload?.();
    LazyIncidentManagement.IncidentTracker.preload?.();
    preloadedFeatures.add('basic');
  }
}

/**
 * Preload features based on current page
 */
export function preloadForPage(pageName: string) {
  switch (pageName) {
    case 'dashboard':
      LazyReporting.DashboardAnalytics.preload?.();
      LazyDataVisualization.InteractiveDashboard.preload?.();
      break;
    case 'risks':
      LazyRiskManagement.RiskRegister.preload?.();
      LazyRiskManagement.RiskAnalytics.preload?.();
      break;
    case 'incidents':
      LazyIncidentManagement.IncidentTracker.preload?.();
      LazyIncidentManagement.IncidentAnalytics.preload?.();
      break;
    case 'reports':
      LazyReporting.ReportBuilder.preload?.();
      LazyReporting.CustomReports.preload?.();
      break;
  }
}

/**
 * Get the current set of preloaded features (for debugging)
 */
export function getPreloadedFeatures(): string[] {
  return Array.from(preloadedFeatures);
}

/**
 * Clear all preloaded features (for testing)
 */
export function clearPreloadedFeatures(): void {
  preloadedFeatures.clear();
}

export default {
  LazyRiskManagement,
  LazyIncidentManagement,
  LazyReporting,
  LazyAdministration,
  LazyDataVisualization,
  LazyIntegrations,
  preloadForRole,
  preloadForPage,
  getPreloadedFeatures,
  clearPreloadedFeatures,
};
