/**
 * UI utility functions for consistent styling and user interface operations
 * Provides standardized UI helpers with comprehensive error handling and type safety
 */

import { RiskSeverity } from "@/types";
import { StringInput, UtilityOptions } from "./types/common";
import { ValidationError, safeExecute } from "./errors/standardErrors";

/**
 * Color scheme options for UI elements
 */
export interface ColorSchemeOptions extends UtilityOptions {
  /** Theme variant (light/dark) */
  theme?: "light" | "dark";
  /** Fallback color classes */
  fallback?: string;
}

/**
 * Gets CSS color classes for risk severity levels with comprehensive error handling
 *
 * @param severity - Risk severity level
 * @param options - Color scheme options
 * @returns CSS color classes for the severity level
 *
 * @example
 * ```typescript
 * getSeverityColor(RiskSeverity.HIGH) // Returns "bg-risk-high border-risk-high hover:bg-risk-high/90"
 * getSeverityColor(RiskSeverity.LOW, { theme: 'dark' }) // Returns dark theme colors
 * getSeverityColor('invalid' as RiskSeverity, { fallback: 'bg-gray-500' }) // Returns fallback
 * ```
 *
 * @throws {ValidationError} When severity is invalid and throwOnError is true
 */

export const getSeverityColor = (
  severity: RiskSeverity,
  options: ColorSchemeOptions = {
    // Implementation needed
  }
): string => {
  const {
    theme = "light",
    fallback = "bg-gray-500 border-gray-500 hover:bg-gray-500/90",
    throwOnError = false,
    context = {},
  } = options;

  const result = safeExecute(
    () => {
      if (!Object.values(RiskSeverity).includes(severity)) {
        throw new ValidationError(`Invalid risk severity: ${severity}`, {
          severity,
          operation: "getSeverityColor",
          ...context,
        });
      }

      const colorMap = {
        [RiskSeverity.LOW]: "bg-risk-low border-risk-low hover:bg-risk-low/90",
        [RiskSeverity.MEDIUM]: "bg-risk-medium border-risk-medium hover:bg-risk-medium/90",
        [RiskSeverity.HIGH]: "bg-risk-high border-risk-high hover:bg-risk-high/90",
        [RiskSeverity.CRITICAL]: "bg-risk-critical border-risk-critical hover:bg-risk-critical/90",
      };

      return colorMap[severity];
    },
    "Failed to get severity color",
    { severity, theme, ...context }
  );

  if (result.success) {
    return result.data;
  }

  if (throwOnError) {
    throw result.error;
  }

  return fallback;
};

/**
 * Text transformation options
 */
export interface TextTransformOptions extends UtilityOptions {
  /** Fallback value for invalid input */
  fallback?: string;
  /** Maximum length for truncation */
  maxLength?: number;
}

/**
 * Capitalizes the first letter of a string with comprehensive error handling
 *
 * @param input - String to capitalize
 * @param options - Text transformation options
 * @returns Capitalized string or fallback value
 *
 * @example
 * ```typescript
 * capitalizeFirst('hello world') // Returns "Hello world"
 * capitalizeFirst('') // Returns ""
 * capitalizeFirst(null, { fallback: 'N/A' }) // Returns "N/A"
 * ```
 */

export const capitalizeFirst = (input: StringInput, options: TextTransformOptions = {}): string => {
  const { fallback = "", context = {} } = options;

  if (!input) {
    return fallback;
  }

  const result = safeExecute(
    () => {
      const str = String(input).trim();
      if (str.length === 0) {
        return str;
      }
      return str.charAt(0).toUpperCase() + str.slice(1);
    },
    "Failed to capitalize first letter",
    { input, ...context }
  );

  return result.success ? result.data : fallback;
};

/**
 * Truncates text to a specified length with ellipsis
 *
 * @param input - String to truncate
 * @param options - Text transformation options including maxLength
 * @returns Truncated string with ellipsis if needed
 *
 * @example
 * ```typescript
 * truncateText('This is a long text', { maxLength: 10 }) // Returns "This is a..."
 * truncateText('Short', { maxLength: 10 }) // Returns "Short"
 * truncateText(null, { fallback: 'N/A' }) // Returns "N/A"
 * ```
 */

export const truncateText = (input: StringInput, options: TextTransformOptions = {}): string => {
  const { maxLength = 50, fallback = "", context = {} } = options;

  if (!input) {
    return fallback;
  }

  const result = safeExecute(
    () => {
      const str = String(input).trim();
      if (str.length <= maxLength) {
        return str;
      }
      return str.substring(0, maxLength - 3) + "...";
    },
    "Failed to truncate text",
    { input, maxLength, ...context }
  );

  return result.success ? result.data : fallback;
};

/**
 * Formats a number as a percentage string
 *
 * @param value - Number to format as percentage
 * @param options - Formatting options
 * @returns Formatted percentage string
 *
 * @example
 * ```typescript
 * formatPercentage(0.75) // Returns "75%"
 * formatPercentage(1.5) // Returns "150%"
 * formatPercentage(null, { fallback: '0%' }) // Returns "0%"
 * ```
 */

export const formatPercentage = (
  value: number | null | undefined,
  options: TextTransformOptions = {
    // Implementation needed
  }
): string => {
  const { fallback = "0%", context = {} } = options;

  if (value === null || value === undefined) {
    return fallback;
  }

  const result = safeExecute(
    () => {
      if (typeof value !== "number" || isNaN(value)) {
        throw new ValidationError(`Invalid number for percentage formatting: ${value}`, {
          value,
          operation: "formatPercentage",
          ...context,
        });
      }
      return `${Math.round(value * 100)}%`;
    },
    "Failed to format percentage",
    { value, ...context }
  );

  return result.success ? result.data : fallback;
};

/**
 * Generates initials from a full name
 *
 * @param name - Full name to generate initials from
 * @param options - Text transformation options
 * @returns Initials string (up to 2 characters)
 *
 * @example
 * ```typescript
 * getInitials('John Doe') // Returns "JD"
 * getInitials('Jane Smith Johnson') // Returns "JS"
 * getInitials('') // Returns ""
 * ```
 */

export const getInitials = (name: StringInput, options: TextTransformOptions = {}): string => {
  const { fallback = "", context = {} } = options;

  if (!name) {
    return fallback;
  }

  const result = safeExecute(
    () => {
      const str = String(name).trim();
      if (str.length === 0) {
        return "";
      }

      const words = str.split(/\s+/).filter(word => word.length > 0);
      if (words.length === 0) {
        return "";
      }

      if (words.length === 1) {
        return words[0].charAt(0).toUpperCase();
      }

      return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
    },
    "Failed to generate initials",
    { name, ...context }
  );

  return result.success ? result.data : fallback;
};

/**
 * Formats a file size in bytes to human-readable format
 *
 * @param bytes - File size in bytes
 * @param options - Formatting options
 * @returns Human-readable file size string
 *
 * @example
 * ```typescript
 * formatFileSize(1024) // Returns "1.0 KB"
 * formatFileSize(1048576) // Returns "1.0 MB"
 * formatFileSize(0) // Returns "0 B"
 * ```
 */

export const formatFileSize = (
  bytes: number | null | undefined,
  options: TextTransformOptions = {
    // Implementation needed
  }
): string => {
  const { fallback = "0 B", context = {} } = options;

  if (bytes === null || bytes === undefined) {
    return fallback;
  }

  const result = safeExecute(
    () => {
      if (typeof bytes !== "number" || isNaN(bytes) || bytes < 0) {
        throw new ValidationError(`Invalid bytes value for file size formatting: ${bytes}`, {
          bytes,
          operation: "formatFileSize",
          ...context,
        });
      }

      if (bytes === 0) {
        return "0 B";
      }

      const units = ["B", "KB", "MB", "GB", "TB"];
      const unitIndex = Math.floor(Math.log(bytes) / Math.log(1024));
      const size = bytes / Math.pow(1024, unitIndex);

      return `${size.toFixed(1)} ${units[unitIndex]}`;
    },
    "Failed to format file size",
    { bytes, ...context }
  );

  return result.success ? result.data : fallback;
};

/**
 * Generates a CSS class string from an object of conditional classes
 *
 * @param classes - Object with class names as keys and boolean conditions as values
 * @param options - Class generation options
 * @returns Space-separated CSS class string
 *
 * @example
 * ```typescript
 * generateClassNames({ 'active': true, 'disabled': false, 'primary': true }) // Returns "active primary"
 * generateClassNames({}) // Returns ""
 * ```
 */

export const generateClassNames = (
  classes: Record<string, boolean>,
  options: UtilityOptions = {
    // Implementation needed
  }
): string => {
  const { context = {} } = options;

  const result = safeExecute(
    () => {
      if (!classes || typeof classes !== "object") {
        throw new ValidationError("Invalid classes object for class name generation", {
          classes,
          operation: "generateClassNames",
          ...context,
        });
      }

      return Object.entries(classes)
        .filter(([, condition]) => Boolean(condition))
        .map(([className]) => className)
        .join(" ");
    },
    "Failed to generate class names",
    { classes, ...context }
  );

  return result.success ? result.data : "";
};
