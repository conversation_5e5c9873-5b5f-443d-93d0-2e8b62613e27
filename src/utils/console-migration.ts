import { log } from "@/services/loggingService";
import { ErrorContext } from "@/types";

/**
 * Console migration utility to help replace console statements
 * with structured logging throughout the codebase
 */

/**
 * Enhanced console replacement that provides structured logging
 * while maintaining familiar console API for easier migration
 */
export const enhancedConsole = {
  /**
   * Replace console.log with structured info logging
   */
  log: (message: string, ...args: any[]) => {
    const context: Partial<ErrorContext> = {
      component: getCallerComponent(),
      action: "console_log",
    };

    if (args.length > 0) {
      log.info(message, { args }, context);
    } else {
      log.info(message, undefined, context);
    }
  },

  /**
   * Replace console.info with structured info logging
   */
  info: (message: string, ...args: any[]) => {
    const context: Partial<ErrorContext> = {
      component: getCallerComponent(),
      action: "console_info",
    };

    if (args.length > 0) {
      log.info(message, { args }, context);
    } else {
      log.info(message, undefined, context);
    }
  },

  /**
   * Replace console.warn with structured warning logging
   */
  warn: (message: string, ...args: any[]) => {
    const context: Partial<ErrorContext> = {
      component: getCallerComponent(),
      action: "console_warn",
    };

    if (args.length > 0) {
      log.warn(message, { args }, context);
    } else {
      log.warn(message, undefined, context);
    }
  },

  /**
   * Replace console.error with structured error logging
   */
  error: (message: string, ...args: any[]) => {
    const context: Partial<ErrorContext> = {
      component: getCallerComponent(),
      action: "console_error",
    };

    const error = args.find(arg => arg instanceof Error);
    const otherArgs = args.filter(arg => !(arg instanceof Error));

    if (otherArgs.length > 0) {
      log.error(message, error, context, { args: otherArgs });
    } else {
      log.error(message, error, context);
    }
  },

  /**
   * Replace console.debug with structured debug logging
   */
  debug: (message: string, ...args: any[]) => {
    const context: Partial<ErrorContext> = {
      component: getCallerComponent(),
      action: "console_debug",
    };

    if (args.length > 0) {
      log.debug(message, { args }, context);
    } else {
      log.debug(message, undefined, context);
    }
  },

  /**
   * Replace console.group with structured logging
   */
  group: (label?: string) => {
    const context: Partial<ErrorContext> = {
      component: getCallerComponent(),
      action: "console_group",
    };

    log.info(`Group: ${label || "Unnamed"}`, undefined, context);
  },

  /**
   * Replace console.groupEnd with structured logging
   */
  groupEnd: () => {
    const context: Partial<ErrorContext> = {
      component: getCallerComponent(),
      action: "console_group_end",
    };

    log.debug("Group ended", undefined, context);
  },
};

/**
 * Attempt to get the calling component name from stack trace
 */
function getCallerComponent(): string {
  try {
    const stack = new Error().stack;
    if (!stack) return "unknown";

    const lines = stack.split("\n");
    // Skip the first few lines (this function, enhancedConsole method)
    for (let i = 3; i < lines.length; i++) {
      const line = lines[i];

      // Look for React component patterns
      const reactMatch = line.match(/at (\w+(?:Component|Hook|Provider|Context))/);
      if (reactMatch) {
        return reactMatch[1].toLowerCase();
      }

      // Look for file-based component names
      const fileMatch = line.match(/\/([^/]+)\.(tsx?|jsx?):/);
      if (fileMatch) {
        const fileName = fileMatch[1];
        // Convert PascalCase to kebab-case
        return fileName
          .replace(/([A-Z])/g, "_$1")
          .toLowerCase()
          .replace(/^_/, "");
      }
    }

    return "unknown";
  } catch {
    return "unknown";
  }
}

/**
 * Migration helper functions for specific use cases
 */
export const migrationHelpers = {
  /**
   * For performance logging (replacing console.log with timing info)
   */
  logPerformance: (operation: string, startTime: number, metadata?: Record<string, any>) => {
    const duration = performance.now() - startTime;
    log.performance(
      `${operation} completed`,
      { duration, ...metadata },
      {
        component: getCallerComponent(),
        action: "performance_log",
      }
    );
  },

  /**
   * For API request logging
   */
  logApiCall: (method: string, url: string, status: number, duration: number) => {
    log.api(method, url, status, duration, {
      component: getCallerComponent(),
    });
  },

  /**
   * For user action logging
   */
  logUserAction: (action: string, userId: string, details?: Record<string, any>) => {
    log.audit(action, userId, details, {
      component: getCallerComponent(),
    });
  },

  /**
   * For error boundary logging
   */
  logErrorBoundary: (error: Error, errorInfo: any, componentName: string) => {
    log.error(`Error boundary caught error in ${componentName}`, error, {
      component: "error_boundary",
      action: "error_caught",
      additionalData: { errorInfo, componentName },
    });
  },

  /**
   * For route navigation logging
   */
  logNavigation: (from: string, to: string, duration?: number) => {
    log.info(`Navigation: ${from} -> ${to}`, undefined, {
      component: "router",
      action: "navigation",
      additionalData: { from, to, duration },
    });
  },
};

/**
 * Development-only console that only logs in development mode
 */
export const devConsole = {
  log: (message: string, ...args: any[]) => {
    if (import.meta.env.MODE === "development") {
      enhancedConsole.log(message, ...args);
    }
  },

  info: (message: string, ...args: any[]) => {
    if (import.meta.env.MODE === "development") {
      enhancedConsole.info(message, ...args);
    }
  },

  warn: (message: string, ...args: any[]) => {
    if (import.meta.env.MODE === "development") {
      enhancedConsole.warn(message, ...args);
    }
  },

  error: (message: string, ...args: any[]) => {
    enhancedConsole.error(message, ...args); // Always log errors
  },

  debug: (message: string, ...args: any[]) => {
    if (import.meta.env.MODE === "development") {
      enhancedConsole.debug(message, ...args);
    }
  },
};

/**
 * Conditional logging based on environment
 */
export const conditionalLog = {
  dev: devConsole,
  prod: {
    // Only critical logs in production
    error: enhancedConsole.error,
    warn: enhancedConsole.warn,
  },
};
