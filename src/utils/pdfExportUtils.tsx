// src/utils/pdfExportUtils.ts - Simplified version without canvas charts
import { Risk } from "@/types";
// Simple PDF export function using only jsPDF and autotable
export const exportReportToPDF = async (reportName: string, filteredRisks: Risk[]) => {
  try {
    // Dynamic import with proper default handling
    const jsPDFModule = await import("jspdf");
    const autoTableModule = await import("jspdf-autotable");
    // Access the correct constructor and function
    const jsPDF = jsPDFModule.default;
    const autoTable = autoTableModule.default;
    // Create new PDF instance
    const pdf = new jsPDF();
    // Add title
    pdf.setFontSize(20);
    pdf.setFont("helvetica", "bold");
    pdf.text(reportName, 20, 30);
    // Add generation date and summary
    pdf.setFontSize(10);
    pdf.setFont("helvetica", "normal");
    pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, 20, 45);
    pdf.text(`Total risks analyzed: ${filteredRisks.length}`, 20, 55);
    // Add summary statistics
    const severityCounts = filteredRisks.reduce(
      (acc, risk) => {
        acc[risk.severity] = (acc[risk.severity] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );
    let summaryY = 65;
    pdf.setFontSize(9);
    Object.entries(severityCounts).forEach(([severity, count]) => {
      pdf.text(`${severity}: ${count}`, 20, summaryY);
      summaryY += 8;
    });
    const yPosition = summaryY + 15;
    // Add risks table
    const tableData = filteredRisks
      .slice(0, 50)
      .map(risk => [
        risk.title.length > 35 ? risk.title.substring(0, 35) + "..." : risk.title,
        risk.severity,
        risk.status,
        risk.category ?? "N/A",
      ]);
    if (tableData.length === 0) {
      pdf.setFontSize(10);
      pdf.setFont("helvetica", "normal");
      pdf.text("No risks match the current criteria.", 20, yPosition);
    } else {
      // Use autoTable with the default export
      autoTable(pdf, {
        head: [["Risk Title", "Severity", "Status", "Category"]],
        body: tableData,
        startY: yPosition,
        margin: { left: 20, right: 20 },
        styles: {
          fontSize: 8,
          cellPadding: 3,
        },
        headStyles: {
          fillColor: [59, 130, 246],
          textColor: [255, 255, 255],
          fontStyle: "bold",
        },
        columnStyles: {
          0: { cellWidth: 75 },
          1: { cellWidth: 20 },
          2: { cellWidth: 25 },
          3: { cellWidth: 30 },
        },
        alternateRowStyles: {
          fillColor: [245, 245, 245],
        },
      });
    }
    // Add footer with page numbers and metadata
    const pageCount = pdf.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      pdf.setPage(i);
      pdf.setFontSize(8);
      pdf.setFont("helvetica", "normal");
      pdf.setTextColor(100, 100, 100);
      pdf.text(
        `Page ${i} of ${pageCount}`,
        pdf.internal.pageSize.width - 30,
        pdf.internal.pageSize.height - 10
      );
      pdf.text(
        `${reportName} - Generated ${new Date().toLocaleDateString()}`,
        20,
        pdf.internal.pageSize.height - 10
      );
    }
    // Save the PDF
    const timestamp = new Date().toISOString().split("T")[0];
    pdf.save(`${reportName}_${timestamp}.pdf`);
  } catch (error) {
    throw new Error(
      `Failed to generate PDF: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
};
export const exportReportToCSV = (reportName: string, filteredRisks: Risk[]) => {
  try {
    const headers = [
      "Risk Title",
      "Description",
      "Severity",
      "Status",
      "Category",
      "Impact",
      "Likelihood",
      "Risk Score",
      "Owner",
      "Created Date",
      "Due Date",
      "Last Updated",
    ];
    const csvData = filteredRisks.map(risk => [
      risk.title ?? "",
      (risk.description ?? "").replace(/"/g, '""'),
      risk.severity ?? "",
      risk.status ?? "",
      risk.category ?? "",
      risk.impact?.toString() ?? "",
      risk.likelihood?.toString() ?? "",
      risk.impact && risk.likelihood ? (risk.impact * risk.likelihood).toString() : "",
      risk.ownerName ?? "",
      risk.createdAt ? new Date(risk.createdAt).toLocaleDateString() : "",
      risk.dueDate ? new Date(risk.dueDate).toLocaleDateString() : "",
      risk.updatedAt ? new Date(risk.updatedAt).toLocaleDateString() : "",
    ]);
    const csvContent = [headers, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(","))
      .join("\n");
    const BOM = "\uFEFF";
    const blob = new Blob([BOM + csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `${reportName}_data_${new Date().toISOString().split("T")[0]}.csv`;
    a.style.display = "none";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  } catch (error) {
    throw new Error(
      `Failed to generate CSV: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
};
