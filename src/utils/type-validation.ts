/**
 * Type validation utilities for form data and event handlers
 * Ensures strict type checking across the application
 */

import { z } from "zod";

/**
 * Generic form event handler type
 */
export type FormEventHandler<T = HTMLFormElement> = (
  event: React.FormEvent<T>
) => void | Promise<void>;

/**
 * Generic change event handler type
 */
export type ChangeEventHandler<T = HTMLInputElement> = (event: React.ChangeEvent<T>) => void;

/**
 * Form data validation schema
 */

export const formDataSchema = z.object({
  // Common form fields with validation
  email: z.string().email("Invalid email format").optional(),
  password: z.string().min(6, "Password must be at least 6 characters").optional(),
  name: z.string().min(1, "Name is required").optional(),
  title: z.string().min(1, "Title is required").optional(),
  description: z.string().min(1, "Description is required").optional(),
});

/**
 * Type-safe form data validator
 */
export function validateFormData<T extends Record<string, unknown>>(
  data: T,
  schema: z.ZodSchema<T>
): { isValid: boolean; errors: string[]; data?: T } {
  try {
    const validatedData = schema.parse(data);
    return {
      isValid: true,
      errors: [],
      data: validatedData,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        errors: error.errors.map(err => err.message),
      };
    }
    return {
      isValid: false,
      errors: ["Unknown validation error"],
    };
  }
}

/**
 * Type guard for form elements
 */
export function isFormElement(element: EventTarget | null): element is HTMLFormElement {
  return element instanceof HTMLFormElement;
}

/**
 * Type guard for input elements
 */
export function isInputElement(element: EventTarget | null): element is HTMLInputElement {
  return element instanceof HTMLInputElement;
}

/**
 * Type guard for textarea elements
 */
export function isTextAreaElement(element: EventTarget | null): element is HTMLTextAreaElement {
  return element instanceof HTMLTextAreaElement;
}

/**
 * Type guard for select elements
 */
export function isSelectElement(element: EventTarget | null): element is HTMLSelectElement {
  return element instanceof HTMLSelectElement;
}

/**
 * Safe event target value extractor
 */
export function getEventTargetValue(
  event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
): string {
  return event.target.value;
}

/**
 * Safe event target checked extractor for checkboxes
 */
export function getEventTargetChecked(event: React.ChangeEvent<HTMLInputElement>): boolean {
  return event.target.checked;
}

/**
 * Type-safe form submission handler wrapper
 */
export function createFormSubmissionHandler<T extends Record<string, unknown>>(
  handler: (data: T) => void | Promise<void>,
  schema: z.ZodSchema<T>
): FormEventHandler {
  return async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    if (!isFormElement(event.currentTarget)) {
      throw new Error("Invalid form element");
    }

    const formData = new FormData(event.currentTarget);
    const data = Object.fromEntries(formData.entries()) as T;

    const validation = validateFormData(data, schema);
    if (!validation.isValid) {
      throw new Error(`Form validation failed: ${validation.errors.join(", ")}`);
    }

    await handler(validation.data!);
  };
}

/**
 * Type-safe input change handler wrapper
 */
export function createInputChangeHandler(
  setter: (value: string) => void
): ChangeEventHandler<HTMLInputElement> {
  return (event: React.ChangeEvent<HTMLInputElement>) => {
    setter(getEventTargetValue(event));
  };
}

/**
 * Type-safe checkbox change handler wrapper
 */
export function createCheckboxChangeHandler(
  setter: (checked: boolean) => void
): ChangeEventHandler<HTMLInputElement> {
  return (event: React.ChangeEvent<HTMLInputElement>) => {
    setter(getEventTargetChecked(event));
  };
}

/**
 * Common form field validation schemas
 */
export const commonFieldSchemas = {
  email: z.string().email("Invalid email format"),
  password: z.string().min(6, "Password must be at least 6 characters"),
  name: z.string().min(1, "Name is required"),
  title: z.string().min(1, "Title is required"),
  description: z.string().min(1, "Description is required"),
  url: z.string().url("Invalid URL format"),
  phone: z.string().regex(/^\+?[\d\s\-()]+$/, "Invalid phone number format"),
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Invalid date format (YYYY-MM-DD)"),
} as const;

/**
 * Type-safe form field validator
 */
export function validateField<T>(
  value: unknown,
  schema: z.ZodSchema<T>
): { isValid: boolean; error?: string; value?: T } {
  try {
    const validatedValue = schema.parse(value);
    return {
      isValid: true,
      value: validatedValue,
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        isValid: false,
        error: error.errors[0]?.message || "Validation error",
      };
    }
    return {
      isValid: false,
      error: "Unknown validation error",
    };
  }
}
