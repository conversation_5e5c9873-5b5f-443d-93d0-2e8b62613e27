import { Policy, PolicyRequest, PolicyFromDB, PolicyRequestFromDB } from "@/types/policy";

/**
 * Map a policy from DB format (snake_case) to application format (camelCase)
 */

export const mapPolicyFromDB = (policy: PolicyFromDB): Policy => ({
  id: policy.id,
  title: policy.title,
  description: policy.description,
  category: policy.category,
  version: policy.version,
  status: policy.status,
  effectiveDate: policy.effective_date,
  documentUrl: policy.document_url,
  createdBy: policy.created_by,
  createdAt: policy.created_at,
  updatedAt: policy.updated_at,
});

/**
 * Map a policy request from DB format (snake_case) to application format (camelCase)
 */

export const mapPolicyRequestFromDB = (request: PolicyRequestFromDB): PolicyRequest => ({
  id: request.id,
  title: request.title,
  description: request.description,
  reason: request.reason,
  status: request.status,
  requesterId: request.requester_id,
  reviewerId: request.reviewer_id,
  referenceDocumentUrl: request.reference_document_url,
  feedback: request.feedback,
  createdAt: request.created_at,
  updatedAt: request.updated_at,
});
