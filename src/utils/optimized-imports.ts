/**
 * Optimized import utilities to reduce bundle size through tree shaking
 * This file provides selective imports for large libraries
 */
// ============================================================================
// DATE-FNS OPTIMIZED IMPORTS
// ============================================================================
/**
 * Optimized date-fns imports - only import what you need
 * Instead of: import { format, parseISO, addDays } from 'date-fns'
 * Use: import { formatDate, parseISODate, addDaysToDate } from '@/utils/optimized-imports'
 */
// Individual function imports for better tree shaking
export { format as formatDate } from "date-fns/format";
export { parseISO as parseISODate } from "date-fns/parseISO";
export { addDays as addDaysToDate } from "date-fns/addDays";
export { subDays as subtractDaysFromDate } from "date-fns/subDays";
export { differenceInDays as daysDifference } from "date-fns/differenceInDays";
export { startOfDay } from "date-fns/startOfDay";
export { endOfDay } from "date-fns/endOfDay";
export { isAfter } from "date-fns/isAfter";
export { isBefore } from "date-fns/isBefore";
export { isEqual as isDateEqual } from "date-fns/isEqual";
// ============================================================================
// LUCIDE REACT OPTIMIZED IMPORTS
// ============================================================================
/**
 * Optimized Lucide React imports - only import specific icons
 * Instead of: import { ChevronDown, User, Settings } from 'lucide-react'
 * Use individual imports or this centralized approach
 */
// Core navigation icons
export { ChevronDown, ChevronUp, ChevronLeft, ChevronRight } from "lucide-react";
export { Menu, X, Home, ArrowLeft, ArrowRight } from "lucide-react";
// User interface icons
export { User, Settings, Bell, Search, Filter } from "lucide-react";
export { Plus, Minus, Edit, Trash2, Save, Download } from "lucide-react";
// Status and feedback icons
export { Check, AlertTriangle, Info, AlertCircle, XCircle } from "lucide-react";
export { Eye, EyeOff, Lock, Unlock, Shield } from "lucide-react";
// Data and analytics icons
export { BarChart3, LineChart, PieChart, TrendingUp, TrendingDown } from "lucide-react";
export { Calendar, Clock, FileText, Folder, Database } from "lucide-react";
// ============================================================================
// RADIX UI OPTIMIZED IMPORTS
// ============================================================================
/**
 * Optimized Radix UI imports - only import what you need
 * This helps reduce bundle size by avoiding unused components
 */
// Dialog components
export {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
// Dropdown menu components
export {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuRadioItem,
  DropdownMenuRadioGroup,
  DropdownMenuPortal,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuGroup,
  DropdownMenuShortcut,
} from "@/components/ui/dropdown-menu";
// Form components
export {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
// ============================================================================
// UTILITY FUNCTIONS FOR TREE SHAKING
// ============================================================================
/**
 * Optimized utility functions that help with tree shaking
 */
// Optimized clsx alternative for smaller bundle
export function cn(...classes: (string | undefined | null | boolean)[]): string {
  return classes.filter(Boolean).join(" ");
}
// Optimized object utilities
export function pick<T, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> {
  const result = {} as Pick<T, K>;
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key];
    }
  });
  return result;
}
export function omit<T, K extends keyof T>(obj: T, keys: K[]): Omit<T, K> {
  const result = { ...obj };
  keys.forEach(key => {
    delete result[key];
  });
  return result;
}
// Optimized array utilities
export function unique<T>(array: T[]): T[] {
  return [...new Set(array)];
}
export function groupBy<T, K extends string | number | symbol>(
  array: T[],
  keyFn: (item: T) => K
): Record<K, T[]> {
  return array.reduce(
    (groups, item) => {
      const key = keyFn(item);
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(item);
      return groups;
    },
    {} as Record<K, T[]>
  );
}
// ============================================================================
// CONDITIONAL IMPORTS FOR HEAVY LIBRARIES
// ============================================================================
/**
 * Conditional import utilities for heavy libraries
 * These are only loaded when actually needed
 */
// PDF utilities - only loaded when needed
export const PDFUtils = {
  async generatePDF(data: unknown, options: Record<string, unknown> = {}) {
    const { LazyPDFExporter } = await import("./library-lazy-loader");
    return LazyPDFExporter.exportToPDF(data, options);
  },
};
// Excel utilities - only loaded when needed
export const ExcelUtils = {
  async exportToExcel(data: unknown[], filename: string = "export.xlsx") {
    const { LazyExcelExporter } = await import("./library-lazy-loader");
    return LazyExcelExporter.exportToExcel(data, filename);
  },
  async readExcelFile(file: File) {
    const { LazyExcelExporter } = await import("./library-lazy-loader");
    return LazyExcelExporter.readExcelFile(file);
  },
};
// Chart utilities - only loaded when needed
export const ChartUtils = {
  async renderChart(type: string, data: unknown[], config: Record<string, unknown> = {}) {
    const { LazyChartRenderer } = await import("./library-lazy-loader");
    return LazyChartRenderer.renderChart(type, data, config);
  },
};
// Animation utilities - only loaded when needed
export const AnimationUtils = {
  async getMotionComponent() {
    const { LazyAnimationProvider } = await import("./library-lazy-loader");
    return LazyAnimationProvider.getMotionComponent();
  },
  async getAnimatePresence() {
    const { LazyAnimationProvider } = await import("./library-lazy-loader");
    return LazyAnimationProvider.getAnimatePresence();
  },
};
// ============================================================================
// BUNDLE SIZE MONITORING
// ============================================================================
/**
 * Development utilities to monitor import usage
 */
export const BundleMonitor = {
  logImportUsage(moduleName: string, functionName: string) {
    if (import.meta.env.MODE === "development") {
      // Condition handled
    }
  },
  warnLargeImport(moduleName: string, size: string) {
    if (import.meta.env.MODE === "development") {
      // Condition handled
    }
  },
};
// ============================================================================
// EXPORT OPTIMIZATION HELPERS
// ============================================================================
/**
 * Helper functions to optimize exports and reduce bundle size
 */
// Re-export commonly used utilities with tree shaking
export { clsx } from "clsx";
export { twMerge } from "tailwind-merge";
// Optimized class name utility
export function classNames(...classes: (string | undefined | null | boolean)[]): string {
  return classes.filter(Boolean).join(" ");
}
// Type-safe object key utilities
export function objectKeys<T extends Record<string, any>>(obj: T): (keyof T)[] {
  return Object.keys(obj) as (keyof T)[];
}
export function objectEntries<T extends Record<string, any>>(obj: T): [keyof T, T[keyof T]][] {
  return Object.entries(obj) as [keyof T, T[keyof T]][];
}
// Debounce utility for performance
export function debounce<T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: number;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
// Throttle utility for performance
export function throttle<T extends (...args: unknown[]) => unknown>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}
export default {
  PDFUtils,
  ExcelUtils,
  ChartUtils,
  AnimationUtils,
  BundleMonitor,
  cn,
  pick,
  omit,
  unique,
  groupBy,
  classNames,
  objectKeys,
  objectEntries,
  debounce,
  throttle,
};
