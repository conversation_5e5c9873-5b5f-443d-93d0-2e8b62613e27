import { Risk, Incident, Policy } from "@/types";
import { saveAs } from "file-saver";
// Export data interfaces
export interface ExportDataProps {
  risks?: Risk[];
  incidents?: Incident[];
  policies?: Policy[];
}
export type ExportFormat = "csv" | "excel" | "pdf";
export type ExportDataType = "risks" | "incidents" | "policies";
/**
 * Enum for supported report types to prevent ambiguous string matching
 */
export enum ReportType {
  RISK_SUMMARY = "risk-summary",
  RISK_DETAILED = "risk-detailed",
  INCIDENTS = "incidents",
  BOARD = "board",
  CATEGORIES = "categories",
  COMBINED = "combined",
  RISK_AND_INCIDENT_REPORT = "risk-and-incident-report",
  INCIDENT_ANALYSIS = "incident-analysis",
  RISK_ANALYSIS = "risk-analysis",
  COMPLIANCE = "compliance",
  AUDIT = "audit",
}
/**
 * Data type enum to clearly identify what data should be included
 */
export enum DataType {
  RISKS_ONLY = "risks-only",
  INCIDENTS_ONLY = "incidents-only",
  BOTH = "both",
  SUMMARY = "summary",
}
/**
 * Clear mapping from report types to data types
 * This prevents ambiguous matches and ensures correct data selection
 */
export const REPORT_TYPE_MAPPING: Record<ReportType, DataType> = {
  [ReportType.RISK_SUMMARY]: DataType.RISKS_ONLY,
  [ReportType.RISK_DETAILED]: DataType.RISKS_ONLY,
  [ReportType.RISK_ANALYSIS]: DataType.RISKS_ONLY,
  [ReportType.INCIDENTS]: DataType.INCIDENTS_ONLY,
  [ReportType.INCIDENT_ANALYSIS]: DataType.INCIDENTS_ONLY,
  [ReportType.BOARD]: DataType.BOTH,
  [ReportType.CATEGORIES]: DataType.RISKS_ONLY,
  [ReportType.COMBINED]: DataType.BOTH,
  [ReportType.RISK_AND_INCIDENT_REPORT]: DataType.BOTH,
  [ReportType.COMPLIANCE]: DataType.BOTH,
  [ReportType.AUDIT]: DataType.BOTH,
};
/**
 * Validates if a report type is supported
 * @param reportType - The report type to validate
 * @returns boolean indicating if the report type is valid
 */
export const isValidReportType = (reportType: string): reportType is ReportType => {
  return Object.values(ReportType).includes(reportType as ReportType);
};
/**
 * Gets the data type for a given report type using explicit mapping
 * @param reportType - The report type
 * @returns The corresponding data type
 */
export const getDataTypeForReport = (reportType: string): DataType => {
  if (!isValidReportType(reportType)) {
    throw new Error(
      `Unsupported report type: ${reportType}. Supported types: ${Object.values(ReportType).join(", ")}`
    );
  }
  return REPORT_TYPE_MAPPING[reportType as ReportType];
};
/**
 * Determines if risks should be included in the export
 * @param reportType - The report type
 * @returns boolean indicating if risks should be included
 */
export const shouldIncludeRisks = (reportType: string): boolean => {
  const dataType = getDataTypeForReport(reportType);
  return (
    dataType === DataType.RISKS_ONLY || dataType === DataType.BOTH || dataType === DataType.SUMMARY
  );
};
/**
 * Determines if incidents should be included in the export
 * @param reportType - The report type
 * @returns boolean indicating if incidents should be included
 */
export const shouldIncludeIncidents = (reportType: string): boolean => {
  const dataType = getDataTypeForReport(reportType);
  return (
    dataType === DataType.INCIDENTS_ONLY ||
    dataType === DataType.BOTH ||
    dataType === DataType.SUMMARY
  );
};
// Utility functions
const formatDate = (date: Date | string | null | undefined): string => {
  if (!date) return "";
  const d = typeof date === "string" ? new Date(date) : date;
  return isNaN(d.getTime()) ? "" : d.toLocaleDateString();
};
const formatDateTime = (date: Date | string | null | undefined): string => {
  if (!date) return "";
  const d = typeof date === "string" ? new Date(date) : date;
  return isNaN(d.getTime()) ? "" : d.toLocaleString();
};
const sanitizeForCSV = (value: unknown): string => {
  if (value === null || value === undefined) return "";
  const str = String(value);
  // Escape quotes and wrap in quotes if contains comma, quote, or newline
  if (str.includes(",") || str.includes('"') || str.includes("\n")) {
    return `"${str.replace(/"/g, '""')}"`;
  }
  return str;
};
// Generate filename with timestamp
export const generateFileName = (prefix: string, extension: string): string => {
  const now = new Date();
  const timestamp = now.toISOString().replace(/[:.]/g, "-").replace("T", "_").slice(0, 19);
  return `${prefix}_${timestamp}.${extension}`;
};
// Format data for export based on type
export const formatDataForExport = (
  data: unknown[],
  type: ExportDataType
): Record<string, unknown>[] => {
  if (!Array.isArray(data) || data.length === 0) {
    return [];
  }
  switch (type) {
    case "risks":
      return data.map((risk: Risk) => ({
        ID: risk.id ?? "",
        Title: risk.title ?? "",
        Description: risk.description ?? "",
        Category: risk.category ?? "",
        Severity: risk.severity ?? "",
        Status: risk.status ?? "",
        Owner: risk.ownerName ?? "",
        Likelihood: risk.likelihood ?? "",
        Impact: risk.impact ?? "",
        "Inherent Likelihood": risk.inherentLikelihood ?? "",
        "Inherent Impact": risk.inherentImpact ?? "",
        "Inherent Severity": risk.inherentSeverity ?? "",
        "Current Controls": risk.currentControls ?? "",
        "Mitigation Approach": risk.mitigationApproach ?? "",
        "Due Date": formatDate(risk.dueDate),
        "Created At": formatDateTime(risk.createdAt),
        "Updated At": formatDateTime(risk.updatedAt),
      }));
    case "incidents":
      return data.map((incident: Incident) => ({
        ID: incident.id ?? "",
        Title: incident.title ?? "",
        Description: incident.description ?? "",
        Severity: incident.severity ?? "",
        Status: incident.status ?? "",
        Reporter: incident.reporterName ?? "",
        "Related Risk": incident.relatedRiskTitle ?? "",
        Date: formatDateTime(incident.date),
      }));
    case "policies":
      return data.map((policy: Policy) => ({
        ID: policy.id ?? "",
        Title: policy.title ?? "",
        Description: policy.description ?? "",
        Category: policy.category ?? "",
        Version: policy.version ?? "",
        Status: policy.status ?? "",
        "Effective Date": policy.effectiveDate ?? "",
        "Created At": policy.createdAt ?? "",
      }));
    default:
      return data;
  }
};
// CSV Export Function
export const exportToCSV = async (data: unknown[], type: ExportDataType): Promise<void> => {
  try {
    const formattedData = formatDataForExport(data, type);
    if (formattedData.length === 0) {
      throw new Error("No data to export");
    }
    // Get headers from first row
    const headers = Object.keys(formattedData[0]);
    // Create CSV content
    const csvContent = [
      // Header row
      headers.map(header => sanitizeForCSV(header)).join(","),
      // Data rows
      ...formattedData.map(row => headers.map(header => sanitizeForCSV(row[header])).join(",")),
    ].join("\n");
    // Create blob and download
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const filename = generateFileName(type, "csv");
    saveAs(blob, filename);
  } catch (error) {
    throw new Error(
      `Failed to export ${type} to CSV: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
};
// Excel Export Function
export const exportToExcel = async (data: unknown[], type: ExportDataType): Promise<void> => {
  try {
    // Dynamic import to avoid including xlsx in main bundle
    const XLSX = await import("xlsx");
    const formattedData = formatDataForExport(data, type);
    if (formattedData.length === 0) {
      throw new Error("No data to export");
    }
    // Create workbook and worksheet
    const workbook = XLSX.utils.book_new();
    const worksheet = XLSX.utils.json_to_sheet(formattedData);
    // Add worksheet to workbook
    const sheetName = type.charAt(0).toUpperCase() + type.slice(1);
    XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    // Generate Excel file
    const excelBuffer = XLSX.write(workbook, {
      bookType: "xlsx",
      type: "array",
    });
    // Create blob and download
    const blob = new Blob([excelBuffer], {
      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    });
    const filename = generateFileName(type, "xlsx");
    saveAs(blob, filename);
  } catch (error) {
    throw new Error(
      `Failed to export ${type} to Excel: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
};
// PDF Export Function
export const exportToPDF = async (data: unknown[], type: ExportDataType): Promise<void> => {
  try {
    // Dynamic import to avoid including jsPDF in main bundle
    const jsPDFModule = await import("jspdf");
    const autoTableModule = await import("jspdf-autotable");
    // Get the jsPDF constructor and autoTable function
    const jsPDF = jsPDFModule.default;
    const autoTable = autoTableModule.default;
    const formattedData = formatDataForExport(data, type);
    if (formattedData.length === 0) {
      throw new Error("No data to export");
    }
    const doc = new jsPDF();
    // Add title
    doc.setFontSize(16);
    const title = `${type.charAt(0).toUpperCase() + type.slice(1)} Report`;
    doc.text(title, 14, 22);
    // Add generation date
    doc.setFontSize(10);
    doc.text(`Generated on: ${new Date().toLocaleString()}`, 14, 30);
    // Get headers and prepare table data
    const headers = Object.keys(formattedData[0]);
    const tableData = formattedData.map(row => headers.map(header => String(row[header] ?? "")));
    // Add table using autoTable function directly
    autoTable(doc, {
      startY: 40,
      head: [headers],
      body: tableData,
      headStyles: { fillColor: [67, 83, 191] },
      styles: { fontSize: 8 },
      columnStyles: {
        0: { cellWidth: "auto" },
      },
    });
    // Save the PDF
    const filename = generateFileName(type, "pdf");
    doc.save(filename);
  } catch (error) {
    throw new Error(
      `Failed to export ${type} to PDF: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
};
// Download file utility
export const downloadFile = (content: Blob | string, filename: string, mimeType?: string): void => {
  try {
    let blob: Blob;
    if (content instanceof Blob) {
      blob = content;
    } else {
      blob = new Blob([content], { type: mimeType ?? "text/plain" });
    }
    saveAs(blob, filename);
  } catch (error) {
    throw new Error(
      `Failed to download file: ${error instanceof Error ? error.message : "Unknown error"}`
    );
  }
};
// Legacy functions for backward compatibility with existing components
export const exportToPdf = async (reportType: string, data: ExportDataProps): Promise<void> => {
  const { risks = [], incidents = [], policies = [] } = data;
  if (reportType.includes("risk") || risks.length > 0) {
    return exportToPDF(risks, "risks");
  } else if (reportType.includes("incident") || incidents.length > 0) {
    return exportToPDF(incidents, "incidents");
  } else if (reportType.includes("polic") || policies.length > 0) {
    return exportToPDF(policies, "policies");
  } else {
    throw new Error("No data provided for export");
  }
};
export const exportToExcelLegacy = async (
  reportType: string,
  data: ExportDataProps
): Promise<void> => {
  const { risks = [], incidents = [], policies = [] } = data;
  if (reportType.includes("risk") || risks.length > 0) {
    return exportToExcel(risks, "risks");
  } else if (reportType.includes("incident") || incidents.length > 0) {
    return exportToExcel(incidents, "incidents");
  } else if (reportType.includes("polic") || policies.length > 0) {
    return exportToExcel(policies, "policies");
  } else {
    throw new Error("No data provided for export");
  }
};
