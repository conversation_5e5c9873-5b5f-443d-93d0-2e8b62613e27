/**
 * Type validation utilities to ensure runtime type safety
 * Includes HTML sanitization and XSS prevention
 */
import { ControlMeasure, MitigationAction, Risk, RiskSeverity, RiskStatus } from "@/types";
import { isControlMeasure, isMitigationAction, isRiskSeverity, isRiskStatus } from "./typeGuards";
/**
 * Escapes HTML entities to prevent XSS attacks
 * @param text - The text to escape
 * @returns The escaped text
 */
export const escapeHtmlEntities = (text: unknown): string => {
  // Handle null/undefined differently than other falsy values
  if (text === null) {
    return "null";
  }
  if (text === undefined) {
    return "undefined";
  }
  // Convert other input to string
  const str = text.toString();
  const entityMap: Record<string, string> = {
    "&": "&amp;",
    "<": "&lt;",
    ">": "&gt;",
    '"': "&quot;",
    "'": "&#x27;", // Use &#x27; instead of &#39; for consistency with tests
    "/": "&#x2F;",
    "`": "&#x60;",
    "=": "&#x3D;",
  };
  return str.replace(/[&<>"'`=/]/g, s => entityMap[s]);
};
/**
 * Sanitizes HTML content by removing dangerous tags and attributes
 * This is an async version that could be extended to use a proper HTML sanitizer library
 * @param html - The HTML content to sanitize
 * @param options - Sanitization options
 * @returns Promise resolving to sanitized HTML
 */
export const sanitizeHtml = async (
  html: string,
  options: {
    allowedTags?: string[];
    stripTags?: boolean;
  } = {
    // Implementation needed
  }
): Promise<string> => {
  // Convert non-string to empty string
  if (typeof html !== "string") {
    return "";
  }
  return sanitizeHtmlSync(html, options);
};
/**
 * Synchronous HTML sanitization
 * Removes script tags, event handlers, and other potentially dangerous content
 * @param html - The HTML content to sanitize
 * @param options - Sanitization options
 * @returns Sanitized HTML
 */
export const sanitizeHtmlSync = (
  html: string,
  options: {
    allowedTags?: string[];
    stripTags?: boolean;
  } = {}
): string => {
  if (typeof html !== "string") {
    return "";
  }
  const { allowedTags, stripTags = false } = options;
  // If stripTags is true, remove all HTML tags
  if (stripTags) {
    return html.replace(/<[^>]*>/g, "");
  }
  let sanitized = html;
  // Define dangerous tags that should always be removed
  const dangerousTags = [
    "script",
    "object",
    "embed",
    "form",
    "input",
    "iframe",
    "meta",
    "link",
    "style",
    "applet",
    "frameset",
    "frame",
    "base",
    "body",
    "html",
    "head",
  ];
  // Remove dangerous tags and their content
  dangerousTags.forEach(tag => {
    const regex = new RegExp(`<${tag}\\b[^<]*(?:(?!<\\/${tag}>)<[^<]*)*<\\/${tag}>`, "gi");
    sanitized = sanitized.replace(regex, "");
    // Also remove self-closing versions
    const selfClosingRegex = new RegExp(`<${tag}\\b[^>]*/?\u003e`, "gi");
    sanitized = sanitized.replace(selfClosingRegex, "");
  });
  // If allowedTags is specified, remove any tags not in the list
  if (allowedTags && Array.isArray(allowedTags)) {
    // Create regex to match all tags
    const tagRegex = /<\/?([a-zA-Z][a-zA-Z0-9]*)[^>]*>/g;
    sanitized = sanitized.replace(tagRegex, (match, tagName) => {
      if (allowedTags.includes(tagName.toLowerCase())) {
        return match;
      }
      return "";
    });
  }
  // Remove event handlers (onclick, onload, etc.)
  sanitized = sanitized.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, "");
  // Remove javascript: protocol
  sanitized = sanitized.replace(/javascript:/gi, "");
  // Remove data: protocol that contains dangerous content
  sanitized = sanitized.replace(/data:text\/html/gi, "");
  // Remove vbscript: protocol
  sanitized = sanitized.replace(/vbscript:/gi, "");
  // Remove style attributes that could contain expressions
  sanitized = sanitized.replace(/\s*style\s*=\s*["'][^"']*expression[^"']*["']/gi, "");
  return sanitized;
};
/**
 * Validates and sanitizes input with comprehensive XSS prevention
 * @param input - The input to validate and sanitize
 * @param options - Validation options
 * @returns Promise resolving to sanitized input
 */
export const validateAndSanitizeInput = async (
  input: unknown,
  options: {
    allowHtml?: boolean;
    maxLength?: number;
    required?: boolean;
    allowedTags?: string[];
    strictLength?: boolean; // If true, throw error. If false, truncate.
  } = {}
): Promise<string> => {
  const {
    allowHtml = false,
    maxLength = 10000,
    required = false,
    allowedTags,
    strictLength = false,
  } = options;
  // Convert non-string input to string
  if (typeof input !== "string") {
    if (input === null || input === undefined) {
      if (required) {
        throw new Error("Input is required and must be a string");
      }
      return "";
    }
    // Convert to string
    input = String(input);
  }
  if (required && input.trim().length === 0) {
    throw new Error("Input is required and cannot be empty");
  }
  // Handle length constraint
  if (input.length > maxLength) {
    if (strictLength) {
      throw new Error(`Input exceeds maximum length of ${maxLength} characters`);
    } else {
      // Truncate instead of throwing
      input = input.substring(0, maxLength);
    }
  }
  if (allowHtml) {
    return await sanitizeHtml(input, { allowedTags });
  } else {
    return escapeHtmlEntities(input);
  }
};
/**
 * Synchronous version of validateAndSanitizeInput
 * @param input - The input to validate and sanitize
 * @param options - Validation options
 * @returns Sanitized input
 */
export const validateAndSanitizeInputSync = (
  input: string,
  options: {
    allowHtml?: boolean;
    maxLength?: number;
    required?: boolean;
  } = {}
): string => {
  const { allowHtml = false, maxLength = 10000, required = false } = options;
  if (typeof input !== "string") {
    if (required) {
      throw new Error("Input is required and must be a string");
    }
    return "";
  }
  if (required && input.trim().length === 0) {
    throw new Error("Input is required and cannot be empty");
  }
  if (input.length > maxLength) {
    throw new Error(`Input exceeds maximum length of ${maxLength} characters`);
  }
  if (allowHtml) {
    return sanitizeHtmlSync(input);
  } else {
    return escapeHtmlEntities(input);
  }
};
/**
 * Type guard for control measure effectiveness values
 */
export const isValidEffectiveness = (value: unknown): value is "High" | "Medium" | "Low" => {
  return value === "High" || value === "Medium" || value === "Low";
};
/**
 * Validates and returns a properly typed effectiveness value with a fallback
 */
export const validateEffectiveness = (
  value: unknown,
  fallback: "High" | "Medium" | "Low" = "Medium"
): "High" | "Medium" | "Low" => {
  if (isValidEffectiveness(value)) {
    return value;
  }
  return fallback;
};
/**
 * Validates a control measure object for required properties and types
 */
export const validateControlMeasure = (
  measure: Partial<ControlMeasure>
): Partial<ControlMeasure> => {
  if (!measure) return {};
  return {
    ...measure,
    description: measure.description ?? "",
    effectiveness: validateEffectiveness(measure.effectiveness),
    implemented: typeof measure.implemented === "boolean" ? measure.implemented : true,
  };
};
/**
 * Validates a mitigation action object for required properties and types
 */
export const validateMitigationAction = (
  action: Partial<MitigationAction>
): Partial<MitigationAction> => {
  if (!action) return {};
  return {
    ...action,
    description: action.description ?? "",
    completed: typeof action.completed === "boolean" ? action.completed : false,
  };
};
/**
 * Validates risk severity value
 */
export const validateSeverity = (value: unknown): RiskSeverity => {
  if (isRiskSeverity(value)) {
    return value;
  }
  return RiskSeverity.LOW; // Default fallback
};
/**
 * Validates risk status value
 */
export const validateStatus = (value: unknown): RiskStatus => {
  if (isRiskStatus(value)) {
    return value;
  }
  return RiskStatus.IDENTIFIED; // Default fallback
};
/**
 * Validates and normalizes a date string to a Date object
 */
export const validateDate = (dateString: unknown): Date | undefined => {
  if (!dateString) {
    return undefined;
  }
  try {
    if (dateString instanceof Date) {
      return dateString;
    }
    if (typeof dateString === "string") {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        return undefined;
      }
      return date;
    }
  } catch (error) {
    // Date parsing error, return undefined
  }
  return undefined;
};
/**
 * Validates if a value is a valid date (Date object or parseable date string)
 */
export const isValidDate = (value: unknown): boolean => {
  if (!value) {
    return false;
  }
  try {
    if (value instanceof Date) {
      return !isNaN(value.getTime());
    }
    if (typeof value === "string") {
      const date = new Date(value);
      return !isNaN(date.getTime());
    }
  } catch (error) {
    return false;
  }
  return false;
};
/**
 * Validation result interface
 */
export interface ValidationResult<T = unknown> {
  isValid: boolean;
  errors: string[];
  validatedData?: T;
}
/**
 * Validates complete risk data object
 */
export const validateRiskData = (risk: Record<string, unknown>): ValidationResult<Risk> => {
  const errors: string[] = [];
  if (!risk || typeof risk !== "object") {
    return {
      isValid: false,
      errors: ["Risk must be an object"],
    };
  }
  // Required fields validation
  if (!risk.id || typeof risk.id !== "string") {
    errors.push("Risk must have a valid id");
  }
  if (!risk.title || typeof risk.title !== "string") {
    errors.push("Risk must have a valid title");
  }
  if (!risk.description || typeof risk.description !== "string") {
    errors.push("Risk must have a valid description");
  }
  if (!risk.organizationId || typeof risk.organizationId !== "string") {
    errors.push("Risk must have a valid organizationId");
  }
  // Date validation - accept both Date objects and valid date strings
  if (!risk.createdAt || !isValidDate(risk.createdAt)) {
    errors.push("Risk must have a valid createdAt date (Date object or valid date string)");
  }
  if (!risk.updatedAt || !isValidDate(risk.updatedAt)) {
    errors.push("Risk must have a valid updatedAt date (Date object or valid date string)");
  }
  // Likelihood and impact validation (1-5 range)
  if (
    typeof risk.inherentLikelihood !== "number" ||
    risk.inherentLikelihood < 1 ||
    risk.inherentLikelihood > 5
  ) {
    errors.push("Risk inherentLikelihood must be a number between 1 and 5");
  }
  if (
    typeof risk.inherentImpact !== "number" ||
    risk.inherentImpact < 1 ||
    risk.inherentImpact > 5
  ) {
    errors.push("Risk inherentImpact must be a number between 1 and 5");
  }
  if (typeof risk.likelihood !== "number" || risk.likelihood < 1 || risk.likelihood > 5) {
    errors.push("Risk likelihood must be a number between 1 and 5");
  }
  if (typeof risk.impact !== "number" || risk.impact < 1 || risk.impact > 5) {
    errors.push("Risk impact must be a number between 1 and 5");
  }
  // Severity validation
  if (!Object.values(RiskSeverity).includes(risk.inherentSeverity)) {
    errors.push("Risk must have a valid inherentSeverity");
  }
  if (!Object.values(RiskSeverity).includes(risk.severity)) {
    errors.push("Risk must have a valid severity");
  }
  // Status validation
  if (!Object.values(RiskStatus).includes(risk.status)) {
    errors.push("Risk must have a valid status");
  }
  return {
    isValid: errors.length === 0,
    errors,
    validatedData: errors.length === 0 ? (risk as Risk) : undefined,
  };
};
/**
 * Sanitizes risk data by removing potentially dangerous content
 */
export const sanitizeRiskData = (risk: Record<string, unknown>): Record<string, unknown> => {
  if (!risk || typeof risk !== "object") {
    return risk;
  }
  const sanitized = { ...risk };
  // Sanitize string fields to remove potentially dangerous content
  if (typeof sanitized.title === "string") {
    sanitized.title = sanitized.title.replace(
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      ""
    );
  }
  if (typeof sanitized.description === "string") {
    sanitized.description = sanitized.description.replace(
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      ""
    );
  }
  if (typeof sanitized.currentControls === "string") {
    sanitized.currentControls = sanitized.currentControls.replace(
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      ""
    );
  }
  if (typeof sanitized.mitigationApproach === "string") {
    sanitized.mitigationApproach = sanitized.mitigationApproach.replace(
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      ""
    );
  }
  return sanitized;
};
/**
 * Normalizes risk data by trimming strings and converting types
 */
export const normalizeRiskData = (risk: Record<string, unknown>): Record<string, unknown> => {
  if (!risk || typeof risk !== "object") {
    return risk;
  }
  const normalized = { ...risk };
  // Trim string fields
  if (typeof normalized.title === "string") {
    normalized.title = normalized.title.trim();
  }
  if (typeof normalized.description === "string") {
    normalized.description = normalized.description.trim();
  }
  if (typeof normalized.category === "string") {
    normalized.category = normalized.category.trim();
  }
  // Convert string numbers to actual numbers
  if (typeof normalized.likelihood === "string") {
    const parsed = parseInt(normalized.likelihood, 10);
    normalized.likelihood = isNaN(parsed) ? undefined : parsed;
  }
  if (typeof normalized.impact === "string") {
    const parsed = parseInt(normalized.impact, 10);
    normalized.impact = isNaN(parsed) ? undefined : parsed;
  }
  if (typeof normalized.inherentLikelihood === "string") {
    const parsed = parseInt(normalized.inherentLikelihood, 10);
    normalized.inherentLikelihood = isNaN(parsed) ? undefined : parsed;
  }
  if (typeof normalized.inherentImpact === "string") {
    const parsed = parseInt(normalized.inherentImpact, 10);
    normalized.inherentImpact = isNaN(parsed) ? undefined : parsed;
  }
  return normalized;
};
/**
 * Validates control measures array
 */
export const validateControlMeasures = (
  controlMeasures: unknown[]
): ValidationResult<ControlMeasure[]> => {
  const errors: string[] = [];
  const validatedData: ControlMeasure[] = [];
  if (!Array.isArray(controlMeasures)) {
    return {
      isValid: false,
      errors: ["Control measures must be an array"],
    };
  }
  if (controlMeasures.length === 0) {
    return {
      isValid: true,
      errors: [],
      validatedData: [],
    };
  }
  controlMeasures.forEach((measure, index) => {
    const itemErrors: string[] = [];
    if (!measure || typeof measure !== "object") {
      const error = `Control measure at index ${index} must be an object`;
      errors.push(error);
      itemErrors.push(error);
      return;
    }
    if (!measure.id || typeof measure.id !== "string") {
      const error = `Control measure at index ${index} must have a valid id`;
      errors.push(error);
      itemErrors.push(error);
    }
    if (!measure.riskId || typeof measure.riskId !== "string") {
      const error = `Control measure at index ${index} must have a valid riskId`;
      errors.push(error);
      itemErrors.push(error);
    }
    if (!measure.description || typeof measure.description !== "string") {
      const error = `Control measure at index ${index} must have a valid description`;
      errors.push(error);
      itemErrors.push(error);
    }
    if (measure.effectiveness && !isValidEffectiveness(measure.effectiveness)) {
      const error = `Control measure at index ${index} has invalid effectiveness value`;
      errors.push(error);
      itemErrors.push(error);
    }
    if (typeof measure.implemented !== "boolean") {
      const error = `Control measure at index ${index} must have a boolean implemented value`;
      errors.push(error);
      itemErrors.push(error);
    }
    if (!measure.organizationId || typeof measure.organizationId !== "string") {
      const error = `Control measure at index ${index} must have a valid organizationId`;
      errors.push(error);
      itemErrors.push(error);
    }
    // Date validation - accept both Date objects and valid date strings
    if (measure.createdAt && !isValidDate(measure.createdAt)) {
      const error = `Control measure at index ${index} must have a valid createdAt date (Date object or valid date string)`;
      errors.push(error);
      itemErrors.push(error);
    }
    if (measure.updatedAt && !isValidDate(measure.updatedAt)) {
      const error = `Control measure at index ${index} must have a valid updatedAt date (Date object or valid date string)`;
      errors.push(error);
      itemErrors.push(error);
    }
    // Only add to validatedData if this specific item has no errors
    if (itemErrors.length === 0) {
      validatedData.push(measure as ControlMeasure);
    }
  });
  return {
    isValid: errors.length === 0,
    errors,
    validatedData,
  };
};
/**
 * Validates mitigation actions array
 */
export const validateMitigationActions = (
  mitigationActions: unknown[]
): ValidationResult<MitigationAction[]> => {
  const errors: string[] = [];
  const validatedData: MitigationAction[] = [];
  if (!Array.isArray(mitigationActions)) {
    return {
      isValid: false,
      errors: ["Mitigation actions must be an array"],
    };
  }
  if (mitigationActions.length === 0) {
    return {
      isValid: true,
      errors: [],
      validatedData: [],
    };
  }
  mitigationActions.forEach((action, index) => {
    const itemErrors: string[] = [];
    if (!action || typeof action !== "object") {
      const error = `Mitigation action at index ${index} must be an object`;
      errors.push(error);
      itemErrors.push(error);
      return;
    }
    if (!action.id || typeof action.id !== "string") {
      const error = `Mitigation action at index ${index} must have a valid id`;
      errors.push(error);
      itemErrors.push(error);
    }
    if (!action.riskId || typeof action.riskId !== "string") {
      const error = `Mitigation action at index ${index} must have a valid riskId`;
      errors.push(error);
      itemErrors.push(error);
    }
    if (!action.description || typeof action.description !== "string") {
      const error = `Mitigation action at index ${index} must have a valid description`;
      errors.push(error);
      itemErrors.push(error);
    }
    if (typeof action.completed !== "boolean") {
      const error = `Mitigation action at index ${index} must have a boolean completed value`;
      errors.push(error);
      itemErrors.push(error);
    }
    if (!action.organizationId || typeof action.organizationId !== "string") {
      const error = `Mitigation action at index ${index} must have a valid organizationId`;
      errors.push(error);
      itemErrors.push(error);
    }
    // Date validation - accept both Date objects and valid date strings
    if (action.createdAt && !isValidDate(action.createdAt)) {
      const error = `Mitigation action at index ${index} must have a valid createdAt date (Date object or valid date string)`;
      errors.push(error);
      itemErrors.push(error);
    }
    if (action.updatedAt && !isValidDate(action.updatedAt)) {
      const error = `Mitigation action at index ${index} must have a valid updatedAt date (Date object or valid date string)`;
      errors.push(error);
      itemErrors.push(error);
    }
    // Only add to validatedData if this specific item has no errors
    if (itemErrors.length === 0) {
      validatedData.push(action as MitigationAction);
    }
  });
  return {
    isValid: errors.length === 0,
    errors,
    validatedData,
  };
};
