/**
 * Utility functions for consistent date handling throughout the application
 * Provides standardized date operations with comprehensive error handling and type safety
 */

import { DateInput, DateUtilityOptions } from "./types/common";
import {
  ValidationError,
  safeExecute,
  UtilityResult,
  createSuccess,
} from "./errors/standardErrors";

/**
 * Date format options for formatting functions
 */
export type DateFormat = "short" | "long" | "default";

/**
 * Extended date utility options with format-specific settings
 */
export interface DateFormatOptions extends DateUtilityOptions {
  /** Date format style */
  format?: DateFormat;
  /** Fallback value for invalid dates */
  fallback?: string;
}

/**
 * Formats a date to a more readable format with comprehensive error handling
 *
 * @param dateInput - Date to format (Date object, string, or null/undefined)
 * @param options - Formatting options including format style and locale
 * @returns Formatted date string or fallback value for invalid dates
 *
 * @example
 * ```typescript
 * formatDate(new Date('2024-01-15')) // Returns "Jan 15, 2024"
 * formatDate('2024-01-15', { format: 'short' }) // Returns "1/15/24"
 * formatDate(null, { fallback: 'No date' }) // Returns "No date"
 * ```
 *
 * @throws {ValidationError} When date format is invalid and throwOnError is true
 */

export const formatDate = (
  dateInput: DateInput,
  formatOrOptions?: DateFormat | DateFormatOptions
): string => {
  // Handle backward compatibility - if second parameter is a string, treat it as format
  let options: DateFormatOptions;
  if (typeof formatOrOptions === "string") {
    options = { format: formatOrOptions };
  } else {
    options = formatOrOptions ?? {};
  }

  const {
    format = "default",
    fallback = "Invalid Date",
    locale = "en-US",
    throwOnError = false,
    context = {},
  } = options;

  if (!dateInput) {
    return fallback;
  }

  const result = safeExecute(
    () => {
      const date = dateInput instanceof Date ? dateInput : new Date(dateInput);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        throw new ValidationError(`Invalid date value: ${dateInput}`, {
          input: dateInput,
          operation: "formatDate",
          ...context,
        });
      }

      const formatOptions: Intl.DateTimeFormatOptions = {
        timeZone: "UTC",
      };

      switch (format) {
        case "short":
          formatOptions.year = "2-digit";
          formatOptions.month = "numeric";
          formatOptions.day = "numeric";
          break;
        case "long":
          formatOptions.year = "numeric";
          formatOptions.month = "long";
          formatOptions.day = "numeric";
          break;
        default:
          formatOptions.year = "numeric";
          formatOptions.month = "short";
          formatOptions.day = "numeric";
          break;
      }

      return date.toLocaleDateString(locale, formatOptions);
    },
    "Failed to format date",
    { input: dateInput, format, ...context }
  );

  if (result.success) {
    return result.data;
  }

  if (throwOnError) {
    throw result.error;
  }

  return fallback;
};

/**
 * Extended options for date-time formatting
 */
export interface DateTimeFormatOptions extends DateUtilityOptions {
  /** Whether to use 24-hour format */
  use24Hour?: boolean;
  /** Fallback value for invalid dates */
  fallback?: string;
}

/**
 * Formats a date string to include both date and time with comprehensive error handling
 *
 * @param dateInput - Date to format (Date object, string, or null/undefined)
 * @param options - Formatting options including 24-hour format and locale
 * @returns Formatted date and time string or fallback value for invalid dates
 *
 * @example
 * ```typescript
 * formatDateTime(new Date('2024-01-15T14:30:00Z')) // Returns "Jan 15, 2024 at 2:30 PM"
 * formatDateTime('2024-01-15T14:30:00Z', { use24Hour: true }) // Returns "Jan 15, 2024 at 14:30"
 * formatDateTime(null, { fallback: 'No date' }) // Returns "No date"
 * ```
 *
 * @throws {ValidationError} When date format is invalid and throwOnError is true
 */

export const formatDateTime = (
  dateInput: DateInput,
  use24HourOrOptions?: boolean | DateTimeFormatOptions
): string => {
  // Handle backward compatibility - if second parameter is a boolean, treat it as use24Hour
  let options: DateTimeFormatOptions;
  if (typeof use24HourOrOptions === "boolean") {
    options = { use24Hour: use24HourOrOptions };
  } else {
    options = use24HourOrOptions ?? {};
  }

  const {
    use24Hour = false,
    fallback = "Invalid Date",
    locale = "en-US",
    throwOnError = false,
    context = {},
  } = options;

  if (!dateInput) {
    return fallback;
  }

  const result = safeExecute(
    () => {
      const date = dateInput instanceof Date ? dateInput : new Date(dateInput);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        throw new ValidationError(`Invalid date value: ${dateInput}`, {
          input: dateInput,
          operation: "formatDateTime",
          ...context,
        });
      }

      // Format date part separately from time part for consistent output
      const dateOptions: Intl.DateTimeFormatOptions = {
        year: "numeric",
        month: "short",
        day: "numeric",
        timeZone: "UTC",
      };

      const timeOptions: Intl.DateTimeFormatOptions = {
        hour: use24Hour ? "2-digit" : "numeric",
        minute: "2-digit",
        hour12: !use24Hour,
        timeZone: "UTC",
      };

      const dateFormatter = new Intl.DateTimeFormat(locale, dateOptions);
      const timeFormatter = new Intl.DateTimeFormat(locale, timeOptions);

      const datePart = dateFormatter.format(date);
      const timePart = timeFormatter.format(date);

      return `${datePart} at ${timePart}`;
    },
    "Failed to format date and time",
    { input: dateInput, use24Hour, ...context }
  );

  if (result.success) {
    return result.data;
  }

  if (throwOnError) {
    throw result.error;
  }

  return fallback;
};

/**
 * Converts a date to ISO string format for database storage with comprehensive error handling
 *
 * @param dateInput - Date to convert (Date object, string, or null/undefined)
 * @param options - Conversion options
 * @returns ISO string or undefined if invalid
 *
 * @example
 * ```typescript
 * toISOString(new Date('2024-01-15')) // Returns "2024-01-15T00:00:00.000Z"
 * toISOString('2024-01-15') // Returns "2024-01-15T00:00:00.000Z"
 * toISOString(null) // Returns undefined
 * ```
 *
 * @throws {ValidationError} When date is invalid and throwOnError is true
 */

export const toISOString = (
  dateInput: DateInput,
  options: DateUtilityOptions = {
    // Implementation needed
  }
): string | undefined => {
  const { throwOnError = false, context = {} } = options;

  if (!dateInput) {
    return undefined;
  }

  const result = safeExecute(
    () => {
      const dateObj = typeof dateInput === "string" ? new Date(dateInput) : dateInput;

      // Check if date is valid
      if (isNaN(dateObj.getTime())) {
        throw new ValidationError(`Invalid date for ISO conversion: ${dateInput}`, {
          input: dateInput,
          operation: "toISOString",
          ...context,
        });
      }

      return dateObj.toISOString();
    },
    "Failed to convert date to ISO string",
    { input: dateInput, ...context }
  );

  if (result.success) {
    return result.data;
  }

  if (throwOnError) {
    throw result.error;
  }

  return undefined;
};

/**
 * Checks if a date is in the past with comprehensive error handling
 *
 * @param dateInput - Date to check (Date object, string, or null/undefined)
 * @param options - Validation options
 * @returns True if date is in the past, false otherwise or for invalid dates
 *
 * @example
 * ```typescript
 * isDateInPast(new Date('2023-01-01')) // Returns true
 * isDateInPast(new Date('2025-01-01')) // Returns false
 * isDateInPast(null) // Returns false
 * ```
 */

export const isDateInPast = (dateInput: DateInput, options: DateUtilityOptions = {}): boolean => {
  const { context = {} } = options;

  if (!dateInput) {
    return false;
  }

  const result = safeExecute(
    () => {
      const dateObj = typeof dateInput === "string" ? new Date(dateInput) : dateInput;
      const now = new Date();

      // Check if date is valid
      if (isNaN(dateObj.getTime())) {
        throw new ValidationError(`Invalid date for past check: ${dateInput}`, {
          input: dateInput,
          operation: "isDateInPast",
          ...context,
        });
      }

      return dateObj < now;
    },
    "Failed to check if date is in past",
    { input: dateInput, ...context }
  );

  // For boolean functions, return false on error instead of throwing
  return result.success ? result.data : false;
};

/**
 * Options for relative time formatting
 */
export interface RelativeTimeOptions extends DateUtilityOptions {
  /** Fallback value for invalid dates */
  fallback?: string;
  /** Reference date for comparison (defaults to current time) */
  referenceDate?: Date;
}

/**
 * Gets relative time description (e.g., "2 days ago", "in 3 days") with comprehensive error handling
 *
 * @param dateInput - Date to get relative time for (Date object, string, or null/undefined)
 * @param options - Formatting options including fallback and reference date
 * @returns Relative time string or fallback value for invalid dates
 *
 * @example
 * ```typescript
 * getRelativeTime(new Date('2024-01-13')) // Returns "2 days ago" (if today is 2024-01-15)
 * getRelativeTime(new Date('2024-01-17')) // Returns "in 2 days"
 * getRelativeTime(null, { fallback: 'No date' }) // Returns "No date"
 * ```
 */

export const getRelativeTime = (
  dateInput: DateInput,
  options: RelativeTimeOptions = {
    // Implementation needed
  }
): string => {
  const { fallback = "N/A", referenceDate = new Date(), context = {} } = options;

  if (!dateInput) {
    return fallback;
  }

  const result = safeExecute(
    () => {
      const dateObj = typeof dateInput === "string" ? new Date(dateInput) : dateInput;

      // Check if date is valid
      if (isNaN(dateObj.getTime())) {
        throw new ValidationError(`Invalid date for relative time: ${dateInput}`, {
          input: dateInput,
          operation: "getRelativeTime",
          ...context,
        });
      }

      const diffMs = dateObj.getTime() - referenceDate.getTime();
      const absDiffMs = Math.abs(diffMs);

      const minutes = Math.floor(absDiffMs / (1000 * 60));
      const hours = Math.floor(absDiffMs / (1000 * 60 * 60));
      const days = Math.floor(absDiffMs / (1000 * 60 * 60 * 24));
      const weeks = Math.floor(absDiffMs / (1000 * 60 * 60 * 24 * 7));
      const months = Math.floor(absDiffMs / (1000 * 60 * 60 * 24 * 30));

      const isFuture = diffMs > 0;

      // Very recent (less than 1 minute)
      if (minutes < 1) {
        return "just now";
      }

      // Hours
      if (hours < 24) {
        const hourCount = Math.floor(hours);
        if (hourCount === 1) {
          return isFuture ? "in 1 hour" : "1 hour ago";
        } else if (hourCount > 0) {
          return isFuture ? `in ${hourCount} hours` : `${hourCount} hours ago`;
        }
      }

      // Days - tests expect specific format
      if (days < 7) {
        if (days === 0) {
          return "Today";
        } else if (days === 1) {
          return isFuture ? "in 1 day" : "1 day ago";
        } else {
          return isFuture ? `in ${days} days` : `${days} days ago`;
        }
      }

      // Weeks
      if (weeks < 4) {
        return isFuture
          ? `in ${weeks} week${weeks === 1 ? "" : "s"}`
          : `${weeks} week${weeks === 1 ? "" : "s"} ago`;
      }

      // Months
      return isFuture
        ? `in ${months} month${months === 1 ? "" : "s"}`
        : `${months} month${months === 1 ? "" : "s"} ago`;
    },
    "Failed to calculate relative time",
    { input: dateInput, ...context }
  );

  return result.success ? result.data : fallback;
};

/**
 * Formats relative time with more detailed options (alias for getRelativeTime)
 *
 * @param dateInput - Date to format (Date object, string, or null/undefined)
 * @param options - Formatting options
 * @returns Relative time string
 *
 * @example
 * ```typescript
 * formatRelativeTime(new Date('2024-01-13')) // Returns "2 days ago"
 * formatRelativeTime(null) // Returns "N/A"
 * ```
 */

export const formatRelativeTime = (
  dateInput: DateInput,
  options: RelativeTimeOptions = {
    // Implementation needed
  }
): string => {
  return getRelativeTime(dateInput, options);
};

/**
 * Checks if a date is overdue (past due date) with comprehensive error handling
 *
 * @param dateInput - Date to check (Date object, string, or null/undefined)
 * @param options - Validation options
 * @returns True if date is overdue, false otherwise or for invalid dates
 *
 * @example
 * ```typescript
 * isOverdue(new Date('2023-01-01')) // Returns true (if today is after 2023-01-01)
 * isOverdue(new Date('2025-01-01')) // Returns false
 * isOverdue(null) // Returns false
 * ```
 */

export const isOverdue = (dateInput: DateInput, options: DateUtilityOptions = {}): boolean => {
  const { context = {} } = options;

  if (!dateInput) {
    return false;
  }

  const result = safeExecute(
    () => {
      const dateObj = typeof dateInput === "string" ? new Date(dateInput) : dateInput;

      // Check if date is valid
      if (isNaN(dateObj.getTime())) {
        throw new ValidationError(`Invalid date for overdue check: ${dateInput}`, {
          input: dateInput,
          operation: "isOverdue",
          ...context,
        });
      }

      const now = new Date();
      // Set time to start of day for accurate comparison
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const due = new Date(dateObj.getFullYear(), dateObj.getMonth(), dateObj.getDate());

      return due < today;
    },
    "Failed to check if date is overdue",
    { input: dateInput, ...context }
  );

  // For boolean functions, return false on error instead of throwing
  return result.success ? result.data : false;
};

/**
 * Gets the number of days until a due date with comprehensive error handling
 *
 * @param dateInput - Date to check (Date object, string, or null/undefined)
 * @param options - Calculation options
 * @returns Number of days until due (negative if overdue), 0 for invalid dates
 *
 * @example
 * ```typescript
 * getDaysUntilDue(new Date('2024-01-17')) // Returns 2 (if today is 2024-01-15)
 * getDaysUntilDue(new Date('2024-01-13')) // Returns -2 (overdue)
 * getDaysUntilDue(null) // Returns 0
 * ```
 */

export const getDaysUntilDue = (dateInput: DateInput, options: DateUtilityOptions = {}): number => {
  const { context = {} } = options;

  if (!dateInput) {
    return 0;
  }

  const result = safeExecute(
    () => {
      const dateObj = typeof dateInput === "string" ? new Date(dateInput) : dateInput;

      // Check if date is valid
      if (isNaN(dateObj.getTime())) {
        throw new ValidationError(`Invalid date for days until due calculation: ${dateInput}`, {
          input: dateInput,
          operation: "getDaysUntilDue",
          ...context,
        });
      }

      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const due = new Date(dateObj.getFullYear(), dateObj.getMonth(), dateObj.getDate());

      const diffMs = due.getTime() - today.getTime();
      return Math.ceil(diffMs / (1000 * 60 * 60 * 24));
    },
    "Failed to calculate days until due",
    { input: dateInput, ...context }
  );

  // For numeric functions, return 0 on error instead of throwing
  return result.success ? result.data : 0;
};

/**
 * Gets a date range between two dates with comprehensive error handling
 *
 * @param startDate - Start date (Date object or string)
 * @param endDate - End date (Date object or string)
 * @param options - Range generation options
 * @returns Array of dates in the range, empty array for invalid inputs
 *
 * @example
 * ```typescript
 * getDateRange('2024-01-15', '2024-01-17') // Returns [Date(2024-01-15), Date(2024-01-16), Date(2024-01-17)]
 * getDateRange(new Date('2024-01-15'), new Date('2024-01-15')) // Returns [Date(2024-01-15)]
 * getDateRange('invalid', '2024-01-17') // Returns []
 * ```
 */

export const getDateRange = (
  startDate: Date | string,
  endDate: Date | string,
  options: DateUtilityOptions = {
    // Implementation needed
  }
): Date[] => {
  const { context = {} } = options;

  const result = safeExecute(
    () => {
      const start = typeof startDate === "string" ? new Date(startDate) : startDate;
      const end = typeof endDate === "string" ? new Date(endDate) : endDate;

      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        throw new ValidationError("Invalid start or end date for range generation", {
          startDate,
          endDate,
          operation: "getDateRange",
          ...context,
        });
      }

      const dates: Date[] = [];
      const current = new Date(start);

      while (current <= end) {
        dates.push(new Date(current));
        current.setDate(current.getDate() + 1);
      }

      return dates;
    },
    "Failed to generate date range",
    { startDate, endDate, ...context }
  );

  return result.success ? result.data : [];
};

/**
 * Parses a date string into a Date object with comprehensive error handling
 *
 * @param dateInput - Date string to parse (string, null, or undefined)
 * @param options - Parsing options
 * @returns Date object or null if invalid
 *
 * @example
 * ```typescript
 * parseDate('2024-01-15') // Returns Date(2024-01-15)
 * parseDate('invalid-date') // Returns null
 * parseDate(null) // Returns null
 * ```
 */

export const parseDate = (
  dateInput: string | null | undefined,
  options: DateUtilityOptions = {
    // Implementation needed
  }
): Date | null => {
  const { context = {} } = options;

  if (!dateInput) {
    return null;
  }

  const result = safeExecute(
    () => {
      const date = new Date(dateInput);

      if (isNaN(date.getTime())) {
        throw new ValidationError(`Invalid date string for parsing: ${dateInput}`, {
          input: dateInput,
          operation: "parseDate",
          ...context,
        });
      }

      return date;
    },
    "Failed to parse date string",
    { input: dateInput, ...context }
  );

  return result.success ? result.data : null;
};

/**
 * Checks if a date string or Date object is valid with comprehensive error handling
 *
 * @param dateInput - Date to validate (Date object, string, or null/undefined)
 * @param options - Validation options
 * @returns True if date is valid, false otherwise
 *
 * @example
 * ```typescript
 * isValidDate(new Date('2024-01-15')) // Returns true
 * isValidDate('2024-01-15') // Returns true
 * isValidDate('invalid-date') // Returns false
 * isValidDate(null) // Returns false
 * ```
 */

export const isValidDate = (dateInput: DateInput, options: DateUtilityOptions = {}): boolean => {
  const { context = {} } = options;

  if (!dateInput) {
    return false;
  }

  const result = safeExecute(
    () => {
      const dateObj = typeof dateInput === "string" ? new Date(dateInput) : dateInput;

      if (isNaN(dateObj.getTime())) {
        throw new ValidationError(`Invalid date for validation: ${dateInput}`, {
          input: dateInput,
          operation: "isValidDate",
          ...context,
        });
      }

      return true;
    },
    "Failed to validate date",
    { input: dateInput, ...context }
  );

  return result.success ? result.data : false;
};

/**
 * Input type for HTML date inputs
 */
export type HTMLDateInputType = "date" | "datetime-local";

/**
 * Options for HTML input formatting
 */
export interface InputFormatOptions extends DateUtilityOptions {
  /** Type of HTML input element */
  inputType?: HTMLDateInputType;
  /** Fallback value for invalid dates */
  fallback?: string;
}

/**
 * Formats a date for HTML input elements with comprehensive error handling
 *
 * @param dateInput - Date to format (Date object, string, or null/undefined)
 * @param options - Formatting options including input type
 * @returns Date string in appropriate format for HTML inputs or fallback value
 *
 * @example
 * ```typescript
 * formatDateForInput(new Date('2024-01-15')) // Returns "2024-01-15"
 * formatDateForInput('2024-01-15T14:30:00Z', { inputType: 'datetime-local' }) // Returns "2024-01-15T14:30"
 * formatDateForInput(null, { fallback: '' }) // Returns ""
 * ```
 */

export const formatDateForInput = (
  dateInput: DateInput,
  inputTypeOrOptions?: HTMLDateInputType | InputFormatOptions
): string => {
  // Handle backward compatibility - if second parameter is a string, treat it as inputType
  let options: InputFormatOptions;
  if (typeof inputTypeOrOptions === "string") {
    options = { inputType: inputTypeOrOptions };
  } else {
    options = inputTypeOrOptions ?? {};
  }

  const { inputType = "date", fallback = "", context = {} } = options;

  if (!dateInput) {
    return fallback;
  }

  const result = safeExecute(
    () => {
      const dateObj = typeof dateInput === "string" ? new Date(dateInput) : dateInput;

      if (isNaN(dateObj.getTime())) {
        throw new ValidationError(`Invalid date for input formatting: ${dateInput}`, {
          input: dateInput,
          inputType,
          operation: "formatDateForInput",
          ...context,
        });
      }

      if (inputType === "datetime-local") {
        // Return YYYY-MM-DDTHH:MM format for datetime-local inputs (use UTC)
        const year = dateObj.getUTCFullYear();
        const month = String(dateObj.getUTCMonth() + 1).padStart(2, "0");
        const day = String(dateObj.getUTCDate()).padStart(2, "0");
        const hours = String(dateObj.getUTCHours()).padStart(2, "0");
        const minutes = String(dateObj.getUTCMinutes()).padStart(2, "0");
        return `${year}-${month}-${day}T${hours}:${minutes}`;
      } else {
        // Return YYYY-MM-DD format for date inputs
        return dateObj.toISOString().split("T")[0];
      }
    },
    "Failed to format date for input",
    { input: dateInput, inputType, ...context }
  );

  return result.success ? result.data : fallback;
};

/**
 * Gets the start of day for a given date with comprehensive error handling
 *
 * @param dateInput - Date to get start of day for (Date object, string, or null/undefined)
 * @param options - Processing options
 * @returns Date object set to start of day or null if invalid
 *
 * @example
 * ```typescript
 * getStartOfDay(new Date('2024-01-15T14:30:00Z')) // Returns Date(2024-01-15T00:00:00)
 * getStartOfDay('2024-01-15') // Returns Date(2024-01-15T00:00:00)
 * getStartOfDay(null) // Returns null
 * ```
 */

export const getStartOfDay = (
  dateInput: DateInput,
  options: DateUtilityOptions = {
    // Implementation needed
  }
): Date | null => {
  const { context = {} } = options;

  if (!dateInput) {
    return null;
  }

  const result = safeExecute(
    () => {
      const dateObj = typeof dateInput === "string" ? new Date(dateInput) : dateInput;

      if (isNaN(dateObj.getTime())) {
        throw new ValidationError(`Invalid date for start of day: ${dateInput}`, {
          input: dateInput,
          operation: "getStartOfDay",
          ...context,
        });
      }

      return new Date(dateObj.getFullYear(), dateObj.getMonth(), dateObj.getDate());
    },
    "Failed to get start of day",
    { input: dateInput, ...context }
  );

  return result.success ? result.data : null;
};

/**
 * Gets the end of day for a given date with comprehensive error handling
 *
 * @param dateInput - Date to get end of day for (Date object, string, or null/undefined)
 * @param options - Processing options
 * @returns Date object set to end of day or null if invalid
 *
 * @example
 * ```typescript
 * getEndOfDay(new Date('2024-01-15T14:30:00Z')) // Returns Date(2024-01-15T23:59:59.999)
 * getEndOfDay('2024-01-15') // Returns Date(2024-01-15T23:59:59.999)
 * getEndOfDay(null) // Returns null
 * ```
 */

export const getEndOfDay = (
  dateInput: DateInput,
  options: DateUtilityOptions = {
    // Implementation needed
  }
): Date | null => {
  const { context = {} } = options;

  if (!dateInput) {
    return null;
  }

  const result = safeExecute(
    () => {
      const dateObj = typeof dateInput === "string" ? new Date(dateInput) : dateInput;

      if (isNaN(dateObj.getTime())) {
        throw new ValidationError(`Invalid date for end of day: ${dateInput}`, {
          input: dateInput,
          operation: "getEndOfDay",
          ...context,
        });
      }

      return new Date(
        dateObj.getFullYear(),
        dateObj.getMonth(),
        dateObj.getDate(),
        23,
        59,
        59,
        999
      );
    },
    "Failed to get end of day",
    { input: dateInput, ...context }
  );

  return result.success ? result.data : null;
};

/**
 * Adds days to a date with comprehensive error handling
 *
 * @param dateInput - Base date (Date object, string, or null/undefined)
 * @param days - Number of days to add (can be negative to subtract)
 * @param options - Processing options
 * @returns New date with days added or null if invalid
 *
 * @example
 * ```typescript
 * addDays(new Date('2024-01-15'), 5) // Returns Date(2024-01-20)
 * addDays('2024-01-15', -3) // Returns Date(2024-01-12)
 * addDays(null, 5) // Returns null
 * ```
 */

export const addDays = (
  dateInput: DateInput,
  days: number,
  options: DateUtilityOptions = {
    // Implementation needed
  }
): Date | null => {
  const { context = {} } = options;

  if (!dateInput) {
    return null;
  }

  const result = safeExecute(
    () => {
      const dateObj = typeof dateInput === "string" ? new Date(dateInput) : dateInput;

      if (isNaN(dateObj.getTime())) {
        throw new ValidationError(`Invalid date for adding days: ${dateInput}`, {
          input: dateInput,
          days,
          operation: "addDays",
          ...context,
        });
      }

      const resultDate = new Date(dateObj);
      resultDate.setDate(resultDate.getDate() + days);
      return resultDate;
    },
    "Failed to add days to date",
    { input: dateInput, days, ...context }
  );

  return result.success ? result.data : null;
};

/**
 * Subtracts days from a date with comprehensive error handling (alias for addDays with negative value)
 *
 * @param dateInput - Base date (Date object, string, or null/undefined)
 * @param days - Number of days to subtract
 * @param options - Processing options
 * @returns New date with days subtracted or null if invalid
 *
 * @example
 * ```typescript
 * subtractDays(new Date('2024-01-15'), 5) // Returns Date(2024-01-10)
 * subtractDays('2024-01-15', 3) // Returns Date(2024-01-12)
 * subtractDays(null, 5) // Returns null
 * ```
 */

export const subtractDays = (
  dateInput: DateInput,
  days: number,
  options: DateUtilityOptions = {
    // Implementation needed
  }
): Date | null => {
  return addDays(dateInput, -days, options);
};

/**
 * Checks if two dates are the same day with comprehensive error handling
 *
 * @param date1 - First date (Date object, string, or null/undefined)
 * @param date2 - Second date (Date object, string, or null/undefined)
 * @param options - Comparison options
 * @returns True if dates are the same day, false otherwise or for invalid dates
 *
 * @example
 * ```typescript
 * isSameDay(new Date('2024-01-15T10:30:00Z'), new Date('2024-01-15T14:45:00Z')) // Returns true
 * isSameDay('2024-01-15', '2024-01-16') // Returns false
 * isSameDay(null, new Date()) // Returns false
 * ```
 */

export const isSameDay = (
  date1: DateInput,
  date2: DateInput,
  options: DateUtilityOptions = {
    // Implementation needed
  }
): boolean => {
  const { context = {} } = options;

  if (!date1 || !date2) {
    return false;
  }

  const result = safeExecute(
    () => {
      const dateObj1 = typeof date1 === "string" ? new Date(date1) : date1;
      const dateObj2 = typeof date2 === "string" ? new Date(date2) : date2;

      if (isNaN(dateObj1.getTime()) || isNaN(dateObj2.getTime())) {
        throw new ValidationError("Invalid dates for same day comparison", {
          date1,
          date2,
          operation: "isSameDay",
          ...context,
        });
      }

      return (
        dateObj1.getFullYear() === dateObj2.getFullYear() &&
        dateObj1.getMonth() === dateObj2.getMonth() &&
        dateObj1.getDate() === dateObj2.getDate()
      );
    },
    "Failed to compare dates for same day",
    { date1, date2, ...context }
  );

  return result.success ? result.data : false;
};

/**
 * Checks if a date is within a given range with comprehensive error handling
 *
 * @param dateInput - Date to check (Date object, string, or null/undefined)
 * @param startDate - Range start date (Date object, string, or null/undefined)
 * @param endDate - Range end date (Date object, string, or null/undefined)
 * @param options - Comparison options
 * @returns True if date is within range (inclusive), false otherwise or for invalid dates
 *
 * @example
 * ```typescript
 * isWithinRange(new Date('2024-01-15'), new Date('2024-01-10'), new Date('2024-01-20')) // Returns true
 * isWithinRange('2024-01-25', '2024-01-10', '2024-01-20') // Returns false
 * isWithinRange(null, new Date(), new Date()) // Returns false
 * ```
 */

export const isWithinRange = (
  dateInput: DateInput,
  startDate: DateInput,
  endDate: DateInput,
  options: DateUtilityOptions = {
    // Implementation needed
  }
): boolean => {
  const { context = {} } = options;

  if (!dateInput || !startDate || !endDate) {
    return false;
  }

  const result = safeExecute(
    () => {
      const dateObj = typeof dateInput === "string" ? new Date(dateInput) : dateInput;
      const startObj = typeof startDate === "string" ? new Date(startDate) : startDate;
      const endObj = typeof endDate === "string" ? new Date(endDate) : endDate;

      if (isNaN(dateObj.getTime()) || isNaN(startObj.getTime()) || isNaN(endObj.getTime())) {
        throw new ValidationError("Invalid dates for range comparison", {
          date: dateInput,
          startDate,
          endDate,
          operation: "isWithinRange",
          ...context,
        });
      }

      return dateObj >= startObj && dateObj <= endObj;
    },
    "Failed to check if date is within range",
    { date: dateInput, startDate, endDate, ...context }
  );

  return result.success ? result.data : false;
};
