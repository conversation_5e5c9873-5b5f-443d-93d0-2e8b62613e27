/**
 * Performance Observer Manager
 * Manages PerformanceObserver instances to prevent memory leaks
 */
type CleanupFunction = () => void;
/**
 * Manager class for handling multiple PerformanceObserver instances
 * Ensures proper cleanup to prevent memory leaks
 */
export class PerformanceObserverManager {
  private cleanupFunctions: CleanupFunction[] = [];
  private isActive = true;
  /**
   * Add a cleanup function to be called when the manager is destroyed
   */
  addCleanup(cleanup: CleanupFunction): void {
    if (this.isActive) {
      this.cleanupFunctions.push(cleanup);
    } else {
      // If manager is already destroyed, cleanup immediately
      cleanup();
    }
  }
  /**
   * Start bundle performance tracking
   */
  startBundleTracking(): void {
    if (!this.isActive || import.meta.env.MODE !== "development") return;
    const observer = new PerformanceObserver(list => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === "resource" && entry.name.includes(".js")) {
          const resource = entry as PerformanceResourceTiming;
          const name = resource.name.split("/").pop() ?? "unknown";
          const loadTime = resource.responseEnd - resource.requestStart;
          if (loadTime > 1000) {
            // Warn for chunks taking > 1s
          }
        }
      }
    });
    try {
      observer.observe({ entryTypes: ["resource"] });
      this.addCleanup(() => observer.disconnect());
    } catch (error) {
      // Error caught and handled
    }
  }
  /**
   * Start chunk caching monitoring
   */
  startCacheMonitoring(): void {
    if (!this.isActive || import.meta.env.MODE !== "development") return;
    const observer = new PerformanceObserver(list => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === "resource" && entry.name.includes(".js")) {
          const resource = entry as PerformanceResourceTiming;
          const name = resource.name.split("/").pop() ?? "unknown";
          // Check if resource was served from cache
          if (resource.transferSize === 0 && resource.decodedBodySize > 0) {
            // Condition handled
          } else if (resource.transferSize > 0) {
            const size = this.formatBytes(resource.transferSize);
          }
        }
      }
    });
    try {
      observer.observe({ entryTypes: ["resource"] });
      this.addCleanup(() => observer.disconnect());
    } catch (error) {
      // Error caught and handled
    }
  }
  /**
   * Start navigation performance monitoring
   */
  startNavigationMonitoring(): void {
    if (!this.isActive || import.meta.env.MODE !== "development") return;
    const observer = new PerformanceObserver(list => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === "navigation") {
          const navigation = entry as PerformanceNavigationTiming;
          const loadTime = navigation.loadEventEnd - navigation.navigationStart;
          if (loadTime > 0) {
            // Condition handled
          }
        }
      }
    });
    try {
      observer.observe({ entryTypes: ["navigation"] });
      this.addCleanup(() => observer.disconnect());
    } catch (error) {
      // Error caught and handled
    }
  }
  /**
   * Start all performance monitoring
   */
  startAll(): void {
    this.startBundleTracking();
    this.startCacheMonitoring();
    this.startNavigationMonitoring();
  }
  /**
   * Cleanup all observers and prevent memory leaks
   */
  destroy(): void {
    if (!this.isActive) return;
    this.isActive = false;
    // Call all cleanup functions
    this.cleanupFunctions.forEach(cleanup => {
      try {
        cleanup();
      } catch (error) {
        // Error caught and handled
      }
    });
    // Clear the cleanup functions array
    this.cleanupFunctions = [];
  }
  /**
   * Check if the manager is still active
   */
  get active(): boolean {
    return this.isActive;
  }
  /**
   * Get the number of active observers
   */
  get observerCount(): number {
    return this.cleanupFunctions.length;
  }
  /**
   * Format bytes to human readable format
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }
}
/**
 * Global performance observer manager instance
 * Use this for application-wide performance monitoring
 */
let globalPerformanceManager: PerformanceObserverManager | null = null;
/**
 * Get or create the global performance manager
 */
export function getGlobalPerformanceManager(): PerformanceObserverManager {
  if (!globalPerformanceManager) {
    globalPerformanceManager = new PerformanceObserverManager();
  }
  return globalPerformanceManager;
}
/**
 * Destroy the global performance manager
 */
export function destroyGlobalPerformanceManager(): void {
  if (globalPerformanceManager) {
    globalPerformanceManager.destroy();
    globalPerformanceManager = null;
  }
}
/**
 * Initialize performance monitoring with automatic cleanup
 * Returns a cleanup function
 */
export function initializePerformanceMonitoring(): () => void {
  if (import.meta.env.MODE !== "development") {
    return () => {}; // No-op in production
  }
  const manager = getGlobalPerformanceManager();
  manager.startAll();
  // Setup automatic cleanup on page unload
  const handleBeforeUnload = () => {
    destroyGlobalPerformanceManager();
  };
  const handleVisibilityChange = () => {
    if (document.hidden) {
      destroyGlobalPerformanceManager();
    }
  };
  window.addEventListener("beforeunload", handleBeforeUnload);
  document.addEventListener("visibilitychange", handleVisibilityChange);
  // Return manual cleanup function
  return () => {
    window.removeEventListener("beforeunload", handleBeforeUnload);
    document.removeEventListener("visibilitychange", handleVisibilityChange);
    destroyGlobalPerformanceManager();
  };
}
export default {
  PerformanceObserverManager,
  getGlobalPerformanceManager,
  destroyGlobalPerformanceManager,
  initializePerformanceMonitoring,
};
