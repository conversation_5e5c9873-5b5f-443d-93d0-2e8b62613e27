/**
 * Component-level code splitting utilities
 * Breaks down large components into smaller, focused chunks for better performance
 */

import React, { Suspense, ComponentType } from 'react';
import { Skeleton } from '@/components/ui/skeleton';
import { createLazyComponent } from './component-lazy-loader';

/**
 * Split large page components into focused sub-components
 */

// ============================================================================
// REPORTS PAGE COMPONENTS - Split heavy reporting components
// ============================================================================

export const LazyReportsComponents = {
  // Overview tab with charts and metrics
  OverviewTab: createLazyComponent(
    () => import('@/components/reports/OverviewTab'),
    {
      fallback: () => (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
          </div>
          <Skeleton className="h-64 w-full" />
        </div>
      )
    }
  ),

  // Matrix visualization - heavy component
  MatrixTab: createLazyComponent(
    () => import('@/components/reports/MatrixTab'),
    {
      fallback: () => (
        <div className="space-y-4">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-96 w-full" />
        </div>
      )
    }
  ),

  // Historical data with complex charts
  HistoricalTab: createLazyComponent(
    () => import('@/components/reports/HistoricalTab'),
    {
      fallback: () => (
        <div className="space-y-4">
          <div className="flex gap-4">
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-32" />
          </div>
          <Skeleton className="h-80 w-full" />
        </div>
      )
    }
  ),

  // Advanced reports with complex filtering
  AdvancedReportsTab: createLazyComponent(
    () => import('@/components/reports/AdvancedReportsTab'),
    {
      fallback: () => (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-32 w-full" />
          </div>
          <Skeleton className="h-64 w-full" />
        </div>
      )
    }
  ),

  // Export functionality - heavy libraries
  ExportsTab: createLazyComponent(
    () => import('@/components/reports/ExportsTab'),
    {
      fallback: () => (
        <div className="space-y-4">
          <Skeleton className="h-8 w-48" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-24 w-full" />
          </div>
        </div>
      )
    }
  ),
};

