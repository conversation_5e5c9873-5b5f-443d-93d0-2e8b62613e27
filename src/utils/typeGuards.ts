
import { Risk, ControlMeasure, MitigationAction, RiskSeverity, RiskStatus, User, UserRole } from "@/types";
import { DbRisk, DbControlMeasure, DbMitigationAction, DbProfile, DbRiskCategory } from "@/types/db";

/**
 * Type guard for Risk objects
 */
export function isRisk(obj: unknown): obj is Risk {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'title' in obj &&
    'description' in obj &&
    'category' in obj &&
    'categoryId' in obj && // Added categoryId check
    'severity' in obj &&
    'status' in obj
  );
}

/**
 * Type guard for ControlMeasure objects
 */
export function isControlMeasure(obj: unknown): obj is ControlMeasure {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'riskId' in obj &&
    'description' in obj
  );
}

/**
 * Type guard for MitigationAction objects
 */
export function isMitigationAction(obj: unknown): obj is MitigationAction {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'riskId' in obj &&
    'description' in obj &&
    'completed' in obj
  );
}

/**
 * Type guard for User objects
 */
export function isUser(obj: unknown): obj is User {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'name' in obj &&
    'email' in obj &&
    'role' in obj
  );
}

/**
 * Type guard for RiskSeverity enum values
 */
export function isRiskSeverity(value: unknown): value is RiskSeverity {
  return (
    typeof value === 'string' &&
    Object.values(RiskSeverity).includes(value as RiskSeverity)
  );
}

/**
 * Type guard for RiskStatus enum values
 */
export function isRiskStatus(value: unknown): value is RiskStatus {
  return (
    typeof value === 'string' &&
    Object.values(RiskStatus).includes(value as RiskStatus)
  );
}

/**
 * Type guard for UserRole enum values
 */
export function isUserRole(value: unknown): value is UserRole {
  return (
    typeof value === 'string' &&
    Object.values(UserRole).includes(value as UserRole)
  );
}

/**
 * Type guard for database Risk objects
 */
export function isDbRisk(obj: unknown): obj is DbRisk {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'title' in obj &&
    'description' in obj &&
    'severity' in obj &&
    'status' in obj &&
    'likelihood' in obj &&
    'impact' in obj
  );
}

/**
 * Type guard for database ControlMeasure objects
 */
export function isDbControlMeasure(obj: unknown): obj is DbControlMeasure {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'risk_id' in obj &&
    'description' in obj &&
    'implemented' in obj
  );
}

/**
 * Type guard for database MitigationAction objects
 */
export function isDbMitigationAction(obj: unknown): obj is DbMitigationAction {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'risk_id' in obj &&
    'description' in obj &&
    'completed' in obj
  );
}

/**
 * Type guard for database Profile objects
 */
export function isDbProfile(obj: unknown): obj is DbProfile {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'name' in obj &&
    'email' in obj &&
    'role' in obj
  );
}

/**
 * Type guard for database RiskCategory objects
 */
export function isDbRiskCategory(obj: unknown): obj is DbRiskCategory {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'name' in obj
  );
}

/**
 * Type guard to check if an object has a profiles property
 */
export function hasProfiles(obj: unknown): obj is { profiles: { name: string } | null } {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'profiles' in obj
  );
}

/**
 * Type guard to check if an object has a categories property
 */
export function hasCategories(obj: unknown): obj is { categories: DbRiskCategory | null } {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'categories' in obj
  );
}
