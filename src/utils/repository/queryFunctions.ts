import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";
/**
 * Basic type-safe query functions for Supabase
 */
// Define table names as a type for better type safety
type TableName = keyof Database["public"]["Tables"];
// Generic type for database tables
export type TableRecord = Record<string, any>;
/**
 * Get a record by ID from a specific table
 *
 * @param table The database table name
 * @param id The record ID
 * @returns The record or null if not found
 */
export async function getById<T extends TableRecord>(
  table: TableName,
  id: string
): Promise<T | null> {
  try {
    const { data, error } = await supabase.from(table).select("*").eq("id", id).maybeSingle();
    if (error) {
      throw error;
    }
    // Use type assertion to handle the conversion safely
    return data as unknown as T | null;
  } catch (error) {
    throw error;
  }
}
/**
 * Create a new record in a specific table
 *
 * @param table The database table name
 * @param data The record data to insert
 * @returns The created record or null if creation failed
 */
export async function create<T extends TableRecord>(
  table: TableName,
  // Use explicit any for the data parameter to avoid type errors with Supabase's insert method
  data: unknown
): Promise<T | null> {
  try {
    const { data: result, error } = await supabase.from(table).insert(data).select().single();
    if (error) {
      throw error;
    }
    // Use type assertion to handle the conversion safely
    return result as unknown as T;
  } catch (error) {
    throw error;
  }
}
/**
 * Update an existing record in a specific table
 *
 * @param table The database table name
 * @param id The record ID
 * @param data The record data to update
 * @returns The updated record or null if update failed
 */
export async function update<T extends TableRecord>(
  table: TableName,
  id: string,
  // Use explicit any for the data parameter to avoid type errors with Supabase's update method
  data: unknown
): Promise<T | null> {
  try {
    // Add updated_at timestamp
    const updateData = {
      ...data,
      updated_at: new Date().toISOString(),
    };
    const { data: result, error } = await supabase
      .from(table)
      .update(updateData)
      .eq("id", id)
      .select()
      .single();
    if (error) {
      throw error;
    }
    // Use type assertion to handle the conversion safely
    return result as unknown as T;
  } catch (error) {
    throw error;
  }
}
/**
 * Delete a record from a specific table
 *
 * @param table The database table name
 * @param id The record ID
 * @returns True if deletion was successful
 */
export async function remove(table: TableName, id: string): Promise<boolean> {
  try {
    const { error } = await supabase.from(table).delete().eq("id", id);
    if (error) {
      throw error;
    }
    return true;
  } catch (error) {
    throw error;
  }
}
/**
 * Get all records from a specific table
 *
 * @param table The database table name
 * @returns An array of records
 */
export async function getAll<T extends TableRecord>(table: TableName): Promise<T[]> {
  try {
    const { data, error } = await supabase.from(table).select("*");
    if (error) {
      throw error;
    }
    // Use type assertion to handle the conversion safely
    return data as unknown as T[];
  } catch (error) {
    throw error;
  }
}
