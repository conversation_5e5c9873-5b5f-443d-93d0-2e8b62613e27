/**
 * Runtime validation utilities for API responses
 * Provides type-safe validation for API data
 */

import {
  RiskApiResponse,
  IncidentApiResponse,
  PolicyApiResponse,
  PolicyRequestApiResponse,
  UserProfileApiResponse,
  OrganizationApiResponse,
  UserInvitationApiResponse,
  ControlMeasureApiResponse,
  MitigationActionApiResponse,
  CommentApiResponse,
  RiskHistoryApiResponse
} from '@/types/api';

// Type guard utilities
function isString(value: unknown): value is string {
  return typeof value === 'string';
}

function isNumber(value: unknown): value is number {
  return typeof value === 'number' && !isNaN(value);
}

function isBoolean(value: unknown): value is boolean {
  return typeof value === 'boolean';
}

function isObject(value: unknown): value is Record<string, unknown> {
  return typeof value === 'object' && value !== null && !Array.isArray(value);
}

function isOptionalString(value: unknown): value is string | undefined | null {
  return value === undefined || value === null || isString(value);
}

function isOptionalNumber(value: unknown): value is number | undefined | null {
  return value === undefined || value === null || isNumber(value);
}

// Risk API response validation
export function isRiskApiResponse(data: unknown): data is RiskApiResponse {
  if (!isObject(data)) return false;
  
  return (
    isString(data['id']) &&
    isString(data['title']) &&
    isString(data['description']) &&
    isOptionalString(data['category_id']) &&
    isString(data['owner_id']) &&
    isString(data['organization_id']) &&
    isString(data['created_at']) &&
    isString(data['updated_at']) &&
    isNumber(data['inherent_likelihood']) &&
    isNumber(data['inherent_impact']) &&
    isString(data['inherent_severity']) &&
    isNumber(data['likelihood']) &&
    isNumber(data['impact']) &&
    isString(data['severity']) &&
    isString(data['status']) &&
    isOptionalString(data['current_controls']) &&
    isOptionalString(data['mitigation_approach']) &&
    isOptionalString(data['due_date']) &&
    isString(data['created_by']) &&
    isOptionalString(data['template_id'])
  );
}

// Incident API response validation
export function isIncidentApiResponse(data: unknown): data is IncidentApiResponse {
  if (!isObject(data)) return false;
  
  return (
    isString(data['id']) &&
    isString(data['title']) &&
    isString(data['description']) &&
    isString(data['reporter_id']) &&
    isString(data['organization_id']) &&
    isString(data['date']) &&
    isString(data['status']) &&
    isString(data['severity']) &&
    isOptionalString(data['related_risk_id']) &&
    isString(data['created_at']) &&
    isString(data['updated_at'])
  );
}

// Policy API response validation
export function isPolicyApiResponse(data: unknown): data is PolicyApiResponse {
  if (!isObject(data)) return false;
  
  return (
    isString(data['id']) &&
    isString(data['title']) &&
    isString(data['description']) &&
    isString(data['category']) &&
    isString(data['status']) &&
    isString(data['version']) &&
    isString(data['created_by']) &&
    isString(data['organization_id']) &&
    isOptionalString(data['effective_date']) &&
    isOptionalString(data['document_url']) &&
    isString(data['created_at']) &&
    isString(data['updated_at'])
  );
}

// Policy request API response validation
export function isPolicyRequestApiResponse(data: unknown): data is PolicyRequestApiResponse {
  if (!isObject(data)) return false;
  
  return (
    isString(data['id']) &&
    isString(data['title']) &&
    isString(data['description']) &&
    isString(data['reason']) &&
    isString(data['status']) &&
    isString(data['requester_id']) &&
    isOptionalString(data['reviewer_id']) &&
    isString(data['organization_id']) &&
    isOptionalString(data['reference_document_url']) &&
    isOptionalString(data['feedback']) &&
    isString(data['created_at']) &&
    isString(data['updated_at'])
  );
}

// User profile API response validation
export function isUserProfileApiResponse(data: unknown): data is UserProfileApiResponse {
  if (!isObject(data)) return false;
  
  return (
    isString(data['id']) &&
    isString(data['name']) &&
    isString(data['email']) &&
    isString(data['role']) &&
    isOptionalString(data['department']) &&
    isOptionalString(data['avatar_url']) &&
    isOptionalString(data['organization_id']) &&
    isString(data['created_at']) &&
    isString(data['updated_at']) &&
    isOptionalString(data['deleted_at'])
  );
}

// Organization API response validation
export function isOrganizationApiResponse(data: unknown): data is OrganizationApiResponse {
  if (!isObject(data)) return false;
  
  return (
    isString(data['id']) &&
    isString(data['name']) &&
    isString(data['slug']) &&
    isOptionalString(data['domain']) &&
    isOptionalString(data['logo_url']) &&
    isOptionalString(data['subscription_plan']) &&
    isOptionalString(data['subscription_status']) &&
    isOptionalNumber(data['max_users']) &&
    isOptionalNumber(data['max_risks']) &&
    isOptionalString(data['organization_size']) &&
    isOptionalString(data['sector_type']) &&
    isOptionalString(data['sector_description']) &&
    isString(data['created_at']) &&
    isString(data['updated_at'])
  );
}

// User invitation API response validation
export function isUserInvitationApiResponse(data: unknown): data is UserInvitationApiResponse {
  if (!isObject(data)) return false;
  
  return (
    isString(data['id']) &&
    isString(data['email']) &&
    isString(data['role']) &&
    isString(data['status']) &&
    isOptionalString(data['invite_code_id']) &&
    isOptionalString(data['email_sent_at']) &&
    isOptionalString(data['email_opened_at']) &&
    isOptionalString(data['accepted_at']) &&
    isString(data['created_at']) &&
    isString(data['updated_at']) &&
    isOptionalString(data['invited_by']) &&
    isString(data['organization_id']) &&
    isOptionalString(data['user_id'])
  );
}

// Control measure API response validation
export function isControlMeasureApiResponse(data: unknown): data is ControlMeasureApiResponse {
  if (!isObject(data)) return false;
  
  return (
    isString(data['id']) &&
    isString(data['risk_id']) &&
    isString(data['organization_id']) &&
    isString(data['description']) &&
    isOptionalString(data['effectiveness']) &&
    isBoolean(data['implemented']) &&
    isString(data['created_at']) &&
    isString(data['updated_at'])
  );
}

// Mitigation action API response validation
export function isMitigationActionApiResponse(data: unknown): data is MitigationActionApiResponse {
  if (!isObject(data)) return false;
  
  return (
    isString(data['id']) &&
    isString(data['risk_id']) &&
    isString(data['organization_id']) &&
    isString(data['description']) &&
    isBoolean(data['completed']) &&
    isString(data['created_at']) &&
    isString(data['updated_at'])
  );
}

// Comment API response validation
export function isCommentApiResponse(data: unknown): data is CommentApiResponse {
  if (!isObject(data)) return false;
  
  return (
    isString(data['id']) &&
    isString(data['content']) &&
    isString(data['entity_type']) &&
    isString(data['entity_id']) &&
    isString(data['user_id']) &&
    isString(data['organization_id']) &&
    isString(data['created_at']) &&
    isString(data['updated_at']) &&
    isOptionalString(data['deleted_at'])
  );
}

// Risk history API response validation
export function isRiskHistoryApiResponse(data: unknown): data is RiskHistoryApiResponse {
  if (!isObject(data)) return false;
  
  return (
    isString(data['id']) &&
    isString(data['risk_id']) &&
    isString(data['organization_id']) &&
    isString(data['recorded_at']) &&
    isNumber(data['likelihood']) &&
    isNumber(data['impact']) &&
    isString(data['severity']) &&
    isString(data['status']) &&
    isOptionalString(data['created_by'])
  );
}

// Array validation helpers
export function isRiskApiResponseArray(data: unknown): data is RiskApiResponse[] {
  return Array.isArray(data) && data.every(isRiskApiResponse);
}

export function isIncidentApiResponseArray(data: unknown): data is IncidentApiResponse[] {
  return Array.isArray(data) && data.every(isIncidentApiResponse);
}

export function isPolicyApiResponseArray(data: unknown): data is PolicyApiResponse[] {
  return Array.isArray(data) && data.every(isPolicyApiResponse);
}

export function isPolicyRequestApiResponseArray(data: unknown): data is PolicyRequestApiResponse[] {
  return Array.isArray(data) && data.every(isPolicyRequestApiResponse);
}

export function isUserInvitationApiResponseArray(data: unknown): data is UserInvitationApiResponse[] {
  return Array.isArray(data) && data.every(isUserInvitationApiResponse);
}

export function isControlMeasureApiResponseArray(data: unknown): data is ControlMeasureApiResponse[] {
  return Array.isArray(data) && data.every(isControlMeasureApiResponse);
}

export function isMitigationActionApiResponseArray(data: unknown): data is MitigationActionApiResponse[] {
  return Array.isArray(data) && data.every(isMitigationActionApiResponse);
}

export function isCommentApiResponseArray(data: unknown): data is CommentApiResponse[] {
  return Array.isArray(data) && data.every(isCommentApiResponse);
}

export function isRiskHistoryApiResponseArray(data: unknown): data is RiskHistoryApiResponse[] {
  return Array.isArray(data) && data.every(isRiskHistoryApiResponse);
}