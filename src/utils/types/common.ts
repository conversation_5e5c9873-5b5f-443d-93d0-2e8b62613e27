/**
 * Common type definitions for utility functions
 * Provides consistent typing patterns across all utilities
 */

/**
 * Generic options interface for utility functions
 */
export interface UtilityOptions {
  /** Whether to throw errors or return error results */
  throwOnError?: boolean;
  /** Additional context for error reporting */
  context?: Record<string, unknown>;
}

/**
 * Date input types accepted by date utilities
 */
export type DateInput = Date | string | null | undefined;

/**
 * String input types accepted by string utilities
 */
export type StringInput = string | null | undefined;

/**
 * Numeric input types accepted by numeric utilities
 */
export type NumericInput = number | string | null | undefined;

/**
 * Generic validation function type
 */
export type ValidationFunction<T> = (value: unknown) => value is T;

/**
 * Generic transformation function type
 */
export type TransformFunction<TInput, TOutput> = (input: TInput) => TOutput;

/**
 * Generic predicate function type
 */
export type PredicateFunction<T> = (value: T) => boolean;

/**
 * Generic comparison function type
 */
export type ComparisonFunction<T> = (a: T, b: T) => number;

/**
 * Generic mapper function type
 */
export type MapperFunction<TInput, TOutput> = (input: TInput, index?: number) => TOutput;

/**
 * Generic filter function type
 */
export type FilterFunction<T> = (value: T, index?: number) => boolean;

/**
 * Generic reducer function type
 */
export type ReducerFunction<T, TResult> = (accumulator: TResult, current: T, index?: number) => TResult;

/**
 * Options for array utility functions
 */
export interface ArrayUtilityOptions extends UtilityOptions {
  /** Whether to preserve original array order */
  preserveOrder?: boolean;
  /** Maximum number of items to process */
  maxItems?: number;
}

/**
 * Options for string utility functions
 */
export interface StringUtilityOptions extends UtilityOptions {
  /** Whether to trim whitespace */
  trim?: boolean;
  /** Whether to ignore case */
  ignoreCase?: boolean;
}

/**
 * Options for date utility functions
 */
export interface DateUtilityOptions extends UtilityOptions {
  /** Timezone to use for operations */
  timezone?: string;
  /** Locale for formatting */
  locale?: string;
}

/**
 * Options for validation utility functions
 */
export interface ValidationOptions extends UtilityOptions {
  /** Whether to return detailed error information */
  detailed?: boolean;
  /** Custom error messages */
  messages?: Record<string, string>;
}

/**
 * Generic cache interface for utility functions
 */
export interface UtilityCache<TKey, TValue> {
  get(key: TKey): TValue | undefined;
  set(key: TKey, value: TValue): void;
  has(key: TKey): boolean;
  clear(): void;
  delete(key: TKey): boolean;
}

/**
 * Performance metrics for utility functions
 */
export interface PerformanceMetrics {
  executionTime: number;
  memoryUsage?: number;
  operationCount?: number;
}

/**
 * Utility function metadata
 */
export interface UtilityMetadata {
  name: string;
  version: string;
  description: string;
  parameters: Record<string, string>;
  returnType: string;
}

/**
 * Configuration for utility function logging
 */
export interface LoggingConfig {
  enabled: boolean;
  level: 'debug' | 'info' | 'warn' | 'error';
  includeStackTrace?: boolean;
  includeContext?: boolean;
}

/**
 * Base configuration for all utility functions
 */
export interface BaseUtilityConfig {
  logging?: LoggingConfig;
  performance?: {
    trackMetrics: boolean;
    maxExecutionTime?: number;
  };
  cache?: {
    enabled: boolean;
    maxSize?: number;
    ttl?: number;
  };
}