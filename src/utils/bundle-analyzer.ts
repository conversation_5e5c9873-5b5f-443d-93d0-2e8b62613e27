/**
 * Utility functions to analyze and monitor code splitting performance
 * Enhanced with bundle size optimization monitoring
 */

// Bundle size thresholds (in bytes)
const BUNDLE_THRESHOLDS = {
  CHUNK_WARNING: 500 * 1024, // 500KB
  CHUNK_ERROR: 1000 * 1024, // 1MB
  TOTAL_WARNING: 2000 * 1024, // 2MB
  TOTAL_ERROR: 5000 * 1024, // 5MB
  VENDOR_WARNING: 800 * 1024, // 800KB
} as const;

/**
 * Log bundle loading information for development
 */
export function logBundleLoad(routeName: string, startTime: number) {
  if (import.meta.env.MODE === "development") {
    const loadTime = performance.now() - startTime;
    import("../services/loggingService").then(({ log }) => {
      log.performance(
        `Route "${routeName}" loaded`,
        { loadTime },
        {
          component: "bundle_analyzer",
          action: "route_load",
        }
      );
    });
  }
}

/**
 * Monitor chunk loading performance
 */
export function monitorChunkLoading() {
  if (import.meta.env.MODE === "development") {
    // Monitor dynamic imports for Vite
    import("../services/loggingService").then(({ log }) => {
      log.debug("Chunk loading monitoring enabled for Vite", undefined, {
        component: "bundle_analyzer",
        action: "monitor_init",
      });
    });

    // Track dynamic imports through performance observer
    if ("PerformanceObserver" in window) {
      const observer = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === "resource" && entry.name.includes("chunk")) {
            import("../services/loggingService").then(({ log }) => {
              log.debug(`Chunk loaded: ${entry.name.split("/").pop()}`, undefined, {
                component: "bundle_analyzer",
                action: "chunk_loaded",
              });
            });
          }
        }
      });
      observer.observe({ entryTypes: ["resource"] });
    }
  }
}

/**
 * Get performance metrics for route loading
 */
export function getRoutePerformanceMetrics() {
  if (
    typeof window !== "undefined" &&
    "performance" in window &&
    "getEntriesByType" in window.performance
  ) {
    const navigationEntries = window.performance.getEntriesByType(
      "navigation"
    ) as PerformanceNavigationTiming[];
    const resourceEntries = window.performance.getEntriesByType(
      "resource"
    ) as PerformanceResourceTiming[];

    // Return null if no navigation entries are available
    if (!navigationEntries || navigationEntries.length === 0) {
      return null;
    }

    const navEntry = navigationEntries[0];
    if (!navEntry) {
      return null;
    }

    return {
      navigation: navEntry,
      resources: resourceEntries.filter(
        entry => entry.name && (entry.name.includes(".js") || entry.name.includes(".css"))
      ),
      totalLoadTime:
        navEntry.loadEventEnd > 0 && navEntry.fetchStart > 0
          ? navEntry.loadEventEnd - navEntry.fetchStart
          : 0,
    };
  }
  return null;
}

/**
 * Check if code splitting is working by analyzing loaded scripts
 */
export function verifyCodeSplitting(): boolean {
  const scripts = Array.from(document.querySelectorAll("script[src]"));
  const jsFiles = scripts.map(script => (script as HTMLScriptElement).src);

  // Look for chunk files (typically named with hashes)
  const chunkFiles = jsFiles.filter(
    src =>
      /\.[a-f0-9]{8,}\.(js|css)$/.test(src) || // Webpack hash pattern
      /chunk\.[a-f0-9]+\.js$/.test(src) || // Chunk pattern
      /\d+\.[a-f0-9]+\.js$/.test(src) // Numbered chunks
  );

  import("../services/loggingService").then(({ log }) => {
    log.info(
      "Code Splitting Analysis",
      {
        totalScripts: jsFiles.length,
        chunkFiles: chunkFiles.length,
        isCodeSplitting: chunkFiles.length > 0,
        chunks: chunkFiles,
      },
      {
        component: "bundle_analyzer",
        action: "code_splitting_analysis",
      }
    );
  });

  return chunkFiles.length > 0;
}

/**
 * Development helper to show loading states
 */
export function showLoadingDebugInfo(routeName: string, isLoading: boolean) {
  if (import.meta.env.MODE === "development") {
    import("../services/loggingService").then(({ log }) => {
      if (isLoading) {
        log.debug(`Loading route: ${routeName}`, undefined, {
          component: "bundle_analyzer",
          action: "route_loading",
        });
      } else {
        log.debug(`Route loaded: ${routeName}`, undefined, {
          component: "bundle_analyzer",
          action: "route_loaded",
        });
      }
    });
  }
}

/**
 * Analyze bundle sizes with detailed metrics
 */
export function analyzeBundleSizes() {
  if (import.meta.env.MODE === "development") {
    import("../services/loggingService").then(({ log }) => {
      log.info(
        "Bundle Analysis Tools Available",
        {
          tools: [
            'Run "npm run build:analyze" to generate visual bundle analysis',
            'Run "npm run build:size" for detailed size breakdown',
            "Check Network tab in DevTools to see chunk loading",
            'Look for files named like "chunk.abc123.js" indicating successful code splitting',
          ],
        },
        {
          component: "bundle_analyzer",
          action: "analyze_bundles",
        }
      );

      // Analyze current loaded resources
      analyzeCurrentResources();

      log.info(
        "Bundle Optimization Recommendations",
        {
          recommendations: [
            "Use dynamic imports for heavy libraries (PDF, Excel, Charts)",
            "Implement component-level code splitting",
            "Consider CDN for vendor libraries",
            "Enable gzip/brotli compression",
            "Monitor bundle size in CI/CD pipeline",
          ],
        },
        {
          component: "bundle_analyzer",
          action: "optimization_recommendations",
        }
      );
    });
  }
}

/**
 * Analyze currently loaded resources in the browser
 */
export function analyzeCurrentResources() {
  if (typeof window === "undefined") return;

  const resources = performance.getEntriesByType("resource") as PerformanceResourceTiming[];
  const jsResources = resources.filter(r => r.name.endsWith(".js"));
  const cssResources = resources.filter(r => r.name.endsWith(".css"));

  const totalJSSize = jsResources.reduce((sum, r) => sum + (r.transferSize ?? 0), 0);
  const totalCSSSize = cssResources.reduce((sum, r) => sum + (r.transferSize ?? 0), 0);

  import("../services/loggingService").then(({ log }) => {
    log.info(
      "Currently Loaded Resources",
      {
        javascript: { count: jsResources.length, size: formatBytes(totalJSSize) },
        css: { count: cssResources.length, size: formatBytes(totalCSSSize) },
        totalTransferSize: formatBytes(totalJSSize + totalCSSSize),
      },
      {
        component: "bundle_analyzer",
        action: "analyze_current_resources",
      }
    );

    // Show largest JS files
    const largestJS = jsResources
      .sort((a, b) => (b.transferSize ?? 0) - (a.transferSize ?? 0))
      .slice(0, 5);

    if (largestJS.length > 0) {
      const largestFiles = largestJS.map((resource, index) => {
        const name = resource.name.split("/").pop() ?? resource.name;
        const size = formatBytes(resource.transferSize ?? 0);
        return `${index + 1}. ${name} - ${size}`;
      });

      log.info(
        "Largest JavaScript files",
        { files: largestFiles },
        {
          component: "bundle_analyzer",
          action: "largest_js_files",
        }
      );
    }
  });
}

/**
 * Format bytes to human readable format
 */
function formatBytes(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

/**
 * Track bundle loading performance over time
 * Returns a cleanup function to disconnect the observer
 */
export function trackBundlePerformance(): (() => void) | null {
  if (import.meta.env.MODE === "development" && typeof PerformanceObserver !== "undefined") {
    const observer = new PerformanceObserver(list => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === "resource" && entry.name?.includes(".js")) {
          const resource = entry as PerformanceResourceTiming;
          const name = resource.name.split("/").pop() ?? "unknown";
          const loadTime = resource.responseEnd - resource.requestStart;

          if (loadTime > 1000) {
            // Warn for chunks taking > 1s
            import("../services/loggingService").then(({ log }) => {
              log.warn(`Slow chunk load: ${name} took ${loadTime.toFixed(0)}ms`, undefined, {
                component: "bundle_analyzer",
                action: "slow_chunk_warning",
              });
            });
          }
        }
      }
    });

    observer.observe({ entryTypes: ["resource"] });

    // Return cleanup function
    return () => {
      observer.disconnect();
    };
  }

  return null;
}

/**
 * Monitor chunk cache effectiveness
 * Returns a cleanup function to disconnect the observer
 */
export function monitorChunkCaching(): (() => void) | null {
  if (import.meta.env.MODE === "development") {
    const observer = new PerformanceObserver(list => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === "resource" && entry.name.includes(".js")) {
          const resource = entry as PerformanceResourceTiming;
          const name = resource.name.split("/").pop() ?? "unknown";

          // Check if resource was served from cache
          if (resource.transferSize === 0 && resource.decodedBodySize > 0) {
            import("../services/loggingService").then(({ log }) => {
              log.debug(`Cache hit: ${name}`, undefined, {
                component: "bundle_analyzer",
                action: "cache_hit",
              });
            });
          } else if (resource.transferSize > 0) {
            import("../services/loggingService").then(({ log }) => {
              log.debug(
                `Network load: ${name} (${formatBytes(resource.transferSize)})`,
                undefined,
                {
                  component: "bundle_analyzer",
                  action: "network_load",
                }
              );
            });
          }
        }
      }
    });

    observer.observe({ entryTypes: ["resource"] });

    // Return cleanup function
    return () => {
      observer.disconnect();
    };
  }

  return null;
}

/**
 * Enhanced bundle size monitoring with optimization recommendations
 */
export function monitorBundleOptimization() {
  if (import.meta.env.MODE === "development") {
    const resources = performance.getEntriesByType("resource") as PerformanceResourceTiming[];
    const jsResources = resources.filter(r => r.name.endsWith(".js"));

    const analysis = {
      totalSize: jsResources.reduce((sum, r) => sum + (r.transferSize ?? 0), 0),
      chunkCount: jsResources.length,
      largeChunks: jsResources.filter(r => (r.transferSize ?? 0) > BUNDLE_THRESHOLDS.CHUNK_WARNING),
      vendorChunks: jsResources.filter(r => r.name.includes("vendor")),
      dynamicChunks: jsResources.filter(r => r.name.includes("chunk")),
    };

    import("../services/loggingService").then(({ log }) => {
      log.info(
        "Bundle Optimization Analysis",
        {
          totalJSSize: formatBytes(analysis.totalSize),
          chunkCount: analysis.chunkCount,
          largeChunks: analysis.largeChunks.length,
          vendorChunks: analysis.vendorChunks.length,
          dynamicChunks: analysis.dynamicChunks.length,
        },
        {
          component: "bundle_analyzer",
          action: "optimization_analysis",
        }
      );

      // Optimization recommendations
      const recommendations = [];

      if (analysis.totalSize > BUNDLE_THRESHOLDS.TOTAL_WARNING) {
        recommendations.push("Total bundle size exceeds 2MB - consider aggressive code splitting");
      }

      if (analysis.largeChunks.length > 0) {
        recommendations.push(
          `${analysis.largeChunks.length} chunks exceed 500KB - consider splitting these chunks`
        );
      }

      if (analysis.vendorChunks.length === 0) {
        recommendations.push("No vendor chunks detected - consider separating vendor libraries");
      }

      if (analysis.dynamicChunks.length < 3) {
        recommendations.push("Limited dynamic chunks - consider more route-based code splitting");
      }

      if (recommendations.length > 0) {
        log.warn(
          "Bundle Optimization Recommendations",
          { recommendations },
          {
            component: "bundle_analyzer",
            action: "optimization_recommendations",
          }
        );
      }
    });

    return analysis;
  }

  return null;
}

/**
 * Track bundle size changes over time
 */
export function trackBundleSizeChanges() {
  const STORAGE_KEY = "bundle-size-history";

  if (import.meta.env.MODE === "development") {
    const resources = performance.getEntriesByType("resource") as PerformanceResourceTiming[];
    const currentSize = resources
      .filter(r => r.name.endsWith(".js"))
      .reduce((sum, r) => sum + (r.transferSize ?? 0), 0);

    try {
      const history = JSON.parse(localStorage.getItem(STORAGE_KEY) || "[]");
      const timestamp = new Date().toISOString();

      history.push({ timestamp, size: currentSize });

      // Keep only last 10 entries
      if (history.length > 10) {
        history.splice(0, history.length - 10);
      }

      localStorage.setItem(STORAGE_KEY, JSON.stringify(history));

      // Show trend if we have previous data
      if (history.length > 1) {
        const previousSize = history[history.length - 2].size;
        const change = currentSize - previousSize;
        const changePercent = ((change / previousSize) * 100).toFixed(1);

        if (Math.abs(change) > 10 * 1024) {
          // Only show if change > 10KB
          const trend = change > 0 ? "increased" : "decreased";
          import("../services/loggingService").then(({ log }) => {
            log.info(
              `Bundle size ${trend}: ${formatBytes(Math.abs(change))} (${changePercent}%)`,
              undefined,
              {
                component: "bundle_analyzer",
                action: "bundle_size_change",
              }
            );
          });
        }
      }
    } catch (error) {
      // Silently fail if localStorage is not available
    }
  }
}

/**
 * Validate bundle optimization goals
 */
export function validateBundleOptimization() {
  if (import.meta.env.MODE === "development") {
    const resources = performance.getEntriesByType("resource") as PerformanceResourceTiming[];
    const jsResources = resources.filter(r => r.name.endsWith(".js"));

    const metrics = {
      totalSize: jsResources.reduce((sum, r) => sum + (r.transferSize ?? 0), 0),
      chunkCount: jsResources.length,
      hasVendorSeparation: jsResources.some(r => r.name.includes("vendor")),
      hasCodeSplitting: jsResources.some(r => r.name.includes("chunk")),
      largeChunkCount: jsResources.filter(
        r => (r.transferSize ?? 0) > BUNDLE_THRESHOLDS.CHUNK_WARNING
      ).length,
    };

    const goals = {
      totalSizeUnder2MB: metrics.totalSize < BUNDLE_THRESHOLDS.TOTAL_WARNING,
      hasVendorSeparation: metrics.hasVendorSeparation,
      hasCodeSplitting: metrics.hasCodeSplitting,
      noLargeChunks: metrics.largeChunkCount === 0,
      multipleChunks: metrics.chunkCount >= 3,
    };

    const passedGoals = Object.values(goals).filter(Boolean).length;
    const totalGoals = Object.keys(goals).length;

    import("../services/loggingService").then(({ log }) => {
      log.info(
        "Bundle Optimization Goals",
        {
          totalSizeUnder2MB: `${goals.totalSizeUnder2MB ? "PASS" : "FAIL"} (${formatBytes(metrics.totalSize)})`,
          vendorSeparation: goals.hasVendorSeparation ? "PASS" : "FAIL",
          codeSplittingEnabled: goals.hasCodeSplitting ? "PASS" : "FAIL",
          noLargeChunks: `${goals.noLargeChunks ? "PASS" : "FAIL"} (${metrics.largeChunkCount} large chunks)`,
          multipleChunks: `${goals.multipleChunks ? "PASS" : "FAIL"} (${metrics.chunkCount} chunks)`,
          optimizationScore: `${passedGoals}/${totalGoals} (${((passedGoals / totalGoals) * 100).toFixed(0)}%)`,
        },
        {
          component: "bundle_analyzer",
          action: "optimization_goals_validation",
        }
      );
    });

    return { metrics, goals, score: passedGoals / totalGoals };
  }

  return null;
}

export default {
  logBundleLoad,
  monitorChunkLoading,
  getRoutePerformanceMetrics,
  verifyCodeSplitting,
  showLoadingDebugInfo,
  analyzeBundleSizes,
  trackBundlePerformance,
  monitorChunkCaching,
  monitorBundleOptimization,
  trackBundleSizeChanges,
  validateBundleOptimization,
};
