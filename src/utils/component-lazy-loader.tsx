import React, { Suspense, ComponentType, useState, useCallback } from "react";
import { ErrorBoundary } from "react-error-boundary";
import { Skeleton } from "@/components/ui/skeleton";
/**
 * Configuration for lazy-loaded components
 */
interface LazyComponentConfig {
  fallback?: React.ComponentType;
  errorFallback?: React.ComponentType<{ error: Error; resetErrorBoundary: () => void }>;
  loadingMessage?: string;
  retryCount?: number;
  preload?: boolean;
}
/**
 * Default loading component
 */
const DefaultLoadingFallback = () => (
  <div className="flex items-center justify-center p-4">
    <div className="space-y-2">
      <Skeleton className="h-4 w-[250px]" />
      <Skeleton className="h-4 w-[200px]" />
    </div>
  </div>
);
/**
 * Enhanced error fallback component with retry mechanism
 */
const DefaultErrorFallback = ({
  error,
  resetErrorBoundary,
}: {
  error: Error;
  resetErrorBoundary: () => void;
}) => {
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const handleRetry = useCallback(async () => {
    if (retryCount >= 3) {
      // After 3 retries, suggest navigation to error page
      return;
    }
    setIsRetrying(true);
    setRetryCount(prev => prev + 1);
    // Add a small delay before retry to avoid immediate failure
    setTimeout(() => {
      setIsRetrying(false);
      resetErrorBoundary();
    }, 1000);
  }, [resetErrorBoundary, retryCount]);
  return (
    <div className="flex flex-col items-center justify-center p-4 text-center">
      <div className="text-red-500 mb-2">⚠️ Component failed to load</div>
      <div className="text-sm text-gray-600 mb-4">{error.message}</div>
      {retryCount < 3 ? (
        <button
          onClick={handleRetry}
          disabled={isRetrying}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isRetrying ? "Retrying..." : `Try Again ${retryCount > 0 ? `(${retryCount}/3)` : ""}`}
        </button>
      ) : (
        <div className="space-y-2">
          <div className="text-sm text-gray-500">Maximum retry attempts reached</div>
          <button
            onClick={() => (window.location.href = "/")}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Go to Home
          </button>
        </div>
      )}
    </div>
  );
};
/**
 * Creates a lazy-loaded component with error boundaries and loading states
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  config: LazyComponentConfig = {
    // Implementation needed
  }
) {
  const {
    fallback = DefaultLoadingFallback,
    errorFallback = DefaultErrorFallback,
    retryCount = 3,
    preload = false,
  } = config;
  // Create the lazy component
  const LazyComponent = React.lazy(() => {
    let retries = 0;
    const loadWithRetry = async (): Promise<{ default: T }> => {
      try {
        return await importFn();
      } catch (error) {
        if (retries < retryCount) {
          retries++;
          // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, retries) * 1000));
          return loadWithRetry();
        }
        throw error;
      }
    };
    return loadWithRetry();
  });
  // Preload if requested
  if (preload) {
    importFn().catch(() => {
      // Silently fail preload attempts
    });
  }
  // Return wrapped component with retry mechanism
  const WrappedComponent = (props: Record<string, unknown>) => {
    const FallbackComponent = fallback;
    return (
      <ErrorBoundary
        FallbackComponent={errorFallback}
        onReset={() => {
          // Implement graceful retry mechanism instead of page reload
          // Create a new lazy component instance to bypass any cached failures
          const retryImport = () => {
            // Add cache busting parameter to force fresh import
            const timestamp = Date.now();
            return importFn().catch(error => {
              // If retry fails, you could:
              // 1. Navigate to a safe route
              // 2. Show a different fallback component
              // 3. Redirect to an error page
              // For now, we'll just log the failure
              // In a real app, you might want to:
              // window.location.href = '/error?reason=component-load-failed';
              // or use React Router: navigate('/error');
              throw error; // Re-throw to let error boundary handle it
            });
          };
          // Attempt the retry
          retryImport();
        }}
      >
        <Suspense fallback={<FallbackComponent />}>
          <LazyComponent {...props} />
        </Suspense>
      </ErrorBoundary>
    );
  };
  // Add preload method to component
  (WrappedComponent as any).preload = () => importFn();
  return WrappedComponent;
}
/**
 * Hook to preload components on user interaction
 */
export function useComponentPreloader() {
  const preloadOnHover = (importFn: () => Promise<unknown>) => ({
    onMouseEnter: () => {
      importFn().catch(() => {
        // Silently handle preload failures
      });
    },
  });
  const preloadOnFocus = (importFn: () => Promise<unknown>) => ({
    onFocus: () => {
      importFn().catch(() => {
        // Silently handle preload failures
      });
    },
  });
  const preloadOnClick = (importFn: () => Promise<unknown>) => ({
    onClick: () => {
      importFn().catch(() => {
        // Silently handle preload failures
      });
    },
  });
  return {
    preloadOnHover,
    preloadOnFocus,
    preloadOnClick,
  };
}
/**
 * Lazy load heavy chart components
 */
export const LazyChart = createLazyComponent(() => import("@/components/ui/chart"), {
  fallback: () => (
    <div className="h-64 w-full flex items-center justify-center">
      <div className="text-center">
        <Skeleton className="h-48 w-full mb-2" />
        <div className="text-sm text-gray-500">Loading chart...</div>
      </div>
    </div>
  ),
  preload: false,
});
/**
 * Lazy load PDF export functionality
 */
export const LazyPDFExporter = createLazyComponent(
  () => import("@/components/export/PDFExporter"),
  {
    fallback: () => (
      <div className="flex items-center justify-center p-2">
        <div className="text-sm">Loading PDF exporter...</div>
      </div>
    ),
  }
);
/**
 * Lazy load Excel export functionality
 */
export const LazyExcelExporter = createLazyComponent(
  () => import("@/components/export/ExcelExporter"),
  {
    fallback: () => (
      <div className="flex items-center justify-center p-2">
        <div className="text-sm">Loading Excel exporter...</div>
      </div>
    ),
  }
);
/**
 * Lazy load data visualization components
 */
export const LazyDataVisualization = createLazyComponent(
  () => import("@/components/charts/DataVisualization"),
  {
    fallback: () => (
      <div className="h-96 w-full flex items-center justify-center">
        <div className="text-center space-y-2">
          <Skeleton className="h-64 w-full" />
          <div className="text-sm text-gray-500">Loading visualization...</div>
        </div>
      </div>
    ),
  }
);
/**
 * Lazy load form components with heavy validation
 */
export const LazyComplexForm = createLazyComponent(() => import("@/components/forms/ComplexForm"), {
  fallback: () => (
    <div className="space-y-4">
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-20 w-full" />
      <Skeleton className="h-10 w-32" />
    </div>
  ),
});
export default {
  createLazyComponent,
  useComponentPreloader,
  LazyChart,
  LazyPDFExporter,
  LazyExcelExporter,
  LazyDataVisualization,
  LazyComplexForm,
};
