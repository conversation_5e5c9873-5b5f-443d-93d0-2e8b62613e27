# Utility Functions Standardization Guide

This document outlines the standardization patterns implemented across all utility functions in the RiskCompass application to ensure consistent error handling, type safety, and maintainability.

## Overview

The utility functions have been standardized to follow consistent patterns for:
- **Error Handling**: Comprehensive error management with typed errors
- **Type Safety**: Strict TypeScript typing with proper type guards
- **Documentation**: Standardized JSDoc comments with examples
- **Testing**: Complete unit test coverage with edge cases
- **Performance**: Optimized implementations with proper validation

## Standardization Components

### 1. Error Handling System (`src/utils/errors/standardErrors.ts`)

#### Core Error Types
- `UtilityError`: Base error class with code and context
- `ValidationError`: For invalid input parameters
- `ParseError`: For data transformation failures
- `ConfigurationError`: For invalid utility configuration

#### Result Pattern
```typescript
type UtilityResult<T> = 
  | { success: true; data: T }
  | { success: false; error: UtilityError }
```

#### Safe Execution Wrappers
- `safeExecute()`: Synchronous error handling
- `safeExecuteAsync()`: Asynchronous error handling
- `createSuccess()` / `createError()`: Result creation helpers

### 2. Common Type Definitions (`src/utils/types/common.ts`)

#### Input Types
- `DateInput`: `Date | string | null | undefined`
- `StringInput`: `string | null | undefined`
- `NumericInput`: `number | string | null | undefined`

#### Options Interfaces
- `UtilityOptions`: Base options for all utilities
- `DateUtilityOptions`: Date-specific options
- `StringUtilityOptions`: String-specific options
- `ArrayUtilityOptions`: Array-specific options
- `ValidationOptions`: Validation-specific options

#### Function Types
- `ValidationFunction<T>`: Type guard functions
- `TransformFunction<TInput, TOutput>`: Transformation functions
- `PredicateFunction<T>`: Boolean predicate functions

### 3. Documentation System (`src/utils/documentation/jsdoc-templates.ts`)

#### JSDoc Templates
- `dateFormatter`: For date formatting functions
- `validator`: For validation functions
- `transformer`: For transformation functions
- `arrayUtility`: For array processing functions
- `stringUtility`: For string processing functions

#### Template Generation
```typescript
const template = commonTemplates.dateFormatter(
  'formatDate', 
  'Formats a date string'
);
const jsdoc = generateJSDocComment(template);
```

## Standardized Utility Functions

### Date Utilities (`src/utils/dateUtils.ts`)

All date functions now follow these patterns:

#### Function Signature Pattern
```typescript
export const functionName = (
  dateInput: DateInput,
  options: DateUtilityOptions = {}
): ReturnType => {
  // Implementation with error handling
}
```

#### Backward Compatibility
Functions maintain backward compatibility with existing signatures:
```typescript
// Old: formatDate(date, 'short')
// New: formatDate(date, { format: 'short' })
// Both work!
```

#### Key Features
- Comprehensive error handling with `ValidationError`
- Null/undefined input handling
- Configurable fallback values
- Optional `throwOnError` behavior
- Context tracking for debugging

### UI Utilities (`src/utils/uiUtils.ts`)

#### Standardized Functions
- `getSeverityColor()`: Risk severity color mapping
- `capitalizeFirst()`: String capitalization
- `truncateText()`: Text truncation with ellipsis
- `formatPercentage()`: Number to percentage formatting
- `getInitials()`: Name to initials conversion
- `formatFileSize()`: Bytes to human-readable format
- `generateClassNames()`: Conditional CSS class generation

#### Error Handling
All functions use the standardized error handling pattern:
```typescript
const result = safeExecute(() => {
  // Core logic with validation
}, 'Operation description', context);

return result.success ? result.data : fallback;
```

### API Validation (`src/utils/api-validation.ts`)

#### Type Guards
Comprehensive runtime validation for API responses:
- `isRiskApiResponse()`
- `isIncidentApiResponse()`
- `isPolicyApiResponse()`
- Array validation functions

#### Validation Pattern
```typescript
function isTypeApiResponse(data: unknown): data is TypeApiResponse {
  if (!isObject(data)) return false;
  
  return (
    isString(data.id) &&
    isString(data.title) &&
    // ... other validations
  );
}
```

### Data Transformations

#### Policy Mappers (`src/utils/policyMappers.ts`)
- `mapPolicyFromDB()`: Database to application format
- `mapPolicyRequestFromDB()`: Request mapping

#### Risk Transformations (`src/utils/riskTransformations.ts`)
- `formatRisksData()`: Array transformation
- `formatRiskData()`: Single item transformation
- `formatIncidentsData()`: Incident transformation

## Testing Standards

### Test Structure
Each utility function has comprehensive tests covering:
- **Happy Path**: Normal operation with valid inputs
- **Edge Cases**: Boundary conditions and unusual inputs
- **Error Cases**: Invalid inputs and error conditions
- **Null/Undefined**: Proper handling of missing data
- **Type Safety**: Validation of type guards and transformations

### Test Naming Convention
```typescript
describe('UtilityName', () => {
  describe('functionName', () => {
    it('should handle normal case correctly', () => {});
    it('should handle edge case gracefully', () => {});
    it('should return fallback for invalid input', () => {});
    it('should throw error when throwOnError is true', () => {});
  });
});
```

### Coverage Requirements
- **Overall Coverage**: 90%+ for utility functions
- **Critical Functions**: 95%+ coverage
- **Error Paths**: All error conditions tested
- **Type Guards**: All validation paths tested

## Usage Examples

### Date Formatting with Error Handling
```typescript
// Basic usage
const formatted = formatDate(new Date());

// With options
const formatted = formatDate(date, {
  format: 'long',
  locale: 'en-US',
  fallback: 'No date available'
});

// With error throwing
try {
  const formatted = formatDate(invalidDate, { throwOnError: true });
} catch (error) {
  if (error instanceof ValidationError) {
    console.log('Invalid date:', error.context);
  }
}
```

### UI Utilities with Fallbacks
```typescript
// Severity colors with fallback
const colorClass = getSeverityColor(severity, {
  fallback: 'bg-gray-500'
});

// Text truncation
const truncated = truncateText(longText, {
  maxLength: 100,
  fallback: 'No content'
});

// Initials generation
const initials = getInitials(fullName, {
  fallback: '??'
});
```

### API Validation
```typescript
// Type-safe API response handling
if (isRiskApiResponse(response)) {
  // TypeScript knows this is RiskApiResponse
  console.log(response.title);
} else {
  throw new ValidationError('Invalid risk response');
}

// Array validation
if (isRiskApiResponseArray(responses)) {
  responses.forEach(risk => {
    // Each item is typed as RiskApiResponse
    console.log(risk.severity);
  });
}
```

## Migration Guide

### For Existing Code

1. **Update Function Calls**: Add options objects where needed
2. **Add Error Handling**: Wrap calls in try-catch or check results
3. **Update Tests**: Add comprehensive test coverage
4. **Add Documentation**: Use JSDoc templates for new functions

### For New Utilities

1. **Use Standard Patterns**: Follow the established error handling patterns
2. **Add Type Definitions**: Use common types from `types/common.ts`
3. **Write Documentation**: Use JSDoc templates
4. **Create Tests**: Follow the testing standards
5. **Handle Errors**: Use `safeExecute` wrappers

## Benefits

### Developer Experience
- **Consistent APIs**: All utilities follow the same patterns
- **Better Error Messages**: Detailed error context and codes
- **Type Safety**: Comprehensive TypeScript coverage
- **Documentation**: Clear examples and usage patterns

### Maintainability
- **Standardized Structure**: Easy to understand and modify
- **Comprehensive Tests**: Confident refactoring and changes
- **Error Tracking**: Better debugging and monitoring
- **Performance**: Optimized implementations

### Quality Assurance
- **Input Validation**: Robust handling of edge cases
- **Error Recovery**: Graceful degradation with fallbacks
- **Testing Coverage**: Thorough validation of all paths
- **Documentation**: Clear usage guidelines

## Future Enhancements

### Planned Improvements
- **Performance Monitoring**: Add metrics collection
- **Caching Layer**: Implement result caching for expensive operations
- **Internationalization**: Add locale-aware formatting
- **Async Utilities**: Standardize async operation patterns

### Extension Points
- **Custom Error Types**: Add domain-specific error classes
- **Validation Schemas**: Integrate with schema validation libraries
- **Logging Integration**: Add structured logging support
- **Metrics Collection**: Add performance and usage metrics

This standardization provides a solid foundation for maintainable, reliable, and well-documented utility functions throughout the RiskCompass application.