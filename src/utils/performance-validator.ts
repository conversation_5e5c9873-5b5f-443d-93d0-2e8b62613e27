/**
 * Performance validation tools for code splitting implementation
 */
interface PerformanceMetrics {
  bundleSize: {
    main: number;
    chunks: number[];
    total: number;
    reduction: number;
  };
  loadTimes: {
    firstContentfulPaint: number;
    largestContentfulPaint: number;
    timeToInteractive: number;
    domContentLoaded: number;
  };
  networkRequests: {
    total: number;
    jsFiles: number;
    chunkFiles: number;
    failedRequests: number;
  };
  memoryUsage: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
}
/**
 * Collect comprehensive performance metrics
 */
export function collectPerformanceMetrics(): PerformanceMetrics | null {
  if (!("performance" in window)) {
    return null;
  }
  const navigation = performance.getEntriesByType("navigation")[0] as PerformanceNavigationTiming;
  const resources = performance.getEntriesByType("resource") as PerformanceResourceTiming[];
  // Analyze bundle sizes from network requests
  const jsFiles = resources.filter(r => r.name.endsWith(".js"));
  const chunkFiles = jsFiles.filter(
    r =>
      /\.[a-f0-9]{8,}\.js$/.test(r.name) ||
      /chunk\.[a-f0-9]+\.js$/.test(r.name) ||
      /\d+\.[a-f0-9]+\.js$/.test(r.name)
  );
  const bundleSize = {
    main: jsFiles.find(f => f.name.includes("index") || f.name.includes("main"))?.transferSize ?? 0,
    chunks: chunkFiles.map(f => f.transferSize ?? 0),
    total: jsFiles.reduce((sum, f) => sum + (f.transferSize ?? 0), 0),
    reduction: 0, // Will be calculated by comparison
  };
  const loadTimes = {
    firstContentfulPaint: getMetricValue("first-contentful-paint"),
    largestContentfulPaint: getMetricValue("largest-contentful-paint"),
    timeToInteractive: navigation.loadEventEnd - navigation.navigationStart,
    domContentLoaded: navigation.domContentLoadedEventEnd - navigation.navigationStart,
  };
  const networkRequests = {
    total: resources.length,
    jsFiles: jsFiles.length,
    chunkFiles: chunkFiles.length,
    failedRequests: resources.filter(r => r.responseStatus >= 400).length,
  };
  const memoryUsage = getMemoryUsage();
  return {
    bundleSize,
    loadTimes,
    networkRequests,
    memoryUsage,
  };
}
/**
 * Get specific performance metric value
 */
function getMetricValue(metricName: string): number {
  const entries = performance.getEntriesByName(metricName);
  return entries.length > 0 ? entries[0].startTime : 0;
}
interface PerformanceWithMemory extends Performance {
  memory?: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
}
/**
 * Get memory usage information
 */
function getMemoryUsage() {
  const memory = (performance as PerformanceWithMemory).memory;
  if (!memory) {
    return {
      usedJSHeapSize: 0,
      totalJSHeapSize: 0,
      jsHeapSizeLimit: 0,
    };
  }
  return {
    usedJSHeapSize: memory.usedJSHeapSize,
    totalJSHeapSize: memory.totalJSHeapSize,
    jsHeapSizeLimit: memory.jsHeapSizeLimit,
  };
}
/**
 * Compare performance metrics before and after code splitting
 */
export function comparePerformanceMetrics(
  before: PerformanceMetrics,
  after: PerformanceMetrics
): {
  improvements: Record<string, { value: number; percentage: number }>;
  summary: string;
} {
  const improvements = {
    bundleSize: {
      value: before.bundleSize.total - after.bundleSize.total,
      percentage:
        ((before.bundleSize.total - after.bundleSize.total) / before.bundleSize.total) * 100,
    },
    firstContentfulPaint: {
      value: before.loadTimes.firstContentfulPaint - after.loadTimes.firstContentfulPaint,
      percentage:
        ((before.loadTimes.firstContentfulPaint - after.loadTimes.firstContentfulPaint) /
          before.loadTimes.firstContentfulPaint) *
        100,
    },
    timeToInteractive: {
      value: before.loadTimes.timeToInteractive - after.loadTimes.timeToInteractive,
      percentage:
        ((before.loadTimes.timeToInteractive - after.loadTimes.timeToInteractive) /
          before.loadTimes.timeToInteractive) *
        100,
    },
    memoryUsage: {
      value: before.memoryUsage.usedJSHeapSize - after.memoryUsage.usedJSHeapSize,
      percentage:
        ((before.memoryUsage.usedJSHeapSize - after.memoryUsage.usedJSHeapSize) /
          before.memoryUsage.usedJSHeapSize) *
        100,
    },
  };
  const summary = generatePerformanceSummary(improvements);
  return { improvements, summary };
}
/**
 * Generate human-readable performance summary
 */
function generatePerformanceSummary(
  improvements: Record<string, { value: number; percentage: number }>
): string {
  const bundleReduction = improvements.bundleSize.percentage;
  const fcp = improvements.firstContentfulPaint.percentage;
  const tti = improvements.timeToInteractive.percentage;
  let summary = "📊 Code Splitting Performance Impact:\n\n";
  if (bundleReduction > 0) {
    summary += `✅ Bundle size reduced by ${bundleReduction.toFixed(1)}% (${(improvements.bundleSize.value / 1024).toFixed(1)}KB)\n`;
  } else {
    summary += `❌ Bundle size increased by ${Math.abs(bundleReduction).toFixed(1)}%\n`;
  }
  if (fcp > 0) {
    summary += `✅ First Contentful Paint improved by ${fcp.toFixed(1)}% (${improvements.firstContentfulPaint.value.toFixed(0)}ms faster)\n`;
  } else {
    summary += `❌ First Contentful Paint slower by ${Math.abs(fcp).toFixed(1)}%\n`;
  }
  if (tti > 0) {
    summary += `✅ Time to Interactive improved by ${tti.toFixed(1)}% (${improvements.timeToInteractive.value.toFixed(0)}ms faster)\n`;
  } else {
    summary += `❌ Time to Interactive slower by ${Math.abs(tti).toFixed(1)}%\n`;
  }
  return summary;
}
/**
 * Validate code splitting implementation
 */
export function validateCodeSplitting(): {
  isValid: boolean;
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];
  // Check for chunk files
  const scripts = Array.from(document.querySelectorAll("script[src]"));
  const chunkFiles = scripts.filter(script => {
    const src = (script as HTMLScriptElement).src;
    return /\.[a-f0-9]{8,}\.js$/.test(src) || /chunk\.[a-f0-9]+\.js$/.test(src);
  });
  if (chunkFiles.length === 0) {
    issues.push("No chunk files detected - code splitting may not be working");
    recommendations.push("Verify React.lazy() and dynamic imports are configured correctly");
  }
  // Check for Suspense boundaries
  const suspenseElements = document.querySelectorAll("[data-suspense]");
  if (suspenseElements.length === 0) {
    issues.push("No Suspense boundaries detected");
    recommendations.push("Ensure Suspense components wrap lazy-loaded routes");
  }
  // Check performance metrics
  const metrics = collectPerformanceMetrics();
  if (metrics) {
    if (metrics.loadTimes.firstContentfulPaint > 2000) {
      issues.push("First Contentful Paint is slow (>2s)");
      recommendations.push("Consider preloading critical routes or optimizing bundle size");
    }
    if (metrics.networkRequests.failedRequests > 0) {
      issues.push(`${metrics.networkRequests.failedRequests} failed network requests detected`);
      recommendations.push("Check error boundaries and retry mechanisms");
    }
  }
  return {
    isValid: issues.length === 0,
    issues,
    recommendations,
  };
}
/**
 * Monitor route loading performance
 */
export class RoutePerformanceMonitor {
  private routeMetrics: Map<string, number[]> = new Map();
  startRouteLoad(routeName: string): number {
    const startTime = performance.now();
    return startTime;
  }
  endRouteLoad(routeName: string, startTime: number): void {
    const endTime = performance.now();
    const loadTime = endTime - startTime;
    if (!this.routeMetrics.has(routeName)) {
      this.routeMetrics.set(routeName, []);
    }
    this.routeMetrics.get(routeName)!.push(loadTime);
  }
  getRouteStats(routeName: string) {
    const times = this.routeMetrics.get(routeName) || [];
    if (times.length === 0) return null;
    const avg = times.reduce((sum, time) => sum + time, 0) / times.length;
    const min = Math.min(...times);
    const max = Math.max(...times);
    return { avg, min, max, count: times.length };
  }
  getAllStats() {
    const stats: Record<string, any> = {};
    for (const [route, times] of this.routeMetrics.entries()) {
      stats[route] = this.getRouteStats(route);
    }
    return stats;
  }
}
// Global performance monitor instance
export const routePerformanceMonitor = new RoutePerformanceMonitor();
export default {
  collectPerformanceMetrics,
  comparePerformanceMetrics,
  validateCodeSplitting,
  RoutePerformanceMonitor,
  routePerformanceMonitor,
};
