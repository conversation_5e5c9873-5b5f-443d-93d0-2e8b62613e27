/**
 * Performance Metrics Collector
 * Implements real-time performance monitoring with FCP, LCP, and bundle size tracking
 */
export interface PerformanceMetrics {
  // Core Web Vitals
  firstContentfulPaint: number | null;
  largestContentfulPaint: number | null;
  cumulativeLayoutShift: number | null;
  firstInputDelay: number | null;
  timeToInteractive: number | null;
  // Bundle and Resource Metrics
  bundleSize: {
    totalJS: number;
    totalCSS: number;
    totalAssets: number;
    chunkSizes: Record<string, number>;
  };
  // Runtime Performance
  memoryUsage: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  } | null;
  // Navigation and Loading
  navigationTiming: {
    domContentLoaded: number;
    loadComplete: number;
    timeToFirstByte: number;
  };
  // Custom Metrics
  routeLoadTime: number;
  chunkLoadTimes: Record<string, number>;
  cacheHitRate: number;
  // Metadata
  timestamp: string;
  url: string;
  userAgent: string;
  connection?: {
    effectiveType: string;
    downlink: number;
    rtt: number;
  };
}
export interface PerformanceBudget {
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  cumulativeLayoutShift: number;
  totalBundleSize: number;
  maxChunkSize: number;
  timeToInteractive: number;
}
export interface PerformanceAlert {
  id: string;
  type: "warning" | "error" | "critical";
  metric: string;
  value: number;
  threshold: number;
  message: string;
  timestamp: string;
  url: string;
}
/**
 * Default performance budgets based on industry standards
 */
export const DEFAULT_PERFORMANCE_BUDGET: PerformanceBudget = {
  firstContentfulPaint: 1500, // 1.5 seconds
  largestContentfulPaint: 2500, // 2.5 seconds
  cumulativeLayoutShift: 0.1, // 0.1 CLS score
  totalBundleSize: 2 * 1024 * 1024, // 2MB
  maxChunkSize: 1 * 1024 * 1024, // 1MB
  timeToInteractive: 3500, // 3.5 seconds
};
/**
 * Performance Metrics Collector Class
 */
export class PerformanceMetricsCollector {
  private metrics: Partial<PerformanceMetrics> = {};
  private budget: PerformanceBudget;
  private observers: PerformanceObserver[] = [];
  private alerts: PerformanceAlert[] = [];
  private onMetricUpdate?: (metrics: Partial<PerformanceMetrics>) => void;
  private onAlert?: (alert: PerformanceAlert) => void;
  constructor(
    budget: PerformanceBudget = DEFAULT_PERFORMANCE_BUDGET,
    callbacks?: {
      onMetricUpdate?: (metrics: Partial<PerformanceMetrics>) => void;
      onAlert?: (alert: PerformanceAlert) => void;
    }
  ) {
    this.budget = budget;
    this.onMetricUpdate = callbacks?.onMetricUpdate;
    this.onAlert = callbacks?.onAlert;
    this.initializeMetrics();
    this.startMonitoring();
  }
  private initializeMetrics(): void {
    this.metrics = {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      bundleSize: {
        totalJS: 0,
        totalCSS: 0,
        totalAssets: 0,
        chunkSizes: {},
      },
      chunkLoadTimes: {},
      firstContentfulPaint: null,
      largestContentfulPaint: null,
      cumulativeLayoutShift: null,
      firstInputDelay: null,
      timeToInteractive: null,
      memoryUsage: null,
      navigationTiming: {
        domContentLoaded: 0,
        loadComplete: 0,
        timeToFirstByte: 0,
      },
      routeLoadTime: 0,
      cacheHitRate: 0,
    };
    // Get connection information if available
    if ("connection" in navigator) {
      const connection = (navigator as any).connection;
      this.metrics.connection = {
        effectiveType: connection.effectiveType || "unknown",
        downlink: connection.downlink ?? 0,
        rtt: connection.rtt ?? 0,
      };
    }
  }
  private startMonitoring(): void {
    this.monitorCoreWebVitals();
    this.monitorResourceLoading();
    this.monitorNavigationTiming();
    this.monitorMemoryUsage();
    this.calculateBundleSize();
  }
  private monitorCoreWebVitals(): void {
    // Monitor FCP, LCP, CLS, FID
    if ("PerformanceObserver" in window) {
      // First Contentful Paint
      const fcpObserver = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          if (entry.name === "first-contentful-paint") {
            this.updateMetric("firstContentfulPaint", entry.startTime);
            this.checkBudget("firstContentfulPaint", entry.startTime);
          }
        }
      });
      try {
        fcpObserver.observe({ entryTypes: ["paint"] });
        this.observers.push(fcpObserver);
      } catch (error) {
        // Error caught and handled
      }
      // Largest Contentful Paint
      const lcpObserver = new PerformanceObserver(list => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        if (lastEntry) {
          this.updateMetric("largestContentfulPaint", lastEntry.startTime);
          this.checkBudget("largestContentfulPaint", lastEntry.startTime);
        }
      });
      try {
        lcpObserver.observe({ entryTypes: ["largest-contentful-paint"] });
        this.observers.push(lcpObserver);
      } catch (error) {
        // Error caught and handled
      }
      // Cumulative Layout Shift
      const clsObserver = new PerformanceObserver(list => {
        let clsValue = 0;
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
          }
        }
        this.updateMetric("cumulativeLayoutShift", clsValue);
        this.checkBudget("cumulativeLayoutShift", clsValue);
      });
      try {
        clsObserver.observe({ entryTypes: ["layout-shift"] });
        this.observers.push(clsObserver);
      } catch (error) {
        // Error caught and handled
      }
      // First Input Delay
      const fidObserver = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          this.updateMetric("firstInputDelay", (entry as any).processingStart - entry.startTime);
        }
      });
      try {
        fidObserver.observe({ entryTypes: ["first-input"] });
        this.observers.push(fidObserver);
      } catch (error) {
        // Error caught and handled
      }
    }
  }
  private monitorResourceLoading(): void {
    if ("PerformanceObserver" in window) {
      const resourceObserver = new PerformanceObserver(list => {
        let totalJS = 0;
        let totalCSS = 0;
        let totalAssets = 0;
        const chunkSizes: Record<string, number> = {};
        const chunkLoadTimes: Record<string, number> = {};
        let cacheHits = 0;
        let totalRequests = 0;
        for (const entry of list.getEntries()) {
          const resource = entry as PerformanceResourceTiming;
          const size = (resource.transferSize || resource.encodedBodySize) ?? 0;
          const loadTime = resource.responseEnd - resource.requestStart;
          totalRequests++;
          if (resource.transferSize === 0 && resource.decodedBodySize > 0) {
            cacheHits++;
          }
          if (resource.name.endsWith(".js")) {
            totalJS += size;
            const chunkName = this.extractChunkName(resource.name);
            chunkSizes[chunkName] = size;
            chunkLoadTimes[chunkName] = loadTime;
            // Check chunk size budget
            if (size > this.budget.maxChunkSize) {
              this.createAlert(
                "error",
                "maxChunkSize",
                size,
                this.budget.maxChunkSize,
                `Chunk ${chunkName} exceeds size budget`
              );
            }
          } else if (resource.name.endsWith(".css")) {
            totalCSS += size;
          } else {
            totalAssets += size;
          }
        }
        this.updateMetric("bundleSize", {
          totalJS,
          totalCSS,
          totalAssets,
          chunkSizes: { ...this.metrics.bundleSize?.chunkSizes, ...chunkSizes },
        });
        this.updateMetric("chunkLoadTimes", {
          ...this.metrics.chunkLoadTimes,
          ...chunkLoadTimes,
        });
        this.updateMetric(
          "cacheHitRate",
          totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0
        );
        // Check total bundle size budget
        const totalBundleSize = totalJS + totalCSS;
        if (totalBundleSize > this.budget.totalBundleSize) {
          this.checkBudget("totalBundleSize", totalBundleSize);
        }
      });
      try {
        resourceObserver.observe({ entryTypes: ["resource"] });
        this.observers.push(resourceObserver);
      } catch (error) {
        // Error caught and handled
      }
    }
  }
  private monitorNavigationTiming(): void {
    if ("PerformanceObserver" in window) {
      const navigationObserver = new PerformanceObserver(list => {
        for (const entry of list.getEntries()) {
          const nav = entry as PerformanceNavigationTiming;
          this.updateMetric("navigationTiming", {
            domContentLoaded: nav.domContentLoadedEventEnd - nav.navigationStart,
            loadComplete: nav.loadEventEnd - nav.navigationStart,
            timeToFirstByte: nav.responseStart - nav.navigationStart,
          });
          // Calculate Time to Interactive (approximation)
          const tti = nav.domInteractive - nav.navigationStart;
          this.updateMetric("timeToInteractive", tti);
          this.checkBudget("timeToInteractive", tti);
        }
      });
      try {
        navigationObserver.observe({ entryTypes: ["navigation"] });
        this.observers.push(navigationObserver);
      } catch (error) {
        // Error caught and handled
      }
    }
  }
  private monitorMemoryUsage(): void {
    const updateMemoryUsage = () => {
      if ("memory" in performance && performance.memory) {
        const memory = performance.memory as any;
        this.updateMetric("memoryUsage", {
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
        });
      }
    };
    // Update memory usage every 30 seconds
    updateMemoryUsage();
    const interval = setInterval(updateMemoryUsage, 30000);
    // Store interval for cleanup
    (this as any).memoryInterval = interval;
  }
  private calculateBundleSize(): void {
    // Calculate initial bundle size from already loaded resources
    const resources = performance.getEntriesByType("resource") as PerformanceResourceTiming[];
    let totalJS = 0;
    let totalCSS = 0;
    let totalAssets = 0;
    const chunkSizes: Record<string, number> = {};
    resources.forEach(resource => {
      const size = (resource.transferSize || resource.encodedBodySize) ?? 0;
      if (resource.name.endsWith(".js")) {
        totalJS += size;
        const chunkName = this.extractChunkName(resource.name);
        chunkSizes[chunkName] = size;
      } else if (resource.name.endsWith(".css")) {
        totalCSS += size;
      } else {
        totalAssets += size;
      }
    });
    this.updateMetric("bundleSize", {
      totalJS,
      totalCSS,
      totalAssets,
      chunkSizes,
    });
  }
  private extractChunkName(url: string): string {
    const parts = url.split("/");
    const filename = parts[parts.length - 1];
    return filename.split("?")[0]; // Remove query parameters
  }
  private updateMetric<K extends keyof PerformanceMetrics>(
    key: K,
    value: PerformanceMetrics[K]
  ): void {
    this.metrics[key] = value;
    this.onMetricUpdate?.(this.metrics);
  }
  private checkBudget(metric: string, value: number): void {
    const budgetKey = metric as keyof PerformanceBudget;
    const threshold = this.budget[budgetKey];
    if (typeof threshold === "number" && value > threshold) {
      const severity =
        value > threshold * 1.5 ? "critical" : value > threshold * 1.2 ? "error" : "warning";
      this.createAlert(
        severity,
        metric,
        value,
        threshold,
        `${metric} (${value.toFixed(0)}) exceeds budget (${threshold})`
      );
    }
  }
  private createAlert(
    type: "warning" | "error" | "critical",
    metric: string,
    value: number,
    threshold: number,
    message: string
  ): void {
    const alert: PerformanceAlert = {
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type,
      metric,
      value,
      threshold,
      message,
      timestamp: new Date().toISOString(),
      url: window.location.href,
    };
    this.alerts.push(alert);
    this.onAlert?.(alert);
    // Log to console in development
    if (import.meta.env.MODE === "development") {
      const emoji = type === "critical" ? "🔴" : type === "error" ? "🟠" : "🟡";
    }
  }
  public getMetrics(): PerformanceMetrics {
    return this.metrics as PerformanceMetrics;
  }
  public getAlerts(): PerformanceAlert[] {
    return [...this.alerts];
  }
  public clearAlerts(): void {
    this.alerts = [];
  }
  public updateBudget(newBudget: Partial<PerformanceBudget>): void {
    this.budget = { ...this.budget, ...newBudget };
  }
  public generateReport(): {
    metrics: PerformanceMetrics;
    alerts: PerformanceAlert[];
    summary: {
      score: number;
      recommendations: string[];
    };
  } {
    const metrics = this.getMetrics();
    const alerts = this.getAlerts();
    // Calculate performance score (0-100)
    let score = 100;
    const criticalAlerts = alerts.filter(a => a.type === "critical").length;
    const errorAlerts = alerts.filter(a => a.type === "error").length;
    const warningAlerts = alerts.filter(a => a.type === "warning").length;
    score -= criticalAlerts * 30;
    score -= errorAlerts * 15;
    score -= warningAlerts * 5;
    score = Math.max(0, score);
    // Generate recommendations
    const recommendations: string[] = [];
    if (
      metrics.firstContentfulPaint &&
      metrics.firstContentfulPaint > this.budget.firstContentfulPaint
    ) {
      recommendations.push("Optimize critical rendering path to improve FCP");
    }
    if (
      metrics.largestContentfulPaint &&
      metrics.largestContentfulPaint > this.budget.largestContentfulPaint
    ) {
      recommendations.push("Optimize largest content elements to improve LCP");
    }
    if (metrics.bundleSize.totalJS > this.budget.totalBundleSize) {
      recommendations.push("Implement code splitting to reduce bundle size");
    }
    if (metrics.cacheHitRate < 80) {
      recommendations.push("Improve caching strategy for better performance");
    }
    return {
      metrics,
      alerts,
      summary: {
        score,
        recommendations,
      },
    };
  }
  public destroy(): void {
    // Disconnect all observers
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    // Clear memory monitoring interval
    if ((this as any).memoryInterval) {
      clearInterval((this as any).memoryInterval);
    }
    // Clear metrics and alerts
    this.metrics = {};
    this.alerts = [];
  }
}
/**
 * Singleton instance for global performance monitoring
 */
let globalCollector: PerformanceMetricsCollector | null = null;
export function getGlobalPerformanceCollector(): PerformanceMetricsCollector {
  if (!globalCollector) {
    globalCollector = new PerformanceMetricsCollector();
  }
  return globalCollector;
}
export function destroyGlobalPerformanceCollector(): void {
  if (globalCollector) {
    globalCollector.destroy();
    globalCollector = null;
  }
}
