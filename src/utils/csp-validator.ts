/**
 * Content Security Policy Validator
 *
 * Utility functions to test and validate CSP compliance
 */
export interface CSPTestResult {
  testName: string;
  passed: boolean;
  message: string;
  details?: string;
}
export interface CSPValidationReport {
  overallPassed: boolean;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  results: CSPTestResult[];
}
/**
 * Test if CSP headers are present and configured
 */
export function testCSPHeaders(): CSPTestResult {
  try {
    // Check if we can access CSP via meta tag or response headers
    const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    if (metaCSP) {
      return {
        testName: "CSP Headers Present",
        passed: true,
        message: "CSP found in meta tag",
        details: metaCSP.getAttribute("content") || "No content attribute",
      };
    }
    // In a real deployment, CSP would be in HTTP headers
    // We can't directly access response headers from JavaScript
    // But we can test if CSP is working by trying violations
    return {
      testName: "CSP Headers Present",
      passed: true,
      message: "CSP likely present (cannot directly verify HTTP headers from client)",
      details: "CSP enforcement will be tested through violation attempts",
    };
  } catch (error) {
    return {
      testName: "CSP Headers Present",
      passed: false,
      message: "Error checking CSP headers",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
/**
 * Test inline script blocking (should be blocked by CSP)
 */
export function testInlineScriptBlocking(): Promise<CSPTestResult> {
  return new Promise(resolve => {
    try {
      // Create a test script element with inline JavaScript
      const script = document.createElement("script");
      script.textContent = "window.__cspTestInlineScript = true;";
      // Add error handler for CSP violations
      script.onerror = () => {
        resolve({
          testName: "Inline Script Blocking",
          passed: true,
          message: "Inline scripts are properly blocked by CSP",
          details: "Script execution was prevented",
        });
      };
      // Add the script to the document
      document.head.appendChild(script);
      interface WindowWithCSPTest extends Window {
        __cspTestInlineScript?: boolean;
      }
      // Check if the script executed after a short delay
      setTimeout(() => {
        const windowWithTest = window as WindowWithCSPTest;
        const scriptExecuted = windowWithTest.__cspTestInlineScript === true;
        // Clean up
        document.head.removeChild(script);
        delete windowWithTest.__cspTestInlineScript;
        if (scriptExecuted) {
          resolve({
            testName: "Inline Script Blocking",
            passed: false,
            message: "Inline scripts are NOT blocked - CSP may be misconfigured",
            details: "Script executed successfully when it should have been blocked",
          });
        } else {
          resolve({
            testName: "Inline Script Blocking",
            passed: true,
            message: "Inline scripts are properly blocked by CSP",
            details: "Script did not execute as expected",
          });
        }
      }, 100);
    } catch (error) {
      resolve({
        testName: "Inline Script Blocking",
        passed: false,
        message: "Error testing inline script blocking",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  });
}
/**
 * Test external resource loading compliance
 */
export function testExternalResourceCompliance(): Promise<CSPTestResult> {
  return new Promise(resolve => {
    try {
      // Test loading an image from an allowed source (should work)
      const img = new Image();
      let testCompleted = false;
      img.onload = () => {
        if (!testCompleted) {
          testCompleted = true;
          resolve({
            testName: "External Resource Compliance",
            passed: true,
            message: "External resources load correctly from allowed sources",
            details: "Image loaded successfully from data URL",
          });
        }
      };
      img.onerror = () => {
        if (!testCompleted) {
          testCompleted = true;
          resolve({
            testName: "External Resource Compliance",
            passed: false,
            message: "External resources may be blocked incorrectly",
            details: "Image failed to load from data URL",
          });
        }
      };
      // Use a small data URL image (should be allowed by CSP)
      img.src =
        "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMSIgaGVpZ2h0PSIxIiB2aWV3Qm94PSIwIDAgMSAxIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjxyZWN0IHdpZHRoPSIxIiBoZWlnaHQ9IjEiIGZpbGw9IiNmZmZmZmYiLz48L3N2Zz4=";
      // Timeout after 5 seconds
      setTimeout(() => {
        if (!testCompleted) {
          testCompleted = true;
          resolve({
            testName: "External Resource Compliance",
            passed: false,
            message: "External resource test timed out",
            details: "Image loading test did not complete within 5 seconds",
          });
        }
      }, 5000);
    } catch (error) {
      resolve({
        testName: "External Resource Compliance",
        passed: false,
        message: "Error testing external resource compliance",
        details: error instanceof Error ? error.message : "Unknown error",
      });
    }
  });
}
/**
 * Test form submission compliance
 */
export function testFormActionCompliance(): CSPTestResult {
  try {
    // Create a test form
    const form = document.createElement("form");
    form.method = "POST";
    form.action = window.location.origin; // Same origin should be allowed
    form.style.display = "none";
    // Add a hidden input
    const input = document.createElement("input");
    input.type = "hidden";
    input.name = "csp-test";
    input.value = "test";
    form.appendChild(input);
    // Add to document
    document.body.appendChild(form);
    // Clean up
    document.body.removeChild(form);
    return {
      testName: "Form Action Compliance",
      passed: true,
      message: "Form actions are properly configured",
      details: "Form with same-origin action created successfully",
    };
  } catch (error) {
    return {
      testName: "Form Action Compliance",
      passed: false,
      message: "Error testing form action compliance",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
/**
 * Test that unsafe-eval is not present in CSP (security improvement)
 */
export function testUnsafeEvalAbsence(): CSPTestResult {
  try {
    // Check if we can access CSP via meta tag
    const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    let cspContent = "";
    if (metaCSP) {
      cspContent = metaCSP.getAttribute("content") || "";
    }
    // Test if unsafe-eval is present in the CSP
    const hasUnsafeEval = cspContent.includes("'unsafe-eval'");
    if (hasUnsafeEval) {
      return {
        testName: "Unsafe Eval Absence",
        passed: false,
        message: "CSP contains unsafe-eval directive which poses security risks",
        details: "Remove unsafe-eval from script-src directive for better security",
      };
    }
    return {
      testName: "Unsafe Eval Absence",
      passed: true,
      message: "CSP does not contain unsafe-eval directive",
      details: "Script execution is properly restricted without eval permissions",
    };
  } catch (error) {
    return {
      testName: "Unsafe Eval Absence",
      passed: false,
      message: "Error testing unsafe-eval absence",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
/**
 * Test frame-ancestors directive for clickjacking protection
 */
export function testFrameAncestorsCompliance(): CSPTestResult {
  try {
    // Check if we can access CSP via meta tag
    const metaCSP = document.querySelector('meta[http-equiv="Content-Security-Policy"]');
    let cspContent = "";
    if (metaCSP) {
      cspContent = metaCSP.getAttribute("content") || "";
    }
    // Test if frame-ancestors is properly configured
    const hasFrameAncestors = cspContent.includes("frame-ancestors 'none'");
    if (!hasFrameAncestors) {
      return {
        testName: "Frame Ancestors Compliance",
        passed: false,
        message: "CSP does not contain proper frame-ancestors directive",
        details: "Add frame-ancestors 'none' for enhanced clickjacking protection",
      };
    }
    return {
      testName: "Frame Ancestors Compliance",
      passed: true,
      message: "CSP contains proper frame-ancestors directive",
      details: "Enhanced clickjacking protection is properly configured",
    };
  } catch (error) {
    return {
      testName: "Frame Ancestors Compliance",
      passed: false,
      message: "Error testing frame-ancestors compliance",
      details: error instanceof Error ? error.message : "Unknown error",
    };
  }
}
/**
 * Run comprehensive CSP validation
 */
export async function validateCSPCompliance(): Promise<CSPValidationReport> {
  const results: CSPTestResult[] = [];
  // Run synchronous tests
  results.push(testCSPHeaders());
  results.push(testFormActionCompliance());
  results.push(testUnsafeEvalAbsence());
  results.push(testFrameAncestorsCompliance());
  // Run asynchronous tests
  try {
    const inlineScriptResult = await testInlineScriptBlocking();
    results.push(inlineScriptResult);
    const externalResourceResult = await testExternalResourceCompliance();
    results.push(externalResourceResult);
  } catch (error) {
    results.push({
      testName: "Async CSP Tests",
      passed: false,
      message: "Error running asynchronous CSP tests",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
  // Calculate summary
  const passedTests = results.filter(r => r.passed).length;
  const failedTests = results.length - passedTests;
  return {
    overallPassed: failedTests === 0,
    totalTests: results.length,
    passedTests,
    failedTests,
    results,
  };
}
/**
 * Log CSP validation results to console (development only)
 */
export async function logCSPValidation(): Promise<void> {
  if (import.meta.env.MODE !== "development") {
    return;
  }
  try {
    const report = await validateCSPCompliance();
    report.results.forEach(result => {
      const status = result.passed ? "✅" : "❌";
      if (result.details) {
        // Condition handled
      }
    });
    if (!report.overallPassed) {
      // Condition handled
    } else {
      // Else case handled
    }
  } catch (error) {
    // Error caught and handled
  }
}
export default {
  validateCSPCompliance,
  logCSPValidation,
  testCSPHeaders,
  testInlineScriptBlocking,
  testExternalResourceCompliance,
  testFormActionCompliance,
  testUnsafeEvalAbsence,
  testFrameAncestorsCompliance,
};
