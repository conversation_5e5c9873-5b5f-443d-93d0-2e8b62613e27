/**
 * JSDoc templates and documentation utilities for consistent function documentation
 * Provides standardized documentation patterns across all utility functions
 */

/**
 * Standard JSDoc template for utility functions
 */
export interface JSDocTemplate {
  /** Function description */
  description: string;
  /** Parameter descriptions */
  params: Array<{
    name: string;
    type: string;
    description: string;
    optional?: boolean;
    defaultValue?: string;
  }>;
  /** Return value description */
  returns: {
    type: string;
    description: string;
  };
  /** Usage examples */
  examples?: string[];
  /** Related functions or references */
  see?: string[];
  /** Version when function was added */
  since?: string;
  /** Deprecation notice */
  deprecated?: string;
  /** Throws information */
  throws?: Array<{
    type: string;
    description: string;
  }>;
}

/**
 * Generates JSDoc comment string from template
 * @param template - The JSDoc template object
 * @returns Formatted JSDoc comment string
 * 
 * @example
 * ```typescript
 * const template: JSDocTemplate = {
 *   description: 'Formats a date string',
 *   params: [
 *     { name: 'date', type: 'DateInput', description: 'Date to format' },
 *     { name: 'format', type: 'string', description: 'Format string', optional: true, defaultValue: 'default' }
 *   ],
 *   returns: { type: 'string', description: 'Formatted date string' }
 * };
 * const jsdoc = generateJSDocComment(template);
 * ```
 */
export function generateJSDocComment(template: JSDocTemplate): string {
  const lines: string[] = ['/**'];
  
  // Description
  lines.push(` * ${template.description}`);
  
  // Parameters
  if (template.params.length > 0) {
    lines.push(' *');
    template.params.forEach(param => {
      const optional = param.optional ? '?' : '';
      const defaultVal = param.defaultValue ? ` - Default: ${param.defaultValue}` : '';
      lines.push(` * @param {${param.type}} ${param.name}${optional} - ${param.description}${defaultVal}`);
    });
  }
  
  // Returns
  lines.push(' *');
  lines.push(` * @returns {${template.returns.type}} ${template.returns.description}`);
  
  // Examples
  if (template.examples && template.examples.length > 0) {
    lines.push(' *');
    template.examples.forEach(example => {
      lines.push(' * @example');
      lines.push(` * ${example}`);
    });
  }
  
  // See also
  if (template.see && template.see.length > 0) {
    lines.push(' *');
    template.see.forEach(ref => {
      lines.push(` * @see ${ref}`);
    });
  }
  
  // Since
  if (template.since) {
    lines.push(' *');
    lines.push(` * @since ${template.since}`);
  }
  
  // Deprecated
  if (template.deprecated) {
    lines.push(' *');
    lines.push(` * @deprecated ${template.deprecated}`);
  }
  
  // Throws
  if (template.throws && template.throws.length > 0) {
    lines.push(' *');
    template.throws.forEach(throwInfo => {
      lines.push(` * @throws {${throwInfo.type}} ${throwInfo.description}`);
    });
  }
  
  lines.push(' */');
  return lines.join('\n');
}

/**
 * Common JSDoc templates for frequently used utility patterns
 */
export const commonTemplates = {
  /**
   * Template for date formatting functions
   */
  dateFormatter: (functionName: string, description: string): JSDocTemplate => ({
    description,
    params: [
      { name: 'date', type: 'DateInput', description: 'Date to format (Date object, string, or null/undefined)' },
      { name: 'options', type: 'DateUtilityOptions', description: 'Formatting options', optional: true }
    ],
    returns: { type: 'string', description: 'Formatted date string or fallback value for invalid dates' },
    examples: [
      `${functionName}(new Date('2024-01-15')) // Returns formatted date`,
      `${functionName}('2024-01-15', { locale: 'en-US' }) // Returns localized format`,
      `${functionName}(null) // Returns fallback value`
    ],
    throws: [
      { type: 'ValidationError', description: 'When date format is invalid and throwOnError is true' }
    ]
  }),

  /**
   * Template for validation functions
   */
  validator: (functionName: string, description: string, validatedType: string): JSDocTemplate => ({
    description,
    params: [
      { name: 'value', type: 'unknown', description: 'Value to validate' },
      { name: 'options', type: 'ValidationOptions', description: 'Validation options', optional: true }
    ],
    returns: { type: `value is ${validatedType}`, description: `True if value is a valid ${validatedType}` },
    examples: [
      `${functionName}(someValue) // Returns boolean`,
      `${functionName}(someValue, { detailed: true }) // Returns detailed validation result`
    ]
  }),

  /**
   * Template for transformation functions
   */
  transformer: (functionName: string, description: string, inputType: string, outputType: string): JSDocTemplate => ({
    description,
    params: [
      { name: 'input', type: inputType, description: 'Input value to transform' },
      { name: 'options', type: 'UtilityOptions', description: 'Transformation options', optional: true }
    ],
    returns: { type: outputType, description: 'Transformed value' },
    examples: [
      `${functionName}(inputValue) // Returns transformed value`,
      `${functionName}(inputValue, { throwOnError: false }) // Returns result with error handling`
    ],
    throws: [
      { type: 'ValidationError', description: 'When input is invalid' },
      { type: 'ParseError', description: 'When transformation fails' }
    ]
  }),

  /**
   * Template for array utility functions
   */
  arrayUtility: (functionName: string, description: string, itemType: string, returnType: string): JSDocTemplate => ({
    description,
    params: [
      { name: 'array', type: `${itemType}[]`, description: 'Array to process' },
      { name: 'options', type: 'ArrayUtilityOptions', description: 'Processing options', optional: true }
    ],
    returns: { type: returnType, description: 'Processed result' },
    examples: [
      `${functionName}([...items]) // Returns processed result`,
      `${functionName}([...items], { maxItems: 100 }) // Returns limited result`
    ],
    throws: [
      { type: 'ValidationError', description: 'When array is invalid' }
    ]
  }),

  /**
   * Template for string utility functions
   */
  stringUtility: (functionName: string, description: string): JSDocTemplate => ({
    description,
    params: [
      { name: 'input', type: 'StringInput', description: 'String to process (string, null, or undefined)' },
      { name: 'options', type: 'StringUtilityOptions', description: 'Processing options', optional: true }
    ],
    returns: { type: 'string', description: 'Processed string or fallback value' },
    examples: [
      `${functionName}('input string') // Returns processed string`,
      `${functionName}(null) // Returns fallback value`,
      `${functionName}('input', { trim: true }) // Returns trimmed result`
    ]
  })
};

/**
 * Validates JSDoc template completeness
 * @param template - JSDoc template to validate
 * @returns Validation result with any missing required fields
 */
export function validateJSDocTemplate(template: JSDocTemplate): {
  isValid: boolean;
  missingFields: string[];
  warnings: string[];
} {
  const missingFields: string[] = [];
  const warnings: string[] = [];

  if (!template.description || template.description.trim().length === 0) {
    missingFields.push('description');
  }

  if (!template.returns) {
    missingFields.push('returns');
  } else {
    if (!template.returns.type) {
      missingFields.push('returns.type');
    }
    if (!template.returns.description) {
      missingFields.push('returns.description');
    }
  }

  // Warnings for best practices
  if (!template.examples || template.examples.length === 0) {
    warnings.push('Consider adding usage examples');
  }

  if (template.params.length > 3 && (!template.examples || template.examples.length === 0)) {
    warnings.push('Functions with many parameters should include examples');
  }

  return {
    isValid: missingFields.length === 0,
    missingFields,
    warnings
  };
}