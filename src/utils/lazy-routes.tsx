import React, { Suspense, ComponentType } from "react";
import { RouteLoading } from "@/components/ui/loading";
import { LazyLoadErrorBoundary } from "@/components/error-boundaries/LazyLoadErrorBoundary";
import { PageErrorBoundary } from "@/components/error-boundaries";
import { logBundleLoad, showLoadingDebugInfo } from "@/utils/bundle-analyzer";
/**
 * Configuration for lazy route creation
 */
interface LazyRouteConfig {
  loadingMessage?: string;
  routeName?: string;
  preload?: boolean;
  fullScreenLoading?: boolean;
  priority?: "high" | "medium" | "low";
  chunkName?: string;
}
/**
 * Creates a lazy-loaded route component with error boundaries and loading states
 * Enhanced with intelligent preloading and performance monitoring
 */
export function createLazyRoute<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  config: LazyRouteConfig = {
    // Implementation needed
  }
) {
  const {
    loadingMessage = "Loading page...",
    routeName,
    preload = false,
    fullScreenLoading = false,
    priority = "medium",
    chunkName,
  } = config;
  // Create the lazy component with enhanced performance monitoring
  const LazyComponent = React.lazy(() => {
    const startTime = performance.now();
    const chunkId = chunkName || routeName || "unknown";
    showLoadingDebugInfo(`${chunkId} (${priority})`, true);
    return importFn()
      .then(module => {
        const loadTime = performance.now() - startTime;
        logBundleLoad(`${chunkId} (${priority})`, startTime);
        showLoadingDebugInfo(`${chunkId} (${priority})`, false);
        // Log performance metrics for optimization
        if (import.meta.env.MODE === "development") {
          // Warn about slow loading chunks
          if (loadTime > 2000) {
            // Condition handled
          }
        }
        return module;
      })
      .catch(error => {
        throw error;
      });
  });
  // Intelligent preloading based on priority
  if (preload) {
    const preloadDelay = priority === "high" ? 50 : priority === "medium" ? 200 : 500;
    setTimeout(() => {
      importFn().catch(() => {
        // Silently fail preloading - component will load when actually needed
        if (import.meta.env.MODE === "development") {
          // Condition handled
        }
      });
    }, preloadDelay);
  }
  // Return the wrapped component with enhanced error handling
  const WrappedComponent: React.FC = props => (
    <PageErrorBoundary pageName={routeName}>
      <LazyLoadErrorBoundary routeName={routeName}>
        <Suspense
          fallback={<RouteLoading message={loadingMessage} fullScreen={fullScreenLoading} />}
        >
          <LazyComponent {...props} />
        </Suspense>
      </LazyLoadErrorBoundary>
    </PageErrorBoundary>
  );
  // Add display name for debugging with chunk info
  WrappedComponent.displayName = `LazyRoute(${routeName ?? "Unknown"}:${chunkName ?? "default"})`;
  // Add preload method to component for manual preloading
  (WrappedComponent as any).preload = () => importFn();
  (WrappedComponent as any).chunkName = chunkName;
  (WrappedComponent as any).priority = priority;
  return WrappedComponent;
}
/**
 * Preload a lazy route component
 */
export function preloadRoute(importFn: () => Promise<{ default: ComponentType<any> }>) {
  return importFn().catch(() => {
    // Silently fail preloading
  });
}
/**
 * Route group configurations for organized code splitting
 * Enhanced with performance monitoring and intelligent preloading
 */
export const ROUTE_GROUPS = {
  // Critical routes - loaded immediately with preloading
  CRITICAL: {
    loadingMessage: "Loading...",
    fullScreenLoading: true,
    preload: true,
    priority: "high" as const,
    chunkName: "critical",
  },
  // Authentication routes - high priority for user flow
  AUTH: {
    loadingMessage: "Loading authentication...",
    fullScreenLoading: true,
    priority: "high" as const,
    chunkName: "auth",
  },
  // Main application routes - medium priority
  MAIN: {
    loadingMessage: "Loading page...",
    fullScreenLoading: false,
    priority: "medium" as const,
    chunkName: "main",
  },
  // Risk management routes - split into focused chunks
  RISK: {
    loadingMessage: "Loading risk management...",
    fullScreenLoading: false,
    priority: "medium" as const,
    chunkName: "risk",
  },
  // Incident management routes - separate chunk for better caching
  INCIDENT: {
    loadingMessage: "Loading incident management...",
    fullScreenLoading: false,
    priority: "medium" as const,
    chunkName: "incident",
  },
  // Administration routes - low priority, admin-only
  ADMIN: {
    loadingMessage: "Loading administration...",
    fullScreenLoading: false,
    priority: "low" as const,
    chunkName: "admin",
  },
  // Report routes - heavy components, separate chunk
  REPORTS: {
    loadingMessage: "Loading reports...",
    fullScreenLoading: false,
    priority: "low" as const,
    chunkName: "reports",
  },
  // Policy routes - separate chunk for compliance features
  POLICY: {
    loadingMessage: "Loading policies...",
    fullScreenLoading: false,
    priority: "medium" as const,
    chunkName: "policy",
  },
  // Profile and user management - low priority
  USER: {
    loadingMessage: "Loading user management...",
    fullScreenLoading: false,
    priority: "low" as const,
    chunkName: "user",
  },
} as const;
export default createLazyRoute;
