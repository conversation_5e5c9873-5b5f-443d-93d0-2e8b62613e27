/**
 * Network Condition Testing Utility
 * Simulates various network conditions for performance validation
 */

export interface NetworkCondition {
  name: string;
  rtt: number; // Round-trip time in milliseconds
  throughput: number; // Throughput in Kbps
  description: string;
}

export interface PerformanceMetrics {
  loadTime: number;
  firstContentfulPaint: number;
  largestContentfulPaint: number;
  timeToInteractive: number;
  cumulativeLayoutShift: number;
  totalBlockingTime: number;
}

export interface NetworkTestResult {
  condition: NetworkCondition;
  metrics: PerformanceMetrics;
  bundleLoadTime: number;
  estimatedUserExperience: 'excellent' | 'good' | 'fair' | 'poor';
}

// Standard network conditions for testing
export const NETWORK_CONDITIONS: NetworkCondition[] = [
  {
    name: 'Slow 3G',
    rtt: 300,
    throughput: 400,
    description: 'Slow 3G connection (400 Kbps, 300ms RTT)',
  },
  {
    name: 'Fast 3G',
    rtt: 150,
    throughput: 1600,
    description: 'Fast 3G connection (1.6 Mbps, 150ms RTT)',
  },
  {
    name: 'Regular 4G',
    rtt: 40,
    throughput: 10000,
    description: 'Regular 4G connection (10 Mbps, 40ms RTT)',
  },
  {
    name: 'WiFi',
    rtt: 10,
    throughput: 50000,
    description: 'WiFi connection (50 Mbps, 10ms RTT)',
  },
  {
    name: 'Offline',
    rtt: 0,
    throughput: 0,
    description: 'Offline condition (cached resources only)',
  },
];

// Performance thresholds for different network conditions
export const PERFORMANCE_THRESHOLDS = {
  'Slow 3G': {
    firstContentfulPaint: 3000, // 3 seconds
    largestContentfulPaint: 5000, // 5 seconds
    timeToInteractive: 8000, // 8 seconds
    cumulativeLayoutShift: 0.15,
  },
  'Fast 3G': {
    firstContentfulPaint: 2000, // 2 seconds
    largestContentfulPaint: 3500, // 3.5 seconds
    timeToInteractive: 5000, // 5 seconds
    cumulativeLayoutShift: 0.1,
  },
  'Regular 4G': {
    firstContentfulPaint: 1500, // 1.5 seconds
    largestContentfulPaint: 2500, // 2.5 seconds
    timeToInteractive: 3500, // 3.5 seconds
    cumulativeLayoutShift: 0.1,
  },
  'WiFi': {
    firstContentfulPaint: 1000, // 1 second
    largestContentfulPaint: 2000, // 2 seconds
    timeToInteractive: 3000, // 3 seconds
    cumulativeLayoutShift: 0.1,
  },
  'Offline': {
    firstContentfulPaint: 500, // 0.5 seconds (cached)
    largestContentfulPaint: 1000, // 1 second (cached)
    timeToInteractive: 1500, // 1.5 seconds (cached)
    cumulativeLayoutShift: 0.05,
  },
};

export class NetworkConditionTester {
  private bundleSize: number;
  private resourceCount: number;

  constructor(bundleSize: number, resourceCount: number = 10) {
    this.bundleSize = bundleSize;
    this.resourceCount = resourceCount;
  }

  /**
   * Estimate bundle load time for a given network condition
   */
  estimateBundleLoadTime(condition: NetworkCondition): number {
    if (condition.throughput === 0) {
      // Offline - assume cached resources load instantly
      return 100; // 100ms for cache access
    }

    // Convert throughput from Kbps to bytes per second
    const bytesPerSecond = (condition.throughput * 1024) / 8;
    
    // Base load time for bundle size
    const baseLoadTime = (this.bundleSize / bytesPerSecond) * 1000; // in milliseconds
    
    // Add RTT overhead for resource requests
    const rttOverhead = condition.rtt * this.resourceCount;
    
    // Add connection establishment overhead
    const connectionOverhead = condition.rtt * 2; // TCP handshake
    
    return baseLoadTime + rttOverhead + connectionOverhead;
  }

  /**
   * Estimate performance metrics for a given network condition
   */
  estimatePerformanceMetrics(condition: NetworkCondition): PerformanceMetrics {
    const bundleLoadTime = this.estimateBundleLoadTime(condition);
    
    // Estimate FCP based on critical resource load time
    const criticalResourceSize = this.bundleSize * 0.3; // 30% of bundle is critical
    const criticalLoadTime = condition.throughput > 0 
      ? (criticalResourceSize / ((condition.throughput * 1024) / 8)) * 1000 + condition.rtt
      : 200; // Cached critical resources

    // Estimate LCP based on largest resource load time
    const largestResourceSize = this.bundleSize * 0.15; // 15% of bundle is largest resource
    const largestResourceLoadTime = condition.throughput > 0
      ? (largestResourceSize / ((condition.throughput * 1024) / 8)) * 1000 + condition.rtt * 2
      : 300; // Cached largest resource

    // Estimate TTI based on total JavaScript execution time
    const jsExecutionTime = Math.max(500, this.bundleSize / (1024 * 1024) * 1000); // 1s per MB of JS
    const timeToInteractive = bundleLoadTime + jsExecutionTime;

    // Estimate CLS based on network stability
    const cumulativeLayoutShift = condition.throughput < 1000 ? 0.15 : 0.08;

    // Estimate TBT based on JavaScript size and network speed
    const totalBlockingTime = Math.max(0, jsExecutionTime - 50); // Blocking time above 50ms

    return {
      loadTime: bundleLoadTime,
      firstContentfulPaint: criticalLoadTime,
      largestContentfulPaint: criticalLoadTime + largestResourceLoadTime,
      timeToInteractive,
      cumulativeLayoutShift,
      totalBlockingTime,
    };
  }

  /**
   * Evaluate user experience for given metrics and condition
   */
  evaluateUserExperience(metrics: PerformanceMetrics, condition: NetworkCondition): 'excellent' | 'good' | 'fair' | 'poor' {
    const thresholds = PERFORMANCE_THRESHOLDS[condition.name as keyof typeof PERFORMANCE_THRESHOLDS];
    
    if (!thresholds) {
      return 'fair';
    }

    let score = 0;
    let maxScore = 0;

    // FCP score (25%)
    maxScore += 25;
    if (metrics.firstContentfulPaint <= thresholds.firstContentfulPaint) {
      score += 25;
    } else if (metrics.firstContentfulPaint <= thresholds.firstContentfulPaint * 1.5) {
      score += 15;
    } else if (metrics.firstContentfulPaint <= thresholds.firstContentfulPaint * 2) {
      score += 5;
    }

    // LCP score (25%)
    maxScore += 25;
    if (metrics.largestContentfulPaint <= thresholds.largestContentfulPaint) {
      score += 25;
    } else if (metrics.largestContentfulPaint <= thresholds.largestContentfulPaint * 1.5) {
      score += 15;
    } else if (metrics.largestContentfulPaint <= thresholds.largestContentfulPaint * 2) {
      score += 5;
    }

    // TTI score (25%)
    maxScore += 25;
    if (metrics.timeToInteractive <= thresholds.timeToInteractive) {
      score += 25;
    } else if (metrics.timeToInteractive <= thresholds.timeToInteractive * 1.5) {
      score += 15;
    } else if (metrics.timeToInteractive <= thresholds.timeToInteractive * 2) {
      score += 5;
    }

    // CLS score (25%)
    maxScore += 25;
    if (metrics.cumulativeLayoutShift <= thresholds.cumulativeLayoutShift) {
      score += 25;
    } else if (metrics.cumulativeLayoutShift <= thresholds.cumulativeLayoutShift * 1.5) {
      score += 15;
    } else if (metrics.cumulativeLayoutShift <= thresholds.cumulativeLayoutShift * 2) {
      score += 5;
    }

    const percentage = (score / maxScore) * 100;

    if (percentage >= 90) return 'excellent';
    if (percentage >= 75) return 'good';
    if (percentage >= 50) return 'fair';
    return 'poor';
  }

  /**
   * Test performance across all network conditions
   */
  testAllNetworkConditions(): NetworkTestResult[] {
    return NETWORK_CONDITIONS.map(condition => {
      const metrics = this.estimatePerformanceMetrics(condition);
      const bundleLoadTime = this.estimateBundleLoadTime(condition);
      const estimatedUserExperience = this.evaluateUserExperience(metrics, condition);

      return {
        condition,
        metrics,
        bundleLoadTime,
        estimatedUserExperience,
      };
    });
  }

  /**
   * Generate performance report for all network conditions
   */
  generateNetworkPerformanceReport(): {
    summary: {
      totalConditions: number;
      excellentCount: number;
      goodCount: number;
      fairCount: number;
      poorCount: number;
      overallScore: number;
    };
    results: NetworkTestResult[];
    recommendations: string[];
  } {
    const results = this.testAllNetworkConditions();
    
    const excellentCount = results.filter(r => r.estimatedUserExperience === 'excellent').length;
    const goodCount = results.filter(r => r.estimatedUserExperience === 'good').length;
    const fairCount = results.filter(r => r.estimatedUserExperience === 'fair').length;
    const poorCount = results.filter(r => r.estimatedUserExperience === 'poor').length;

    const overallScore = Math.round(
      (excellentCount * 100 + goodCount * 75 + fairCount * 50 + poorCount * 25) / results.length
    );

    const recommendations: string[] = [];

    // Generate recommendations based on results
    const slowNetworkResults = results.filter(r => 
      r.condition.name === 'Slow 3G' || r.condition.name === 'Fast 3G'
    );

    if (slowNetworkResults.some(r => r.estimatedUserExperience === 'poor')) {
      recommendations.push('Optimize bundle size for slow network conditions');
      recommendations.push('Implement progressive loading for critical resources');
      recommendations.push('Consider service worker caching strategy');
    }

    if (results.some(r => r.metrics.firstContentfulPaint > 2000)) {
      recommendations.push('Optimize critical rendering path');
      recommendations.push('Reduce render-blocking resources');
      recommendations.push('Implement resource preloading');
    }

    if (results.some(r => r.metrics.largestContentfulPaint > 4000)) {
      recommendations.push('Optimize largest contentful element');
      recommendations.push('Implement image lazy loading');
      recommendations.push('Use responsive images with appropriate sizes');
    }

    if (results.some(r => r.metrics.timeToInteractive > 5000)) {
      recommendations.push('Reduce JavaScript execution time');
      recommendations.push('Implement code splitting for non-critical features');
      recommendations.push('Use web workers for heavy computations');
    }

    if (results.some(r => r.metrics.cumulativeLayoutShift > 0.1)) {
      recommendations.push('Set explicit dimensions for images and ads');
      recommendations.push('Avoid inserting content above existing content');
      recommendations.push('Use CSS aspect-ratio for dynamic content');
    }

    return {
      summary: {
        totalConditions: results.length,
        excellentCount,
        goodCount,
        fairCount,
        poorCount,
        overallScore,
      },
      results,
      recommendations: [...new Set(recommendations)], // Remove duplicates
    };
  }
}

/**
 * Utility function to format performance metrics for display
 */
export function formatPerformanceMetrics(metrics: PerformanceMetrics): Record<string, string> {
  return {
    loadTime: `${(metrics.loadTime / 1000).toFixed(2)}s`,
    firstContentfulPaint: `${(metrics.firstContentfulPaint / 1000).toFixed(2)}s`,
    largestContentfulPaint: `${(metrics.largestContentfulPaint / 1000).toFixed(2)}s`,
    timeToInteractive: `${(metrics.timeToInteractive / 1000).toFixed(2)}s`,
    cumulativeLayoutShift: metrics.cumulativeLayoutShift.toFixed(3),
    totalBlockingTime: `${metrics.totalBlockingTime.toFixed(0)}ms`,
  };
}

/**
 * Utility function to get performance grade based on metrics
 */
export function getPerformanceGrade(userExperience: 'excellent' | 'good' | 'fair' | 'poor'): {
  grade: string;
  color: string;
  emoji: string;
} {
  switch (userExperience) {
    case 'excellent':
      return { grade: 'A', color: 'green', emoji: '🟢' };
    case 'good':
      return { grade: 'B', color: 'lightgreen', emoji: '🟡' };
    case 'fair':
      return { grade: 'C', color: 'orange', emoji: '🟠' };
    case 'poor':
      return { grade: 'D', color: 'red', emoji: '🔴' };
    default:
      return { grade: 'F', color: 'darkred', emoji: '❌' };
  }
}