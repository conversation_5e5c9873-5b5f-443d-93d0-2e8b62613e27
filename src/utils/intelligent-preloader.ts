/**
 * Intelligent preloading system based on user behavior patterns
 * Implements predictive loading to improve perceived performance
 */
interface UserBehaviorPattern {
  route: string;
  timestamp: number;
  timeSpent: number;
  nextRoute?: string;
}
interface PreloadStrategy {
  route: string;
  probability: number;
  priority: "high" | "medium" | "low";
  delay: number;
}
interface NavigationPattern {
  from: string;
  to: string;
  count: number;
  avgTimeSpent: number;
}
class IntelligentPreloader {
  private behaviorHistory: UserBehaviorPattern[] = [];
  private navigationPatterns: Map<string, NavigationPattern[]> = new Map();
  private preloadedRoutes: Set<string> = new Set();
  private currentRoute: string = "";
  private routeStartTime: number = 0;
  private readonly STORAGE_KEY = "user-navigation-patterns";
  private readonly MAX_HISTORY_SIZE = 100;
  constructor() {
    this.loadNavigationPatterns();
    this.setupVisibilityChangeHandler();
  }
  /**
   * Track user navigation to build behavior patterns
   */
  trackNavigation(route: string): void {
    const now = Date.now();
    // Record time spent on previous route
    if (this.currentRoute && this.routeStartTime) {
      const timeSpent = now - this.routeStartTime;
      this.recordBehavior(this.currentRoute, timeSpent, route);
    }
    this.currentRoute = route;
    this.routeStartTime = now;
    // Generate preload strategies for this route
    this.generatePreloadStrategies(route);
  }
  /**
   * Record user behavior pattern
   */
  private recordBehavior(route: string, timeSpent: number, nextRoute: string): void {
    const behavior: UserBehaviorPattern = {
      route,
      timestamp: Date.now(),
      timeSpent,
      nextRoute,
    };
    this.behaviorHistory.push(behavior);
    // Limit history size
    if (this.behaviorHistory.length > this.MAX_HISTORY_SIZE) {
      this.behaviorHistory.shift();
    }
    // Update navigation patterns
    this.updateNavigationPatterns(route, nextRoute, timeSpent);
    this.saveNavigationPatterns();
  }
  /**
   * Update navigation patterns based on user behavior
   */
  private updateNavigationPatterns(from: string, to: string, timeSpent: number): void {
    if (!this.navigationPatterns.has(from)) {
      this.navigationPatterns.set(from, []);
    }
    const patterns = this.navigationPatterns.get(from)!;
    const existingPattern = patterns.find(p => p.to === to);
    if (existingPattern) {
      existingPattern.count++;
      existingPattern.avgTimeSpent =
        (existingPattern.avgTimeSpent * (existingPattern.count - 1) + timeSpent) /
        existingPattern.count;
    } else {
      patterns.push({
        from,
        to,
        count: 1,
        avgTimeSpent: timeSpent,
      });
    }
    // Sort patterns by count (most frequent first)
    patterns.sort((a, b) => b.count - a.count);
  }
  /**
   * Generate intelligent preload strategies for a route
   */
  private generatePreloadStrategies(route: string): PreloadStrategy[] {
    const patterns = this.navigationPatterns.get(route) || [];
    const strategies: PreloadStrategy[] = [];
    // Calculate total navigations from this route
    const totalNavigations = patterns.reduce((sum, p) => sum + p.count, 0);
    patterns.forEach(pattern => {
      const probability = pattern.count / totalNavigations;
      // Only preload routes with significant probability
      if (probability >= 0.1) {
        // 10% threshold
        const strategy: PreloadStrategy = {
          route: pattern.to,
          probability,
          priority: this.calculatePriority(probability, pattern.avgTimeSpent),
          delay: this.calculateDelay(probability, pattern.avgTimeSpent),
        };
        strategies.push(strategy);
      }
    });
    // Execute preload strategies
    this.executePreloadStrategies(strategies);
    return strategies;
  }
  /**
   * Calculate preload priority based on probability and user behavior
   */
  private calculatePriority(probability: number, avgTimeSpent: number): "high" | "medium" | "low" {
    // High priority: high probability and user spends time on current route
    if (probability >= 0.5 && avgTimeSpent >= 5000) return "high";
    // Medium priority: moderate probability or quick navigation
    if (probability >= 0.3 || avgTimeSpent < 2000) return "medium";
    // Low priority: everything else
    return "low";
  }
  /**
   * Calculate preload delay based on user behavior
   */
  private calculateDelay(probability: number, avgTimeSpent: number): number {
    // High probability routes: preload quickly
    if (probability >= 0.7) return 500;
    // Medium probability: wait a bit
    if (probability >= 0.4) return 1000;
    // Low probability: wait longer or until user shows intent
    return Math.min(avgTimeSpent * 0.5, 3000);
  }
  /**
   * Execute preload strategies
   */
  private executePreloadStrategies(strategies: PreloadStrategy[]): void {
    strategies.forEach(strategy => {
      // Don't preload if already preloaded
      if (this.preloadedRoutes.has(strategy.route)) return;
      setTimeout(() => {
        this.preloadRoute(strategy.route, strategy.priority);
      }, strategy.delay);
    });
  }
  /**
   * Preload a specific route
   */
  private async preloadRoute(route: string, priority: "high" | "medium" | "low"): Promise<void> {
    if (this.preloadedRoutes.has(route)) return;
    try {
      // Map route to import function
      const importFn = this.getRouteImportFunction(route);
      if (!importFn) return;
      if (import.meta.env.MODE === "development") {
        // Condition handled
      }
      await importFn();
      this.preloadedRoutes.add(route);
      if (import.meta.env.MODE === "development") {
        // Condition handled
      }
    } catch (error) {
      if (import.meta.env.MODE === "development") {
        // Condition handled
      }
    }
  }
  /**
   * Get import function for a route
   */
  private getRouteImportFunction(route: string): (() => Promise<any>) | null {
    const routeMap: Record<string, () => Promise<any>> = {
      "/": () => import("@/pages/Index"),
      "/dashboard": () => import("@/pages/Dashboard"),
      "/login": () => import("@/pages/Login"),
      "/signup": () => import("@/pages/Signup"),
      "/risks": () => import("@/pages/RiskRegister"),
      "/risks/create": () => import("@/pages/RiskCreate"),
      "/risks/templates": () => import("@/pages/RiskTemplates"),
      "/incidents": () => import("@/pages/Incidents"),
      "/incidents/new": () => import("@/pages/IncidentCreate"),
      "/reports": () => import("@/pages/Reports"),
      "/administration": () => import("@/pages/Administration"),
      "/organization": () => import("@/pages/OrganizationPage"),
      "/organization/management": () => import("@/pages/OrganizationManagement"),
      "/policies": () => import("@/pages/Policies"),
      "/profile": () => import("@/pages/Profile"),
    };
    // Handle dynamic routes (e.g., /risks/:id, /incidents/:id)
    if (route.startsWith("/risks/") && route !== "/risks/create" && route !== "/risks/templates") {
      return () => import("@/pages/RiskDetails");
    }
    if (route.startsWith("/incidents/") && !route.endsWith("/new") && !route.endsWith("/edit")) {
      return () => import("@/pages/IncidentDetails");
    }
    if (route.includes("/edit")) {
      return () => import("@/pages/IncidentEdit");
    }
    return routeMap[route] || null;
  }
  /**
   * Preload based on user interaction (hover, focus)
   */
  preloadOnInteraction(route: string): void {
    // Immediate preload for user interaction
    setTimeout(() => {
      this.preloadRoute(route, "high");
    }, 100);
  }
  /**
   * Preload based on scroll position or viewport visibility
   */
  preloadOnVisibility(route: string): void {
    // Preload when link becomes visible
    setTimeout(() => {
      this.preloadRoute(route, "medium");
    }, 300);
  }
  /**
   * Get navigation analytics for debugging
   */
  getAnalytics(): {
    totalNavigations: number;
    mostFrequentRoutes: string[];
    navigationPatterns: Array<{ from: string; to: string; count: number; probability: number }>;
    preloadedRoutes: string[];
  } {
    const totalNavigations = this.behaviorHistory.length;
    // Get most frequent routes
    const routeCounts = new Map<string, number>();
    this.behaviorHistory.forEach(behavior => {
      routeCounts.set(behavior.route, (routeCounts.get(behavior.route) || 0) + 1);
    });
    const mostFrequentRoutes = Array.from(routeCounts.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([route]) => route);
    // Get navigation patterns with probabilities
    const navigationPatterns: Array<{
      from: string;
      to: string;
      count: number;
      probability: number;
    }> = [];
    this.navigationPatterns.forEach((patterns, from) => {
      const totalFromRoute = patterns.reduce((sum, p) => sum + p.count, 0);
      patterns.forEach(pattern => {
        navigationPatterns.push({
          from,
          to: pattern.to,
          count: pattern.count,
          probability: pattern.count / totalFromRoute,
        });
      });
    });
    return {
      totalNavigations,
      mostFrequentRoutes,
      navigationPatterns: navigationPatterns.sort((a, b) => b.probability - a.probability),
      preloadedRoutes: Array.from(this.preloadedRoutes),
    };
  }
  /**
   * Load navigation patterns from storage
   */
  private loadNavigationPatterns(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const data = JSON.parse(stored);
        this.navigationPatterns = new Map(data.patterns ?? []);
        this.behaviorHistory = data.history ?? [];
      }
    } catch (error) {
      // Silently fail if localStorage is not available
    }
  }
  /**
   * Save navigation patterns to storage
   */
  private saveNavigationPatterns(): void {
    try {
      const data = {
        patterns: Array.from(this.navigationPatterns.entries()),
        history: this.behaviorHistory.slice(-50), // Keep only recent history
      };
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(data));
    } catch (error) {
      // Silently fail if localStorage is not available
    }
  }
  /**
   * Setup visibility change handler to pause tracking when tab is not active
   */
  private setupVisibilityChangeHandler(): void {
    if (typeof document !== "undefined") {
      document.addEventListener("visibilitychange", () => {
        if (document.hidden) {
          // Pause tracking when tab is hidden
          if (this.currentRoute && this.routeStartTime) {
            const timeSpent = Date.now() - this.routeStartTime;
            // Don't record very short times when tab becomes hidden
            if (timeSpent > 1000) {
              this.recordBehavior(this.currentRoute, timeSpent, "");
            }
          }
        } else {
          // Resume tracking when tab becomes visible
          this.routeStartTime = Date.now();
        }
      });
    }
  }
  /**
   * Clear all stored patterns (for testing or reset)
   */
  clearPatterns(): void {
    this.navigationPatterns.clear();
    this.behaviorHistory = [];
    this.preloadedRoutes.clear();
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      // Silently fail
    }
  }
}
// Create singleton instance
export const intelligentPreloader = new IntelligentPreloader();
export default intelligentPreloader;
