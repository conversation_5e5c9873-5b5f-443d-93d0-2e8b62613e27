import { describe, it, expect } from 'vitest';
import { RiskSeverity } from '@/types';
import {
  getSeverityColor,
  capitalizeFirst,
  truncateText,
  formatPercentage,
  getInitials,
  formatFileSize,
  generateClassNames,
} from '../uiUtils';

describe('UI Utilities', () => {
  describe('getSeverityColor', () => {
    it('should return correct colors for valid severity levels', () => {
      expect(getSeverityColor(RiskSeverity.LOW)).toBe('bg-risk-low border-risk-low hover:bg-risk-low/90');
      expect(getSeverityColor(RiskSeverity.MEDIUM)).toBe('bg-risk-medium border-risk-medium hover:bg-risk-medium/90');
      expect(getSeverityColor(RiskSeverity.HIGH)).toBe('bg-risk-high border-risk-high hover:bg-risk-high/90');
      expect(getSeverityColor(RiskSeverity.CRITICAL)).toBe('bg-risk-critical border-risk-critical hover:bg-risk-critical/90');
    });

    it('should return fallback for invalid severity', () => {
      const invalidSeverity = 'INVALID' as RiskSeverity;
      const result = getSeverityColor(invalidSeverity);
      
      expect(result).toBe('bg-gray-500 border-gray-500 hover:bg-gray-500/90');
    });

    it('should return custom fallback when provided', () => {
      const invalidSeverity = 'INVALID' as RiskSeverity;
      const customFallback = 'bg-blue-500';
      const result = getSeverityColor(invalidSeverity, { fallback: customFallback });
      
      expect(result).toBe(customFallback);
    });

    it('should throw error when throwOnError is true', () => {
      const invalidSeverity = 'INVALID' as RiskSeverity;
      
      expect(() => {
        getSeverityColor(invalidSeverity, { throwOnError: true });
      }).toThrow('Invalid risk severity: INVALID');
    });
  });

  describe('capitalizeFirst', () => {
    it('should capitalize first letter of valid strings', () => {
      expect(capitalizeFirst('hello world')).toBe('Hello world');
      expect(capitalizeFirst('test')).toBe('Test');
      expect(capitalizeFirst('UPPERCASE')).toBe('UPPERCASE');
    });

    it('should handle empty strings', () => {
      expect(capitalizeFirst('')).toBe('');
      expect(capitalizeFirst('   ')).toBe('');
    });

    it('should return fallback for null/undefined input', () => {
      expect(capitalizeFirst(null)).toBe('');
      expect(capitalizeFirst(undefined)).toBe('');
      expect(capitalizeFirst(null, { fallback: 'N/A' })).toBe('N/A');
    });

    it('should handle single character strings', () => {
      expect(capitalizeFirst('a')).toBe('A');
      expect(capitalizeFirst('Z')).toBe('Z');
    });
  });

  describe('truncateText', () => {
    it('should truncate long text with ellipsis', () => {
      const longText = 'This is a very long text that should be truncated';
      const result = truncateText(longText, { maxLength: 20 });
      
      expect(result).toBe('This is a very lo...');
      expect(result.length).toBe(20);
    });

    it('should not truncate short text', () => {
      const shortText = 'Short text';
      const result = truncateText(shortText, { maxLength: 20 });
      
      expect(result).toBe(shortText);
    });

    it('should handle exact length text', () => {
      const exactText = '1234567890';
      const result = truncateText(exactText, { maxLength: 10 });
      
      expect(result).toBe(exactText);
    });

    it('should return fallback for null/undefined input', () => {
      expect(truncateText(null)).toBe('');
      expect(truncateText(undefined)).toBe('');
      expect(truncateText(null, { fallback: 'N/A' })).toBe('N/A');
    });

    it('should use default maxLength when not specified', () => {
      const longText = 'a'.repeat(60);
      const result = truncateText(longText);
      
      expect(result.length).toBe(50);
      expect(result.endsWith('...')).toBe(true);
    });
  });

  describe('formatPercentage', () => {
    it('should format decimal numbers as percentages', () => {
      expect(formatPercentage(0.75)).toBe('75%');
      expect(formatPercentage(0.5)).toBe('50%');
      expect(formatPercentage(1.0)).toBe('100%');
      expect(formatPercentage(1.5)).toBe('150%');
    });

    it('should handle zero and negative numbers', () => {
      expect(formatPercentage(0)).toBe('0%');
      expect(formatPercentage(-0.25)).toBe('-25%');
    });

    it('should return fallback for null/undefined', () => {
      expect(formatPercentage(null)).toBe('0%');
      expect(formatPercentage(undefined)).toBe('0%');
      expect(formatPercentage(null, { fallback: 'N/A' })).toBe('N/A');
    });

    it('should handle invalid numbers', () => {
      expect(formatPercentage(NaN)).toBe('0%');
      expect(formatPercentage(NaN, { fallback: 'Invalid' })).toBe('Invalid');
    });

    it('should round to nearest integer', () => {
      expect(formatPercentage(0.756)).toBe('76%');
      expect(formatPercentage(0.754)).toBe('75%');
    });
  });

  describe('getInitials', () => {
    it('should generate initials from full names', () => {
      expect(getInitials('John Doe')).toBe('JD');
      expect(getInitials('Jane Smith Johnson')).toBe('JJ'); // First and last name
      expect(getInitials('Mary Elizabeth Watson')).toBe('MW');
    });

    it('should handle single names', () => {
      expect(getInitials('John')).toBe('J');
      expect(getInitials('Madonna')).toBe('M');
    });

    it('should handle empty strings and whitespace', () => {
      expect(getInitials('')).toBe('');
      expect(getInitials('   ')).toBe('');
      expect(getInitials('  John  Doe  ')).toBe('JD');
    });

    it('should return fallback for null/undefined input', () => {
      expect(getInitials(null)).toBe('');
      expect(getInitials(undefined)).toBe('');
      expect(getInitials(null, { fallback: '??' })).toBe('??');
    });

    it('should handle names with multiple spaces', () => {
      expect(getInitials('John    Doe')).toBe('JD');
      expect(getInitials('  Mary   Jane   Watson  ')).toBe('MW');
    });

    it('should convert to uppercase', () => {
      expect(getInitials('john doe')).toBe('JD');
      expect(getInitials('mary jane')).toBe('MJ');
    });
  });

  describe('formatFileSize', () => {
    it('should format bytes correctly', () => {
      expect(formatFileSize(0)).toBe('0 B');
      expect(formatFileSize(512)).toBe('512.0 B');
      expect(formatFileSize(1024)).toBe('1.0 KB');
      expect(formatFileSize(1536)).toBe('1.5 KB');
    });

    it('should format larger sizes correctly', () => {
      expect(formatFileSize(1048576)).toBe('1.0 MB');
      expect(formatFileSize(1073741824)).toBe('1.0 GB');
      expect(formatFileSize(1099511627776)).toBe('1.0 TB');
    });

    it('should handle decimal values', () => {
      expect(formatFileSize(1536)).toBe('1.5 KB');
      expect(formatFileSize(2621440)).toBe('2.5 MB');
    });

    it('should return fallback for null/undefined', () => {
      expect(formatFileSize(null)).toBe('0 B');
      expect(formatFileSize(undefined)).toBe('0 B');
      expect(formatFileSize(null, { fallback: 'Unknown' })).toBe('Unknown');
    });

    it('should handle invalid numbers', () => {
      expect(formatFileSize(-100)).toBe('0 B');
      expect(formatFileSize(NaN)).toBe('0 B');
      expect(formatFileSize(-100, { fallback: 'Invalid' })).toBe('Invalid');
    });
  });

  describe('generateClassNames', () => {
    it('should generate class names from object with boolean values', () => {
      const classes = {
        'active': true,
        'disabled': false,
        'primary': true,
        'secondary': false
      };
      
      const result = generateClassNames(classes);
      expect(result).toBe('active primary');
    });

    it('should handle empty objects', () => {
      expect(generateClassNames({})).toBe('');
    });

    it('should handle all false values', () => {
      const classes = {
        'active': false,
        'disabled': false,
        'primary': false
      };
      
      expect(generateClassNames(classes)).toBe('');
    });

    it('should handle all true values', () => {
      const classes = {
        'active': true,
        'disabled': true,
        'primary': true
      };
      
      expect(generateClassNames(classes)).toBe('active disabled primary');
    });

    it('should handle truthy and falsy values', () => {
      const classes = {
        'active': 1,
        'disabled': 0,
        'primary': 'yes',
        'secondary': '',
        'tertiary': null,
        'quaternary': undefined
      };
      
      const result = generateClassNames(classes);
      expect(result).toBe('active primary');
    });

    it('should return empty string for invalid input', () => {
      expect(generateClassNames(null as any)).toBe('');
      expect(generateClassNames(undefined as any)).toBe('');
      expect(generateClassNames('invalid' as any)).toBe('');
    });
  });
});