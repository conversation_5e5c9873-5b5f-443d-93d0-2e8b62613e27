import { describe, it, expect, vi } from 'vitest';
import { z } from 'zod';
import {
  validateFormData,
  isFormElement,
  isInputElement,
  isTextAreaElement,
  isSelectElement,
  getEventTargetValue,
  getEventTargetChecked,
  createFormSubmissionHandler,
  createInputChangeHandler,
  createCheckboxChangeHandler,
  validateField,
  commonFieldSchemas,
} from '../type-validation';

describe('Type Validation Utilities', () => {
  describe('validateFormData', () => {
    const testSchema = z.object({
      name: z.string().min(1),
      email: z.string().email(),
    });

    it('should validate correct form data', () => {
      const data = { name: '<PERSON>', email: '<EMAIL>' };
      const result = validateFormData(data, testSchema);

      expect(result.isValid).toBe(true);
      expect(result.errors).toEqual([]);
      expect(result.data).toEqual(data);
    });

    it('should reject invalid form data', () => {
      const data = { name: '', email: 'invalid-email' };
      const result = validateFormData(data, testSchema);

      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(2);
      expect(result.data).toBeUndefined();
    });
  });

  describe('Type Guards', () => {
    it('should identify form elements correctly', () => {
      const formElement = document.createElement('form');
      const divElement = document.createElement('div');

      expect(isFormElement(formElement)).toBe(true);
      expect(isFormElement(divElement)).toBe(false);
      expect(isFormElement(null)).toBe(false);
    });

    it('should identify input elements correctly', () => {
      const inputElement = document.createElement('input');
      const divElement = document.createElement('div');

      expect(isInputElement(inputElement)).toBe(true);
      expect(isInputElement(divElement)).toBe(false);
      expect(isInputElement(null)).toBe(false);
    });

    it('should identify textarea elements correctly', () => {
      const textareaElement = document.createElement('textarea');
      const divElement = document.createElement('div');

      expect(isTextAreaElement(textareaElement)).toBe(true);
      expect(isTextAreaElement(divElement)).toBe(false);
      expect(isTextAreaElement(null)).toBe(false);
    });

    it('should identify select elements correctly', () => {
      const selectElement = document.createElement('select');
      const divElement = document.createElement('div');

      expect(isSelectElement(selectElement)).toBe(true);
      expect(isSelectElement(divElement)).toBe(false);
      expect(isSelectElement(null)).toBe(false);
    });
  });

  describe('Event Handlers', () => {
    it('should extract value from change events', () => {
      const input = document.createElement('input');
      input.value = 'test value';

      const event = {
        target: input,
      } as React.ChangeEvent<HTMLInputElement>;

      expect(getEventTargetValue(event)).toBe('test value');
    });

    it('should extract checked state from checkbox events', () => {
      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.checked = true;

      const event = {
        target: checkbox,
      } as React.ChangeEvent<HTMLInputElement>;

      expect(getEventTargetChecked(event)).toBe(true);
    });

    it('should create input change handler', () => {
      const setter = vi.fn();
      const handler = createInputChangeHandler(setter);

      const input = document.createElement('input');
      input.value = 'new value';

      const event = {
        target: input,
      } as React.ChangeEvent<HTMLInputElement>;

      handler(event);
      expect(setter).toHaveBeenCalledWith('new value');
    });

    it('should create checkbox change handler', () => {
      const setter = vi.fn();
      const handler = createCheckboxChangeHandler(setter);

      const checkbox = document.createElement('input');
      checkbox.type = 'checkbox';
      checkbox.checked = true;

      const event = {
        target: checkbox,
      } as React.ChangeEvent<HTMLInputElement>;

      handler(event);
      expect(setter).toHaveBeenCalledWith(true);
    });
  });

  describe('Field Validation', () => {
    it('should validate email field correctly', () => {
      const validEmail = validateField('<EMAIL>', commonFieldSchemas.email);
      expect(validEmail.isValid).toBe(true);
      expect(validEmail.value).toBe('<EMAIL>');

      const invalidEmail = validateField('invalid-email', commonFieldSchemas.email);
      expect(invalidEmail.isValid).toBe(false);
      expect(invalidEmail.error).toContain('Invalid email format');
    });

    it('should validate password field correctly', () => {
      const validPassword = validateField('password123', commonFieldSchemas.password);
      expect(validPassword.isValid).toBe(true);
      expect(validPassword.value).toBe('password123');

      const invalidPassword = validateField('123', commonFieldSchemas.password);
      expect(invalidPassword.isValid).toBe(false);
      expect(invalidPassword.error).toContain('Password must be at least 6 characters');
    });

    it('should validate name field correctly', () => {
      const validName = validateField('John Doe', commonFieldSchemas.name);
      expect(validName.isValid).toBe(true);
      expect(validName.value).toBe('John Doe');

      const invalidName = validateField('', commonFieldSchemas.name);
      expect(invalidName.isValid).toBe(false);
      expect(invalidName.error).toContain('Name is required');
    });

    it('should validate URL field correctly', () => {
      const validUrl = validateField('https://example.com', commonFieldSchemas.url);
      expect(validUrl.isValid).toBe(true);
      expect(validUrl.value).toBe('https://example.com');

      const invalidUrl = validateField('not-a-url', commonFieldSchemas.url);
      expect(invalidUrl.isValid).toBe(false);
      expect(invalidUrl.error).toContain('Invalid URL format');
    });

    it('should validate date field correctly', () => {
      const validDate = validateField('2024-01-15', commonFieldSchemas.date);
      expect(validDate.isValid).toBe(true);
      expect(validDate.value).toBe('2024-01-15');

      const invalidDate = validateField('15/01/2024', commonFieldSchemas.date);
      expect(invalidDate.isValid).toBe(false);
      expect(invalidDate.error).toContain('Invalid date format');
    });
  });

  describe('Form Submission Handler', () => {
    it('should create type-safe form submission handler', async () => {
      const schema = z.object({
        name: z.string().min(1),
        email: z.string().email(),
      });

      const mockHandler = vi.fn();
      const formHandler = createFormSubmissionHandler(mockHandler, schema);

      // Mock form element and FormData
      const form = document.createElement('form');
      const formData = new FormData();
      formData.append('name', 'John Doe');
      formData.append('email', '<EMAIL>');

      // Mock FormData constructor
      const originalFormData = global.FormData;
      global.FormData = vi.fn().mockImplementation(() => formData);

      const event = {
        preventDefault: vi.fn(),
        currentTarget: form,
      } as unknown as React.FormEvent<HTMLFormElement>;

      await formHandler(event);

      expect(event.preventDefault).toHaveBeenCalled();
      expect(mockHandler).toHaveBeenCalledWith({
        name: 'John Doe',
        email: '<EMAIL>',
      });

      // Restore original FormData
      global.FormData = originalFormData;
    });
  });
});