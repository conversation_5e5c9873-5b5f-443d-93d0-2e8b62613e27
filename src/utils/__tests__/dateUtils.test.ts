import { describe, it, expect, vi } from 'vitest'
import {
  formatDate,
  formatDateTime,
  formatRelativeTime,
  isOverdue,
  getDaysUntilDue,
  getDateRange,
  parseDate,
  isValidDate,
  formatDateForInput,
  getStartOfDay,
  getEndOfDay,
  addDays,
  subtractDays,
  isSameDay,
  isWithinRange,
} from '../dateUtils'

describe('Date Utilities', () => {
  // Mock current date for consistent testing
  const mockCurrentDate = new Date('2024-06-15T10:30:00Z')
  
  beforeEach(() => {
    vi.useFakeTimers()
    vi.setSystemTime(mockCurrentDate)
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('formatDate', () => {
    it('should format dates correctly', () => {
      const testDate = new Date('2024-01-15T10:30:00Z')
      
      expect(formatDate(testDate)).toBe('Jan 15, 2024')
      expect(formatDate(testDate, 'short')).toBe('1/15/24')
      expect(formatDate(testDate, 'long')).toBe('January 15, 2024')
    })

    it('should handle invalid dates', () => {
      expect(formatDate(null)).toBe('Invalid Date')
      expect(formatDate(undefined)).toBe('Invalid Date')
      expect(formatDate(new Date('invalid'))).toBe('Invalid Date')
    })

    it('should handle string dates', () => {
      expect(formatDate('2024-01-15')).toBe('Jan 15, 2024')
      expect(formatDate('invalid-date')).toBe('Invalid Date')
    })
  })

  describe('formatDateTime', () => {
    it('should format date and time correctly', () => {
      const testDate = new Date('2024-01-15T14:30:00Z')
      
      expect(formatDateTime(testDate)).toBe('Jan 15, 2024 at 2:30 PM')
      expect(formatDateTime(testDate, true)).toBe('Jan 15, 2024 at 14:30')
    })

    it('should handle invalid dates', () => {
      expect(formatDateTime(null)).toBe('Invalid Date')
      expect(formatDateTime(undefined)).toBe('Invalid Date')
    })
  })

  describe('formatRelativeTime', () => {
    it('should format relative time correctly', () => {
      const now = new Date('2024-06-15T10:30:00Z')
      const oneHourAgo = new Date('2024-06-15T09:30:00Z')
      const oneDayAgo = new Date('2024-06-14T10:30:00Z')
      const oneWeekAgo = new Date('2024-06-08T10:30:00Z')
      const oneMonthAgo = new Date('2024-05-15T10:30:00Z')
      
      expect(formatRelativeTime(oneHourAgo)).toBe('1 hour ago')
      expect(formatRelativeTime(oneDayAgo)).toBe('1 day ago')
      expect(formatRelativeTime(oneWeekAgo)).toBe('1 week ago')
      expect(formatRelativeTime(oneMonthAgo)).toBe('1 month ago')
    })

    it('should handle future dates', () => {
      const oneHourFromNow = new Date('2024-06-15T11:30:00Z')
      const oneDayFromNow = new Date('2024-06-16T10:30:00Z')
      
      expect(formatRelativeTime(oneHourFromNow)).toBe('in 1 hour')
      expect(formatRelativeTime(oneDayFromNow)).toBe('in 1 day')
    })

    it('should handle very recent times', () => {
      const thirtySecondsAgo = new Date('2024-06-15T10:29:30Z')
      
      expect(formatRelativeTime(thirtySecondsAgo)).toBe('just now')
    })
  })

  describe('isOverdue', () => {
    it('should correctly identify overdue dates', () => {
      const pastDate = new Date('2024-06-14T10:30:00Z')
      const futureDate = new Date('2024-06-16T10:30:00Z')
      const todayDate = new Date('2024-06-15T10:30:00Z')
      
      expect(isOverdue(pastDate)).toBe(true)
      expect(isOverdue(futureDate)).toBe(false)
      expect(isOverdue(todayDate)).toBe(false)
    })

    it('should handle null/undefined dates', () => {
      expect(isOverdue(null)).toBe(false)
      expect(isOverdue(undefined)).toBe(false)
    })

    it('should handle string dates', () => {
      expect(isOverdue('2024-06-14')).toBe(true)
      expect(isOverdue('2024-06-16')).toBe(false)
    })
  })

  describe('getDaysUntilDue', () => {
    it('should calculate days until due correctly', () => {
      const threeDaysFromNow = new Date('2024-06-18T10:30:00Z')
      const twoDaysAgo = new Date('2024-06-13T10:30:00Z')
      const today = new Date('2024-06-15T10:30:00Z')
      
      expect(getDaysUntilDue(threeDaysFromNow)).toBe(3)
      expect(getDaysUntilDue(twoDaysAgo)).toBe(-2)
      expect(getDaysUntilDue(today)).toBe(0)
    })

    it('should handle null/undefined dates', () => {
      expect(getDaysUntilDue(null)).toBe(0)
      expect(getDaysUntilDue(undefined)).toBe(0)
    })
  })

  describe('getDateRange', () => {
    it('should generate correct date ranges', () => {
      const startDate = new Date('2024-06-15')
      const endDate = new Date('2024-06-17')
      
      const range = getDateRange(startDate, endDate)
      
      expect(range).toHaveLength(3)
      expect(range[0]).toEqual(new Date('2024-06-15'))
      expect(range[1]).toEqual(new Date('2024-06-16'))
      expect(range[2]).toEqual(new Date('2024-06-17'))
    })

    it('should handle single day range', () => {
      const singleDate = new Date('2024-06-15')
      
      const range = getDateRange(singleDate, singleDate)
      
      expect(range).toHaveLength(1)
      expect(range[0]).toEqual(singleDate)
    })

    it('should handle reversed dates', () => {
      const startDate = new Date('2024-06-17')
      const endDate = new Date('2024-06-15')
      
      const range = getDateRange(startDate, endDate)
      
      expect(range).toHaveLength(0)
    })
  })

  describe('parseDate', () => {
    it('should parse various date formats', () => {
      expect(parseDate('2024-06-15')).toEqual(new Date('2024-06-15'))
      expect(parseDate('2024-06-15T10:30:00Z')).toEqual(new Date('2024-06-15T10:30:00Z'))
      expect(parseDate(new Date('2024-06-15'))).toEqual(new Date('2024-06-15'))
    })

    it('should handle invalid dates', () => {
      expect(parseDate('invalid-date')).toBeNull()
      expect(parseDate(null)).toBeNull()
      expect(parseDate(undefined)).toBeNull()
    })
  })

  describe('isValidDate', () => {
    it('should validate dates correctly', () => {
      expect(isValidDate(new Date('2024-06-15'))).toBe(true)
      expect(isValidDate('2024-06-15')).toBe(true)
      expect(isValidDate(new Date('invalid'))).toBe(false)
      expect(isValidDate('invalid-date')).toBe(false)
      expect(isValidDate(null)).toBe(false)
      expect(isValidDate(undefined)).toBe(false)
    })
  })

  describe('formatDateForInput', () => {
    it('should format dates for HTML input elements', () => {
      const testDate = new Date('2024-06-15T10:30:00Z')
      
      expect(formatDateForInput(testDate)).toBe('2024-06-15')
      expect(formatDateForInput(testDate, 'datetime-local')).toBe('2024-06-15T10:30')
    })

    it('should handle invalid dates', () => {
      expect(formatDateForInput(null)).toBe('')
      expect(formatDateForInput(undefined)).toBe('')
    })
  })

  describe('getStartOfDay', () => {
    it('should get start of day correctly', () => {
      const testDate = new Date('2024-06-15T14:30:45Z')
      const startOfDay = getStartOfDay(testDate)
      
      expect(startOfDay.getHours()).toBe(0)
      expect(startOfDay.getMinutes()).toBe(0)
      expect(startOfDay.getSeconds()).toBe(0)
      expect(startOfDay.getMilliseconds()).toBe(0)
    })
  })

  describe('getEndOfDay', () => {
    it('should get end of day correctly', () => {
      const testDate = new Date('2024-06-15T14:30:45Z')
      const endOfDay = getEndOfDay(testDate)
      
      expect(endOfDay.getHours()).toBe(23)
      expect(endOfDay.getMinutes()).toBe(59)
      expect(endOfDay.getSeconds()).toBe(59)
      expect(endOfDay.getMilliseconds()).toBe(999)
    })
  })

  describe('addDays', () => {
    it('should add days correctly', () => {
      const testDate = new Date('2024-06-15')
      const result = addDays(testDate, 5)
      
      expect(result).toEqual(new Date('2024-06-20'))
    })

    it('should handle negative days', () => {
      const testDate = new Date('2024-06-15')
      const result = addDays(testDate, -5)
      
      expect(result).toEqual(new Date('2024-06-10'))
    })
  })

  describe('subtractDays', () => {
    it('should subtract days correctly', () => {
      const testDate = new Date('2024-06-15')
      const result = subtractDays(testDate, 5)
      
      expect(result).toEqual(new Date('2024-06-10'))
    })
  })

  describe('isSameDay', () => {
    it('should compare days correctly', () => {
      const date1 = new Date('2024-06-15T10:30:00Z')
      const date2 = new Date('2024-06-15T14:45:00Z')
      const date3 = new Date('2024-06-16T10:30:00Z')
      
      expect(isSameDay(date1, date2)).toBe(true)
      expect(isSameDay(date1, date3)).toBe(false)
    })

    it('should handle null/undefined dates', () => {
      const testDate = new Date('2024-06-15')
      
      expect(isSameDay(testDate, null)).toBe(false)
      expect(isSameDay(null, testDate)).toBe(false)
      expect(isSameDay(null, null)).toBe(false)
    })
  })

  describe('isWithinRange', () => {
    it('should check if date is within range', () => {
      const testDate = new Date('2024-06-15')
      const startDate = new Date('2024-06-10')
      const endDate = new Date('2024-06-20')
      const outsideDate = new Date('2024-06-25')
      
      expect(isWithinRange(testDate, startDate, endDate)).toBe(true)
      expect(isWithinRange(outsideDate, startDate, endDate)).toBe(false)
    })

    it('should handle inclusive boundaries', () => {
      const startDate = new Date('2024-06-15')
      const endDate = new Date('2024-06-20')
      
      expect(isWithinRange(startDate, startDate, endDate)).toBe(true)
      expect(isWithinRange(endDate, startDate, endDate)).toBe(true)
    })
  })
})
