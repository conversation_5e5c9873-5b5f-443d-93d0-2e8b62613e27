import { describe, it, expect } from 'vitest';
import {
  generateJSDocComment,
  commonTemplates,
  validateJSDocTemplate,
  type JSDocTemplate,
} from '../documentation/jsdoc-templates';

describe('JSDoc Templates', () => {
  describe('generateJSDocComment', () => {
    it('should generate basic JSDoc comment', () => {
      const template: JSDocTemplate = {
        description: 'Test function description',
        params: [
          { name: 'input', type: 'string', description: 'Input parameter' },
          { name: 'options', type: 'object', description: 'Options parameter', optional: true }
        ],
        returns: { type: 'string', description: 'Return value description' }
      };

      const result = generateJSDocComment(template);

      expect(result).toContain('* Test function description');
      expect(result).toContain('* @param {string} input - Input parameter');
      expect(result).toContain('* @param {object} options? - Options parameter');
      expect(result).toContain('* @returns {string} Return value description');
    });

    it('should include examples when provided', () => {
      const template: JSDocTemplate = {
        description: 'Test function',
        params: [],
        returns: { type: 'void', description: 'No return value' },
        examples: [
          'testFunction() // Basic usage',
          'testFunction({ option: true }) // With options'
        ]
      };

      const result = generateJSDocComment(template);

      expect(result).toContain('* @example');
      expect(result).toContain('* testFunction() // Basic usage');
      expect(result).toContain('* testFunction({ option: true }) // With options');
    });

    it('should include see references when provided', () => {
      const template: JSDocTemplate = {
        description: 'Test function',
        params: [],
        returns: { type: 'void', description: 'No return value' },
        see: ['relatedFunction', 'anotherFunction']
      };

      const result = generateJSDocComment(template);

      expect(result).toContain('* @see relatedFunction');
      expect(result).toContain('* @see anotherFunction');
    });

    it('should include since information when provided', () => {
      const template: JSDocTemplate = {
        description: 'Test function',
        params: [],
        returns: { type: 'void', description: 'No return value' },
        since: '1.0.0'
      };

      const result = generateJSDocComment(template);

      expect(result).toContain('* @since 1.0.0');
    });

    it('should include deprecation notice when provided', () => {
      const template: JSDocTemplate = {
        description: 'Test function',
        params: [],
        returns: { type: 'void', description: 'No return value' },
        deprecated: 'Use newFunction instead'
      };

      const result = generateJSDocComment(template);

      expect(result).toContain('* @deprecated Use newFunction instead');
    });

    it('should include throws information when provided', () => {
      const template: JSDocTemplate = {
        description: 'Test function',
        params: [],
        returns: { type: 'void', description: 'No return value' },
        throws: [
          { type: 'ValidationError', description: 'When input is invalid' },
          { type: 'ParseError', description: 'When parsing fails' }
        ]
      };

      const result = generateJSDocComment(template);

      expect(result).toContain('* @throws {ValidationError} When input is invalid');
      expect(result).toContain('* @throws {ParseError} When parsing fails');
    });

    it('should include default values for optional parameters', () => {
      const template: JSDocTemplate = {
        description: 'Test function',
        params: [
          { 
            name: 'format', 
            type: 'string', 
            description: 'Format string', 
            optional: true, 
            defaultValue: 'default' 
          }
        ],
        returns: { type: 'string', description: 'Formatted result' }
      };

      const result = generateJSDocComment(template);

      expect(result).toContain('* @param {string} format? - Format string - Default: default');
    });
  });

  describe('Common Templates', () => {
    describe('dateFormatter', () => {
      it('should generate date formatter template', () => {
        const template = commonTemplates.dateFormatter('formatDate', 'Formats a date string');

        expect(template.description).toBe('Formats a date string');
        expect(template.params).toHaveLength(2);
        expect(template.params[0].name).toBe('date');
        expect(template.params[0].type).toBe('DateInput');
        expect(template.params[1].name).toBe('options');
        expect(template.params[1].optional).toBe(true);
        expect(template.returns.type).toBe('string');
        expect(template.examples).toHaveLength(3);
        expect(template.throws).toHaveLength(1);
      });
    });

    describe('validator', () => {
      it('should generate validator template', () => {
        const template = commonTemplates.validator('isString', 'Validates if value is a string', 'string');

        expect(template.description).toBe('Validates if value is a string');
        expect(template.params).toHaveLength(2);
        expect(template.params[0].name).toBe('value');
        expect(template.params[0].type).toBe('unknown');
        expect(template.returns.type).toBe('value is string');
        expect(template.examples).toHaveLength(2);
      });
    });

    describe('transformer', () => {
      it('should generate transformer template', () => {
        const template = commonTemplates.transformer('parseDate', 'Parses date string', 'string', 'Date');

        expect(template.description).toBe('Parses date string');
        expect(template.params).toHaveLength(2);
        expect(template.params[0].type).toBe('string');
        expect(template.returns.type).toBe('Date');
        expect(template.throws).toHaveLength(2);
      });
    });

    describe('arrayUtility', () => {
      it('should generate array utility template', () => {
        const template = commonTemplates.arrayUtility('filterArray', 'Filters array items', 'T', 'T[]');

        expect(template.description).toBe('Filters array items');
        expect(template.params).toHaveLength(2);
        expect(template.params[0].type).toBe('T[]');
        expect(template.returns.type).toBe('T[]');
        expect(template.examples).toHaveLength(2);
      });
    });

    describe('stringUtility', () => {
      it('should generate string utility template', () => {
        const template = commonTemplates.stringUtility('trimString', 'Trims whitespace from string');

        expect(template.description).toBe('Trims whitespace from string');
        expect(template.params).toHaveLength(2);
        expect(template.params[0].type).toBe('StringInput');
        expect(template.returns.type).toBe('string');
        expect(template.examples).toHaveLength(3);
      });
    });
  });

  describe('validateJSDocTemplate', () => {
    it('should validate complete template as valid', () => {
      const template: JSDocTemplate = {
        description: 'Complete function description',
        params: [
          { name: 'input', type: 'string', description: 'Input parameter' }
        ],
        returns: { type: 'string', description: 'Return value' },
        examples: ['example usage']
      };

      const result = validateJSDocTemplate(template);

      expect(result.isValid).toBe(true);
      expect(result.missingFields).toHaveLength(0);
      expect(result.warnings).toHaveLength(0);
    });

    it('should identify missing required fields', () => {
      const template: JSDocTemplate = {
        description: '',
        params: [],
        returns: { type: '', description: '' }
      };

      const result = validateJSDocTemplate(template);

      expect(result.isValid).toBe(false);
      expect(result.missingFields).toContain('description');
      expect(result.missingFields).toContain('returns.type');
      expect(result.missingFields).toContain('returns.description');
    });

    it('should provide warnings for best practices', () => {
      const template: JSDocTemplate = {
        description: 'Function without examples',
        params: [],
        returns: { type: 'void', description: 'No return value' }
      };

      const result = validateJSDocTemplate(template);

      expect(result.isValid).toBe(true);
      expect(result.warnings).toContain('Consider adding usage examples');
    });

    it('should warn about complex functions without examples', () => {
      const template: JSDocTemplate = {
        description: 'Complex function',
        params: [
          { name: 'param1', type: 'string', description: 'First param' },
          { name: 'param2', type: 'number', description: 'Second param' },
          { name: 'param3', type: 'boolean', description: 'Third param' },
          { name: 'param4', type: 'object', description: 'Fourth param' }
        ],
        returns: { type: 'any', description: 'Complex result' }
      };

      const result = validateJSDocTemplate(template);

      expect(result.warnings).toContain('Functions with many parameters should include examples');
    });
  });
});