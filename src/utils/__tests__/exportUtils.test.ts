import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  ReportType,
  DataType,
  REPORT_TYPE_MAPPING,
  isValidReportType,
  getDataTypeForReport,
  shouldIncludeRisks,
  shouldIncludeIncidents,
  exportToCSV,
  exportToExcel,
  exportToPDF,
  formatDataForExport,
  generateFileName,
  downloadFile,
} from '../exportUtils';
import { Risk, Incident, RiskSeverity, RiskStatus } from '@/types';
import { mockRisk, mockIncident, mockPolicy } from '@/test/test-utils';

// Mock data for testing
const mockRisks: Risk[] = [
  {
    id: '1',
    title: 'Test Risk 1',
    description: 'Test description',
    category: 'Security',
    categoryId: 'cat-1',
    ownerId: 'user-1',
    ownerName: '<PERSON>',
    organizationId: 'org-1',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-02'),
    inherentLikelihood: 4,
    inherentImpact: 3,
    inherentSeverity: RiskSeverity.HIGH,
    likelihood: 2,
    impact: 2,
    severity: RiskSeverity.CRITICAL,
    status: RiskStatus.IDENTIFIED,
    currentControls: 'Some controls',
    mitigationApproach: 'Risk mitigation approach',
    dueDate: new Date('2023-12-31'),
  },
  {
    id: '2',
    title: 'Test Risk 2',
    description: 'Test description 2',
    category: 'Operational',
    categoryId: 'cat-2',
    ownerId: 'user-2',
    ownerName: 'Jane Smith',
    organizationId: 'org-1',
    createdAt: new Date('2023-01-03'),
    updatedAt: new Date('2023-01-04'),
    inherentLikelihood: 3,
    inherentImpact: 4,
    inherentSeverity: RiskSeverity.HIGH,
    likelihood: 1,
    impact: 1,
    severity: RiskSeverity.HIGH,
    status: RiskStatus.IN_PROGRESS,
    currentControls: 'Other controls',
    mitigationApproach: 'Another approach',
    dueDate: new Date('2023-11-30'),
  }
];

const mockIncidents: Incident[] = [
  {
    id: '1',
    title: 'Test Incident 1',
    description: 'Test incident description',
    severity: 'High',
    status: 'Open',
    reporterId: 'user-1',
    reporterName: 'John Doe',
    organizationId: 'org-1',
    date: new Date('2023-06-01'),
    relatedRiskId: '1',
    relatedRiskTitle: 'Test Risk 1',
    createdAt: new Date('2023-06-01'),
    updatedAt: new Date('2023-06-02'),
  },
  {
    id: '2',
    title: 'Test Incident 2',
    description: 'Another incident',
    severity: 'Medium',
    status: 'Resolved',
    reporterId: 'user-2',
    reporterName: 'Jane Smith',
    organizationId: 'org-1',
    date: new Date('2023-06-15'),
    relatedRiskId: null,
    relatedRiskTitle: null,
    createdAt: new Date('2023-06-15'),
    updatedAt: new Date('2023-06-16'),
  }
];

// Mock external dependencies
vi.mock('file-saver', () => ({
  saveAs: vi.fn(),
}))

vi.mock('xlsx', () => ({
  utils: {
    json_to_sheet: vi.fn(() => ({ '!ref': 'A1:C3' })),
    book_new: vi.fn(() => ({})),
    book_append_sheet: vi.fn(),
  },
  write: vi.fn(() => new ArrayBuffer(8)),
}))

vi.mock('jspdf', () => ({
  default: vi.fn().mockImplementation(() => ({
    save: vi.fn(),
    text: vi.fn(),
    setFontSize: vi.fn(),
    setFont: vi.fn(),
    internal: {
      pageSize: {
        width: 210,
        height: 297,
      },
    },
  })),
}))

vi.mock('jspdf-autotable', () => ({
  default: vi.fn(),
}))

describe('Export Utils - Report Type Mapping', () => {
  describe('ReportType enum', () => {
    it('should contain all expected report types', () => {
      expect(ReportType.RISK_SUMMARY).toBe('risk-summary');
      expect(ReportType.RISK_DETAILED).toBe('risk-detailed');
      expect(ReportType.INCIDENTS).toBe('incidents');
      expect(ReportType.BOARD).toBe('board');
      expect(ReportType.CATEGORIES).toBe('categories');
      expect(ReportType.COMBINED).toBe('combined');
      expect(ReportType.RISK_AND_INCIDENT_REPORT).toBe('risk-and-incident-report');
      expect(ReportType.INCIDENT_ANALYSIS).toBe('incident-analysis');
      expect(ReportType.RISK_ANALYSIS).toBe('risk-analysis');
      expect(ReportType.COMPLIANCE).toBe('compliance');
      expect(ReportType.AUDIT).toBe('audit');
    });
  });

  describe('DataType enum', () => {
    it('should contain all expected data types', () => {
      expect(DataType.RISKS_ONLY).toBe('risks-only');
      expect(DataType.INCIDENTS_ONLY).toBe('incidents-only');
      expect(DataType.BOTH).toBe('both');
      expect(DataType.SUMMARY).toBe('summary');
    });
  });

  describe('REPORT_TYPE_MAPPING', () => {
    it('should map risk-only reports correctly', () => {
      expect(REPORT_TYPE_MAPPING[ReportType.RISK_SUMMARY]).toBe(DataType.RISKS_ONLY);
      expect(REPORT_TYPE_MAPPING[ReportType.RISK_DETAILED]).toBe(DataType.RISKS_ONLY);
      expect(REPORT_TYPE_MAPPING[ReportType.RISK_ANALYSIS]).toBe(DataType.RISKS_ONLY);
      expect(REPORT_TYPE_MAPPING[ReportType.CATEGORIES]).toBe(DataType.RISKS_ONLY);
    });

    it('should map incident-only reports correctly', () => {
      expect(REPORT_TYPE_MAPPING[ReportType.INCIDENTS]).toBe(DataType.INCIDENTS_ONLY);
      expect(REPORT_TYPE_MAPPING[ReportType.INCIDENT_ANALYSIS]).toBe(DataType.INCIDENTS_ONLY);
    });

    it('should map combined reports correctly', () => {
      expect(REPORT_TYPE_MAPPING[ReportType.BOARD]).toBe(DataType.BOTH);
      expect(REPORT_TYPE_MAPPING[ReportType.COMBINED]).toBe(DataType.BOTH);
      expect(REPORT_TYPE_MAPPING[ReportType.RISK_AND_INCIDENT_REPORT]).toBe(DataType.BOTH);
      expect(REPORT_TYPE_MAPPING[ReportType.COMPLIANCE]).toBe(DataType.BOTH);
      expect(REPORT_TYPE_MAPPING[ReportType.AUDIT]).toBe(DataType.BOTH);
    });

    it('should have mapping for all report types', () => {
      const reportTypes = Object.values(ReportType);
      const mappedTypes = Object.keys(REPORT_TYPE_MAPPING);
      
      expect(mappedTypes).toHaveLength(reportTypes.length);
      reportTypes.forEach(type => {
        expect(REPORT_TYPE_MAPPING).toHaveProperty(type);
      });
    });
  });

  describe('isValidReportType', () => {
    it('should validate correct report types', () => {
      expect(isValidReportType('risk-summary')).toBe(true);
      expect(isValidReportType('incidents')).toBe(true);
      expect(isValidReportType('risk-and-incident-report')).toBe(true);
      expect(isValidReportType('compliance')).toBe(true);
    });

    it('should reject invalid report types', () => {
      expect(isValidReportType('invalid-report')).toBe(false);
      expect(isValidReportType('risk')).toBe(false);
      expect(isValidReportType('incident')).toBe(false);
      expect(isValidReportType('')).toBe(false);
      expect(isValidReportType('risk_and_incident_report')).toBe(false); // underscore version
    });

    it('should handle edge cases', () => {
      expect(isValidReportType(null as any)).toBe(false);
      expect(isValidReportType(undefined as any)).toBe(false);
      expect(isValidReportType(123 as any)).toBe(false);
      expect(isValidReportType({} as any)).toBe(false);
    });
  });

  describe('getDataTypeForReport', () => {
    it('should return correct data types for valid reports', () => {
      expect(getDataTypeForReport('risk-summary')).toBe(DataType.RISKS_ONLY);
      expect(getDataTypeForReport('incidents')).toBe(DataType.INCIDENTS_ONLY);
      expect(getDataTypeForReport('board')).toBe(DataType.BOTH);
      expect(getDataTypeForReport('risk-and-incident-report')).toBe(DataType.BOTH);
    });

    it('should throw error for invalid report types', () => {
      expect(() => getDataTypeForReport('invalid-report')).toThrow('Unsupported report type');
      expect(() => getDataTypeForReport('risk')).toThrow('Unsupported report type');
      expect(() => getDataTypeForReport('')).toThrow('Unsupported report type');
    });

    it('should include supported types in error message', () => {
      try {
        getDataTypeForReport('invalid-report');
      } catch (error) {
        expect(error instanceof Error ? error.message : '').toContain('risk-summary');
        expect(error instanceof Error ? error.message : '').toContain('incidents');
        expect(error instanceof Error ? error.message : '').toContain('board');
      }
    });
  });

  describe('shouldIncludeRisks', () => {
    it('should return true for risk-only reports', () => {
      expect(shouldIncludeRisks('risk-summary')).toBe(true);
      expect(shouldIncludeRisks('risk-detailed')).toBe(true);
      expect(shouldIncludeRisks('risk-analysis')).toBe(true);
      expect(shouldIncludeRisks('categories')).toBe(true);
    });

    it('should return true for combined reports', () => {
      expect(shouldIncludeRisks('board')).toBe(true);
      expect(shouldIncludeRisks('combined')).toBe(true);
      expect(shouldIncludeRisks('risk-and-incident-report')).toBe(true);
      expect(shouldIncludeRisks('compliance')).toBe(true);
      expect(shouldIncludeRisks('audit')).toBe(true);
    });

    it('should return false for incident-only reports', () => {
      expect(shouldIncludeRisks('incidents')).toBe(false);
      expect(shouldIncludeRisks('incident-analysis')).toBe(false);
    });

    it('should throw error for invalid report types', () => {
      expect(() => shouldIncludeRisks('invalid-report')).toThrow('Unsupported report type');
    });
  });

  describe('shouldIncludeIncidents', () => {
    it('should return true for incident-only reports', () => {
      expect(shouldIncludeIncidents('incidents')).toBe(true);
      expect(shouldIncludeIncidents('incident-analysis')).toBe(true);
    });

    it('should return true for combined reports', () => {
      expect(shouldIncludeIncidents('board')).toBe(true);
      expect(shouldIncludeIncidents('combined')).toBe(true);
      expect(shouldIncludeIncidents('risk-and-incident-report')).toBe(true);
      expect(shouldIncludeIncidents('compliance')).toBe(true);
      expect(shouldIncludeIncidents('audit')).toBe(true);
    });

    it('should return false for risk-only reports', () => {
      expect(shouldIncludeIncidents('risk-summary')).toBe(false);
      expect(shouldIncludeIncidents('risk-detailed')).toBe(false);
      expect(shouldIncludeIncidents('risk-analysis')).toBe(false);
      expect(shouldIncludeIncidents('categories')).toBe(false);
    });

    it('should throw error for invalid report types', () => {
      expect(() => shouldIncludeIncidents('invalid-report')).toThrow('Unsupported report type');
    });
  });

  describe('Ambiguous Matching Prevention', () => {
    it('should prevent "risk_and_incident_report" from matching risks when incidents intended', () => {
      // This test demonstrates the fix for the original problem
      const ambiguousReportType = 'risk_and_incident_report'; // underscore version
      
      // Should not be valid (prevents ambiguous matching)
      expect(isValidReportType(ambiguousReportType)).toBe(false);
      
      // Should throw error instead of incorrectly matching
      expect(() => getDataTypeForReport(ambiguousReportType)).toThrow('Unsupported report type');
      expect(() => shouldIncludeRisks(ambiguousReportType)).toThrow('Unsupported report type');
      expect(() => shouldIncludeIncidents(ambiguousReportType)).toThrow('Unsupported report type');
    });

    it('should only match exact report type strings', () => {
      // Test that partial matches don't work
      const partialMatches = [
        'risk',
        'incident',
        'summary',
        'detailed',
        'risk-',
        '-summary',
        'risk-summary-extra',
        'prefix-risk-summary'
      ];

      partialMatches.forEach(partial => {
        expect(isValidReportType(partial)).toBe(false);
        expect(() => getDataTypeForReport(partial)).toThrow('Unsupported report type');
      });
    });

    it('should handle case sensitivity correctly', () => {
      // Test that case variations don't match
      const caseVariations = [
        'RISK-SUMMARY',
        'Risk-Summary',
        'risk-Summary',
        'INCIDENTS',
        'Incidents',
        'BOARD',
        'Board'
      ];

      caseVariations.forEach(variation => {
        expect(isValidReportType(variation)).toBe(false);
        expect(() => getDataTypeForReport(variation)).toThrow('Unsupported report type');
      });
    });

    it('should prevent substring matching issues', () => {
      // Test scenarios where includes() would cause problems
      const problematicInputs = [
        'my-risk-summary-report',    // contains 'risk-summary'
        'incidents-and-more',        // contains 'incidents'
        'board-meeting-notes',       // contains 'board'
        'risk-summary-v2',           // contains 'risk-summary'
        'super-incidents-report'     // contains 'incidents'
      ];

      problematicInputs.forEach(input => {
        expect(isValidReportType(input)).toBe(false);
        expect(() => getDataTypeForReport(input)).toThrow('Unsupported report type');
      });
    });
  });

  describe('Data Type Logic Consistency', () => {
    it('should have consistent logic between shouldIncludeRisks and shouldIncludeIncidents', () => {
      Object.values(ReportType).forEach(reportType => {
        const dataType = REPORT_TYPE_MAPPING[reportType];
        const includesRisks = shouldIncludeRisks(reportType);
        const includesIncidents = shouldIncludeIncidents(reportType);

        switch (dataType) {
          case DataType.RISKS_ONLY:
            expect(includesRisks).toBe(true);
            expect(includesIncidents).toBe(false);
            break;
          case DataType.INCIDENTS_ONLY:
            expect(includesRisks).toBe(false);
            expect(includesIncidents).toBe(true);
            break;
          case DataType.BOTH:
          case DataType.SUMMARY:
            expect(includesRisks).toBe(true);
            expect(includesIncidents).toBe(true);
            break;
        }
      });
    });
  });
});

describe('Export Utilities', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Mock URL.createObjectURL
    global.URL.createObjectURL = vi.fn(() => 'mocked-url')
    global.URL.revokeObjectURL = vi.fn()
  })

  describe('formatDataForExport', () => {
    it('should format risk data correctly', () => {
      const risks = [mockRisk]
      const result = formatDataForExport(risks, 'risks')

      expect(result).toHaveLength(1)
      expect(result[0]).toEqual({
        ID: mockRisk.id,
        Title: mockRisk.title,
        Description: mockRisk.description,
        Category: mockRisk.category,
        Severity: mockRisk.severity,
        Status: mockRisk.status,
        Owner: mockRisk.ownerName,
        'Likelihood': mockRisk.likelihood,
        'Impact': mockRisk.impact,
        'Inherent Likelihood': mockRisk.inherentLikelihood,
        'Inherent Impact': mockRisk.inherentImpact,
        'Inherent Severity': mockRisk.inherentSeverity,
        'Current Controls': mockRisk.currentControls,
        'Mitigation Approach': mockRisk.mitigationApproach,
        'Due Date': expect.any(String),
        'Created At': expect.any(String),
        'Updated At': expect.any(String),
      })
    })

    it('should format incident data correctly', () => {
      const incidents = [mockIncident]
      const result = formatDataForExport(incidents, 'incidents')

      expect(result).toHaveLength(1)
      expect(result[0]).toEqual({
        ID: mockIncident.id,
        Title: mockIncident.title,
        Description: mockIncident.description,
        Severity: mockIncident.severity,
        Status: mockIncident.status,
        Reporter: mockIncident.reporterName,
        'Related Risk': mockIncident.relatedRiskTitle,
        Date: expect.any(String),
      })
    })

    it('should format policy data correctly', () => {
      const policies = [mockPolicy]
      const result = formatDataForExport(policies, 'policies')

      expect(result).toHaveLength(1)
      expect(result[0]).toEqual({
        ID: mockPolicy.id,
        Title: mockPolicy.title,
        Description: mockPolicy.description,
        Category: mockPolicy.category,
        Version: mockPolicy.version,
        Status: mockPolicy.status,
        'Effective Date': mockPolicy.effectiveDate,
        'Created At': mockPolicy.createdAt,
      })
    })

    it('should handle empty data arrays', () => {
      const result = formatDataForExport([], 'risks')
      expect(result).toEqual([])
    })

    it('should handle missing optional fields', () => {
      const riskWithoutOptionalFields = {
        ...mockRisk,
        category: undefined,
        ownerName: undefined,
        dueDate: undefined,
      }

      const result = formatDataForExport([riskWithoutOptionalFields], 'risks')

      expect(result[0]).toEqual(
        expect.objectContaining({
          Category: '',
          Owner: '',
          'Due Date': '',
        })
      )
    })
  })

  describe('generateFileName', () => {
    it('should generate correct file names with timestamps', () => {
      const mockDate = new Date('2024-06-15T10:30:00Z')
      vi.setSystemTime(mockDate)

      expect(generateFileName('risks', 'csv')).toBe('risks_2024-06-15_10-30-00.csv')
      expect(generateFileName('incidents', 'xlsx')).toBe('incidents_2024-06-15_10-30-00.xlsx')
      expect(generateFileName('policies', 'pdf')).toBe('policies_2024-06-15_10-30-00.pdf')
    })

    it('should handle custom prefixes', () => {
      const mockDate = new Date('2024-06-15T10:30:00Z')
      vi.setSystemTime(mockDate)

      expect(generateFileName('custom-report', 'csv')).toBe('custom-report_2024-06-15_10-30-00.csv')
    })
  })

  describe('exportToCSV', () => {
    it('should export data to CSV format', async () => {
      const { saveAs } = await import('file-saver')
      const data = [mockRisk]

      await exportToCSV(data, 'risks')

      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        expect.stringMatching(/^risks_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.csv$/)
      )

      // Check that the blob was created with correct type
      const saveCall = vi.mocked(saveAs).mock.calls[0]
      const blob = saveCall[0] as Blob
      expect(blob.type).toBe('text/csv;charset=utf-8;')
    })

    it('should handle empty data for CSV export', async () => {
      await expect(exportToCSV([], 'risks')).rejects.toThrow('No data to export')
    })

    it('should generate proper CSV content', async () => {
      const { saveAs } = await import('file-saver')
      const testData = [{
        id: '1',
        title: 'Test Risk',
        description: 'Test Description',
      }]

      await exportToCSV(testData, 'risks')

      const saveCall = vi.mocked(saveAs).mock.calls[0]
      const blob = saveCall[0] as Blob

      // Check that blob was created with correct type
      expect(blob.type).toBe('text/csv;charset=utf-8;')
      expect(saveCall[1]).toMatch(/^risks_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.csv$/)
    })
  })

  describe('exportToExcel', () => {
    it('should export data to Excel format', async () => {
      const { saveAs } = await import('file-saver')
      const XLSX = await import('xlsx')
      const data = [mockRisk]

      await exportToExcel(data, 'risks')

      expect(XLSX.utils.json_to_sheet).toHaveBeenCalled()
      expect(XLSX.utils.book_new).toHaveBeenCalled()
      expect(XLSX.utils.book_append_sheet).toHaveBeenCalled()
      expect(XLSX.write).toHaveBeenCalled()
      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        expect.stringMatching(/^risks_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.xlsx$/)
      )
    })

    it('should handle empty data for Excel export', async () => {
      await expect(exportToExcel([], 'risks')).rejects.toThrow('No data to export')
    })

    it('should create Excel blob with correct type', async () => {
      const { saveAs } = await import('file-saver')
      const data = [mockRisk]

      await exportToExcel(data, 'risks')

      const saveCall = vi.mocked(saveAs).mock.calls[0]
      const blob = saveCall[0] as Blob
      expect(blob.type).toBe('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    })
  })

  describe('exportToPDF', () => {
    it('should export data to PDF format', async () => {
      const jsPDF = (await import('jspdf')).default
      const autoTable = (await import('jspdf-autotable')).default
      const data = [mockRisk]

      await exportToPDF(data, 'risks')

      expect(jsPDF).toHaveBeenCalled()
      const pdfInstance = vi.mocked(jsPDF).mock.results[0].value
      expect(pdfInstance.text).toHaveBeenCalled()
      expect(autoTable).toHaveBeenCalled()
      expect(pdfInstance.save).toHaveBeenCalledWith(
        expect.stringMatching(/^risks_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.pdf$/)
      )
    })

    it('should handle empty data for PDF export', async () => {
      await expect(exportToPDF([], 'risks')).rejects.toThrow('No data to export')
    })

    it('should set correct PDF title and headers', async () => {
      const jsPDF = (await import('jspdf')).default
      const data = [mockRisk]

      await exportToPDF(data, 'risks')

      const pdfInstance = vi.mocked(jsPDF).mock.results[0].value
      expect(pdfInstance.text).toHaveBeenCalledWith('Risks Report', expect.any(Number), expect.any(Number))
      expect(pdfInstance.setFontSize).toHaveBeenCalledWith(16)
    })
  })

  describe('downloadFile', () => {
    it('should download file with correct parameters', async () => {
      const { saveAs } = await import('file-saver')
      const mockBlob = new Blob(['test content'], { type: 'text/plain' })

      downloadFile(mockBlob, 'test-file.txt')

      expect(vi.mocked(saveAs)).toHaveBeenCalledWith(mockBlob, 'test-file.txt')
    })

    it('should handle blob creation from string content', async () => {
      const { saveAs } = await import('file-saver')
      const content = 'test content'

      downloadFile(content, 'test-file.txt', 'text/plain')

      expect(vi.mocked(saveAs)).toHaveBeenCalledWith(
        expect.any(Blob),
        'test-file.txt'
      )
    })
  })

  describe('Error Handling', () => {
    it('should handle CSV export errors gracefully', async () => {
      const { saveAs } = await import('file-saver')
      vi.mocked(saveAs).mockImplementation(() => {
        throw new Error('Save failed')
      })

      await expect(exportToCSV([mockRisk], 'risks')).rejects.toThrow('Save failed')
    })

    it('should handle Excel export errors gracefully', async () => {
      const XLSX = await import('xlsx')
      vi.mocked(XLSX.write).mockImplementation(() => {
        throw new Error('Excel write failed')
      })

      await expect(exportToExcel([mockRisk], 'risks')).rejects.toThrow('Excel write failed')
    })

    it('should handle PDF export errors gracefully', async () => {
      const jsPDF = (await import('jspdf')).default
      vi.mocked(jsPDF).mockImplementation(() => {
        throw new Error('PDF creation failed')
      })

      await expect(exportToPDF([mockRisk], 'risks')).rejects.toThrow('PDF creation failed')
    })
  })
})
