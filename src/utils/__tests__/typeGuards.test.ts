import { describe, it, expect } from "vitest";
import {
  isRisk,
  isControlMeasure,
  isMitigationAction,
  isRiskSeverity,
  isRiskStatus,
  isUser,
  isDbRisk,
  isDbControlMeasure,
  isDbMitigationAction,
  hasProfiles,
  hasCategories,
} from "../typeGuards";
import { RiskSeverity, RiskStatus, UserRole } from "@/types";

describe("Type Guards", () => {
  describe("isRisk", () => {
    it("should return true for valid Risk objects", () => {
      const validRisk = {
        id: "test-id",
        title: "Test Risk",
        description: "Test description",
        category: "Test Category",
        categoryId: "test-category-id",
        severity: RiskSeverity.HIGH,
        status: "In Progress",
      };

      expect(isRisk(validRisk)).toBe(true);
    });

    it("should return false for invalid Risk objects", () => {
      const invalidRisk1 = {
        id: "test-id",
        title: "Test Risk",
        // missing required fields
      };

      const invalidRisk2 = null;
      const invalidRisk3 = "not an object";
      const invalidRisk4 = {
        // Implementation needed
      };
      expect(isRisk(invalidRisk1)).toBe(false);
      expect(isRisk(invalidRisk2)).toBe(false);
      expect(isRisk(invalidRisk3)).toBe(false);
      expect(isRisk(invalidRisk4)).toBe(false);
    });
  });

  describe("isControlMeasure", () => {
    it("should return true for valid ControlMeasure objects", () => {
      const validControlMeasure = {
        id: "test-id",
        riskId: "risk-id",
        description: "Test control measure",
      };

      expect(isControlMeasure(validControlMeasure)).toBe(true);
    });

    it("should return false for invalid ControlMeasure objects", () => {
      const invalidControlMeasure1 = {
        id: "test-id",
        // missing riskId and description
      };

      const invalidControlMeasure2 = null;
      const invalidControlMeasure3 = "not an object";

      expect(isControlMeasure(invalidControlMeasure1)).toBe(false);
      expect(isControlMeasure(invalidControlMeasure2)).toBe(false);
      expect(isControlMeasure(invalidControlMeasure3)).toBe(false);
    });
  });

  describe("isMitigationAction", () => {
    it("should return true for valid MitigationAction objects", () => {
      const validMitigationAction = {
        id: "test-id",
        riskId: "risk-id",
        description: "Test mitigation action",
        completed: false,
      };

      expect(isMitigationAction(validMitigationAction)).toBe(true);
    });

    it("should return false for invalid MitigationAction objects", () => {
      const invalidMitigationAction1 = {
        id: "test-id",
        // missing riskId and description
      };

      const invalidMitigationAction2 = null;

      expect(isMitigationAction(invalidMitigationAction1)).toBe(false);
      expect(isMitigationAction(invalidMitigationAction2)).toBe(false);
    });
  });

  describe("isRiskSeverity", () => {
    it("should return true for valid RiskSeverity values", () => {
      expect(isRiskSeverity(RiskSeverity.LOW)).toBe(true);
      expect(isRiskSeverity(RiskSeverity.MEDIUM)).toBe(true);
      expect(isRiskSeverity(RiskSeverity.HIGH)).toBe(true);
      expect(isRiskSeverity(RiskSeverity.CRITICAL)).toBe(true);
    });

    it("should return false for invalid RiskSeverity values", () => {
      expect(isRiskSeverity("INVALID")).toBe(false);
      expect(isRiskSeverity(null)).toBe(false);
      expect(isRiskSeverity(undefined)).toBe(false);
      expect(isRiskSeverity(123)).toBe(false);
    });
  });

  describe("isRiskStatus", () => {
    it("should return true for valid RiskStatus values", () => {
      expect(isRiskStatus(RiskStatus.IDENTIFIED)).toBe(true);
      expect(isRiskStatus(RiskStatus.IN_PROGRESS)).toBe(true);
      expect(isRiskStatus(RiskStatus.MITIGATED)).toBe(true);
      expect(isRiskStatus(RiskStatus.ACCEPTED)).toBe(true);
      expect(isRiskStatus(RiskStatus.CLOSED)).toBe(true);
    });

    it("should return false for invalid RiskStatus values", () => {
      expect(isRiskStatus("INVALID")).toBe(false);
      expect(isRiskStatus(null)).toBe(false);
      expect(isRiskStatus(undefined)).toBe(false);
      expect(isRiskStatus(123)).toBe(false);
    });
  });

  describe("isUser", () => {
    it("should return true for valid User objects", () => {
      const validUser = {
        id: "test-id",
        name: "Test User",
        email: "<EMAIL>",
        role: UserRole.ADMIN,
      };

      expect(isUser(validUser)).toBe(true);
    });

    it("should return false for invalid User objects", () => {
      const invalidUser1 = {
        id: "test-id",
        // missing required fields
      };

      const invalidUser2 = null;
      const invalidUser3 = "not an object";

      expect(isUser(invalidUser1)).toBe(false);
      expect(isUser(invalidUser2)).toBe(false);
      expect(isUser(invalidUser3)).toBe(false);
    });
  });

  describe("isDbRisk", () => {
    it("should return true for valid DbRisk objects", () => {
      const validDbRisk = {
        id: "test-id",
        title: "Test Risk",
        description: "Test description",
        organization_id: "org-id",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        likelihood: 3,
        impact: 4,
        severity: "High",
        status: "In Progress",
        created_by: "user-id",
      };

      expect(isDbRisk(validDbRisk)).toBe(true);
    });

    it("should return false for invalid DbRisk objects", () => {
      const invalidDbRisk1 = {
        id: "test-id",
        // missing required fields
      };

      const invalidDbRisk2 = null;

      expect(isDbRisk(invalidDbRisk1)).toBe(false);
      expect(isDbRisk(invalidDbRisk2)).toBe(false);
    });
  });

  describe("isDbControlMeasure", () => {
    it("should return true for valid DbControlMeasure objects", () => {
      const validDbControlMeasure = {
        id: "test-id",
        risk_id: "risk-id",
        organization_id: "org-id",
        description: "Test control measure",
        implemented: true,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      };

      expect(isDbControlMeasure(validDbControlMeasure)).toBe(true);
    });

    it("should return false for invalid DbControlMeasure objects", () => {
      const invalidDbControlMeasure = {
        id: "test-id",
        // missing required fields
      };

      expect(isDbControlMeasure(invalidDbControlMeasure)).toBe(false);
    });
  });

  describe("isDbMitigationAction", () => {
    it("should return true for valid DbMitigationAction objects", () => {
      const validDbMitigationAction = {
        id: "test-id",
        risk_id: "risk-id",
        organization_id: "org-id",
        description: "Test mitigation action",
        completed: false,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      };

      expect(isDbMitigationAction(validDbMitigationAction)).toBe(true);
    });

    it("should return false for invalid DbMitigationAction objects", () => {
      const invalidDbMitigationAction = {
        id: "test-id",
        // missing required fields
      };

      expect(isDbMitigationAction(invalidDbMitigationAction)).toBe(false);
    });
  });

  describe("hasProfiles", () => {
    it("should return true for objects with profiles property", () => {
      const objectWithProfiles = {
        id: "test-id",
        profiles: { name: "Test User" },
      };

      expect(hasProfiles(objectWithProfiles)).toBe(true);
    });

    it("should return false for objects without profiles property", () => {
      const objectWithoutProfiles = {
        id: "test-id",
      };

      expect(hasProfiles(objectWithoutProfiles)).toBe(false);
      expect(hasProfiles(null)).toBe(false);
      expect(hasProfiles(undefined)).toBe(false);
    });
  });

  describe("hasCategories", () => {
    it("should return true for objects with categories property", () => {
      const objectWithCategories = {
        id: "test-id",
        categories: { name: "Test Category" },
      };

      expect(hasCategories(objectWithCategories)).toBe(true);
    });

    it("should return false for objects without categories property", () => {
      const objectWithoutCategories = {
        id: "test-id",
      };

      expect(hasCategories(objectWithoutCategories)).toBe(false);
      expect(hasCategories(null)).toBe(false);
      expect(hasCategories(undefined)).toBe(false);
    });
  });
});
