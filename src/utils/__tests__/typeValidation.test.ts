import { describe, it, expect, beforeAll, vi } from 'vitest';
import {
  escapeHtmlEntities,
  sanitizeHtml,
  sanitizeHtmlSync,
  validateAndSanitizeInput,
  validateAndSanitizeInputSync,
  validateEffectiveness,
  validateSeverity,
  validateStatus,
  validateDate,
  isValidEffectiveness,
  validateControlMeasures,
  validateMitigationActions,
  validateRiskData,
  sanitizeRiskData,
  normalizeRiskData,
} from '../typeValidation';
import { RiskSeverity, RiskStatus } from '@/types';

// Mock console methods to avoid noise in tests
vi.spyOn(console, 'warn').mockImplementation(() => {})
vi.spyOn(console, 'error').mockImplementation(() => {})

describe('HTML Sanitization and XSS Prevention', () => {
  describe('escapeHtmlEntities', () => {
    it('should escape basic HTML entities', () => {
      expect(escapeHtmlEntities('<script>alert("xss")</script>')).toBe(
        '&lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2F;script&gt;'
      );
    });

    it('should escape all dangerous characters', () => {
      const dangerous = '&<>"\'`=/';
      const expected = '&amp;&lt;&gt;&quot;&#x27;&#x60;&#x3D;&#x2F;';
      expect(escapeHtmlEntities(dangerous)).toBe(expected);
    });

    it('should handle non-string input', () => {
      expect(escapeHtmlEntities(123 as any)).toBe('123');
      expect(escapeHtmlEntities(null as any)).toBe('null');
      expect(escapeHtmlEntities(undefined as any)).toBe('undefined');
    });

    it('should preserve safe content', () => {
      const safe = 'Hello World! This is safe text.';
      expect(escapeHtmlEntities(safe)).toBe(safe);
    });
  });

  describe('sanitizeHtmlSync', () => {
    it('should remove script tags completely', () => {
      const malicious = '<p>Hello</p><script>alert("xss")</script><p>World</p>';
      const result = sanitizeHtmlSync(malicious);
      expect(result).not.toContain('<script>');
      expect(result).not.toContain('alert');
      expect(result).toContain('Hello');
      expect(result).toContain('World');
    });

    it('should remove dangerous tags', () => {
      const dangerous = '<object data="malicious.swf"></object><embed src="bad.swf"><form><input type="text"></form>';
      const result = sanitizeHtmlSync(dangerous);
      expect(result).not.toContain('<object>');
      expect(result).not.toContain('<embed>');
      expect(result).not.toContain('<form>');
      expect(result).not.toContain('<input>');
    });

    it('should remove event handlers', () => {
      const withEvents = '<div onclick="alert(1)" onmouseover="alert(2)">Content</div>';
      const result = sanitizeHtmlSync(withEvents);
      expect(result).not.toContain('onclick');
      expect(result).not.toContain('onmouseover');
      expect(result).toContain('Content');
    });

    it('should remove javascript: URLs', () => {
      const jsUrl = '<a href="javascript:alert(1)">Click me</a>';
      const result = sanitizeHtmlSync(jsUrl);
      expect(result).not.toContain('javascript:');
      expect(result).toContain('Click me');
    });

    it('should remove dangerous data: URLs', () => {
      const dataUrl = '<img src="data:text/html,<script>alert(1)</script>">';
      const result = sanitizeHtmlSync(dataUrl);
      expect(result).not.toContain('data:text/html,<script>');
    });

    it('should handle empty and non-string input', () => {
      expect(sanitizeHtmlSync('')).toBe('');
      expect(sanitizeHtmlSync(null as any)).toBe('');
      expect(sanitizeHtmlSync(undefined as any)).toBe('');
    });

    it('should preserve safe HTML', () => {
      const safe = '<p>Hello <strong>World</strong></p>';
      const result = sanitizeHtmlSync(safe);
      expect(result).toContain('<p>');
      expect(result).toContain('<strong>');
      expect(result).toContain('Hello');
      expect(result).toContain('World');
      expect(result).not.toContain('<script>');
    });
  });

  describe('sanitizeHtml (async)', () => {
    it('should sanitize HTML using DOMPurify when available', async () => {
      const malicious = '<script>alert("xss")</script><p>Safe content</p>';
      const result = await sanitizeHtml(malicious);
      
      expect(result).not.toContain('<script>');
      expect(result).not.toContain('alert');
      expect(result).toContain('Safe content');
    });

    it('should respect allowed tags configuration', async () => {
      const html = '<p>Paragraph</p><div>Div</div><script>alert(1)</script>';
      const result = await sanitizeHtml(html, {
        allowedTags: ['p']
      });
      
      expect(result).toContain('<p>');
      expect(result).not.toContain('<div>');
      expect(result).not.toContain('<script>');
    });

    it('should handle non-string input', async () => {
      expect(await sanitizeHtml(123 as any)).toBe('');
      expect(await sanitizeHtml(null as any)).toBe('');
      expect(await sanitizeHtml(undefined as any)).toBe('');
    });

    it('should strip tags when requested', async () => {
      const html = '<p>Hello <strong>World</strong></p>';
      const result = await sanitizeHtml(html, { stripTags: true });

      // Should remove tags but keep content
      expect(result).not.toContain('<p>');
      expect(result).not.toContain('<strong>');
      expect(result).toContain('Hello');
      expect(result).toContain('World');
      // Should be plain text
      expect(result.trim()).toBe('Hello World');
    });
  });

  describe('validateAndSanitizeInput (async)', () => {
    it('should escape HTML when allowHtml is false', async () => {
      const input = '<script>alert("xss")</script>';
      const result = await validateAndSanitizeInput(input, { allowHtml: false });
      
      expect(result).toBe('&lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2F;script&gt;');
    });

    it('should sanitize HTML when allowHtml is true', async () => {
      const input = '<p>Safe</p><script>alert("xss")</script>';
      const result = await validateAndSanitizeInput(input, { allowHtml: true });
      
      expect(result).toContain('Safe');
      expect(result).not.toContain('<script>');
      expect(result).not.toContain('alert');
    });

    it('should enforce maximum length', async () => {
      const longInput = 'a'.repeat(1000);
      
      // Test truncation (default behavior)
      const truncatedResult = await validateAndSanitizeInput(longInput, { maxLength: 10 });
      expect(truncatedResult).toHaveLength(10);
      expect(truncatedResult).toBe('aaaaaaaaaa');
      
      // Test strict mode that throws errors
      await expect(
        validateAndSanitizeInput(longInput, { maxLength: 10, strictLength: true })
      ).rejects.toThrow('Input exceeds maximum length of 10 characters');
    });

    it('should handle non-string input', async () => {
      expect(await validateAndSanitizeInput(123)).toBe('123');
      expect(await validateAndSanitizeInput(null)).toBe('');
      expect(await validateAndSanitizeInput(undefined)).toBe('');
    });

    it('should respect allowed tags', async () => {
      const input = '<p>Para</p><div>Div</div><span>Span</span>';
      const result = await validateAndSanitizeInput(input, {
        allowHtml: true,
        allowedTags: ['p', 'span']
      });
      
      expect(result).toContain('<p>');
      expect(result).toContain('<span>');
      expect(result).not.toContain('<div>');
    });
  });

  describe('validateAndSanitizeInputSync', () => {
    it('should escape HTML when allowHtml is false', () => {
      const input = '<script>alert("xss")</script>';
      const result = validateAndSanitizeInputSync(input, { allowHtml: false });
      
      expect(result).toBe('&lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2F;script&gt;');
    });

    it('should sanitize HTML when allowHtml is true', () => {
      const input = '<p>Safe</p><script>alert("xss")</script>';
      const result = validateAndSanitizeInputSync(input, { allowHtml: true });
      
      expect(result).toContain('Safe');
      expect(result).not.toContain('<script>');
      expect(result).not.toContain('alert');
    });

    it('should enforce maximum length', () => {
      const longInput = 'a'.repeat(1000);
      
      expect(() => 
        validateAndSanitizeInputSync(longInput, { maxLength: 10 })
      ).toThrow('Input exceeds maximum length of 10 characters');
    });
  });

  describe('XSS Attack Vectors', () => {
    const xssVectors = [
      '<script>alert("XSS")</script>',
      '<img src="x" onerror="alert(1)">',
      '<svg onload="alert(1)">',
      '<iframe src="javascript:alert(1)"></iframe>',
      '<object data="data:text/html,<script>alert(1)</script>"></object>',
      '<embed src="data:image/svg+xml,<svg onload=alert(1)>">',
      '<form><button formaction="javascript:alert(1)">Click</button></form>',
      '<input onfocus="alert(1)" autofocus>',
      '<select onfocus="alert(1)" autofocus><option>',
      '<textarea onfocus="alert(1)" autofocus>',
      '<keygen onfocus="alert(1)" autofocus>',
      '<video><source onerror="alert(1)">',
      '<audio src="x" onerror="alert(1)">',
      '<details open ontoggle="alert(1)">',
      '<marquee onstart="alert(1)">',
      '<style>@import"javascript:alert(1)";</style>',
      '<link rel="stylesheet" href="javascript:alert(1)">',
      '<base href="javascript:alert(1)//">',
      '<meta http-equiv="refresh" content="0;url=javascript:alert(1)">',
      '<body onload="alert(1)">',
      '<div style="background:url(javascript:alert(1))">',
      '<div style="expression(alert(1))">',
      '"><script>alert(1)</script>',
      '\';alert(1);//',
      'javascript:alert(1)',
      'data:text/html,<script>alert(1)</script>',
      'vbscript:msgbox(1)',
      '<![CDATA[<script>alert(1)</script>]]>',
    ];

    it('should neutralize common XSS vectors with sync sanitization', () => {
      xssVectors.forEach((vector) => {
        const result = sanitizeHtmlSync(vector);

        // Should not contain dangerous executable patterns
        expect(result).not.toMatch(/javascript\s*:/i);
        expect(result).not.toMatch(/vbscript\s*:/i);
        expect(result).not.toMatch(/on\w+\s*=/i);
        expect(result).not.toContain('<script');
        expect(result).not.toContain('<object');
        expect(result).not.toContain('<embed');
        expect(result).not.toContain('<iframe');
        expect(result).not.toContain('<form');
        expect(result).not.toContain('<input');
        expect(result).not.toContain('<button');

        // Alert may be present but should be in safe context (escaped or in text)
        if (result.includes('alert')) {
          // If alert is present, it should be escaped or in a safe context
          expect(result).not.toMatch(/javascript\s*:/i);
          expect(result).not.toMatch(/on\w+\s*=.*alert/i);
        }
      });
    });

    it('should neutralize XSS vectors with async sanitization', async () => {
      for (const vector of xssVectors) {
        const result = await sanitizeHtml(vector);

        // Should not contain dangerous HTML tags
        expect(result).not.toContain('<script');
        expect(result).not.toContain('<object');
        expect(result).not.toContain('<embed');
        expect(result).not.toContain('<iframe');
        expect(result).not.toContain('<form');
        expect(result).not.toContain('<input');
        expect(result).not.toContain('<button');

        // Should not contain event handlers in HTML context
        expect(result).not.toMatch(/on\w+\s*=/i);

        // For vectors that are just plain text (like 'javascript:alert(1)'),
        // DOMPurify will preserve them as text, which is safe
        // The danger is only when they're in HTML attributes like href
        if (vector.includes('<') && vector.includes('>')) {
          // Only test for javascript: removal in HTML context
          if (vector.includes('javascript:') && vector.includes('href')) {
            expect(result).not.toMatch(/href\s*=\s*["']?javascript\s*:/i);
          }
          if (vector.includes('vbscript:')) {
            expect(result).not.toMatch(/vbscript\s*:/i);
          }
        }
      }
    });
  });
});

describe('Existing Type Validation Functions', () => {
  describe('validateEffectiveness', () => {
    it('should validate correct effectiveness values', () => {
      expect(validateEffectiveness('High')).toBe('High');
      expect(validateEffectiveness('Medium')).toBe('Medium');
      expect(validateEffectiveness('Low')).toBe('Low');
    });

    it('should use fallback for invalid values', () => {
      expect(validateEffectiveness('Invalid')).toBe('Medium');
      expect(validateEffectiveness('Invalid', 'High')).toBe('High');
    });
  });

  describe('validateSeverity', () => {
    it('should validate correct severity values', () => {
      expect(validateSeverity(RiskSeverity.HIGH)).toBe(RiskSeverity.HIGH);
      expect(validateSeverity(RiskSeverity.LOW)).toBe(RiskSeverity.LOW);
    });

    it('should use fallback for invalid values', () => {
      expect(validateSeverity('Invalid')).toBe(RiskSeverity.LOW);
    });
  });

  describe('validateStatus', () => {
    it('should validate correct status values', () => {
      expect(validateStatus(RiskStatus.IDENTIFIED)).toBe(RiskStatus.IDENTIFIED);
      expect(validateStatus(RiskStatus.CLOSED)).toBe(RiskStatus.CLOSED);
    });

    it('should use fallback for invalid values', () => {
      expect(validateStatus('Invalid')).toBe(RiskStatus.IDENTIFIED);
    });
  });

  describe('validateDate', () => {
    it('should validate correct date values', () => {
      const date = new Date('2023-01-01');
      expect(validateDate(date)).toEqual(date);
      expect(validateDate('2023-01-01')).toEqual(new Date('2023-01-01'));
    });

    it('should return undefined for invalid dates', () => {
      expect(validateDate('invalid-date')).toBeUndefined();
      expect(validateDate(null)).toBeUndefined();
      expect(validateDate(undefined)).toBeUndefined();
    });
  });
});

describe('Type Validation Utilities', () => {
  describe('isValidEffectiveness', () => {
    it('should validate correct effectiveness values', () => {
      expect(isValidEffectiveness('High')).toBe(true)
      expect(isValidEffectiveness('Medium')).toBe(true)
      expect(isValidEffectiveness('Low')).toBe(true)
    })

    it('should reject invalid effectiveness values', () => {
      expect(isValidEffectiveness('Invalid')).toBe(false)
      expect(isValidEffectiveness('high')).toBe(false) // case sensitive
      expect(isValidEffectiveness(null)).toBe(false)
      expect(isValidEffectiveness(undefined)).toBe(false)
      expect(isValidEffectiveness(123)).toBe(false)
    })
  })

  describe('validateControlMeasures', () => {
    it('should validate correct control measures', () => {
      const validControlMeasures = [
        {
          id: 'cm-1',
          riskId: 'risk-1',
          description: 'Test control measure',
          effectiveness: 'High' as const,
          implemented: true,
          organizationId: 'org-1',
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      ]

      const result = validateControlMeasures(validControlMeasures)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.validatedData).toEqual(validControlMeasures)
    })

    it('should handle invalid control measures', () => {
      const invalidControlMeasures = [
        {
          id: 'cm-1',
          // missing required fields
        },
        {
          id: 'cm-2',
          riskId: 'risk-1',
          description: 'Test control measure',
          effectiveness: 'Invalid' as any,
          implemented: true,
        }
      ]

      const result = validateControlMeasures(invalidControlMeasures as any)
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })

    it('should handle empty array', () => {
      const result = validateControlMeasures([])
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.validatedData).toEqual([])
    })

    it('should handle null/undefined input', () => {
      const result1 = validateControlMeasures(null as any)
      const result2 = validateControlMeasures(undefined as any)
      
      expect(result1.isValid).toBe(false)
      expect(result2.isValid).toBe(false)
    })
  })

  describe('validateMitigationActions', () => {
    it('should validate correct mitigation actions', () => {
      const validMitigationActions = [
        {
          id: 'ma-1',
          riskId: 'risk-1',
          description: 'Test mitigation action',
          completed: false,
          organizationId: 'org-1',
          createdAt: new Date(),
          updatedAt: new Date(),
        }
      ]

      const result = validateMitigationActions(validMitigationActions)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.validatedData).toEqual(validMitigationActions)
    })

    it('should handle invalid mitigation actions', () => {
      const invalidMitigationActions = [
        {
          id: 'ma-1',
          // missing required fields
        }
      ]

      const result = validateMitigationActions(invalidMitigationActions as any)
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })

    it('should handle empty array', () => {
      const result = validateMitigationActions([])
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
      expect(result.validatedData).toEqual([])
    })
  })

  describe('validateRiskData', () => {
    it('should validate complete risk data', () => {
      const validRiskData = {
        id: 'risk-1',
        title: 'Test Risk',
        description: 'Test risk description',
        category: 'Information Security',
        categoryId: 'cat-1',
        ownerId: 'user-1',
        ownerName: 'Test User',
        organizationId: 'org-1',
        createdAt: new Date(),
        updatedAt: new Date(),
        inherentLikelihood: 4,
        inherentImpact: 3,
        inherentSeverity: RiskSeverity.HIGH,
        likelihood: 2,
        impact: 3,
        severity: RiskSeverity.MEDIUM,
        status: RiskStatus.IN_PROGRESS,
      }

      const result = validateRiskData(validRiskData)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should handle missing required fields', () => {
      const invalidRiskData = {
        id: 'risk-1',
        // missing required fields
      }

      const result = validateRiskData(invalidRiskData as any)
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })

    it('should validate likelihood and impact ranges', () => {
      const riskDataWithInvalidRanges = {
        id: 'risk-1',
        title: 'Test Risk',
        description: 'Test risk description',
        organizationId: 'org-1',
        createdAt: new Date(),
        updatedAt: new Date(),
        inherentLikelihood: 6, // invalid range
        inherentImpact: 0, // invalid range
        inherentSeverity: RiskSeverity.HIGH,
        likelihood: -1, // invalid range
        impact: 10, // invalid range
        severity: RiskSeverity.MEDIUM,
        status: RiskStatus.IN_PROGRESS,
      }

      const result = validateRiskData(riskDataWithInvalidRanges as any)
      expect(result.isValid).toBe(false)
      expect(result.errors.some(error => error.includes('likelihood'))).toBe(true)
      expect(result.errors.some(error => error.includes('impact'))).toBe(true)
    })
  })

  describe('validateDate', () => {
    it('should validate valid dates', () => {
      const validDate = new Date('2024-01-01')
      const validDateString = '2024-01-01T00:00:00Z'
      
      expect(validateDate(validDate)).toEqual(validDate)
      expect(validateDate(validDateString)).toBeInstanceOf(Date)
    })

    it('should handle invalid dates', () => {
      expect(validateDate('invalid-date')).toBeUndefined()
      expect(validateDate(null)).toBeUndefined()
      expect(validateDate(undefined)).toBeUndefined()
      expect(validateDate(123)).toBeUndefined()
    })

    it('should handle edge cases', () => {
      expect(validateDate('')).toBeUndefined()
      expect(validateDate('2024-13-01')).toBeUndefined() // invalid month
    })
  })

  describe('sanitizeRiskData', () => {
    it('should sanitize risk data by removing dangerous content', () => {
      const unsafeRiskData = {
        id: 'risk-1',
        title: '<script>alert("xss")</script>Safe Title',
        description: 'Safe description<script>alert("xss")</script>',
        organizationId: 'org-1',
      }

      const result = sanitizeRiskData(unsafeRiskData as any)
      expect(result.title).not.toContain('<script>')
      expect(result.description).not.toContain('<script>')
      expect(result.title).toContain('Safe Title')
      expect(result.description).toContain('Safe description')
    })

    it('should preserve safe content', () => {
      const safeRiskData = {
        id: 'risk-1',
        title: 'Safe Title',
        description: 'Safe description with <em>emphasis</em>',
        organizationId: 'org-1',
      }

      const result = sanitizeRiskData(safeRiskData as any)
      expect(result.title).toBe('Safe Title')
      expect(result.description).toBe('Safe description with <em>emphasis</em>')
    })
  })

  describe('normalizeRiskData', () => {
    it('should normalize risk data fields', () => {
      const unnormalizedRiskData = {
        id: 'risk-1',
        title: '  Test Risk  ',
        description: '  Test description  ',
        category: '  Information Security  ',
        organizationId: 'org-1',
        likelihood: '3' as any, // string instead of number
        impact: '4' as any, // string instead of number
      }

      const result = normalizeRiskData(unnormalizedRiskData as any)
      expect(result.title).toBe('Test Risk')
      expect(result.description).toBe('Test description')
      expect(result.category).toBe('Information Security')
      expect(result.likelihood).toBe(3)
      expect(result.impact).toBe(4)
    })

    it('should handle missing optional fields', () => {
      const minimalRiskData = {
        id: 'risk-1',
        title: 'Test Risk',
        description: 'Test description',
        organizationId: 'org-1',
      }

      const result = normalizeRiskData(minimalRiskData as any)
      expect(result.id).toBe('risk-1')
      expect(result.title).toBe('Test Risk')
      expect(result.description).toBe('Test description')
    })

    it('should handle invalid numeric conversions', () => {
      const riskDataWithInvalidNumbers = {
        id: 'risk-1',
        title: 'Test Risk',
        description: 'Test description',
        organizationId: 'org-1',
        likelihood: 'invalid' as any,
        impact: 'also-invalid' as any,
      }

      const result = normalizeRiskData(riskDataWithInvalidNumbers as any)
      expect(result.likelihood).toBeUndefined()
      expect(result.impact).toBeUndefined()
    })
  })
})
