import { describe, it, expect } from 'vitest';
import {
  UtilityError,
  ValidationError,
  ParseError,
  ConfigurationError,
  createSuccess,
  createError,
  safeExecute,
  safeExecuteAsync,
  isSuccess,
  isError,
  unwrapResult,
  getResultOrDefault,
} from '../errors/standardErrors';

describe('Standard Error Utilities', () => {
  describe('UtilityError', () => {
    it('should create utility error with code and context', () => {
      const context = { input: 'test', operation: 'format' };
      const error = new UtilityError('Test error', 'TEST_ERROR', context);

      expect(error.message).toBe('Test error');
      expect(error.code).toBe('TEST_ERROR');
      expect(error.context).toEqual(context);
      expect(error.name).toBe('UtilityError');
    });

    it('should create utility error without context', () => {
      const error = new UtilityError('Test error', 'TEST_ERROR');

      expect(error.message).toBe('Test error');
      expect(error.code).toBe('TEST_ERROR');
      expect(error.context).toBeUndefined();
    });
  });

  describe('ValidationError', () => {
    it('should create validation error with correct properties', () => {
      const context = { field: 'email', value: 'invalid' };
      const error = new ValidationError('Invalid email format', context);

      expect(error.message).toBe('Invalid email format');
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.context).toEqual(context);
      expect(error.name).toBe('ValidationError');
    });
  });

  describe('ParseError', () => {
    it('should create parse error with correct properties', () => {
      const context = { input: 'invalid-date', parser: 'dateParser' };
      const error = new ParseError('Failed to parse date', context);

      expect(error.message).toBe('Failed to parse date');
      expect(error.code).toBe('PARSE_ERROR');
      expect(error.context).toEqual(context);
      expect(error.name).toBe('ParseError');
    });
  });

  describe('ConfigurationError', () => {
    it('should create configuration error with correct properties', () => {
      const context = { config: 'timezone', value: 'invalid' };
      const error = new ConfigurationError('Invalid timezone configuration', context);

      expect(error.message).toBe('Invalid timezone configuration');
      expect(error.code).toBe('CONFIGURATION_ERROR');
      expect(error.context).toEqual(context);
      expect(error.name).toBe('ConfigurationError');
    });
  });

  describe('Result Creation', () => {
    it('should create successful result', () => {
      const data = { value: 'test' };
      const result = createSuccess(data);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(data);
    });

    it('should create error result', () => {
      const error = new ValidationError('Test error');
      const result = createError(error);

      expect(result.success).toBe(false);
      expect(result.error).toBe(error);
    });
  });

  describe('Safe Execution', () => {
    it('should return success for successful function execution', () => {
      const fn = () => 'success';
      const result = safeExecute(fn, 'Test operation');

      expect(isSuccess(result)).toBe(true);
      if (isSuccess(result)) {
        expect(result.data).toBe('success');
      }
    });

    it('should return error for failed function execution', () => {
      const fn = () => {
        throw new Error('Test error');
      };
      const result = safeExecute(fn, 'Test operation', { context: 'test' });

      expect(isError(result)).toBe(true);
      if (isError(result)) {
        expect(result.error.message).toContain('Test operation');
        expect(result.error.message).toContain('Test error');
        expect(result.error.context?.context).toBe('test');
      }
    });

    it('should preserve utility errors', () => {
      const originalError = new ValidationError('Original error');
      const fn = () => {
        throw originalError;
      };
      const result = safeExecute(fn, 'Test operation');

      expect(isError(result)).toBe(true);
      if (isError(result)) {
        expect(result.error).toBe(originalError);
      }
    });
  });

  describe('Async Safe Execution', () => {
    it('should return success for successful async function execution', async () => {
      const fn = async () => 'async success';
      const result = await safeExecuteAsync(fn, 'Async test operation');

      expect(isSuccess(result)).toBe(true);
      if (isSuccess(result)) {
        expect(result.data).toBe('async success');
      }
    });

    it('should return error for failed async function execution', async () => {
      const fn = async () => {
        throw new Error('Async test error');
      };
      const result = await safeExecuteAsync(fn, 'Async test operation');

      expect(isError(result)).toBe(true);
      if (isError(result)) {
        expect(result.error.message).toContain('Async test operation');
        expect(result.error.message).toContain('Async test error');
      }
    });
  });

  describe('Type Guards', () => {
    it('should correctly identify successful results', () => {
      const successResult = createSuccess('data');
      const errorResult = createError(new ValidationError('error'));

      expect(isSuccess(successResult)).toBe(true);
      expect(isSuccess(errorResult)).toBe(false);
    });

    it('should correctly identify error results', () => {
      const successResult = createSuccess('data');
      const errorResult = createError(new ValidationError('error'));

      expect(isError(successResult)).toBe(false);
      expect(isError(errorResult)).toBe(true);
    });
  });

  describe('Result Unwrapping', () => {
    it('should unwrap successful results', () => {
      const data = 'test data';
      const result = createSuccess(data);

      expect(unwrapResult(result)).toBe(data);
    });

    it('should throw error for failed results', () => {
      const error = new ValidationError('Test error');
      const result = createError(error);

      expect(() => unwrapResult(result)).toThrow(error);
    });

    it('should return data for successful results with default', () => {
      const data = 'test data';
      const result = createSuccess(data);

      expect(getResultOrDefault(result, 'default')).toBe(data);
    });

    it('should return default for failed results', () => {
      const error = new ValidationError('Test error');
      const result = createError(error);
      const defaultValue = 'default value';

      expect(getResultOrDefault(result, defaultValue)).toBe(defaultValue);
    });
  });
});