import { describe, it, expect } from 'vitest';
import { mapPolicyFromDB, mapPolicyRequestFromDB } from '../policyMappers';
import type { PolicyFromDB, PolicyRequestFromDB } from '@/types/policy';

describe('Policy Mappers', () => {
  describe('mapPolicyFromDB', () => {
    it('should map policy from DB format to application format', () => {
      const dbPolicy: PolicyFromDB = {
        id: 'policy-123',
        title: 'Test Policy',
        description: 'Test policy description',
        category: 'Security',
        version: '1.0',
        status: 'active',
        effective_date: '2024-01-15',
        document_url: 'https://example.com/policy.pdf',
        created_by: 'user-123',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z'
      };

      const result = mapPolicyFromDB(dbPolicy);

      expect(result).toEqual({
        id: 'policy-123',
        title: 'Test Policy',
        description: 'Test policy description',
        category: 'Security',
        version: '1.0',
        status: 'active',
        effectiveDate: '2024-01-15',
        documentUrl: 'https://example.com/policy.pdf',
        createdBy: 'user-123',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-15T00:00:00Z'
      });
    });

    it('should handle null values correctly', () => {
      const dbPolicy: PolicyFromDB = {
        id: 'policy-123',
        title: 'Test Policy',
        description: 'Test policy description',
        category: 'Security',
        version: '1.0',
        status: 'active',
        effective_date: null,
        document_url: null,
        created_by: 'user-123',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z'
      };

      const result = mapPolicyFromDB(dbPolicy);

      expect(result.effectiveDate).toBeNull();
      expect(result.documentUrl).toBeNull();
      expect(result.id).toBe('policy-123');
      expect(result.title).toBe('Test Policy');
    });

    it('should preserve all required fields', () => {
      const dbPolicy: PolicyFromDB = {
        id: 'policy-456',
        title: 'Another Policy',
        description: 'Another description',
        category: 'Compliance',
        version: '2.1',
        status: 'draft',
        effective_date: '2024-02-01',
        document_url: 'https://example.com/another-policy.pdf',
        created_by: 'user-456',
        created_at: '2024-01-20T00:00:00Z',
        updated_at: '2024-01-25T00:00:00Z'
      };

      const result = mapPolicyFromDB(dbPolicy);

      // Verify all fields are mapped correctly
      expect(result.id).toBe(dbPolicy.id);
      expect(result.title).toBe(dbPolicy.title);
      expect(result.description).toBe(dbPolicy.description);
      expect(result.category).toBe(dbPolicy.category);
      expect(result.version).toBe(dbPolicy.version);
      expect(result.status).toBe(dbPolicy.status);
      expect(result.effectiveDate).toBe(dbPolicy.effective_date);
      expect(result.documentUrl).toBe(dbPolicy.document_url);
      expect(result.createdBy).toBe(dbPolicy.created_by);
      expect(result.createdAt).toBe(dbPolicy.created_at);
      expect(result.updatedAt).toBe(dbPolicy.updated_at);
    });
  });

  describe('mapPolicyRequestFromDB', () => {
    it('should map policy request from DB format to application format', () => {
      const dbRequest: PolicyRequestFromDB = {
        id: 'request-123',
        title: 'Test Policy Request',
        description: 'Test request description',
        reason: 'Need new security policy',
        status: 'pending',
        requester_id: 'user-123',
        reviewer_id: 'user-456',
        reference_document_url: 'https://example.com/reference.pdf',
        feedback: 'Looks good, needs minor changes',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z'
      };

      const result = mapPolicyRequestFromDB(dbRequest);

      expect(result).toEqual({
        id: 'request-123',
        title: 'Test Policy Request',
        description: 'Test request description',
        reason: 'Need new security policy',
        status: 'pending',
        requesterId: 'user-123',
        reviewerId: 'user-456',
        referenceDocumentUrl: 'https://example.com/reference.pdf',
        feedback: 'Looks good, needs minor changes',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2024-01-15T00:00:00Z'
      });
    });

    it('should handle null values correctly', () => {
      const dbRequest: PolicyRequestFromDB = {
        id: 'request-123',
        title: 'Test Policy Request',
        description: 'Test request description',
        reason: 'Need new security policy',
        status: 'pending',
        requester_id: 'user-123',
        reviewer_id: null,
        reference_document_url: null,
        feedback: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z'
      };

      const result = mapPolicyRequestFromDB(dbRequest);

      expect(result.reviewerId).toBeNull();
      expect(result.referenceDocumentUrl).toBeNull();
      expect(result.feedback).toBeNull();
      expect(result.id).toBe('request-123');
      expect(result.requesterId).toBe('user-123');
    });

    it('should preserve all required fields', () => {
      const dbRequest: PolicyRequestFromDB = {
        id: 'request-789',
        title: 'Another Request',
        description: 'Another description',
        reason: 'Update existing policy',
        status: 'approved',
        requester_id: 'user-789',
        reviewer_id: 'user-101',
        reference_document_url: 'https://example.com/ref.pdf',
        feedback: 'Approved with conditions',
        created_at: '2024-02-01T00:00:00Z',
        updated_at: '2024-02-05T00:00:00Z'
      };

      const result = mapPolicyRequestFromDB(dbRequest);

      // Verify all fields are mapped correctly
      expect(result.id).toBe(dbRequest.id);
      expect(result.title).toBe(dbRequest.title);
      expect(result.description).toBe(dbRequest.description);
      expect(result.reason).toBe(dbRequest.reason);
      expect(result.status).toBe(dbRequest.status);
      expect(result.requesterId).toBe(dbRequest.requester_id);
      expect(result.reviewerId).toBe(dbRequest.reviewer_id);
      expect(result.referenceDocumentUrl).toBe(dbRequest.reference_document_url);
      expect(result.feedback).toBe(dbRequest.feedback);
      expect(result.createdAt).toBe(dbRequest.created_at);
      expect(result.updatedAt).toBe(dbRequest.updated_at);
    });
  });
});