import { describe, it, expect } from "vitest";
import { formatRisksData, formatRiskData, formatIncidentsData } from "../riskTransformations";
import { RiskSeverity, RiskStatus } from "@/types";

describe("Risk Transformations", () => {
  describe("formatRisksData", () => {
    it("should transform array of Supabase risks to domain Risk objects", () => {
      const supabaseRisks = [
        {
          id: "risk-123",
          title: "Test Risk 1",
          description: "Test risk description 1",
          category_id: "cat-123",
          owner_id: "user-123",
          organization_id: "org-123",
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-15T00:00:00Z",
          inherent_likelihood: 4,
          inherent_impact: 3,
          inherent_severity: "High",
          likelihood: 2,
          impact: 3,
          severity: "Medium",
          status: "In Progress",
          current_controls: "Existing controls",
          mitigation_approach: "Risk mitigation plan",
          due_date: "2024-02-01T00:00:00Z",
          categories: { name: "Security" },
          profiles: { name: "<PERSON>" },
        },
        {
          id: "risk-456",
          title: "Test Risk 2",
          description: "Test risk description 2",
          category_id: "cat-456",
          owner_id: "user-456",
          organization_id: "org-123",
          created_at: "2024-01-10T00:00:00Z",
          updated_at: "2024-01-20T00:00:00Z",
          inherent_likelihood: 5,
          inherent_impact: 4,
          inherent_severity: "Critical",
          likelihood: 3,
          impact: 4,
          severity: "High",
          status: "Identified",
          current_controls: null,
          mitigation_approach: null,
          due_date: null,
          categories: { name: "Operational" },
          profiles: { name: "Jane Smith" },
        },
      ];

      const result = formatRisksData(supabaseRisks);

      expect(result).toHaveLength(2);

      // Check first risk
      expect(result[0]).toEqual({
        id: "risk-123",
        title: "Test Risk 1",
        description: "Test risk description 1",
        category: "Security",
        categoryId: "cat-123",
        ownerId: "user-123",
        ownerName: "John Doe",
        organizationId: "org-123",
        createdAt: new Date("2024-01-01T00:00:00Z"),
        updatedAt: new Date("2024-01-15T00:00:00Z"),
        inherentLikelihood: 4,
        inherentImpact: 3,
        inherentSeverity: RiskSeverity.HIGH,
        likelihood: 2,
        impact: 3,
        severity: RiskSeverity.MEDIUM,
        status: RiskStatus.IN_PROGRESS,
        currentControls: "Existing controls",
        mitigationApproach: "Risk mitigation plan",
        dueDate: new Date("2024-02-01T00:00:00Z"),
        controlMeasures: [],
        mitigationActions: [],
      });

      // Check second risk
      expect(result[1]).toEqual({
        id: "risk-456",
        title: "Test Risk 2",
        description: "Test risk description 2",
        category: "Operational",
        categoryId: "cat-456",
        ownerId: "user-456",
        ownerName: "Jane Smith",
        organizationId: "org-123",
        createdAt: new Date("2024-01-10T00:00:00Z"),
        updatedAt: new Date("2024-01-20T00:00:00Z"),
        inherentLikelihood: 5,
        inherentImpact: 4,
        inherentSeverity: RiskSeverity.CRITICAL,
        likelihood: 3,
        impact: 4,
        severity: RiskSeverity.HIGH,
        status: RiskStatus.IDENTIFIED,
        currentControls: null,
        mitigationApproach: null,
        dueDate: undefined,
        controlMeasures: [],
        mitigationActions: [],
      });
    });

    it("should handle missing nested objects gracefully", () => {
      const supabaseRisks = [
        {
          id: "risk-123",
          title: "Test Risk",
          description: "Test description",
          category_id: "cat-123",
          owner_id: null,
          organization_id: "org-123",
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-15T00:00:00Z",
          inherent_likelihood: 3,
          inherent_impact: 3,
          inherent_severity: "Medium",
          likelihood: 2,
          impact: 3,
          severity: "Medium",
          status: "Identified",
          current_controls: null,
          mitigation_approach: null,
          due_date: null,
          categories: null,
          profiles: null,
        },
      ];

      const result = formatRisksData(supabaseRisks);

      expect(result).toHaveLength(1);
      expect(result[0].category).toBe("Uncategorized");
      expect(result[0].ownerName).toBe("Unassigned");
      expect(result[0].ownerId).toBe("");
    });

    it("should handle empty array", () => {
      const result = formatRisksData([]);
      expect(result).toEqual([]);
    });

    it("should use inherent values as fallback when missing", () => {
      const supabaseRisks = [
        {
          id: "risk-123",
          title: "Test Risk",
          description: "Test description",
          category_id: "cat-123",
          owner_id: "user-123",
          organization_id: "org-123",
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-15T00:00:00Z",
          likelihood: 3,
          impact: 4,
          severity: "High",
          status: "Identified",
          current_controls: null,
          mitigation_approach: null,
          due_date: null,
          categories: { name: "Security" },
          profiles: { name: "John Doe" },
        },
      ];

      const result = formatRisksData(supabaseRisks);

      expect(result[0].inherentLikelihood).toBe(3);
      expect(result[0].inherentImpact).toBe(4);
      expect(result[0].inherentSeverity).toBe(RiskSeverity.HIGH);
    });
  });

  describe("formatRiskData", () => {
    it("should transform single Supabase risk to domain Risk object", () => {
      const supabaseRisk = {
        id: "risk-123",
        title: "Test Risk",
        description: "Test description",
        category_id: "cat-123",
        owner_id: "user-123",
        organization_id: "org-123",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-15T00:00:00Z",
        inherent_likelihood: 4,
        inherent_impact: 3,
        inherent_severity: "High",
        likelihood: 2,
        impact: 3,
        severity: "Medium",
        status: "In Progress",
        current_controls: "Existing controls",
        mitigation_approach: "Risk mitigation plan",
        due_date: "2024-02-01T00:00:00Z",
        categories: { name: "Security" },
        profiles: { name: "John Doe" },
      };

      const result = formatRiskData(supabaseRisk);

      expect(result).toEqual({
        id: "risk-123",
        title: "Test Risk",
        description: "Test description",
        category: "Security",
        categoryId: "cat-123",
        ownerId: "user-123",
        ownerName: "John Doe",
        organizationId: "org-123",
        createdAt: new Date("2024-01-01T00:00:00Z"),
        updatedAt: new Date("2024-01-15T00:00:00Z"),
        inherentLikelihood: 4,
        inherentImpact: 3,
        inherentSeverity: RiskSeverity.HIGH,
        likelihood: 2,
        impact: 3,
        severity: RiskSeverity.MEDIUM,
        status: RiskStatus.IN_PROGRESS,
        currentControls: "Existing controls",
        mitigationApproach: "Risk mitigation plan",
        dueDate: new Date("2024-02-01T00:00:00Z"),
        controlMeasures: [],
        mitigationActions: [],
      });
    });
  });

  describe("formatIncidentsData", () => {
    it("should transform array of Supabase incidents to domain objects", () => {
      const supabaseIncidents = [
        {
          id: "incident-123",
          title: "Test Incident 1",
          description: "Test incident description 1",
          reporter_id: "user-123",
          organization_id: "org-123",
          date: "2024-01-15T10:30:00Z",
          status: "Open",
          severity: "High",
          related_risk_id: "risk-123",
          profiles: { name: "John Reporter" },
        },
        {
          id: "incident-456",
          title: "Test Incident 2",
          description: "Test incident description 2",
          reporter_id: "user-456",
          organization_id: "org-123",
          date: "2024-01-20T14:45:00Z",
          status: "Closed",
          severity: "Medium",
          related_risk_id: null,
          profiles: { name: "Jane Reporter" },
        },
      ];

      const result = formatIncidentsData(supabaseIncidents);

      expect(result).toHaveLength(2);

      // Check first incident
      expect(result[0]).toEqual({
        id: "incident-123",
        title: "Test Incident 1",
        description: "Test incident description 1",
        reporterId: "user-123",
        reporterName: "John Reporter",
        organizationId: "org-123",
        date: new Date("2024-01-15T10:30:00Z"),
        status: "Open",
        severity: "High",
        relatedRiskId: "risk-123",
      });

      // Check second incident
      expect(result[1]).toEqual({
        id: "incident-456",
        title: "Test Incident 2",
        description: "Test incident description 2",
        reporterId: "user-456",
        reporterName: "Jane Reporter",
        organizationId: "org-123",
        date: new Date("2024-01-20T14:45:00Z"),
        status: "Closed",
        severity: "Medium",
        relatedRiskId: null,
      });
    });

    it("should handle missing profiles gracefully", () => {
      const supabaseIncidents = [
        {
          id: "incident-123",
          title: "Test Incident",
          description: "Test description",
          reporter_id: "user-123",
          organization_id: "org-123",
          date: "2024-01-15T10:30:00Z",
          status: "Open",
          severity: "High",
          related_risk_id: "risk-123",
          profiles: null,
        },
      ];

      const result = formatIncidentsData(supabaseIncidents);

      expect(result).toHaveLength(1);
      expect(result[0].reporterName).toBe("Unknown Reporter");
    });

    it("should handle empty array", () => {
      const result = formatIncidentsData([]);
      expect(result).toEqual([]);
    });

    it("should handle missing nested profile name", () => {
      const supabaseIncidents = [
        {
          id: "incident-123",
          title: "Test Incident",
          description: "Test description",
          reporter_id: "user-123",
          organization_id: "org-123",
          date: "2024-01-15T10:30:00Z",
          status: "Open",
          severity: "High",
          related_risk_id: "risk-123",
          profiles: {
            // Implementation needed
          },
        },
      ];

      const result = formatIncidentsData(supabaseIncidents);

      expect(result[0].reporterName).toBe("Unknown Reporter");
    });
  });
});
