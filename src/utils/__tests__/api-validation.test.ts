import { describe, it, expect } from 'vitest';
import {
  isRiskApiResponse,
  isIncidentApiResponse,
  isPolicyApiResponse,
  isPolicyRequestApiResponse,
  isUserProfileApiResponse,
  isOrganizationApiResponse,
  isUserInvitationApiResponse,
  isControlMeasureApiResponse,
  isMitigationActionApiResponse,
  isCommentApiResponse,
  isRiskHistoryApiResponse,
  isRiskApiResponseArray,
  isIncidentApiResponseArray,
  isPolicyApiResponseArray,
} from '../api-validation';

describe('API Validation', () => {
  describe('isRiskApiResponse', () => {
    it('should validate correct risk API response', () => {
      const validRisk = {
        id: 'risk-123',
        title: 'Test Risk',
        description: 'Test description',
        category_id: 'cat-123',
        owner_id: 'user-123',
        organization_id: 'org-123',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z',
        inherent_likelihood: 4,
        inherent_impact: 3,
        inherent_severity: 'High',
        likelihood: 2,
        impact: 3,
        severity: 'Medium',
        status: 'In Progress',
        current_controls: 'Existing controls',
        mitigation_approach: 'Risk mitigation plan',
        due_date: '2024-02-01T00:00:00Z',
        created_by: 'user-123',
        template_id: 'template-123'
      };

      expect(isRiskApiResponse(validRisk)).toBe(true);
    });

    it('should handle optional fields as null', () => {
      const validRisk = {
        id: 'risk-123',
        title: 'Test Risk',
        description: 'Test description',
        category_id: null,
        owner_id: 'user-123',
        organization_id: 'org-123',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z',
        inherent_likelihood: 4,
        inherent_impact: 3,
        inherent_severity: 'High',
        likelihood: 2,
        impact: 3,
        severity: 'Medium',
        status: 'In Progress',
        current_controls: null,
        mitigation_approach: null,
        due_date: null,
        created_by: 'user-123',
        template_id: null
      };

      expect(isRiskApiResponse(validRisk)).toBe(true);
    });

    it('should reject invalid risk API response', () => {
      const invalidRisk = {
        id: 'risk-123',
        title: 'Test Risk',
        // missing required fields
      };

      expect(isRiskApiResponse(invalidRisk)).toBe(false);
      expect(isRiskApiResponse(null)).toBe(false);
      expect(isRiskApiResponse('invalid')).toBe(false);
      expect(isRiskApiResponse([])).toBe(false);
    });

    it('should reject risk with wrong field types', () => {
      const invalidRisk = {
        id: 123, // should be string
        title: 'Test Risk',
        description: 'Test description',
        category_id: 'cat-123',
        owner_id: 'user-123',
        organization_id: 'org-123',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z',
        inherent_likelihood: 4,
        inherent_impact: 3,
        inherent_severity: 'High',
        likelihood: 2,
        impact: 3,
        severity: 'Medium',
        status: 'In Progress',
        current_controls: 'Existing controls',
        mitigation_approach: 'Risk mitigation plan',
        due_date: '2024-02-01T00:00:00Z',
        created_by: 'user-123',
        template_id: 'template-123'
      };

      expect(isRiskApiResponse(invalidRisk)).toBe(false);
    });
  });

  describe('isIncidentApiResponse', () => {
    it('should validate correct incident API response', () => {
      const validIncident = {
        id: 'incident-123',
        title: 'Test Incident',
        description: 'Test description',
        reporter_id: 'user-123',
        organization_id: 'org-123',
        date: '2024-01-15T10:30:00Z',
        status: 'Open',
        severity: 'High',
        related_risk_id: 'risk-123',
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-15T10:30:00Z'
      };

      expect(isIncidentApiResponse(validIncident)).toBe(true);
    });

    it('should handle optional related_risk_id as null', () => {
      const validIncident = {
        id: 'incident-123',
        title: 'Test Incident',
        description: 'Test description',
        reporter_id: 'user-123',
        organization_id: 'org-123',
        date: '2024-01-15T10:30:00Z',
        status: 'Open',
        severity: 'High',
        related_risk_id: null,
        created_at: '2024-01-15T10:30:00Z',
        updated_at: '2024-01-15T10:30:00Z'
      };

      expect(isIncidentApiResponse(validIncident)).toBe(true);
    });

    it('should reject invalid incident API response', () => {
      const invalidIncident = {
        id: 'incident-123',
        // missing required fields
      };

      expect(isIncidentApiResponse(invalidIncident)).toBe(false);
      expect(isIncidentApiResponse(null)).toBe(false);
    });
  });

  describe('isPolicyApiResponse', () => {
    it('should validate correct policy API response', () => {
      const validPolicy = {
        id: 'policy-123',
        title: 'Test Policy',
        description: 'Test description',
        category: 'Security',
        status: 'active',
        version: '1.0',
        created_by: 'user-123',
        organization_id: 'org-123',
        effective_date: '2024-01-15',
        document_url: 'https://example.com/policy.pdf',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z'
      };

      expect(isPolicyApiResponse(validPolicy)).toBe(true);
    });

    it('should handle optional fields as null', () => {
      const validPolicy = {
        id: 'policy-123',
        title: 'Test Policy',
        description: 'Test description',
        category: 'Security',
        status: 'active',
        version: '1.0',
        created_by: 'user-123',
        organization_id: 'org-123',
        effective_date: null,
        document_url: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z'
      };

      expect(isPolicyApiResponse(validPolicy)).toBe(true);
    });
  });

  describe('isPolicyRequestApiResponse', () => {
    it('should validate correct policy request API response', () => {
      const validRequest = {
        id: 'request-123',
        title: 'Test Request',
        description: 'Test description',
        reason: 'Need new policy',
        status: 'pending',
        requester_id: 'user-123',
        reviewer_id: 'user-456',
        organization_id: 'org-123',
        reference_document_url: 'https://example.com/ref.pdf',
        feedback: 'Looks good',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z'
      };

      expect(isPolicyRequestApiResponse(validRequest)).toBe(true);
    });

    it('should handle optional fields as null', () => {
      const validRequest = {
        id: 'request-123',
        title: 'Test Request',
        description: 'Test description',
        reason: 'Need new policy',
        status: 'pending',
        requester_id: 'user-123',
        reviewer_id: null,
        organization_id: 'org-123',
        reference_document_url: null,
        feedback: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z'
      };

      expect(isPolicyRequestApiResponse(validRequest)).toBe(true);
    });
  });

  describe('isUserProfileApiResponse', () => {
    it('should validate correct user profile API response', () => {
      const validProfile = {
        id: 'user-123',
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'admin',
        department: 'IT',
        avatar_url: 'https://example.com/avatar.jpg',
        organization_id: 'org-123',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z',
        deleted_at: null
      };

      expect(isUserProfileApiResponse(validProfile)).toBe(true);
    });

    it('should handle optional fields as null', () => {
      const validProfile = {
        id: 'user-123',
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'admin',
        department: null,
        avatar_url: null,
        organization_id: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z',
        deleted_at: null
      };

      expect(isUserProfileApiResponse(validProfile)).toBe(true);
    });
  });

  describe('isOrganizationApiResponse', () => {
    it('should validate correct organization API response', () => {
      const validOrg = {
        id: 'org-123',
        name: 'Test Organization',
        slug: 'test-org',
        domain: 'test.com',
        logo_url: 'https://example.com/logo.png',
        subscription_plan: 'premium',
        subscription_status: 'active',
        max_users: 100,
        max_risks: 1000,
        organization_size: 'medium',
        sector_type: 'technology',
        sector_description: 'Software development',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z'
      };

      expect(isOrganizationApiResponse(validOrg)).toBe(true);
    });

    it('should handle optional fields as null', () => {
      const validOrg = {
        id: 'org-123',
        name: 'Test Organization',
        slug: 'test-org',
        domain: null,
        logo_url: null,
        subscription_plan: null,
        subscription_status: null,
        max_users: null,
        max_risks: null,
        organization_size: null,
        sector_type: null,
        sector_description: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z'
      };

      expect(isOrganizationApiResponse(validOrg)).toBe(true);
    });
  });

  describe('Array validation functions', () => {
    it('should validate risk API response arrays', () => {
      const validRisks = [
        {
          id: 'risk-123',
          title: 'Test Risk',
          description: 'Test description',
          category_id: 'cat-123',
          owner_id: 'user-123',
          organization_id: 'org-123',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-15T00:00:00Z',
          inherent_likelihood: 4,
          inherent_impact: 3,
          inherent_severity: 'High',
          likelihood: 2,
          impact: 3,
          severity: 'Medium',
          status: 'In Progress',
          current_controls: 'Existing controls',
          mitigation_approach: 'Risk mitigation plan',
          due_date: '2024-02-01T00:00:00Z',
          created_by: 'user-123',
          template_id: 'template-123'
        }
      ];

      expect(isRiskApiResponseArray(validRisks)).toBe(true);
      expect(isRiskApiResponseArray([])).toBe(true);
      expect(isRiskApiResponseArray([{ invalid: 'object' }])).toBe(false);
      expect(isRiskApiResponseArray('not an array')).toBe(false);
    });

    it('should validate incident API response arrays', () => {
      const validIncidents = [
        {
          id: 'incident-123',
          title: 'Test Incident',
          description: 'Test description',
          reporter_id: 'user-123',
          organization_id: 'org-123',
          date: '2024-01-15T10:30:00Z',
          status: 'Open',
          severity: 'High',
          related_risk_id: 'risk-123',
          created_at: '2024-01-15T10:30:00Z',
          updated_at: '2024-01-15T10:30:00Z'
        }
      ];

      expect(isIncidentApiResponseArray(validIncidents)).toBe(true);
      expect(isIncidentApiResponseArray([])).toBe(true);
      expect(isIncidentApiResponseArray([{ invalid: 'object' }])).toBe(false);
    });

    it('should validate policy API response arrays', () => {
      const validPolicies = [
        {
          id: 'policy-123',
          title: 'Test Policy',
          description: 'Test description',
          category: 'Security',
          status: 'active',
          version: '1.0',
          created_by: 'user-123',
          organization_id: 'org-123',
          effective_date: '2024-01-15',
          document_url: 'https://example.com/policy.pdf',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-15T00:00:00Z'
        }
      ];

      expect(isPolicyApiResponseArray(validPolicies)).toBe(true);
      expect(isPolicyApiResponseArray([])).toBe(true);
      expect(isPolicyApiResponseArray([{ invalid: 'object' }])).toBe(false);
    });
  });

  describe('isControlMeasureApiResponse', () => {
    it('should validate correct control measure API response', () => {
      const validControlMeasure = {
        id: 'control-123',
        risk_id: 'risk-123',
        organization_id: 'org-123',
        description: 'Test control measure',
        effectiveness: 'high',
        implemented: true,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z'
      };

      expect(isControlMeasureApiResponse(validControlMeasure)).toBe(true);
    });

    it('should handle optional effectiveness as null', () => {
      const validControlMeasure = {
        id: 'control-123',
        risk_id: 'risk-123',
        organization_id: 'org-123',
        description: 'Test control measure',
        effectiveness: null,
        implemented: false,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z'
      };

      expect(isControlMeasureApiResponse(validControlMeasure)).toBe(true);
    });
  });

  describe('isMitigationActionApiResponse', () => {
    it('should validate correct mitigation action API response', () => {
      const validMitigationAction = {
        id: 'action-123',
        risk_id: 'risk-123',
        organization_id: 'org-123',
        description: 'Test mitigation action',
        completed: false,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z'
      };

      expect(isMitigationActionApiResponse(validMitigationAction)).toBe(true);
    });
  });

  describe('isCommentApiResponse', () => {
    it('should validate correct comment API response', () => {
      const validComment = {
        id: 'comment-123',
        content: 'Test comment',
        entity_type: 'risk',
        entity_id: 'risk-123',
        user_id: 'user-123',
        organization_id: 'org-123',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z',
        deleted_at: null
      };

      expect(isCommentApiResponse(validComment)).toBe(true);
    });

    it('should handle optional deleted_at as null', () => {
      const validComment = {
        id: 'comment-123',
        content: 'Test comment',
        entity_type: 'risk',
        entity_id: 'risk-123',
        user_id: 'user-123',
        organization_id: 'org-123',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-15T00:00:00Z',
        deleted_at: '2024-01-20T00:00:00Z'
      };

      expect(isCommentApiResponse(validComment)).toBe(true);
    });
  });

  describe('isRiskHistoryApiResponse', () => {
    it('should validate correct risk history API response', () => {
      const validHistory = {
        id: 'history-123',
        risk_id: 'risk-123',
        organization_id: 'org-123',
        recorded_at: '2024-01-15T10:30:00Z',
        likelihood: 3,
        impact: 4,
        severity: 'High',
        status: 'In Progress',
        created_by: 'user-123'
      };

      expect(isRiskHistoryApiResponse(validHistory)).toBe(true);
    });

    it('should handle optional created_by as null', () => {
      const validHistory = {
        id: 'history-123',
        risk_id: 'risk-123',
        organization_id: 'org-123',
        recorded_at: '2024-01-15T10:30:00Z',
        likelihood: 3,
        impact: 4,
        severity: 'High',
        status: 'In Progress',
        created_by: null
      };

      expect(isRiskHistoryApiResponse(validHistory)).toBe(true);
    });
  });
});