/**
 * Memory Usage Monitoring Utility
 * Monitors memory usage and detects potential memory leaks
 */
export interface MemorySnapshot {
  timestamp: number;
  usedJSHeapSize: number;
  totalJSHeapSize: number;
  jsHeapSizeLimit: number;
  url: string;
  route: string;
}
export interface MemoryLeakDetection {
  isLeakDetected: boolean;
  leakRate: number; // MB per minute
  confidence: number; // 0-1 confidence score
  recommendations: string[];
}
export interface MemoryUsageReport {
  snapshots: MemorySnapshot[];
  peakUsage: number;
  averageUsage: number;
  leakDetection: MemoryLeakDetection;
  summary: {
    withinBudget: boolean;
    budget: number;
    currentUsage: number;
    efficiency: number;
  };
}
export class MemoryUsageMonitor {
  private snapshots: MemorySnapshot[] = [];
  private isMonitoring = false;
  private monitoringInterval: number | null = null;
  private memoryBudget: number;
  constructor(memoryBudget: number = 50 * 1024 * 1024) {
    // 50MB default
    this.memoryBudget = memoryBudget;
  }
  /**
   * Start monitoring memory usage
   */
  startMonitoring(intervalMs: number = 5000): void {
    if (this.isMonitoring) {
      return;
    }
    if (!this.isMemoryAPIAvailable()) {
      return;
    }
    this.isMonitoring = true;
    this.snapshots = [];
    this.monitoringInterval = window.setInterval(() => {
      this.takeSnapshot();
    }, intervalMs);
    // Take initial snapshot
    this.takeSnapshot();
  }
  /**
   * Stop monitoring memory usage
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    this.isMonitoring = false;
  }
  /**
   * Take a memory snapshot
   */
  takeSnapshot(): MemorySnapshot | null {
    if (!this.isMemoryAPIAvailable()) {
      return null;
    }
    const memory = (performance as any).memory;
    const snapshot: MemorySnapshot = {
      timestamp: Date.now(),
      usedJSHeapSize: memory.usedJSHeapSize,
      totalJSHeapSize: memory.totalJSHeapSize,
      jsHeapSizeLimit: memory.jsHeapSizeLimit,
      url: window.location.href,
      route: window.location.pathname,
    };
    this.snapshots.push(snapshot);
    // Keep only last 100 snapshots to prevent memory issues
    if (this.snapshots.length > 100) {
      this.snapshots = this.snapshots.slice(-100);
    }
    return snapshot;
  }
  /**
   * Get current memory usage
   */
  getCurrentMemoryUsage(): MemorySnapshot | null {
    return this.takeSnapshot();
  }
  /**
   * Detect potential memory leaks
   */
  detectMemoryLeaks(): MemoryLeakDetection {
    if (this.snapshots.length < 5) {
      return {
        isLeakDetected: false,
        leakRate: 0,
        confidence: 0,
        recommendations: ["Insufficient data for leak detection. Monitor for longer period."],
      };
    }
    // Calculate memory growth trend
    const recentSnapshots = this.snapshots.slice(-10); // Last 10 snapshots
    const timeSpan =
      recentSnapshots[recentSnapshots.length - 1].timestamp - recentSnapshots[0].timestamp;
    const memoryGrowth =
      recentSnapshots[recentSnapshots.length - 1].usedJSHeapSize -
      recentSnapshots[0].usedJSHeapSize;
    // Calculate leak rate in MB per minute
    const leakRate = memoryGrowth / (1024 * 1024) / (timeSpan / (1000 * 60));
    // Determine if leak is detected
    const isLeakDetected = leakRate > 1; // More than 1MB per minute growth
    // Calculate confidence based on consistency of growth
    const growthRates: number[] = [];
    for (let i = 1; i < recentSnapshots.length; i++) {
      const timeDiff = recentSnapshots[i].timestamp - recentSnapshots[i - 1].timestamp;
      const memoryDiff = recentSnapshots[i].usedJSHeapSize - recentSnapshots[i - 1].usedJSHeapSize;
      const rate = memoryDiff / (1024 * 1024) / (timeDiff / (1000 * 60));
      growthRates.push(rate);
    }
    const avgGrowthRate = growthRates.reduce((sum, rate) => sum + rate, 0) / growthRates.length;
    const variance =
      growthRates.reduce((sum, rate) => sum + Math.pow(rate - avgGrowthRate, 2), 0) /
      growthRates.length;
    const confidence = Math.max(0, Math.min(1, 1 - variance / Math.abs(avgGrowthRate)));
    // Generate recommendations
    const recommendations: string[] = [];
    if (isLeakDetected) {
      recommendations.push("Potential memory leak detected");
      recommendations.push("Check for uncleaned event listeners");
      recommendations.push("Verify useEffect cleanup functions");
      recommendations.push("Review component unmounting logic");
      recommendations.push("Check for circular references");
      if (leakRate > 5) {
        recommendations.push("CRITICAL: High memory leak rate detected");
        recommendations.push("Immediate investigation required");
      }
    } else {
      recommendations.push("No significant memory leaks detected");
      recommendations.push("Continue monitoring during heavy usage");
    }
    return {
      isLeakDetected,
      leakRate,
      confidence,
      recommendations,
    };
  }
  /**
   * Generate comprehensive memory usage report
   */
  generateReport(): MemoryUsageReport {
    if (this.snapshots.length === 0) {
      throw new Error("No memory snapshots available. Start monitoring first.");
    }
    const usedMemorySizes = this.snapshots.map(s => s.usedJSHeapSize);
    const peakUsage = Math.max(...usedMemorySizes);
    const averageUsage =
      usedMemorySizes.reduce((sum, size) => sum + size, 0) / usedMemorySizes.length;
    const currentUsage = usedMemorySizes[usedMemorySizes.length - 1];
    const leakDetection = this.detectMemoryLeaks();
    const withinBudget = currentUsage <= this.memoryBudget;
    const efficiency = Math.min(1, this.memoryBudget / currentUsage);
    return {
      snapshots: [...this.snapshots],
      peakUsage,
      averageUsage,
      leakDetection,
      summary: {
        withinBudget,
        budget: this.memoryBudget,
        currentUsage,
        efficiency,
      },
    };
  }
  /**
   * Get memory usage statistics
   */
  getMemoryStats(): {
    current: number;
    peak: number;
    average: number;
    budget: number;
    utilization: number;
    trend: "increasing" | "decreasing" | "stable";
  } {
    if (this.snapshots.length === 0) {
      return {
        current: 0,
        peak: 0,
        average: 0,
        budget: this.memoryBudget,
        utilization: 0,
        trend: "stable",
      };
    }
    const usedMemorySizes = this.snapshots.map(s => s.usedJSHeapSize);
    const current = usedMemorySizes[usedMemorySizes.length - 1];
    const peak = Math.max(...usedMemorySizes);
    const average = usedMemorySizes.reduce((sum, size) => sum + size, 0) / usedMemorySizes.length;
    const utilization = current / this.memoryBudget;
    // Determine trend
    let trend: "increasing" | "decreasing" | "stable" = "stable";
    if (this.snapshots.length >= 5) {
      const recent = usedMemorySizes.slice(-5);
      const older = usedMemorySizes.slice(-10, -5);
      if (older.length > 0) {
        const recentAvg = recent.reduce((sum, size) => sum + size, 0) / recent.length;
        const olderAvg = older.reduce((sum, size) => sum + size, 0) / older.length;
        const changePercent = (recentAvg - olderAvg) / olderAvg;
        if (changePercent > 0.1) trend = "increasing";
        else if (changePercent < -0.1) trend = "decreasing";
      }
    }
    return {
      current,
      peak,
      average,
      budget: this.memoryBudget,
      utilization,
      trend,
    };
  }
  /**
   * Force garbage collection (if available)
   */
  forceGarbageCollection(): boolean {
    if (typeof window !== "undefined" && (window as any).gc) {
      (window as any).gc();
      return true;
    }
    return false;
  }
  /**
   * Check if Memory API is available
   */
  private isMemoryAPIAvailable(): boolean {
    return (
      typeof window !== "undefined" &&
      typeof performance !== "undefined" &&
      (performance as any).memory
    );
  }
  /**
   * Clear all snapshots
   */
  clearSnapshots(): void {
    this.snapshots = [];
  }
  /**
   * Export snapshots for analysis
   */
  exportSnapshots(): string {
    return JSON.stringify(this.snapshots, null, 2);
  }
  /**
   * Import snapshots from exported data
   */
  importSnapshots(data: string): void {
    try {
      const snapshots = JSON.parse(data);
      if (Array.isArray(snapshots)) {
        this.snapshots = snapshots;
      }
    } catch (error) {
      // Error caught and handled
    }
  }
}
/**
 * Utility function to format memory size
 */
export function formatMemorySize(bytes: number): string {
  if (bytes === 0) return "0 B";
  const k = 1024;
  const sizes = ["B", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}
/**
 * Utility function to get memory usage color based on utilization
 */
export function getMemoryUsageColor(utilization: number): string {
  if (utilization < 0.5) return "green";
  if (utilization < 0.75) return "yellow";
  if (utilization < 0.9) return "orange";
  return "red";
}
/**
 * Global memory monitor instance
 */
export const globalMemoryMonitor = new MemoryUsageMonitor();
/**
 * Hook for React components to monitor memory usage
 */
export function useMemoryMonitor(autoStart: boolean = false) {
  const [memoryStats, setMemoryStats] = React.useState(globalMemoryMonitor.getMemoryStats());
  React.useEffect(() => {
    if (autoStart) {
      globalMemoryMonitor.startMonitoring();
    }
    const interval = setInterval(() => {
      setMemoryStats(globalMemoryMonitor.getMemoryStats());
    }, 1000);
    return () => {
      clearInterval(interval);
      if (autoStart) {
        globalMemoryMonitor.stopMonitoring();
      }
    };
  }, [autoStart]);
  return {
    memoryStats,
    startMonitoring: () => globalMemoryMonitor.startMonitoring(),
    stopMonitoring: () => globalMemoryMonitor.stopMonitoring(),
    takeSnapshot: () => globalMemoryMonitor.takeSnapshot(),
    generateReport: () => globalMemoryMonitor.generateReport(),
    forceGC: () => globalMemoryMonitor.forceGarbageCollection(),
  };
}
// Add React import for the hook
import React from "react";
