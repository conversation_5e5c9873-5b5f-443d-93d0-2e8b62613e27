/**
 * Dynamic import utilities for heavy libraries and components
 * This helps reduce initial bundle size by loading heavy dependencies only when needed
 */
import { ComponentType, lazy } from "react";
/**
 * Dynamic import for chart components (Recharts)
 */
export const loadChartComponents = () => {
  return import("recharts");
};
/**
 * Dynamic import for PDF generation (jsPDF)
 */
export const loadPDFLibrary = () => {
  return import("jspdf").then(module => ({
    jsPDF: module.jsPDF,
    default: module.default,
  }));
};
/**
 * Dynamic import for Excel processing (XLSX)
 */
export const loadExcelLibrary = () => {
  return import("xlsx");
};
/**
 * Dynamic import for HTML to Canvas conversion
 */
export const loadHtml2Canvas = () => {
  return import("html2canvas");
};
/**
 * Dynamic import for file saving utilities
 */
export const loadFileSaver = () => {
  return import("file-saver");
};
/**
 * Dynamic import for drag and drop functionality
 */
export const loadDragAndDrop = () => {
  return import("react-beautiful-dnd");
};
/**
 * Create a lazy-loaded chart component wrapper
 */
export const createLazyChartComponent = <T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T } | T>
) => {
  return lazy(async () => {
    const module = await importFn();
    // Handle both default and named exports
    if ("default" in module) {
      return module as { default: T };
    }
    return { default: module as T };
  });
};
/**
 * Preload heavy libraries for better UX
 */
export const preloadHeavyLibraries = () => {
  // Preload chart library if user is likely to see charts
  const preloadCharts = () => {
    setTimeout(() => {
      loadChartComponents().catch(() => {
        // Silently fail preloading
      });
    }, 2000);
  };
  // Preload PDF library if user has export permissions
  const preloadPDF = () => {
    setTimeout(() => {
      loadPDFLibrary().catch(() => {
        // Silently fail preloading
      });
    }, 3000);
  };
  return {
    preloadCharts,
    preloadPDF,
  };
};
/**
 * Bundle size monitoring utilities
 */
export const monitorDynamicImports = () => {
  if (import.meta.env.MODE === "development") {
    const originalImport = window.import || ((specifier: string) => import(specifier));
    // Override dynamic import to log loading
    const monitoredImport = (specifier: string) => {
      const startTime = performance.now();
      return originalImport(specifier)
        .then(module => {
          const loadTime = performance.now() - startTime;
          return module;
        })
        .catch(error => {
          throw error;
        });
    };
    // Store reference for cleanup
    return monitoredImport;
  }
  return null;
};
/**
 * Utility to check if a heavy library is already loaded
 */
export const isLibraryLoaded = (libraryName: string): boolean => {
  // Check if the library is already in the module cache
  // This is a heuristic check for development purposes
  const scripts = Array.from(document.querySelectorAll("script[src]"));
  return scripts.some(script =>
    (script as HTMLScriptElement).src.includes(libraryName.toLowerCase())
  );
};
/**
 * Smart loading strategy for heavy components
 */
export const createSmartLoader = <T>(
  importFn: () => Promise<T>,
  options: {
    preload?: boolean;
    preloadDelay?: number;
    cacheName?: string;
  } = {
    // Implementation needed
  }
) => {
  const { preload = false, preloadDelay = 1000, cacheName } = options;
  let loadPromise: Promise<T> | null = null;
  const load = (): Promise<T> => {
    if (!loadPromise) {
      loadPromise = importFn();
    }
    return loadPromise;
  };
  // Preload if requested
  if (preload) {
    setTimeout(() => {
      load().catch(() => {
        // Silently fail preloading
      });
    }, preloadDelay);
  }
  return load;
};
export default {
  loadChartComponents,
  loadPDFLibrary,
  loadExcelLibrary,
  loadHtml2Canvas,
  loadFileSaver,
  loadDragAndDrop,
  createLazyChartComponent,
  preloadHeavyLibraries,
  monitorDynamicImports,
  isLibraryLoaded,
  createSmartLoader,
};
