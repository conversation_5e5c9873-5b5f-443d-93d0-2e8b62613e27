/**
 * Performance Budget Enforcer
 * Enforces performance budgets in CI/CD pipeline and provides alerts
 */
import {
  PerformanceBudget,
  PerformanceAlert,
  DEFAULT_PERFORMANCE_BUDGET,
} from "./performance-metrics-collector";
export interface BudgetEnforcementConfig {
  budget: PerformanceBudget;
  failOnCritical: boolean;
  failOnError: boolean;
  alertWebhookUrl?: string;
  slackWebhookUrl?: string;
  emailNotifications?: string[];
}
export interface BudgetViolation {
  metric: string;
  actual: number;
  budget: number;
  severity: "warning" | "error" | "critical";
  percentage: number;
  message: string;
}
export interface EnforcementResult {
  passed: boolean;
  violations: BudgetViolation[];
  summary: {
    total: number;
    critical: number;
    errors: number;
    warnings: number;
  };
  recommendations: string[];
}
/**
 * Performance Budget Enforcer Class
 */
export class PerformanceBudgetEnforcer {
  private config: BudgetEnforcementConfig;
  constructor(config: Partial<BudgetEnforcementConfig> = {}) {
    this.config = {
      budget: DEFAULT_PERFORMANCE_BUDGET,
      failOnCritical: true,
      failOnError: false,
      ...config,
    };
  }
  /**
   * Enforce budget against current performance metrics
   */
  public async enforceBudget(metrics: any): Promise<EnforcementResult> {
    const violations: BudgetViolation[] = [];
    // Check First Contentful Paint
    if (
      metrics.firstContentfulPaint &&
      metrics.firstContentfulPaint > this.config.budget.firstContentfulPaint
    ) {
      violations.push(
        this.createViolation(
          "firstContentfulPaint",
          metrics.firstContentfulPaint,
          this.config.budget.firstContentfulPaint,
          "First Contentful Paint exceeds budget"
        )
      );
    }
    // Check Largest Contentful Paint
    if (
      metrics.largestContentfulPaint &&
      metrics.largestContentfulPaint > this.config.budget.largestContentfulPaint
    ) {
      violations.push(
        this.createViolation(
          "largestContentfulPaint",
          metrics.largestContentfulPaint,
          this.config.budget.largestContentfulPaint,
          "Largest Contentful Paint exceeds budget"
        )
      );
    }
    // Check Cumulative Layout Shift
    if (
      metrics.cumulativeLayoutShift &&
      metrics.cumulativeLayoutShift > this.config.budget.cumulativeLayoutShift
    ) {
      violations.push(
        this.createViolation(
          "cumulativeLayoutShift",
          metrics.cumulativeLayoutShift,
          this.config.budget.cumulativeLayoutShift,
          "Cumulative Layout Shift exceeds budget"
        )
      );
    }
    // Check Time to Interactive
    if (
      metrics.timeToInteractive &&
      metrics.timeToInteractive > this.config.budget.timeToInteractive
    ) {
      violations.push(
        this.createViolation(
          "timeToInteractive",
          metrics.timeToInteractive,
          this.config.budget.timeToInteractive,
          "Time to Interactive exceeds budget"
        )
      );
    }
    // Check Total Bundle Size
    if (metrics.bundleSize) {
      const totalBundleSize = metrics.bundleSize.totalJS + metrics.bundleSize.totalCSS;
      if (totalBundleSize > this.config.budget.totalBundleSize) {
        violations.push(
          this.createViolation(
            "totalBundleSize",
            totalBundleSize,
            this.config.budget.totalBundleSize,
            "Total bundle size exceeds budget"
          )
        );
      }
      // Check individual chunk sizes
      if (metrics.bundleSize.chunkSizes) {
        Object.entries(metrics.bundleSize.chunkSizes).forEach(([chunkName, size]) => {
          if (typeof size === "number" && size > this.config.budget.maxChunkSize) {
            violations.push(
              this.createViolation(
                `chunkSize.${chunkName}`,
                size,
                this.config.budget.maxChunkSize,
                `Chunk ${chunkName} exceeds size budget`
              )
            );
          }
        });
      }
    }
    // Calculate summary
    const summary = {
      total: violations.length,
      critical: violations.filter(v => v.severity === "critical").length,
      errors: violations.filter(v => v.severity === "error").length,
      warnings: violations.filter(v => v.severity === "warning").length,
    };
    // Determine if enforcement passed
    const passed = !(
      (this.config.failOnCritical && summary.critical > 0) ||
      (this.config.failOnError && summary.errors > 0)
    );
    // Generate recommendations
    const recommendations = this.generateRecommendations(violations);
    const result: EnforcementResult = {
      passed,
      violations,
      summary,
      recommendations,
    };
    // Send notifications if configured
    if (violations.length > 0) {
      await this.sendNotifications(result);
    }
    return result;
  }
  /**
   * Enforce budget against bundle analysis results
   */
  public async enforceBundleBudget(bundleAnalysis: {
    totalJSSize: number;
    totalCSSSize: number;
    jsFiles: Array<{ name: string; size: number }>;
  }): Promise<EnforcementResult> {
    const violations: BudgetViolation[] = [];
    // Check total bundle size
    const totalBundleSize = bundleAnalysis.totalJSSize + bundleAnalysis.totalCSSSize;
    if (totalBundleSize > this.config.budget.totalBundleSize) {
      violations.push(
        this.createViolation(
          "totalBundleSize",
          totalBundleSize,
          this.config.budget.totalBundleSize,
          "Total bundle size exceeds budget"
        )
      );
    }
    // Check individual chunk sizes
    bundleAnalysis.jsFiles.forEach(file => {
      if (file.size > this.config.budget.maxChunkSize) {
        violations.push(
          this.createViolation(
            `chunkSize.${file.name}`,
            file.size,
            this.config.budget.maxChunkSize,
            `Chunk ${file.name} exceeds size budget`
          )
        );
      }
    });
    const summary = {
      total: violations.length,
      critical: violations.filter(v => v.severity === "critical").length,
      errors: violations.filter(v => v.severity === "error").length,
      warnings: violations.filter(v => v.severity === "warning").length,
    };
    const passed = !(
      (this.config.failOnCritical && summary.critical > 0) ||
      (this.config.failOnError && summary.errors > 0)
    );
    const recommendations = this.generateRecommendations(violations);
    const result: EnforcementResult = {
      passed,
      violations,
      summary,
      recommendations,
    };
    if (violations.length > 0) {
      await this.sendNotifications(result);
    }
    return result;
  }
  private createViolation(
    metric: string,
    actual: number,
    budget: number,
    message: string
  ): BudgetViolation {
    const percentage = ((actual - budget) / budget) * 100;
    let severity: "warning" | "error" | "critical";
    if (percentage > 100) {
      severity = "critical";
    } else if (percentage > 50) {
      severity = "error";
    } else {
      severity = "warning";
    }
    return {
      metric,
      actual,
      budget,
      severity,
      percentage,
      message,
    };
  }
  private generateRecommendations(violations: BudgetViolation[]): string[] {
    const recommendations: string[] = [];
    const seenRecommendations = new Set<string>();
    violations.forEach(violation => {
      let recommendation: string;
      switch (violation.metric) {
        case "firstContentfulPaint":
          recommendation = "Optimize critical rendering path, reduce render-blocking resources";
          break;
        case "largestContentfulPaint":
          recommendation = "Optimize images and largest content elements, implement lazy loading";
          break;
        case "cumulativeLayoutShift":
          recommendation =
            "Set explicit dimensions for images and ads, avoid inserting content above existing content";
          break;
        case "timeToInteractive":
          recommendation = "Reduce JavaScript execution time, implement code splitting";
          break;
        case "totalBundleSize":
          recommendation =
            "Implement aggressive code splitting, remove unused dependencies, use tree shaking";
          break;
        default:
          if (violation.metric.startsWith("chunkSize.")) {
            recommendation = "Split large chunks into smaller pieces, implement dynamic imports";
          } else {
            recommendation = `Optimize ${violation.metric} performance`;
          }
      }
      if (!seenRecommendations.has(recommendation)) {
        recommendations.push(recommendation);
        seenRecommendations.add(recommendation);
      }
    });
    return recommendations;
  }
  private async sendNotifications(result: EnforcementResult): Promise<void> {
    const promises: Promise<void>[] = [];
    // Send webhook notification
    if (this.config.alertWebhookUrl) {
      promises.push(this.sendWebhookNotification(result));
    }
    // Send Slack notification
    if (this.config.slackWebhookUrl) {
      promises.push(this.sendSlackNotification(result));
    }
    // Send email notifications
    if (this.config.emailNotifications && this.config.emailNotifications.length > 0) {
      promises.push(this.sendEmailNotifications(result));
    }
    await Promise.allSettled(promises);
  }
  private async sendWebhookNotification(result: EnforcementResult): Promise<void> {
    if (!this.config.alertWebhookUrl) return;
    try {
      const payload = {
        type: "performance_budget_violation",
        timestamp: new Date().toISOString(),
        passed: result.passed,
        summary: result.summary,
        violations: result.violations,
        recommendations: result.recommendations,
        url: typeof window !== "undefined" ? window.location.href : "unknown",
      };
      await fetch(this.config.alertWebhookUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });
    } catch (error) {
      // Error caught and handled
    }
  }
  private async sendSlackNotification(result: EnforcementResult): Promise<void> {
    if (!this.config.slackWebhookUrl) return;
    try {
      const color =
        result.summary.critical > 0 ? "danger" : result.summary.errors > 0 ? "warning" : "good";
      const fields = result.violations.slice(0, 5).map(violation => ({
        title: violation.metric,
        value: `${violation.actual.toFixed(0)} (budget: ${violation.budget.toFixed(0)}) - ${violation.severity}`,
        short: true,
      }));
      const payload = {
        text: `Performance Budget ${result.passed ? "Passed" : "Failed"}`,
        attachments: [
          {
            color,
            fields,
            footer: `${result.summary.total} violations found`,
            ts: Math.floor(Date.now() / 1000),
          },
        ],
      };
      await fetch(this.config.slackWebhookUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(payload),
      });
    } catch (error) {
      // Error caught and handled
    }
  }
  private async sendEmailNotifications(result: EnforcementResult): Promise<void> {
    // This would typically integrate with an email service
    // For now, we'll just log the notification
  }
  /**
   * Update the budget configuration
   */
  public updateBudget(newBudget: Partial<PerformanceBudget>): void {
    this.config.budget = { ...this.config.budget, ...newBudget };
  }
  /**
   * Update the enforcement configuration
   */
  public updateConfig(newConfig: Partial<BudgetEnforcementConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
  /**
   * Get current budget configuration
   */
  public getBudget(): PerformanceBudget {
    return { ...this.config.budget };
  }
  /**
   * Generate a performance budget report
   */
  public generateBudgetReport(): {
    budget: PerformanceBudget;
    config: BudgetEnforcementConfig;
    lastEnforcement?: EnforcementResult;
  } {
    return {
      budget: this.getBudget(),
      config: { ...this.config },
    };
  }
}
/**
 * CLI-friendly budget enforcement for CI/CD
 */
export async function enforceBudgetFromCLI(
  metricsFile: string,
  budgetFile?: string,
  options: {
    failOnCritical?: boolean;
    failOnError?: boolean;
    verbose?: boolean;
  } = {
    // Implementation needed
  }
): Promise<boolean> {
  try {
    // This would be implemented for Node.js CLI usage
    // For now, return true as placeholder
    return true;
  } catch (error) {
    return false;
  }
}
/**
 * Default enforcer instance
 */
export const defaultBudgetEnforcer = new PerformanceBudgetEnforcer();
