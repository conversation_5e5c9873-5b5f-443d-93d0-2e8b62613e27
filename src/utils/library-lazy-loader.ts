/**
 * Lazy loading utilities for heavy third-party libraries
 * This helps reduce initial bundle size by loading libraries only when needed
 */
// Type definitions for lazy-loaded libraries
type PDFLib = typeof import("jspdf");
type ExcelLib = typeof import("xlsx");
type ChartLib = typeof import("recharts");
type DateLib = typeof import("date-fns");
type AnimationLib = typeof import("framer-motion");
/**
 * Lazy load jsPDF for PDF generation with autoTable plugin
 */
export async function loadPDFLibrary(): Promise<PDFLib> {
  const [jsPDF, autoTable] = await Promise.all([import("jspdf"), import("jspdf-autotable")]);
  // Explicitly integrate autoTable plugin with jsPDF
  // The autoTable plugin automatically extends jsPDF when imported
  // but we need to ensure it's properly attached
  if (jsPDF.jsPDF && autoTable.default) {
    // The plugin should automatically extend jsPDF.jsPDF.prototype
    // Verify the plugin is available
    const testDoc = new jsPDF.jsPDF();
    if (!("autoTable" in testDoc)) {
      // Implementation needed
    }
  }
  return jsPDF;
}
/**
 * Lazy load XLSX for Excel operations
 */
export async function loadExcelLibrary(): Promise<ExcelLib> {
  return import("xlsx");
}
/**
 * Lazy load Recharts for data visualization
 */
export async function loadChartLibrary(): Promise<ChartLib> {
  return import("recharts");
}
/**
 * Lazy load date-fns utilities
 */
export async function loadDateLibrary(): Promise<DateLib> {
  return import("date-fns");
}
/**
 * Lazy load Framer Motion for animations
 */
export async function loadAnimationLibrary(): Promise<AnimationLib> {
  return import("framer-motion");
}
/**
 * Lazy load React Beautiful DND
 */
export async function loadDragDropLibrary(): Promise<typeof import("react-beautiful-dnd")> {
  return import("react-beautiful-dnd");
}
/**
 * Lazy load HTML2Canvas for screenshot functionality
 */
export async function loadScreenshotLibrary(): Promise<typeof import("html2canvas")> {
  return import("html2canvas");
}
/**
 * Lazy load DOMPurify for HTML sanitization
 */
export async function loadSanitizerLibrary(): Promise<typeof import("dompurify")> {
  return import("dompurify");
}
/**
 * PDF Export utility with lazy loading
 */
export class LazyPDFExporter {
  private static pdfLib: PDFLib | null = null;
  static async getInstance(): Promise<PDFLib> {
    if (!this.pdfLib) {
      this.pdfLib = await loadPDFLibrary();
    }
    return this.pdfLib;
  }
  static async exportToPDF(data: unknown, options: Record<string, unknown> = {}) {
    const jsPDF = await this.getInstance();
    const doc = new jsPDF.jsPDF(options);
    // Add your PDF generation logic here
    return doc;
  }
}
/**
 * Excel Export utility with lazy loading
 */
export class LazyExcelExporter {
  private static excelLib: ExcelLib | null = null;
  static async getInstance(): Promise<ExcelLib> {
    if (!this.excelLib) {
      this.excelLib = await loadExcelLibrary();
    }
    return this.excelLib;
  }
  static async exportToExcel(data: unknown[], filename: string = "export.xlsx") {
    const XLSX = await this.getInstance();
    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
    // Write file
    XLSX.writeFile(workbook, filename);
  }
  static async readExcelFile(file: File): Promise<any[]> {
    const XLSX = await this.getInstance();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = e => {
        try {
          const data = new Uint8Array(e.target?.result as ArrayBuffer);
          const workbook = XLSX.read(data, { type: "array" });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet);
          resolve(jsonData);
        } catch (error) {
          reject(error);
        }
      };
      reader.onerror = reject;
      reader.readAsArrayBuffer(file);
    });
  }
}
/**
 * Chart utility with lazy loading
 */
export class LazyChartRenderer {
  private static chartLib: ChartLib | null = null;
  static async getInstance(): Promise<ChartLib> {
    if (!this.chartLib) {
      this.chartLib = await loadChartLibrary();
    }
    return this.chartLib;
  }
  static async renderChart(type: string, data: unknown[], config: Record<string, unknown> = {}) {
    const charts = await this.getInstance();
    // Return the appropriate chart component
    switch (type) {
      case "line":
        return charts.LineChart;
      case "bar":
        return charts.BarChart;
      case "pie":
        return charts.PieChart;
      case "area":
        return charts.AreaChart;
      default:
        return charts.LineChart;
    }
  }
}
/**
 * Date utility with lazy loading
 */
export class LazyDateFormatter {
  private static dateLib: DateLib | null = null;
  static async getInstance(): Promise<DateLib> {
    if (!this.dateLib) {
      this.dateLib = await loadDateLibrary();
    }
    return this.dateLib;
  }
  static async formatDate(date: Date, format: string = "yyyy-MM-dd"): Promise<string> {
    const dateFns = await this.getInstance();
    return dateFns.format(date, format);
  }
  static async parseDate(dateString: string, format: string = "yyyy-MM-dd"): Promise<Date> {
    const dateFns = await this.getInstance();
    return dateFns.parse(dateString, format, new Date());
  }
  static async addDays(date: Date, amount: number): Promise<Date> {
    const dateFns = await this.getInstance();
    return dateFns.addDays(date, amount);
  }
  static async differenceInDays(dateLeft: Date, dateRight: Date): Promise<number> {
    const dateFns = await this.getInstance();
    return dateFns.differenceInDays(dateLeft, dateRight);
  }
}
/**
 * Animation utility with lazy loading
 */
export class LazyAnimationProvider {
  private static animationLib: AnimationLib | null = null;
  static async getInstance(): Promise<AnimationLib> {
    if (!this.animationLib) {
      this.animationLib = await loadAnimationLibrary();
    }
    return this.animationLib;
  }
  static async getMotionComponent() {
    const framerMotion = await this.getInstance();
    return framerMotion.motion;
  }
  static async getAnimatePresence() {
    const framerMotion = await this.getInstance();
    return framerMotion.AnimatePresence;
  }
}
/**
 * Type definition for HTML2Canvas library
 */
interface HTML2CanvasLibrary {
  default: (element: HTMLElement, options?: Record<string, unknown>) => Promise<HTMLCanvasElement>;
}
/**
 * Screenshot utility with lazy loading and improved type safety
 */
export class LazyScreenshotCapture {
  private static screenshotLib: HTML2CanvasLibrary | null = null;
  static async getInstance(): Promise<HTML2CanvasLibrary> {
    if (!this.screenshotLib) {
      this.screenshotLib = await loadScreenshotLibrary();
    }
    return this.screenshotLib;
  }
  static async captureElement(
    element: HTMLElement,
    options: Record<string, unknown> = {}
  ): Promise<HTMLCanvasElement> {
    const html2canvas = await this.getInstance();
    // Verify that html2canvas and its default export are available
    if (!html2canvas || typeof html2canvas.default !== "function") {
      throw new Error("HTML2Canvas library failed to load or is missing default export");
    }
    try {
      return await html2canvas.default(element, options);
    } catch (error) {
      throw new Error(
        `Failed to capture element: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }
  static async downloadScreenshot(element: HTMLElement, filename: string = "screenshot.png") {
    const canvas = await this.captureElement(element);
    // Convert to blob and download
    canvas.toBlob(blob => {
      if (blob) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }
    });
  }
}
/**
 * Preload critical libraries based on user behavior
 */
export class LibraryPreloader {
  private static preloadedLibraries = new Set<string>();
  static async preloadForReports(): Promise<void> {
    if (!this.preloadedLibraries.has("reports")) {
      try {
        await Promise.all([loadChartLibrary(), loadPDFLibrary(), loadExcelLibrary()]);
        this.preloadedLibraries.add("reports");
      } catch (error) {
        // Handle preload failures gracefully
        // Still mark as attempted to avoid repeated failures
        this.preloadedLibraries.add("reports");
      }
    }
  }
  static async preloadForDataEntry(): Promise<void> {
    if (!this.preloadedLibraries.has("data-entry")) {
      try {
        await Promise.all([loadDateLibrary(), loadAnimationLibrary()]);
        this.preloadedLibraries.add("data-entry");
      } catch (error) {
        // Handle preload failures gracefully
        // Still mark as attempted to avoid repeated failures
        this.preloadedLibraries.add("data-entry");
      }
    }
  }
  static async preloadForVisualization(): Promise<void> {
    if (!this.preloadedLibraries.has("visualization")) {
      try {
        await Promise.all([loadChartLibrary(), loadScreenshotLibrary(), loadAnimationLibrary()]);
        this.preloadedLibraries.add("visualization");
      } catch (error) {
        // Handle preload failures gracefully
        // Still mark as attempted to avoid repeated failures
        this.preloadedLibraries.add("visualization");
      }
    }
  }
}
export default {
  LazyPDFExporter,
  LazyExcelExporter,
  LazyChartRenderer,
  LazyDateFormatter,
  LazyAnimationProvider,
  LazyScreenshotCapture,
  LibraryPreloader,
};
