import { Risk, RiskSeverity, RiskStatus } from "@/types";

/**
 * Transform Supabase risk data to domain Risk objects
 * This replaces the mock data transformations with real database data handling
 */

export const formatRisksData = (supabaseRisks: unknown[]): Risk[] => {
  return supabaseRisks.map(risk => {
    // Safely extract category name from the nested structure
    const categoryName = risk.categories?.name ?? "Uncategorized";

    // Safely extract owner name from the nested structure
    const ownerName = risk.profiles?.name ?? "Unassigned";

    return {
      id: risk.id,
      title: risk.title,
      description: risk.description,
      category: categoryName,
      categoryId: risk.category_id,
      ownerId: risk.owner_id ?? "",
      ownerName: ownerName,
      organizationId: risk.organization_id, // ADD THIS LINE
      createdAt: new Date(risk.created_at),
      updatedAt: new Date(risk.updated_at),

      // Inherent risk properties (risk without controls)
      inherentLikelihood: risk.inherent_likelihood || risk.likelihood,
      inherentImpact: risk.inherent_impact || risk.impact,
      inherentSeverity: (risk.inherent_severity as RiskSeverity) || (risk.severity as RiskSeverity),

      // Residual risk properties (risk after controls are applied)
      likelihood: risk.likelihood,
      impact: risk.impact,
      severity: risk.severity as RiskSeverity,

      status: risk.status as RiskStatus,
      currentControls: risk.current_controls,
      mitigationApproach: risk.mitigation_approach,
      dueDate: risk.due_date ? new Date(risk.due_date) : undefined,
      controlMeasures: [], // Will be populated separately if needed
      mitigationActions: [], // Will be populated separately if needed
    };
  });
};

/**
 * Transform single Supabase risk data to domain Risk object
 */

export const formatRiskData = (supabaseRisk: Record<string, unknown>): Risk => {
  return formatRisksData([supabaseRisk])[0];
};

/**
 * Transform Supabase incident data to domain objects
 */

export const formatIncidentsData = (supabaseIncidents: unknown[]) => {
  return supabaseIncidents.map(incident => ({
    id: incident.id,
    title: incident.title,
    description: incident.description,
    reporterId: incident.reporter_id,
    reporterName: incident.profiles?.name ?? "Unknown Reporter",
    organizationId: incident.organization_id, // ADD THIS LINE
    date: new Date(incident.date),
    status: incident.status,
    severity: incident.severity,
    relatedRiskId: incident.related_risk_id,
  }));
};
