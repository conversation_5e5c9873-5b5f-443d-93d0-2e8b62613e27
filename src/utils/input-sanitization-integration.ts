/**
 * Input Sanitization Integration Examples
 *
 * This file demonstrates how to integrate the input sanitization service
 * throughout the application for various use cases.
 */

import {
  sanitizeHtml,
  sanitizeText,
  validateAndSanitizeInput,
  sanitizeFormData,
  sanitizeUrl,
  createSanitizationMiddleware,
  SANITIZATION_PRESETS,
  type SanitizationOptions,
} from "../services/inputSanitizationService";

/**
 * Example: Sanitizing user comments
 */
export function sanitizeUserComment(comment: string): string {
  return sanitizeHtml(comment, SANITIZATION_PRESETS.USER_CONTENT);
}

/**
 * Example: Sanitizing risk descriptions (rich text)
 */
export function sanitizeRiskDescription(description: string): string {
  return sanitizeHtml(description, SANITIZATION_PRESETS.RICH_TEXT);
}

/**
 * Example: Sanitizing search queries (plain text only)
 */
export function sanitizeSearchQuery(query: string): string {
  return sanitizeText(query, 100); // Limit to 100 characters
}

/**
 * Example: Sanitizing form data for risk creation
 */
export function sanitizeRiskFormData(formData: Record<string, unknown>) {
  const fieldOptions: Record<string, SanitizationOptions> = {
    title: SANITIZATION_PRESETS.TEXT_ONLY,
    description: SANITIZATION_PRESETS.RICH_TEXT,
    mitigation_actions: SANITIZATION_PRESETS.USER_CONTENT,
    notes: SANITIZATION_PRESETS.USER_CONTENT,
    owner_email: SANITIZATION_PRESETS.TEXT_ONLY,
    category: SANITIZATION_PRESETS.TEXT_ONLY,
  };

  return sanitizeFormData(formData, fieldOptions);
}

/**
 * Example: Sanitizing incident form data
 */
export function sanitizeIncidentFormData(formData: Record<string, unknown>) {
  const fieldOptions: Record<string, SanitizationOptions> = {
    title: SANITIZATION_PRESETS.TEXT_ONLY,
    description: SANITIZATION_PRESETS.RICH_TEXT,
    resolution_notes: SANITIZATION_PRESETS.USER_CONTENT,
    reporter_email: SANITIZATION_PRESETS.TEXT_ONLY,
  };

  return sanitizeFormData(formData, fieldOptions);
}

/**
 * Example: Sanitizing policy document content
 */
export function sanitizePolicyContent(content: string): string {
  const policyOptions: SanitizationOptions = {
    allowedTags: [
      "p",
      "br",
      "strong",
      "em",
      "u",
      "ol",
      "ul",
      "li",
      "h1",
      "h2",
      "h3",
      "h4",
      "h5",
      "h6",
      "blockquote",
      "table",
      "thead",
      "tbody",
      "tr",
      "th",
      "td",
    ],
    allowedAttributes: ["class", "id", "href", "title"],
    allowHtml: true,
  };

  return sanitizeHtml(content, policyOptions);
}

/**
 * Example: Creating a middleware for API endpoints
 */

export const riskApiSanitizationMiddleware = createSanitizationMiddleware({
  title: SANITIZATION_PRESETS.TEXT_ONLY,
  description: SANITIZATION_PRESETS.RICH_TEXT,
  mitigation_actions: SANITIZATION_PRESETS.USER_CONTENT,
  notes: SANITIZATION_PRESETS.USER_CONTENT,
});

export const incidentApiSanitizationMiddleware = createSanitizationMiddleware({
  title: SANITIZATION_PRESETS.TEXT_ONLY,
  description: SANITIZATION_PRESETS.RICH_TEXT,
  resolution_notes: SANITIZATION_PRESETS.USER_CONTENT,
});

/**
 * Example: Sanitizing user profile data
 */
export function sanitizeUserProfileData(profileData: Record<string, unknown>) {
  const fieldOptions: Record<string, SanitizationOptions> = {
    full_name: SANITIZATION_PRESETS.TEXT_ONLY,
    bio: SANITIZATION_PRESETS.USER_CONTENT,
    department: SANITIZATION_PRESETS.TEXT_ONLY,
    job_title: SANITIZATION_PRESETS.TEXT_ONLY,
    phone: SANITIZATION_PRESETS.TEXT_ONLY,
  };

  return sanitizeFormData(profileData, fieldOptions);
}

/**
 * Example: Sanitizing external URLs (for links in content)
 */
export function sanitizeExternalUrl(url: string): string {
  const sanitized = sanitizeUrl(url);

  // Additional validation for external URLs
  if (sanitized && !sanitized.startsWith("/")) {
    // Ensure external URLs use HTTPS
    if (sanitized.startsWith("http://")) {
      return sanitized.replace("http://", "https://");
    }
  }

  return sanitized;
}

/**
 * Example: Batch sanitization for multiple items
 */
export function sanitizeRiskList(risks: Array<Record<string, unknown>>) {
  return risks.map(risk => {
    const sanitizationResults = sanitizeRiskFormData(risk);

    // Extract sanitized values
    const sanitizedRisk: Record<string, unknown> = {};
    Object.entries(sanitizationResults).forEach(([key, result]) => {
      sanitizedRisk[key] = result.sanitizedValue;
    });

    return sanitizedRisk;
  });
}

/**
 * Example: Sanitization with validation for API responses
 */
export function sanitizeAndValidateApiResponse(data: unknown): {
  sanitizedData: Record<string, unknown>;
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  if (!data || typeof data !== "object") {
    return {
      sanitizedData: {},
      isValid: false,
      errors: ["Invalid data format"],
      warnings: [],
    };
  }

  const sanitizationResults = sanitizeFormData(data as Record<string, unknown>);

  const sanitizedData: Record<string, unknown> = {};
  const allErrors: string[] = [];
  const allWarnings: string[] = [];
  let isValid = true;

  Object.entries(sanitizationResults).forEach(([key, result]) => {
    sanitizedData[key] = result.sanitizedValue;
    allErrors.push(...result.errors);
    allWarnings.push(...result.warnings);

    if (!result.isValid) {
      isValid = false;
    }
  });

  return {
    sanitizedData,
    isValid,
    errors: allErrors,
    warnings: allWarnings,
  };
}

/**
 * Example: Custom sanitization for specific field types
 */
export function sanitizeByFieldType(value: unknown, fieldType: string): string {
  if (typeof value !== "string") {
    return "";
  }

  switch (fieldType) {
    case "email":
      return sanitizeText(value, 254); // RFC 5321 limit

    case "phone":
      return sanitizeText(value.replace(/[^\d\s\-+()]/g, ""), 20);

    case "url":
      return sanitizeUrl(value);

    case "rich_text":
      return sanitizeHtml(value, SANITIZATION_PRESETS.RICH_TEXT);

    case "user_content":
      return sanitizeHtml(value, SANITIZATION_PRESETS.USER_CONTENT);

    case "file_name":
      return sanitizeHtml(value, SANITIZATION_PRESETS.FILE_NAME);

    case "plain_text":
    default:
      return sanitizeText(value);
  }
}

/**
 * Example: React hook for form sanitization
 */
export function useSanitizedFormData<T extends Record<string, unknown>>(
  formData: T,
  fieldOptions?: Record<string, SanitizationOptions>
) {
  const sanitizationResults = sanitizeFormData(formData, fieldOptions);

  const sanitizedData = {} as T;
  const hasErrors = Object.values(sanitizationResults).some(result => !result.isValid);
  const hasWarnings = Object.values(sanitizationResults).some(result => result.warnings.length > 0);

  Object.entries(sanitizationResults).forEach(([key, result]) => {
    (sanitizedData as Record<string, unknown>)[key] = result.sanitizedValue;
  });

  return {
    sanitizedData,
    sanitizationResults,
    hasErrors,
    hasWarnings,
    isValid: !hasErrors,
  };
}

/**
 * Example: Sanitization for CSV import data
 */
export function sanitizeCsvImportData(csvData: Array<Record<string, string>>) {
  return csvData.map((row, index) => {
    const sanitizedRow: Record<string, string> = {};
    const rowErrors: string[] = [];

    Object.entries(row).forEach(([key, value]) => {
      const result = validateAndSanitizeInput(value, SANITIZATION_PRESETS.TEXT_ONLY);
      sanitizedRow[key] = result.sanitizedValue;

      if (!result.isValid) {
        rowErrors.push(`Row ${index + 1}, Column ${key}: ${result.errors.join(", ")}`);
      }
    });

    return {
      data: sanitizedRow,
      errors: rowErrors,
      isValid: rowErrors.length === 0,
    };
  });
}

export default {
  sanitizeUserComment,
  sanitizeRiskDescription,
  sanitizeSearchQuery,
  sanitizeRiskFormData,
  sanitizeIncidentFormData,
  sanitizePolicyContent,
  sanitizeUserProfileData,
  sanitizeExternalUrl,
  sanitizeRiskList,
  sanitizeAndValidateApiResponse,
  sanitizeByFieldType,
  useSanitizedFormData,
  sanitizeCsvImportData,
  riskApiSanitizationMiddleware,
  incidentApiSanitizationMiddleware,
};
