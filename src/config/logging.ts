import { LogLevel, LoggerConfig } from "@/utils/errors/Logger";
/**
 * Environment-specific logging configuration
 */
export interface LoggingEnvironmentConfig {
  logLevel: LogLevel;
  enableConsole: boolean;
  enableRemote: boolean;
  remoteEndpoint?: string;
  enableCorrelationIds: boolean;
  batchSize: number;
  flushInterval: number;
  maxLogEntries: number;
}
/**
 * Get logging configuration based on environment
 */
export function getLoggingConfig(): LoggingEnvironmentConfig {
  const environment = import.meta.env.MODE;
  const isProduction = environment === "production";
  const isDevelopment = environment === "development";
  // Base configuration
  const baseConfig: LoggingEnvironmentConfig = {
    logLevel: isProduction ? LogLevel.ERROR : LogLevel.DEBUG,
    enableConsole: !isProduction,
    enableRemote: isProduction,
    enableCorrelationIds: true,
    batchSize: isProduction ? 20 : 5,
    flushInterval: isProduction ? 10000 : 2000, // 10s in prod, 2s in dev
    maxLogEntries: isProduction ? 2000 : 500,
  };
  // Environment-specific overrides
  if (isProduction) {
    return {
      ...baseConfig,
      remoteEndpoint: import.meta.env["VITE_LOGGING_ENDPOINT"] || "/api/logs",
      logLevel: LogLevel.WARN, // Allow warnings in production for important issues
      enableConsole: false, // Never log to console in production
      enableRemote: true,
    };
  }
  if (isDevelopment) {
    return {
      ...baseConfig,
      logLevel: LogLevel.DEBUG,
      enableConsole: true,
      enableRemote: import.meta.env["VITE_ENABLE_REMOTE_LOGGING"] === "true",
      remoteEndpoint: import.meta.env["VITE_LOGGING_ENDPOINT"] || "http://localhost:3001/api/logs",
    };
  }
  // Test environment
  if (environment === "test") {
    return {
      ...baseConfig,
      logLevel: LogLevel.ERROR, // Only errors in tests
      enableConsole: false,
      enableRemote: false,
      batchSize: 1,
      flushInterval: 100,
      maxLogEntries: 100,
    };
  }
  return baseConfig;
}
/**
 * Convert logging config to logger config
 */
export function toLoggerConfig(config: LoggingEnvironmentConfig): Partial<LoggerConfig> {
  const loggerConfig: Partial<LoggerConfig> = {
    minLevel: config.logLevel,
    enableConsole: config.enableConsole,
    enableRemote: config.enableRemote,
    enableCorrelationIds: config.enableCorrelationIds,
    batchSize: config.batchSize,
    flushInterval: config.flushInterval,
    maxLogEntries: config.maxLogEntries,
    environment: import.meta.env.MODE,
    enableStackTrace: true,
  };

  // Only add remoteEndpoint if it's defined
  if (config.remoteEndpoint !== undefined) {
    loggerConfig.remoteEndpoint = config.remoteEndpoint;
  }

  return loggerConfig;
}
/**
 * Initialize logging with environment-specific configuration
 */
export function initializeLogging() {
  const config = getLoggingConfig();
  // Log initialization in development
  if (config.enableConsole) {
    // Condition handled
  }
  return config;
}
/**
 * Logging configuration constants
 */
export const LOGGING_CONFIG = {
  // Correlation ID patterns
  CORRELATION_ID_HEADER: "X-Correlation-ID",
  CORRELATION_ID_QUERY_PARAM: "correlationId",
  // Log retention
  MAX_LOG_RETENTION_DAYS: 30,
  // Performance thresholds for automatic logging
  SLOW_API_THRESHOLD_MS: 2000,
  SLOW_COMPONENT_RENDER_MS: 100,
  // Batch processing
  MAX_BATCH_SIZE: 50,
  MIN_FLUSH_INTERVAL_MS: 1000,
  MAX_FLUSH_INTERVAL_MS: 30000,
  // Error categorization
  CRITICAL_ERROR_KEYWORDS: ["authentication", "authorization", "payment", "data loss"],
  HIGH_PRIORITY_COMPONENTS: ["auth", "payment", "risk-management", "user-management"],
} as const;
