/**
 * Environment configuration
 *
 * This file centralizes environment variables and configuration settings
 * for different deployment environments (development, production, etc.)
 */

// Environment detection
export const isProd = import.meta.env.MODE === "production";
export const isDev = !isProd;

// Environment name for logging
export const environmentName = isProd ? "PRODUCTION" : "DEVELOPMENT";

// Log current environment on app startup (in non-production builds)
if (isDev) {
  // Import logging service dynamically to avoid circular dependencies
  import("../services/loggingService").then(({ log }) => {
    log.info(`Running in ${environmentName} environment`, undefined, {
      component: "environment",
      action: "startup",
    });
  });
}

// Force reload the page - useful for auth operations to ensure clean state

export const forcePageReload = (path: string = window.location.pathname) => {
  window.location.href = path;
};
