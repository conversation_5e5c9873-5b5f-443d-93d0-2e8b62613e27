/**
 * Integration tests for enhanced API response typing
 * Demonstrates type-safe API interactions with runtime validation
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { createIncident, updateIncident, reopenIncident } from '@/services/incidentService';
import { createRisk, updateRisk, deleteRisk, fetchRiskById } from '@/services/risk/riskCRUDService';
import { executeWithErrorHandling } from '@/services/errorHandlingService';
import { isServiceSuccess, isServiceError } from '@/types/api';
import { User, RiskSeverity, RiskStatus } from '@/types';

// Mock Supabase client
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(() => ({
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn()
        }))
      })),
      update: vi.fn(() => ({
        eq: vi.fn(() => ({
          select: vi.fn(() => ({
            single: vi.fn()
          }))
        }))
      })),
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          single: vi.fn()
        }))
      })),
      delete: vi.fn(() => ({
        eq: vi.fn()
      }))
    })),
    auth: {
      getUser: vi.fn()
    }
  }
}));

describe('API Integration with Enhanced Typing', () => {
  const mockUser: User = {
    id: 'user-123',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'admin' as any,
    organizationId: 'org-123'
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Incident Service Integration', () => {
    it('should handle successful incident creation with type safety', async () => {
      const { supabase } = await import('@/integrations/supabase/client');
      
      // Mock successful response
      const mockIncidentResponse = {
        id: 'incident-123',
        title: 'Test Incident',
        description: 'Test Description',
        reporter_id: 'user-123',
        organization_id: 'org-123',
        date: '2024-01-01T00:00:00Z',
        status: 'Open',
        severity: 'High',
        related_risk_id: null,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      };

      const mockChain = {
        insert: vi.fn(() => mockChain),
        select: vi.fn(() => mockChain),
        single: vi.fn().mockResolvedValue({ data: mockIncidentResponse, error: null })
      };
      
      vi.mocked(supabase.from).mockReturnValue(mockChain as any);

      const incidentValues = {
        title: 'Test Incident',
        description: 'Test Description',
        severity: 'High',
        status: 'Open',
        relatedRiskId: null
      };

      const result = await createIncident(incidentValues, mockUser);

      expect(isServiceSuccess(result)).toBe(true);
      if (isServiceSuccess(result)) {
        expect(result.data.id).toBe('incident-123');
        expect(result.data.title).toBe('Test Incident');
        expect(result.metadata?.timestamp).toBeDefined();
      }
    });

    it('should handle incident creation failure with proper error typing', async () => {
      const { supabase } = await import('@/integrations/supabase/client');
      
      // Mock error response
      const mockError = {
        message: 'Database constraint violation',
        code: 'PGRST301',
        details: 'Title already exists'
      };

      const mockChain = {
        insert: vi.fn(() => mockChain),
        select: vi.fn(() => mockChain),
        single: vi.fn().mockResolvedValue({ data: null, error: mockError })
      };
      
      vi.mocked(supabase.from).mockReturnValue(mockChain as any);

      const incidentValues = {
        title: 'Duplicate Incident',
        description: 'Test Description',
        severity: 'High',
        status: 'Open',
        relatedRiskId: null
      };

      const result = await createIncident(incidentValues, mockUser);

      expect(isServiceError(result)).toBe(true);
      if (isServiceError(result)) {
        expect(result.error.message).toBe('Database constraint violation');
        expect(result.error.code).toBe('PGRST301');
        expect(result.error.category).toBe('network');
      }
    });

    it('should handle invalid response format with validation error', async () => {
      const { supabase } = await import('@/integrations/supabase/client');
      
      // Mock invalid response (missing required fields)
      const invalidResponse = {
        id: 'incident-123',
        title: 'Test Incident'
        // Missing required fields
      };

      const mockChain = {
        insert: vi.fn(() => mockChain),
        select: vi.fn(() => mockChain),
        single: vi.fn().mockResolvedValue({ data: invalidResponse, error: null })
      };
      
      vi.mocked(supabase.from).mockReturnValue(mockChain as any);

      const incidentValues = {
        title: 'Test Incident',
        description: 'Test Description',
        severity: 'High',
        status: 'Open',
        relatedRiskId: null
      };

      const result = await createIncident(incidentValues, mockUser);

      expect(isServiceError(result)).toBe(true);
      if (isServiceError(result)) {
        expect(result.error.message).toBe('Invalid incident response format');
        expect(result.error.code).toBe('INVALID_RESPONSE');
        expect(result.error.category).toBe('validation');
      }
    });
  });

  describe('Risk Service Integration', () => {
    it('should handle successful risk creation with comprehensive typing', async () => {
      const { supabase } = await import('@/integrations/supabase/client');
      
      const mockRiskResponse = {
        id: 'risk-123',
        title: 'Test Risk',
        description: 'Test Description',
        category_id: 'cat-123',
        owner_id: 'user-123',
        organization_id: 'org-123',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        inherent_likelihood: 3,
        inherent_impact: 4,
        inherent_severity: 'High',
        likelihood: 2,
        impact: 3,
        severity: 'Medium',
        status: 'In Progress',
        current_controls: 'Some controls',
        mitigation_approach: 'Some approach',
        due_date: '2024-12-31T00:00:00Z',
        created_by: 'user-123',
        template_id: null,
        profiles: { name: 'Test User' },
        categories: { name: 'Security' }
      };

      const mockChain = {
        insert: vi.fn(() => mockChain),
        select: vi.fn(() => mockChain),
        single: vi.fn().mockResolvedValue({ data: mockRiskResponse, error: null })
      };
      
      vi.mocked(supabase.from).mockReturnValue(mockChain as any);

      const riskData = {
        title: 'Test Risk',
        description: 'Test Description',
        categoryId: 'cat-123',
        inherentLikelihood: 3,
        inherentImpact: 4,
        inherentSeverity: RiskSeverity.HIGH,
        likelihood: 2,
        impact: 3,
        severity: RiskSeverity.MEDIUM,
        status: RiskStatus.IN_PROGRESS,
        mitigationApproach: 'Some approach',
        dueDate: new Date('2024-12-31'),
        ownerId: 'user-123',
        organizationId: 'org-123'
      };

      const result = await createRisk(riskData, 'user-123');

      expect(isServiceSuccess(result)).toBe(true);
      if (isServiceSuccess(result)) {
        expect(result.data.id).toBe('risk-123');
        expect(result.data.title).toBe('Test Risk');
        expect(result.data.severity).toBe(RiskSeverity.MEDIUM);
        expect(result.metadata?.timestamp).toBeDefined();
      }
    });

    it('should handle risk update with partial data', async () => {
      const { supabase } = await import('@/integrations/supabase/client');
      
      const mockChain = {
        update: vi.fn(() => mockChain),
        eq: vi.fn().mockResolvedValue({ data: null, error: null })
      };
      
      vi.mocked(supabase.from).mockReturnValue(mockChain as any);

      const updateData = {
        id: 'risk-123',
        title: 'Updated Risk Title',
        status: RiskStatus.CLOSED
      };

      const result = await updateRisk(updateData);

      expect(isServiceSuccess(result)).toBe(true);
      if (isServiceSuccess(result)) {
        expect(result.data).toBeUndefined(); // Update returns void
        expect(result.metadata?.timestamp).toBeDefined();
      }
    });

    it('should handle risk deletion with proper error handling', async () => {
      const { supabase } = await import('@/integrations/supabase/client');
      
      vi.mocked(supabase.from).mockReturnValue({
        delete: vi.fn(() => ({
          eq: vi.fn().mockResolvedValue({ data: null, error: null })
        }))
      } as any);

      const result = await deleteRisk('risk-123');

      expect(isServiceSuccess(result)).toBe(true);
      if (isServiceSuccess(result)) {
        expect(result.data).toBeUndefined(); // Delete returns void
        expect(result.metadata?.timestamp).toBeDefined();
      }
    });
  });

  describe('Error Handling Integration', () => {
    it('should demonstrate retry logic with network errors', async () => {
      let attemptCount = 0;
      const operation = vi.fn().mockImplementation(async () => {
        attemptCount++;
        if (attemptCount < 3) {
          const networkError = new Error('Network timeout');
          (networkError as any).code = 'NETWORK_ERROR';
          throw networkError;
        }
        return { success: true, data: 'Operation succeeded after retry' };
      });

      // Mock setTimeout to avoid delays in tests
      const originalSetTimeout = global.setTimeout;
      global.setTimeout = vi.fn().mockImplementation((fn) => {
        fn();
        return 1;
      });

      const result = await executeWithErrorHandling(operation, { logError: false });

      expect(isServiceSuccess(result)).toBe(true);
      if (isServiceSuccess(result)) {
        expect(result.data.data).toBe('Operation succeeded after retry');
        expect(result.metadata?.retryCount).toBe(2);
      }
      expect(operation).toHaveBeenCalledTimes(3);

      // Restore original setTimeout
      global.setTimeout = originalSetTimeout;
    });

    it('should handle non-recoverable errors without retry', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Invalid input format'));

      const result = await executeWithErrorHandling(operation, { 
        logError: false,
        userMessage: 'Please check your input and try again.'
      });

      expect(isServiceError(result)).toBe(true);
      if (isServiceError(result)) {
        expect(result.error.message).toBe('Please check your input and try again.');
        expect(result.error.category).toBe('validation');
        expect(result.error.recoverable).toBe(false);
      }
      expect(operation).toHaveBeenCalledTimes(1); // No retry
    });
  });

  describe('Type Safety Validation', () => {
    it('should ensure compile-time type safety for service results', async () => {
      // This test demonstrates compile-time type safety
      const mockResult = await createIncident({
        title: 'Test',
        description: 'Test',
        severity: 'High',
        status: 'Open',
        relatedRiskId: null
      }, mockUser);

      // TypeScript should enforce proper type checking here
      if (isServiceSuccess(mockResult)) {
        // mockResult.data is properly typed as IncidentApiResponse
        expect(typeof mockResult.data.id).toBe('string');
        expect(typeof mockResult.data.title).toBe('string');
        expect(typeof mockResult.data.created_at).toBe('string');
        
        // This would cause a TypeScript error if uncommented:
        // expect(mockResult.data.nonExistentField).toBeDefined();
      }

      if (isServiceError(mockResult)) {
        // mockResult.error is properly typed as ServiceError
        expect(typeof mockResult.error.message).toBe('string');
        expect(typeof mockResult.error.category).toBe('string');
        
        // This would cause a TypeScript error if uncommented:
        // expect(mockResult.error.nonExistentField).toBeDefined();
      }
    });
  });
});