/**
 * Integration tests for the complete logging and error monitoring system
 * Tests the integration between logging service, error monitoring, and error handling
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { loggingService } from '@/services/loggingService';
import { errorMonitoringService } from '@/services/errorMonitoringService';
import { executeWithErrorHandling } from '@/services/errorHandlingService';
import { SystemError, ValidationError, NetworkError } from '@/utils/errors';
import { ErrorSeverity, ErrorCategory, ErrorContext } from '@/types';

describe('Logging and Error Monitoring Integration', () => {
  let mockContext: ErrorContext;

  beforeEach(() => {
    // Reset services for each test
    errorMonitoringService.reset();
    vi.clearAllMocks();
    
    mockContext = {
      userId: 'test-user-123',
      organizationId: 'test-org-456',
      component: 'integration-test',
      action: 'test-operation',
      timestamp: new Date()
    };

    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'info').mockImplementation(() => {});
    vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'debug').mockImplementation(() => {});
  });

  afterEach(() => {
    errorMonitoringService.destroy();
    vi.restoreAllMocks();
  });

  describe('End-to-End Error Flow', () => {
    it('should track errors from service operations through the complete pipeline', async () => {
      // Mock setTimeout to avoid actual delays
      const originalSetTimeout = global.setTimeout;
      global.setTimeout = vi.fn().mockImplementation((fn) => {
        fn();
        return 1;
      });

      try {
        // Simulate a service operation that fails
        const operation = vi.fn().mockRejectedValue(new Error('Database connection failed'));
        
        const result = await executeWithErrorHandling(
          operation,
          { logError: true },
          mockContext
        );

        // Verify the operation failed
        expect(result.success).toBe(false);
        expect(result.error?.message).toBe('Database connection failed');

        // Verify error was tracked in monitoring service
        const metrics = errorMonitoringService.getMetrics();
        expect(metrics.totalErrors).toBeGreaterThanOrEqual(1);
        expect(metrics.errorsByCategory[ErrorCategory.SYSTEM]).toBeGreaterThanOrEqual(1);
        expect(metrics.errorsByComponent['integration-test']).toBeGreaterThanOrEqual(1);
      } finally {
        // Restore original setTimeout
        global.setTimeout = originalSetTimeout;
      }
    });

    it('should track retry attempts and generate alerts', async () => {
      // Mock setTimeout to avoid actual delays
      const originalSetTimeout = global.setTimeout;
      global.setTimeout = vi.fn().mockImplementation((fn) => {
        fn();
        return 1;
      });

      try {
        // Create a network error that will be retried
        const networkError = new Error('Network timeout');
        (networkError as any).code = 'NETWORK_ERROR';
        
        const operation = vi.fn()
          .mockRejectedValueOnce(networkError)
          .mockRejectedValueOnce(networkError)
          .mockRejectedValueOnce(networkError)
          .mockRejectedValueOnce(networkError); // Will exceed retry limit

        const result = await executeWithErrorHandling(
          operation,
          { logError: true },
          mockContext
        );

        // Verify the operation failed after retries
        expect(result.success).toBe(false);
        expect(operation).toHaveBeenCalledTimes(4); // Initial + 3 retries

        // Verify errors were tracked
        const metrics = errorMonitoringService.getMetrics();
        expect(metrics.totalErrors).toBe(4);
        expect(metrics.errorsByCategory[ErrorCategory.SYSTEM]).toBe(4);
      } finally {
        // Restore original setTimeout
        global.setTimeout = originalSetTimeout;
      }
    });

    it('should generate pattern alerts for recurring errors', async () => {
      // Create multiple similar errors to trigger pattern detection
      for (let i = 0; i < 5; i++) {
        const error = new SystemError(
          'Database connection failed',
          'database-client',
          'DB_CONNECTION_ERROR',
          { ...mockContext, component: 'database-client' },
          ErrorSeverity.HIGH
        );
        errorMonitoringService.trackError(error);
      }

      const alerts = errorMonitoringService.getAlerts();
      const patternAlert = alerts.find(alert => alert.type === 'pattern');
      
      expect(patternAlert).toBeDefined();
      expect(patternAlert!.message).toContain('pattern detected');
    });

    it('should generate threshold alerts for high error rates', async () => {
      // Create errors rapidly to exceed threshold
      for (let i = 0; i < 6; i++) {
        const error = new SystemError(
          `Error ${i}`,
          'test-component',
          'TEST_ERROR',
          mockContext,
          ErrorSeverity.MEDIUM
        );
        errorMonitoringService.trackError(error);
      }

      const alerts = errorMonitoringService.getAlerts();
      const thresholdAlert = alerts.find(alert => alert.type === 'threshold');
      
      expect(thresholdAlert).toBeDefined();
      expect(thresholdAlert!.message).toContain('threshold exceeded');
    });

    it('should generate critical error alerts immediately', async () => {
      const criticalError = new SystemError(
        'Critical system failure',
        'core-system',
        'CRITICAL_FAILURE',
        mockContext,
        ErrorSeverity.CRITICAL
      );

      errorMonitoringService.trackError(criticalError);

      const alerts = errorMonitoringService.getAlerts();
      const criticalAlert = alerts.find(alert => alert.type === 'critical');
      
      expect(criticalAlert).toBeDefined();
      expect(criticalAlert!.severity).toBe(ErrorSeverity.CRITICAL);
      expect(criticalAlert!.message).toContain('Critical error occurred');
    });
  });

  describe('Logging Service Integration', () => {
    it('should log structured information for different error types', () => {
      const consoleSpy = vi.spyOn(console, 'error');
      
      // Test different error types
      const errors = [
        new SystemError('System error', 'sys', 'SYS_ERROR', mockContext, ErrorSeverity.HIGH),
        new ValidationError('Validation error', 'email', 'VALIDATION_ERROR', mockContext),
        new NetworkError('Network error', 500, '/api/test', 'GET', mockContext)
      ];

      errors.forEach(error => errorMonitoringService.trackError(error));

      // Should have logged each error (may include additional audit logs)
      expect(consoleSpy.mock.calls.length).toBeGreaterThanOrEqual(3);
    });

    it('should provide audit logging for error tracking', () => {
      const auditSpy = vi.spyOn(loggingService, 'audit');
      
      const error = new SystemError(
        'Audit test error',
        'audit-component',
        'AUDIT_ERROR',
        mockContext,
        ErrorSeverity.MEDIUM
      );

      errorMonitoringService.trackError(error);

      expect(auditSpy).toHaveBeenCalledWith(
        'error_tracked',
        'test-user-123',
        expect.objectContaining({
          errorId: error.id,
          category: ErrorCategory.SYSTEM,
          severity: ErrorSeverity.MEDIUM,
          component: 'integration-test',
          recoverable: true
        }),
        expect.objectContaining({
          userId: 'test-user-123',
          organizationId: 'test-org-456',
          component: 'integration-test',
          action: 'test-operation'
        })
      );
    });

    it('should correlate logs with correlation IDs', () => {
      const correlationId = loggingService.startCorrelation();
      
      const error = new SystemError(
        'Correlation test error',
        'correlation-component',
        'CORRELATION_ERROR',
        mockContext,
        ErrorSeverity.MEDIUM
      );

      errorMonitoringService.trackError(error);
      
      expect(loggingService.getCorrelationId()).toBe(correlationId);
      
      loggingService.endCorrelation();
      expect(loggingService.getCorrelationId()).toBeNull();
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should handle service failures gracefully', async () => {
      // Simulate a service that fails initially but recovers
      let callCount = 0;
      const operation = vi.fn().mockImplementation(() => {
        callCount++;
        if (callCount <= 2) {
          const error = new Error('Temporary service failure');
          (error as any).code = 'NETWORK_ERROR';
          throw error;
        }
        return 'success';
      });

      const result = await executeWithErrorHandling(
        operation,
        { logError: true },
        mockContext
      );

      // Should succeed after retries
      expect(result.success).toBe(true);
      expect(result.data).toBe('success');
      expect(operation).toHaveBeenCalledTimes(3);

      // Should track the failed attempts
      const metrics = errorMonitoringService.getMetrics();
      expect(metrics.totalErrors).toBe(2);
    });

    it('should provide comprehensive error context for debugging', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Context test error'));
      
      const detailedContext: Partial<ErrorContext> = {
        userId: 'debug-user',
        organizationId: 'debug-org',
        component: 'debug-component',
        action: 'debug-operation',
        additionalData: {
          requestId: 'req-123',
          sessionId: 'sess-456',
          feature: 'error-debugging'
        }
      };

      await executeWithErrorHandling(
        operation,
        { logError: true },
        detailedContext
      );

      const metrics = errorMonitoringService.getMetrics();
      expect(metrics.totalErrors).toBe(1);
      expect(metrics.errorsByComponent['debug-component']).toBe(1);

      // Verify error history contains detailed context
      const history = errorMonitoringService.getErrorHistory();
      expect(history.length).toBe(1);
      expect(history[0].error.context.additionalData).toEqual(
        expect.objectContaining({
          requestId: 'req-123',
          sessionId: 'sess-456',
          feature: 'error-debugging'
        })
      );
    });
  });

  describe('Performance and Scalability', () => {
    it('should handle high error volumes efficiently', () => {
      const startTime = Date.now();
      
      // Generate a large number of errors
      for (let i = 0; i < 100; i++) {
        const error = new SystemError(
          `Performance test error ${i}`,
          'perf-component',
          'PERF_ERROR',
          { ...mockContext, component: `component-${i % 10}` },
          ErrorSeverity.MEDIUM
        );
        errorMonitoringService.trackError(error);
      }
      
      const endTime = Date.now();
      const processingTime = endTime - startTime;
      
      // Should process errors quickly (less than 100ms for 100 errors)
      expect(processingTime).toBeLessThan(100);
      
      const metrics = errorMonitoringService.getMetrics();
      expect(metrics.totalErrors).toBe(100);
      
      // Should have distributed errors across components
      expect(Object.keys(metrics.errorsByComponent).length).toBe(10);
    });

    it('should maintain memory limits with error history cleanup', () => {
      // Configure small limits for testing
      errorMonitoringService.updateConfig({
        maxStoredErrors: 10,
        trackingWindowMinutes: 1
      });

      // Generate more errors than the limit
      for (let i = 0; i < 15; i++) {
        const error = new SystemError(
          `Memory test error ${i}`,
          'memory-component',
          'MEMORY_ERROR',
          mockContext,
          ErrorSeverity.MEDIUM
        );
        errorMonitoringService.trackError(error);
      }

      const history = errorMonitoringService.getErrorHistory();
      expect(history.length).toBeLessThanOrEqual(10);
      
      const metrics = errorMonitoringService.getMetrics();
      expect(metrics.totalErrors).toBe(15); // Metrics should still be accurate
    });
  });

  describe('Configuration and Customization', () => {
    it('should respect custom alert thresholds', () => {
      // Configure custom thresholds
      errorMonitoringService.updateConfig({
        alertThresholds: [
          {
            category: 'total',
            maxErrorsPerMinute: 2,
            maxErrorsPerHour: 20,
            alertOnCritical: true
          }
        ]
      });

      // Generate errors to exceed custom threshold
      for (let i = 0; i < 3; i++) {
        const error = new SystemError(
          `Custom threshold test ${i}`,
          'threshold-component',
          'THRESHOLD_ERROR',
          mockContext,
          ErrorSeverity.MEDIUM
        );
        errorMonitoringService.trackError(error);
      }

      const alerts = errorMonitoringService.getAlerts();
      const thresholdAlert = alerts.find(alert => alert.type === 'threshold');
      
      expect(thresholdAlert).toBeDefined();
      expect(thresholdAlert!.currentValue).toBeGreaterThanOrEqual(2);
      expect(thresholdAlert!.threshold).toBe(2);
    });

    it('should allow disabling monitoring when needed', () => {
      errorMonitoringService.updateConfig({ enabled: false });

      const error = new SystemError(
        'Disabled monitoring test',
        'disabled-component',
        'DISABLED_ERROR',
        mockContext,
        ErrorSeverity.MEDIUM
      );

      errorMonitoringService.trackError(error);

      const metrics = errorMonitoringService.getMetrics();
      expect(metrics.totalErrors).toBe(0); // Should not track when disabled
    });
  });
});