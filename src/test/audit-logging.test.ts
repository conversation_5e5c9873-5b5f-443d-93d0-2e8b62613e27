import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { 
  AuditLoggingService, 
  AuditEventType, 
  AuditSeverity,
  auditLoggingService,
  auditLog
} from '@/services/auditLoggingService';
import { loggingService } from '@/services/loggingService';

// Mock the logging service
vi.mock('@/services/loggingService', () => ({
  loggingService: {
    audit: vi.fn(),
    info: vi.fn(),
    error: vi.fn(),
    getCorrelationId: vi.fn(() => 'test-correlation-id')
  }
}));

// Mock fetch for remote storage tests
global.fetch = vi.fn();

// Mock sessionStorage
const mockSessionStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};
Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage
});

// Mock navigator
Object.defineProperty(window, 'navigator', {
  value: {
    userAgent: 'Mozilla/5.0 (Test Browser)'
  }
});

describe('AuditLoggingService', () => {
  let auditService: AuditLoggingService;

  beforeEach(() => {
    vi.clearAllMocks();
    mockSessionStorage.getItem.mockReturnValue(null);
    
    // Create a fresh instance for each test
    auditService = AuditLoggingService.getInstance({
      enabled: true,
      batchSize: 5,
      flushInterval: 1000,
      integrityCheckEnabled: true,
      realTimeAlertsEnabled: true,
      remoteStorageEnabled: false
    });
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('Event Logging', () => {
    it('should log audit events with correct structure', async () => {
      const userId = 'test-user-123';
      const action = 'test-action';
      const details = { key: 'value' };

      await auditService.logEvent(
        AuditEventType.USER_LOGIN,
        userId,
        action,
        details,
        {
          severity: AuditSeverity.LOW,
          resourceId: 'resource-123',
          resourceType: 'user',
          outcome: 'success'
        }
      );

      expect(loggingService.audit).toHaveBeenCalledWith(
        action,
        userId,
        expect.objectContaining({
          eventType: AuditEventType.USER_LOGIN,
          severity: AuditSeverity.LOW,
          resourceId: 'resource-123',
          resourceType: 'user',
          outcome: 'success',
          auditEventId: expect.any(String),
          key: 'value'
        }),
        expect.objectContaining({
          userId,
          component: 'audit',
          action: AuditEventType.USER_LOGIN
        })
      );
    });

    it('should sanitize sensitive data in details', async () => {
      const sensitiveDetails = {
        username: 'testuser',
        password: 'secret123',
        token: 'jwt-token',
        normalField: 'normal-value'
      };

      await auditService.logEvent(
        AuditEventType.USER_LOGIN,
        'user-123',
        'login',
        sensitiveDetails
      );

      expect(loggingService.audit).toHaveBeenCalledWith(
        'login',
        'user-123',
        expect.objectContaining({
          username: 'testuser',
          password: '[REDACTED]',
          token: '[REDACTED]',
          normalField: 'normal-value'
        }),
        expect.any(Object)
      );
    });

    it('should determine correct severity levels', async () => {
      // Test critical severity
      await auditService.logEvent(
        AuditEventType.SECURITY_VIOLATION_DETECTED,
        'user-123',
        'security-violation'
      );

      expect(loggingService.audit).toHaveBeenCalledWith(
        'security-violation',
        'user-123',
        expect.objectContaining({
          severity: AuditSeverity.CRITICAL
        }),
        expect.any(Object)
      );

      // Test high severity
      await auditService.logEvent(
        AuditEventType.ADMIN_ACCESS_GRANTED,
        'user-123',
        'admin-access'
      );

      expect(loggingService.audit).toHaveBeenCalledWith(
        'admin-access',
        'user-123',
        expect.objectContaining({
          severity: AuditSeverity.HIGH
        }),
        expect.any(Object)
      );

      // Test medium severity
      await auditService.logEvent(
        AuditEventType.USER_ROLE_CHANGED,
        'user-123',
        'role-change'
      );

      expect(loggingService.audit).toHaveBeenCalledWith(
        'role-change',
        'user-123',
        expect.objectContaining({
          severity: AuditSeverity.MEDIUM
        }),
        expect.any(Object)
      );

      // Test low severity (default)
      await auditService.logEvent(
        AuditEventType.RISK_CREATED,
        'user-123',
        'risk-creation'
      );

      expect(loggingService.audit).toHaveBeenCalledWith(
        'risk-creation',
        'user-123',
        expect.objectContaining({
          severity: AuditSeverity.LOW
        }),
        expect.any(Object)
      );
    });

    it('should generate session IDs correctly', async () => {
      mockSessionStorage.getItem.mockReturnValue('existing-session-123');

      await auditService.logEvent(
        AuditEventType.USER_LOGIN,
        'user-123',
        'login'
      );

      expect(mockSessionStorage.getItem).toHaveBeenCalledWith('audit_session_id');
      expect(loggingService.audit).toHaveBeenCalledWith(
        'login',
        'user-123',
        expect.objectContaining({
          auditEventId: expect.stringMatching(/^audit_\d+_[a-z0-9]+$/)
        }),
        expect.any(Object)
      );
    });
  });

  describe('Specialized Logging Methods', () => {
    it('should log authentication events correctly', async () => {
      await auditService.logAuthEvent(
        AuditEventType.USER_LOGIN,
        'user-123',
        '<EMAIL>',
        { loginMethod: 'email' }
      );

      expect(loggingService.audit).toHaveBeenCalledWith(
        AuditEventType.USER_LOGIN,
        'user-123',
        expect.objectContaining({
          eventType: AuditEventType.USER_LOGIN,
          severity: AuditSeverity.LOW,
          outcome: 'success',
          loginMethod: 'email'
        }),
        expect.any(Object)
      );
    });

    it('should log failed login attempts with correct severity', async () => {
      await auditService.logAuthEvent(
        AuditEventType.LOGIN_FAILED,
        'user-123',
        '<EMAIL>',
        { reason: 'invalid_password' }
      );

      expect(loggingService.audit).toHaveBeenCalledWith(
        AuditEventType.LOGIN_FAILED,
        'user-123',
        expect.objectContaining({
          eventType: AuditEventType.LOGIN_FAILED,
          severity: AuditSeverity.MEDIUM,
          outcome: 'failure',
          reason: 'invalid_password'
        }),
        expect.any(Object)
      );
    });

    it('should log user management events', async () => {
      await auditService.logUserManagementEvent(
        AuditEventType.USER_ROLE_CHANGED,
        'admin-123',
        'target-user-456',
        { oldRole: 'staff', newRole: 'admin' }
      );

      expect(loggingService.audit).toHaveBeenCalledWith(
        AuditEventType.USER_ROLE_CHANGED,
        'admin-123',
        expect.objectContaining({
          eventType: AuditEventType.USER_ROLE_CHANGED,
          severity: AuditSeverity.MEDIUM,
          resourceId: 'target-user-456',
          resourceType: 'user',
          oldRole: 'staff',
          newRole: 'admin'
        }),
        expect.any(Object)
      );
    });

    it('should log risk management events', async () => {
      await auditService.logRiskEvent(
        AuditEventType.RISK_CREATED,
        'user-123',
        'risk-456',
        { riskTitle: 'New Security Risk', severity: 'high' }
      );

      expect(loggingService.audit).toHaveBeenCalledWith(
        AuditEventType.RISK_CREATED,
        'user-123',
        expect.objectContaining({
          eventType: AuditEventType.RISK_CREATED,
          severity: AuditSeverity.LOW,
          resourceId: 'risk-456',
          resourceType: 'risk',
          riskTitle: 'New Security Risk'
        }),
        expect.any(Object)
      );
    });

    it('should log policy management events', async () => {
      await auditService.logPolicyEvent(
        AuditEventType.POLICY_UPDATED,
        'user-123',
        'policy-789',
        { changes: ['title', 'description'] }
      );

      expect(loggingService.audit).toHaveBeenCalledWith(
        AuditEventType.POLICY_UPDATED,
        'user-123',
        expect.objectContaining({
          eventType: AuditEventType.POLICY_UPDATED,
          severity: AuditSeverity.MEDIUM,
          resourceId: 'policy-789',
          resourceType: 'policy',
          changes: ['title', 'description']
        }),
        expect.any(Object)
      );
    });

    it('should log admin events with high severity', async () => {
      await auditService.logAdminEvent(
        AuditEventType.ADMIN_ACTION_PERFORMED,
        'admin-123',
        { action: 'bulk_user_delete', affectedUsers: 5 }
      );

      expect(loggingService.audit).toHaveBeenCalledWith(
        AuditEventType.ADMIN_ACTION_PERFORMED,
        'admin-123',
        expect.objectContaining({
          eventType: AuditEventType.ADMIN_ACTION_PERFORMED,
          severity: AuditSeverity.HIGH,
          outcome: 'success',
          action: 'bulk_user_delete',
          affectedUsers: 5
        }),
        expect.any(Object)
      );
    });

    it('should log security events with critical severity', async () => {
      await auditService.logSecurityEvent(
        AuditEventType.SUSPICIOUS_ACTIVITY,
        'user-123',
        { activityType: 'multiple_failed_logins', count: 10 }
      );

      expect(loggingService.audit).toHaveBeenCalledWith(
        AuditEventType.SUSPICIOUS_ACTIVITY,
        'user-123',
        expect.objectContaining({
          eventType: AuditEventType.SUSPICIOUS_ACTIVITY,
          severity: AuditSeverity.CRITICAL,
          outcome: 'failure',
          activityType: 'multiple_failed_logins',
          count: 10
        }),
        expect.any(Object)
      );
    });
  });

  describe('Real-time Alerts', () => {
    it('should trigger alerts for critical events', async () => {
      await auditService.logEvent(
        AuditEventType.SECURITY_VIOLATION_DETECTED,
        'user-123',
        'security-violation',
        { violationType: 'unauthorized_access' }
      );

      expect(loggingService.error).toHaveBeenCalledWith(
        'AUDIT ALERT: security_violation_detected',
        undefined,
        expect.objectContaining({
          component: 'audit-alert',
          userId: 'user-123',
          action: 'security-violation'
        }),
        expect.objectContaining({
          alertType: 'real-time',
          severity: AuditSeverity.CRITICAL
        })
      );
    });

    it('should trigger alerts for high severity failures', async () => {
      await auditService.logEvent(
        AuditEventType.ADMIN_ACCESS_DENIED,
        'user-123',
        'admin-access-denied',
        { reason: 'insufficient_privileges' },
        { outcome: 'failure' }
      );

      expect(loggingService.error).toHaveBeenCalledWith(
        'AUDIT ALERT: admin_access_denied',
        undefined,
        expect.any(Object),
        expect.objectContaining({
          alertType: 'real-time',
          severity: AuditSeverity.HIGH
        })
      );
    });

    it('should not trigger alerts for low severity events', async () => {
      await auditService.logEvent(
        AuditEventType.RISK_CREATED,
        'user-123',
        'risk-creation'
      );

      expect(loggingService.error).not.toHaveBeenCalledWith(
        expect.stringContaining('AUDIT ALERT'),
        expect.any(Object),
        expect.any(Object),
        expect.any(Object)
      );
    });
  });

  describe('Batch Processing and Flushing', () => {
    it('should flush events when batch size is reached', async () => {
      const batchSize = 3;
      const testService = AuditLoggingService.getInstance({
        batchSize,
        remoteStorageEnabled: false
      });

      // Log events up to batch size
      for (let i = 0; i < batchSize; i++) {
        await testService.logEvent(
          AuditEventType.USER_LOGIN,
          `user-${i}`,
          'login'
        );
      }

      // Should have flushed automatically
      expect(loggingService.info).toHaveBeenCalledWith(
        `Flushed ${batchSize} audit events`,
        expect.objectContaining({
          component: 'audit',
          action: 'flush'
        })
      );
    });

    it('should handle flush errors gracefully', async () => {
      const testService = AuditLoggingService.getInstance({
        remoteStorageEnabled: true,
        remoteStorageEndpoint: '/api/audit'
      });

      // Mock fetch to fail
      vi.mocked(fetch).mockRejectedValue(new Error('Network error'));

      await testService.logEvent(
        AuditEventType.USER_LOGIN,
        'user-123',
        'login'
      );

      await testService.flush();

      expect(loggingService.error).toHaveBeenCalledWith(
        'Failed to flush audit events',
        expect.any(Error),
        expect.objectContaining({
          component: 'audit',
          action: 'flush'
        })
      );
    });
  });

  describe('Remote Storage', () => {
    it('should send events to remote storage when enabled', async () => {
      const testService = AuditLoggingService.getInstance({
        remoteStorageEnabled: true,
        remoteStorageEndpoint: '/api/audit'
      });

      vi.mocked(fetch).mockResolvedValue(new Response('OK', { status: 200 }));

      await testService.logEvent(
        AuditEventType.USER_LOGIN,
        'user-123',
        'login'
      );

      await testService.flush();

      expect(fetch).toHaveBeenCalledWith('/api/audit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Correlation-ID': 'test-correlation-id'
        },
        body: expect.stringContaining('"eventType":"user_login"')
      });
    });

    it('should handle remote storage failures', async () => {
      const testService = AuditLoggingService.getInstance({
        remoteStorageEnabled: true,
        remoteStorageEndpoint: '/api/audit'
      });

      vi.mocked(fetch).mockResolvedValue(new Response('Server Error', { status: 500 }));

      await testService.logEvent(
        AuditEventType.USER_LOGIN,
        'user-123',
        'login'
      );

      await testService.flush();

      expect(loggingService.error).toHaveBeenCalledWith(
        'Failed to flush audit events',
        expect.any(Error),
        expect.any(Object)
      );
    });
  });

  describe('Integrity Checking', () => {
    it('should generate integrity hashes when enabled', async () => {
      const testService = AuditLoggingService.getInstance({
        integrityCheckEnabled: true
      });

      await testService.logEvent(
        AuditEventType.USER_LOGIN,
        'user-123',
        'login'
      );

      const stats = testService.getAuditStats();
      expect(stats.integrityChecksEnabled).toBe(true);
      expect(stats.totalEventsLogged).toBeGreaterThan(0);
    });

    it('should verify integrity correctly', async () => {
      const testService = AuditLoggingService.getInstance({
        integrityCheckEnabled: true
      });

      await testService.logEvent(
        AuditEventType.USER_LOGIN,
        'user-123',
        'login'
      );

      // In this test implementation, integrity verification always returns true
      const isValid = await testService.verifyIntegrity('any-event-id');
      expect(isValid).toBe(true);
    });
  });

  describe('Configuration Management', () => {
    it('should update configuration correctly', () => {
      const newConfig = {
        batchSize: 100,
        flushInterval: 5000,
        realTimeAlertsEnabled: false
      };

      auditService.updateConfig(newConfig);

      const stats = auditService.getAuditStats();
      expect(stats.bufferedEvents).toBe(0); // Should still work with new config
    });

    it('should provide audit statistics', () => {
      const stats = auditService.getAuditStats();

      expect(stats).toEqual({
        bufferedEvents: expect.any(Number),
        totalEventsLogged: expect.any(Number),
        integrityChecksEnabled: expect.any(Boolean),
        lastFlushTime: null
      });
    });
  });

  describe('Convenience Functions', () => {
    it('should provide working convenience functions', async () => {
      await auditLog.auth(AuditEventType.USER_LOGIN, 'user-123', '<EMAIL>');
      await auditLog.user(AuditEventType.USER_CREATED, 'admin-123', 'user-456');
      await auditLog.risk(AuditEventType.RISK_CREATED, 'user-123', 'risk-789');
      await auditLog.policy(AuditEventType.POLICY_UPDATED, 'user-123', 'policy-101');
      await auditLog.admin(AuditEventType.ADMIN_ACTION_PERFORMED, 'admin-123');
      await auditLog.security(AuditEventType.SUSPICIOUS_ACTIVITY, 'user-123');

      expect(loggingService.audit).toHaveBeenCalledTimes(6);
    });

    it('should handle custom audit events', async () => {
      await auditLog.custom(
        AuditEventType.SYSTEM_CONFIGURATION_CHANGED,
        'admin-123',
        'config-change',
        { setting: 'max_users', oldValue: 100, newValue: 200 },
        { severity: AuditSeverity.HIGH }
      );

      expect(loggingService.audit).toHaveBeenCalledWith(
        'config-change',
        'admin-123',
        expect.objectContaining({
          eventType: AuditEventType.SYSTEM_CONFIGURATION_CHANGED,
          severity: AuditSeverity.HIGH,
          setting: 'max_users',
          oldValue: 100,
          newValue: 200
        }),
        expect.any(Object)
      );
    });
  });

  describe('Disabled Service', () => {
    it('should not log events when disabled', async () => {
      const disabledService = AuditLoggingService.getInstance({
        enabled: false
      });

      await disabledService.logEvent(
        AuditEventType.USER_LOGIN,
        'user-123',
        'login'
      );

      // Should not have called the logging service since audit is disabled
      expect(loggingService.audit).not.toHaveBeenCalled();
    });
  });
});