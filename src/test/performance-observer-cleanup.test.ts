import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { PerformanceObserverManager } from '@/utils/performance-observer-manager';

// Mock PerformanceObserver
const mockDisconnect = vi.fn();
const mockObserve = vi.fn();

class MockPerformanceObserver {
  callback: PerformanceObserverCallback;
  
  constructor(callback: PerformanceObserverCallback) {
    this.callback = callback;
  }
  
  observe = mockObserve;
  disconnect = mockDisconnect;
}

// Mock global PerformanceObserver
global.PerformanceObserver = MockPerformanceObserver as any;

describe('Performance Observer Memory Leak Prevention', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockDisconnect.mockClear();
    mockObserve.mockClear();
  });

  afterEach(() => {
    // Cleanup any global state
    vi.restoreAllMocks();
  });

  describe('PerformanceObserverManager', () => {
    it('should properly cleanup observers when destroyed', () => {
      const manager = new PerformanceObserverManager();

      // Test the cleanup mechanism directly by adding mock cleanup functions
      const mockCleanup1 = vi.fn();
      const mockCleanup2 = vi.fn();

      manager.addCleanup(mockCleanup1);
      manager.addCleanup(mockCleanup2);

      // Verify state
      expect(manager.observerCount).toBe(2);
      expect(manager.active).toBe(true);

      // Destroy manager
      manager.destroy();

      // Verify cleanup
      expect(mockCleanup1).toHaveBeenCalledTimes(1);
      expect(mockCleanup2).toHaveBeenCalledTimes(1);
      expect(manager.observerCount).toBe(0);
      expect(manager.active).toBe(false);
    });

    it('should handle cleanup functions added after destruction', () => {
      const manager = new PerformanceObserverManager();
      const mockCleanup = vi.fn();
      
      // Destroy manager first
      manager.destroy();
      
      // Add cleanup function after destruction
      manager.addCleanup(mockCleanup);
      
      // Should be called immediately
      expect(mockCleanup).toHaveBeenCalledTimes(1);
    });

    it('should handle errors during cleanup gracefully', () => {
      const manager = new PerformanceObserverManager();
      const errorCleanup = vi.fn(() => {
        throw new Error('Cleanup error');
      });
      const normalCleanup = vi.fn();
      
      manager.addCleanup(errorCleanup);
      manager.addCleanup(normalCleanup);
      
      // Should not throw and should call all cleanup functions
      expect(() => manager.destroy()).not.toThrow();
      expect(errorCleanup).toHaveBeenCalledTimes(1);
      expect(normalCleanup).toHaveBeenCalledTimes(1);
    });

    it('should not start observers when not in development mode', () => {
      const manager = new PerformanceObserverManager();

      // Mock production environment for this test
      const originalEnv = import.meta.env;
      (import.meta as any).env = { MODE: 'production' };

      try {
        manager.startAll();

        // No observers should be created in production
        expect(mockObserve).not.toHaveBeenCalled();
        expect(manager.observerCount).toBe(0);
      } finally {
        // Restore original environment
        (import.meta as any).env = originalEnv;
      }
    });

    it('should demonstrate memory leak prevention pattern', () => {
      const manager = new PerformanceObserverManager();

      // Mock development environment
      const originalEnv = import.meta.env;
      (import.meta as any).env = { MODE: 'development' };

      try {
        // Simulate creating multiple observers
        const mockCleanup1 = vi.fn();
        const mockCleanup2 = vi.fn();
        const mockCleanup3 = vi.fn();

        manager.addCleanup(mockCleanup1);
        manager.addCleanup(mockCleanup2);
        manager.addCleanup(mockCleanup3);

        expect(manager.observerCount).toBe(3);
        expect(manager.active).toBe(true);

        // Destroy manager should call all cleanup functions
        manager.destroy();

        expect(mockCleanup1).toHaveBeenCalledTimes(1);
        expect(mockCleanup2).toHaveBeenCalledTimes(1);
        expect(mockCleanup3).toHaveBeenCalledTimes(1);
        expect(manager.observerCount).toBe(0);
        expect(manager.active).toBe(false);
      } finally {
        // Restore original environment
        (import.meta as any).env = originalEnv;
      }
    });
  });
});
