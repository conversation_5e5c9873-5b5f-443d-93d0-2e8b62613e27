import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { Logger, LogLevel } from '@/utils/errors/Logger';
import { LoggingService, loggingService, log } from '@/services/loggingService';
import { getLoggingConfig, toLoggerConfig } from '@/config/logging';

describe('Centralized Logging Service', () => {
  let logger: Logger;
  let originalFetch: typeof global.fetch;

  beforeEach(() => {
    // Reset logger instance for each test
    Logger.resetInstance();
    logger = Logger.getInstance({
      enableConsole: true, // Enable for test output
      enableRemote: false,
      maxLogEntries: 100,
      batchSize: 5,
      flushInterval: 1000,
      minLevel: LogLevel.DEBUG // Ensure all levels are logged in tests
    });
    
    // Reset logging service instance and provide the test logger
    LoggingService.resetInstance();
    LoggingService.getInstance(logger);
    
    // Mock fetch for remote logging tests
    originalFetch = global.fetch;
    global.fetch = vi.fn();
  });

  afterEach(() => {
    logger.clearLogs();
    global.fetch = originalFetch;
  });

  describe('Logger Configuration', () => {
    it('should configure logger based on environment', () => {
      const config = getLoggingConfig();
      expect(config).toBeDefined();
      expect(config.logLevel).toBeDefined();
      expect(config.enableConsole).toBeDefined();
      expect(config.enableRemote).toBeDefined();
    });

    it('should convert logging config to logger config', () => {
      const loggingConfig = getLoggingConfig();
      const loggerConfig = toLoggerConfig(loggingConfig);
      
      expect(loggerConfig.minLevel).toBe(loggingConfig.logLevel);
      expect(loggerConfig.enableConsole).toBe(loggingConfig.enableConsole);
      expect(loggerConfig.enableRemote).toBe(loggingConfig.enableRemote);
    });
  });

  describe('Basic Logging Functionality', () => {
    it('should log debug messages', () => {
      logger.debug('Test debug message', { component: 'test' });
      const logs = logger.getRecentLogs(1);
      
      expect(logs).toHaveLength(1);
      expect(logs[0].level).toBe(LogLevel.DEBUG);
      expect(logs[0].message).toBe('Test debug message');
    });

    it('should log info messages', () => {
      logger.info('Test info message', { component: 'test' });
      const logs = logger.getRecentLogs(1);
      
      expect(logs).toHaveLength(1);
      expect(logs[0].level).toBe(LogLevel.INFO);
      expect(logs[0].message).toBe('Test info message');
    });

    it('should log warning messages', () => {
      logger.warn('Test warning message', { component: 'test' });
      const logs = logger.getRecentLogs(1);
      
      expect(logs).toHaveLength(1);
      expect(logs[0].level).toBe(LogLevel.WARN);
      expect(logs[0].message).toBe('Test warning message');
    });

    it('should log error messages', () => {
      const testError = new Error('Test error');
      logger.error('Test error message', testError, { component: 'test' });
      const logs = logger.getRecentLogs(1);
      
      expect(logs).toHaveLength(1);
      expect(logs[0].level).toBe(LogLevel.ERROR);
      expect(logs[0].message).toBe('Test error message');
      expect(logs[0].stack).toBe(testError.stack);
    });
  });

  describe('Correlation ID Functionality', () => {
    it('should generate correlation IDs', () => {
      const correlationId = logger.startCorrelation();
      expect(correlationId).toBeDefined();
      expect(correlationId).toMatch(/^corr_\d+_\d+$/);
    });

    it('should use custom correlation ID', () => {
      const customId = 'custom-correlation-123';
      const correlationId = logger.startCorrelation(customId);
      expect(correlationId).toBe(customId);
    });

    it('should include correlation ID in log entries', () => {
      const correlationId = logger.startCorrelation();
      logger.info('Test message with correlation', { component: 'test' });
      
      const logs = logger.getRecentLogs(1);
      expect(logs[0].correlationId).toBe(correlationId);
    });

    it('should filter logs by correlation ID', () => {
      const correlationId1 = logger.startCorrelation();
      logger.info('Message 1', { component: 'test' });
      
      logger.endCorrelation();
      const correlationId2 = logger.startCorrelation();
      logger.info('Message 2', { component: 'test' });
      
      const logs1 = logger.getLogsByCorrelationId(correlationId1);
      const logs2 = logger.getLogsByCorrelationId(correlationId2);
      
      expect(logs1).toHaveLength(1);
      expect(logs2).toHaveLength(1);
      expect(logs1[0].message).toBe('Message 1');
      expect(logs2[0].message).toBe('Message 2');
    });
  });

  describe('Logging Service Convenience Functions', () => {
    it('should provide convenience logging functions', () => {
      log.info('Test info via convenience function');
      log.warn('Test warning via convenience function');
      log.error('Test error via convenience function');
      
      const logs = logger.getRecentLogs(3);
      expect(logs).toHaveLength(3);
      expect(logs.map(l => l.message)).toContain('Test info via convenience function');
      expect(logs.map(l => l.message)).toContain('Test warning via convenience function');
      expect(logs.map(l => l.message)).toContain('Test error via convenience function');
    });

    it('should log performance metrics', () => {
      log.performance('Test operation', { duration: 150, memory: 1024 });
      
      const logs = logger.getRecentLogs(1);
      expect(logs[0].message).toBe('Test operation');
      expect(logs[0].metadata?.performance).toEqual({ duration: 150, memory: 1024 });
    });

    it('should log audit events', () => {
      log.audit('user_login', 'user123', { ip: '***********' });
      
      const logs = logger.getRecentLogs(1);
      expect(logs[0].message).toBe('User action: user_login');
      expect(logs[0].userId).toBe('user123');
      expect(logs[0].action).toBe('user_login');
      expect(logs[0].metadata?.auditDetails).toEqual({ ip: '***********' });
    });

    it('should log API requests', () => {
      log.api('GET', '/api/users', 200, 250);
      
      const logs = logger.getRecentLogs(1);
      expect(logs[0].message).toBe('API request: GET /api/users');
      expect(logs[0].metadata?.method).toBe('GET');
      expect(logs[0].metadata?.url).toBe('/api/users');
      expect(logs[0].metadata?.status).toBe(200);
      expect(logs[0].metadata?.duration).toBe(250);
      expect(logs[0].metadata?.success).toBe(true);
    });

    it('should log failed API requests as errors', () => {
      log.api('POST', '/api/users', 500, 1000);
      
      const logs = logger.getRecentLogs(1);
      expect(logs[0].level).toBe(LogLevel.ERROR);
      expect(logs[0].message).toBe('API request failed: POST /api/users');
      expect(logs[0].metadata?.success).toBe(false);
    });
  });

  describe('Log Filtering and Querying', () => {
    beforeEach(() => {
      // Add test logs
      logger.debug('Debug message', { component: 'test-component' });
      logger.info('Info message', { component: 'test-component' });
      logger.warn('Warning message', { component: 'other-component' });
      logger.error('Error message', undefined, { component: 'test-component' });
    });

    it('should filter logs by level', () => {
      const errorLogs = logger.getLogsByLevel(LogLevel.ERROR);
      const infoLogs = logger.getLogsByLevel(LogLevel.INFO);
      
      expect(errorLogs).toHaveLength(1);
      expect(infoLogs).toHaveLength(1);
      expect(errorLogs[0].message).toBe('Error message');
      expect(infoLogs[0].message).toBe('Info message');
    });

    it('should filter logs by component', () => {
      const testComponentLogs = logger.getLogsByComponent('test-component');
      const otherComponentLogs = logger.getLogsByComponent('other-component');
      
      expect(testComponentLogs).toHaveLength(3); // debug, info, error
      expect(otherComponentLogs).toHaveLength(1); // warn
    });

    it('should get recent logs with limit', () => {
      const recentLogs = logger.getRecentLogs(2);
      expect(recentLogs).toHaveLength(2);
      
      // Should get the most recent logs (error and warn)
      expect(recentLogs[1].message).toBe('Error message');
      expect(recentLogs[0].message).toBe('Warning message');
    });
  });

  describe('Statistics and Monitoring', () => {
    beforeEach(() => {
      logger.debug('Debug message');
      logger.info('Info message 1');
      logger.info('Info message 2');
      logger.warn('Warning message');
      logger.error('Error message');
    });

    it('should provide logging statistics', () => {
      const stats = logger.getStats();
      
      expect(stats.totalLogs).toBe(5);
      expect(stats.logsByLevel.DEBUG).toBe(1);
      expect(stats.logsByLevel.INFO).toBe(2);
      expect(stats.logsByLevel.WARN).toBe(1);
      expect(stats.logsByLevel.ERROR).toBe(1);
      expect(stats.environment).toBeDefined();
      expect(stats.sessionId).toBeDefined();
    });

    it('should track batch size', () => {
      const stats = logger.getStats();
      expect(stats.batchSize).toBe(5); // All logs should be in batch
    });
  });

  describe('Remote Logging', () => {
    it('should attempt to send logs to remote endpoint when enabled', async () => {
      const mockFetch = vi.fn().mockResolvedValue({ ok: true });
      global.fetch = mockFetch;

      Logger.resetInstance();
      const remoteLogger = Logger.getInstance({
        enableRemote: true,
        remoteEndpoint: 'http://test.com/logs',
        batchSize: 1,
        flushInterval: 50,
        enableConsole: false
      });

      remoteLogger.info('Test remote log');
      
      // Force flush the batch
      await remoteLogger.flush();
      
      expect(mockFetch).toHaveBeenCalledWith(
        'http://test.com/logs',
        expect.objectContaining({
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: expect.stringContaining('Test remote log')
        })
      );
    });

    it('should handle remote logging failures gracefully', async () => {
      const mockFetch = vi.fn().mockRejectedValue(new Error('Network error'));
      global.fetch = mockFetch;

      Logger.resetInstance();
      const remoteLogger = Logger.getInstance({
        enableRemote: true,
        remoteEndpoint: 'http://test.com/logs',
        batchSize: 1,
        enableConsole: false
      });

      // Should not throw error
      expect(() => {
        remoteLogger.info('Test remote log');
      }).not.toThrow();
    });
  });

  describe('Log Level Filtering', () => {
    it('should respect minimum log level', () => {
      Logger.resetInstance();
      const filteredLogger = Logger.getInstance({
        minLevel: LogLevel.WARN,
        enableConsole: false,
        enableRemote: false
      });

      filteredLogger.debug('Debug message');
      filteredLogger.info('Info message');
      filteredLogger.warn('Warning message');
      filteredLogger.error('Error message');

      const logs = filteredLogger.getRecentLogs();
      expect(logs).toHaveLength(2); // Only warn and error
      expect(logs.map(l => l.message)).toContain('Warning message');
      expect(logs.map(l => l.message)).toContain('Error message');
    });
  });

  describe('Memory Management', () => {
    it('should limit log entries to prevent memory issues', () => {
      Logger.resetInstance();
      const limitedLogger = Logger.getInstance({
        maxLogEntries: 3,
        enableConsole: false,
        enableRemote: false,
        minLevel: LogLevel.DEBUG
      });

      // Add more logs than the limit
      limitedLogger.info('Log 1');
      limitedLogger.info('Log 2');
      limitedLogger.info('Log 3');
      limitedLogger.info('Log 4');
      limitedLogger.info('Log 5');

      const logs = limitedLogger.getRecentLogs();
      expect(logs).toHaveLength(3);
      
      // Should keep the most recent logs
      expect(logs.map(l => l.message)).toEqual(['Log 3', 'Log 4', 'Log 5']);
    });
  });
});