import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { createQueryClient } from '@/lib/query-client';
import { createLazyRoute, preloadRoute } from '@/utils/lazy-routes';

// Mock components for testing
const MockComponent = () => <div data-testid="mock-component">Mock Component</div>;
const MockComponentModule = { default: MockComponent };

// Mock import function
const mockImport = vi.fn(() => Promise.resolve(MockComponentModule));

// Test wrapper component
const TestWrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = createQueryClient();

  // Override settings for testing
  queryClient.setDefaultOptions({
    queries: { retry: false, staleTime: 0, gcTime: 0 },
    mutations: { retry: false },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Lazy Routes', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('createLazyRoute', () => {
    it('should create a lazy component that renders correctly', async () => {
      const LazyTestComponent = createLazyRoute(mockImport, {
        routeName: 'test-route',
        loadingMessage: 'Loading test...',
      });

      render(
        <TestWrapper>
          <LazyTestComponent />
        </TestWrapper>
      );

      // Should show loading initially
      expect(screen.getByText('Loading test...')).toBeInTheDocument();

      // Should render the actual component after loading
      await waitFor(() => {
        expect(screen.getByTestId('mock-component')).toBeInTheDocument();
      });

      expect(mockImport).toHaveBeenCalledTimes(1);
    });

    it('should handle loading errors gracefully', async () => {
      const failingImport = vi.fn(() => Promise.reject(new Error('Loading failed')));
      
      const LazyFailingComponent = createLazyRoute(failingImport, {
        routeName: 'failing-route',
      });

      render(
        <TestWrapper>
          <LazyFailingComponent />
        </TestWrapper>
      );

      // Should show error boundary content
      await waitFor(() => {
        expect(screen.getByText(/loading error/i)).toBeInTheDocument();
      });
    });

    it('should preload component when preload option is true', async () => {
      const preloadImport = vi.fn(() => Promise.resolve(MockComponentModule));
      
      createLazyRoute(preloadImport, {
        routeName: 'preload-route',
        preload: true,
      });

      // Wait for preload timeout
      await waitFor(() => {
        expect(preloadImport).toHaveBeenCalled();
      }, { timeout: 200 });
    });
  });

  describe('preloadRoute', () => {
    it('should call import function for preloading', async () => {
      const preloadImport = vi.fn(() => Promise.resolve(MockComponentModule));
      
      await preloadRoute(preloadImport);
      
      expect(preloadImport).toHaveBeenCalledTimes(1);
    });

    it('should handle preload failures silently', async () => {
      const failingPreload = vi.fn(() => Promise.reject(new Error('Preload failed')));
      
      // Should not throw
      await expect(preloadRoute(failingPreload)).resolves.toBeUndefined();
      expect(failingPreload).toHaveBeenCalledTimes(1);
    });
  });
});

describe('Route Groups Configuration', () => {
  it('should have correct route group configurations', async () => {
    const { ROUTE_GROUPS } = await import('@/utils/lazy-routes');

    expect(ROUTE_GROUPS.CRITICAL).toEqual({
      loadingMessage: "Loading...",
      fullScreenLoading: true,
      preload: true
    });

    expect(ROUTE_GROUPS.AUTH).toEqual({
      loadingMessage: "Loading authentication...",
      fullScreenLoading: true
    });

    expect(ROUTE_GROUPS.RISK).toEqual({
      loadingMessage: "Loading risk management...",
      fullScreenLoading: false
    });
  });
});
