/**
 * Migration Example: From 'as any' to Type-Safe Reporter Handling
 *
 * This file demonstrates how to migrate from unsafe type casting
 * to the type-safe reporter validation implementation.
 */
import { parseReporterArgument, validateReporter, ValidReporter } from "./test-runner";
// ============================================================================
// BEFORE: Unsafe Type Casting (around lines 244-246 in original)
// ============================================================================
function unsafeReporterHandling(args: { reporter?: unknown }) {
  // ❌ PROBLEMATIC: Bypasses type safety
  const reporter = args.reporter as any;
  // This could cause runtime errors if reporter is not a string
  // or if it's an invalid reporter type
  return reporter;
}
// ============================================================================
// AFTER: Type-Safe Implementation
// ============================================================================
function safeReporterHandling(args: { reporter?: unknown }) {
  // ✅ TYPE-SAFE: Validates and handles all cases
  try {
    const reporter = parseReporterArgument(args.reporter);
    return reporter;
  } catch (error) {
    return ValidReporter.DEFAULT;
  }
}
// ============================================================================
// Comparison Examples
// ============================================================================
function demonstrateDifferences() {
  const testCases = [
    { name: "Valid string reporter", value: "json" },
    { name: "Valid array of reporters", value: ["default", "html"] },
    { name: "Comma-separated reporters", value: "json,verbose,html" },
    { name: "Custom reporter path", value: "./custom-reporter.js" },
    { name: "NPM package reporter", value: "@scope/custom-reporter" },
    { name: "Invalid number", value: 123 },
    { name: "Invalid object", value: { type: "json" } },
    { name: "Invalid string", value: "invalid-reporter!" },
    { name: "Null value", value: null },
    { name: "Undefined value", value: undefined },
  ];
  testCases.forEach((testCase, index) => {
    // Unsafe approach (would cause issues)
    try {
      const unsafeResult = unsafeReporterHandling({ reporter: testCase.value });
    } catch (error) {
      // Error caught and handled
    }
    // Safe approach
    try {
      const safeResult = safeReporterHandling({ reporter: testCase.value });
    } catch (error) {
      // Error caught and handled
    }
  });
}
// ============================================================================
// Advanced Migration Patterns
// ============================================================================
/**
 * Pattern 1: CLI Argument Processing
 */
function migrateCliProcessing() {
  // BEFORE: Unsafe
  function unsafeCliProcessing(argv: string[]) {
    const reporterIndex = argv.indexOf("--reporter");
    if (reporterIndex !== -1 && reporterIndex + 1 < argv.length) {
      const reporter = argv[reporterIndex + 1] as any; // ❌ Unsafe cast
      return reporter;
    }
    return "default";
  }
  // AFTER: Type-safe
  function safeCliProcessing(argv: string[]) {
    const reporterIndex = argv.indexOf("--reporter");
    if (reporterIndex !== -1 && reporterIndex + 1 < argv.length) {
      const reporterValue = argv[reporterIndex + 1];
      return parseReporterArgument(reporterValue); // ✅ Type-safe validation
    }
    return ValidReporter.DEFAULT;
  }
  // Example usage
  const cliArgs = ["--reporter", "json,html"];
}
/**
 * Pattern 2: Configuration Object Processing
 */
function migrateConfigProcessing() {
  interface UnsafeConfig {
    reporter?: string | string[]; // ✅ Proper typing instead of any
  }
  interface SafeConfig {
    reporter?: string | string[]; // ✅ Proper typing
  }
  // BEFORE: Unsafe
  function processUnsafeConfig(config: UnsafeConfig) {
    const reporter = config.reporter as any; // ❌ Unsafe cast
    return { ...config, reporter };
  }
  // AFTER: Type-safe
  function processSafeConfig(config: SafeConfig) {
    const validatedReporter = config.reporter
      ? parseReporterArgument(config.reporter) // ✅ Type-safe validation
      : ValidReporter.DEFAULT;
    return { ...config, reporter: validatedReporter };
  }
  // Example usage
  const testConfig = { reporter: ["json", "html"] };
}
/**
 * Pattern 3: Error Handling Migration
 */
function migrateErrorHandling() {
  // BEFORE: No error handling
  function unsafeWithNoErrorHandling(reporter: unknown) {
    return reporter as any; // ❌ Could explode at runtime
  }
  // AFTER: Comprehensive error handling
  function safeWithErrorHandling(reporter: unknown) {
    try {
      return validateReporter(reporter); // ✅ Validates and throws descriptive errors
    } catch (error) {
      return ValidReporter.DEFAULT;
    }
  }
  // Example usage
  const invalidReporter = 123;
}
// ============================================================================
// Main Demonstration
// ============================================================================
if (require.main === module) {
  demonstrateDifferences();
  migrateCliProcessing();
  migrateConfigProcessing();
  migrateErrorHandling();
}
export {
  unsafeReporterHandling,
  safeReporterHandling,
  demonstrateDifferences,
  migrateCliProcessing,
  migrateConfigProcessing,
  migrateErrorHandling,
};
