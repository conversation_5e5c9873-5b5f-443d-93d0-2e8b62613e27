import "@testing-library/jest-dom";
import { vi } from "vitest";

// Add vitest globals to global scope
declare global {
  var describe: typeof import("vitest").describe;
  var it: typeof import("vitest").it;
  var expect: typeof import("vitest").expect;
  var beforeEach: typeof import("vitest").beforeEach;
  var afterEach: typeof import("vitest").afterEach;
  var beforeAll: typeof import("vitest").beforeAll;
  var afterAll: typeof import("vitest").afterAll;
  var vi: typeof import("vitest").vi;
}

// Mock window.matchMedia for tests that use media queries
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock performance API for tests
Object.defineProperty(window, "performance", {
  writable: true,
  value: {
    getEntriesByType: vi.fn().mockReturnValue([]),
    mark: vi.fn(),
    measure: vi.fn(),
    clearMarks: vi.fn(),
    clearMeasures: vi.fn(),
    now: vi.fn(() => Date.now()),
  },
});

// Mock IntersectionObserver for components that use it
global.IntersectionObserver = class IntersectionObserver {
  constructor() {
    // Implementation needed
  }
  observe() {
    return null;
  }
  disconnect() {
    return null;
  }
  unobserve() {
    return null;
  }
};
