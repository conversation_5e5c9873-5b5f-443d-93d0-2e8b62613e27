import React, { ReactElement } from "react";
import { render, RenderOptions } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { createQueryClient } from "@/lib/query-client";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-router-dom";
import { ThemeProvider } from "next-themes";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider } from "@/contexts/auth";
import { LoadingProvider } from "@/contexts/loading-context";
import { vi } from "vitest";
import {
  User,
  Organization,
  RiskSeverity,
  RiskStatus,
  Risk,
  Incident,
  RiskWizardFormData,
  ControlMeasureData,
  MitigationActionData,
} from "@/types";
import { Policy } from "@/types/policy";
import { IncidentFormValues } from "@/hooks/useIncidentForm";

// Type definitions for form data used in factory functions
export interface RiskFormData {
  title: string;
  description: string;
  category: string;
  categoryId: string;
  ownerId: string;
  inherentLikelihood: number;
  inherentImpact: number;
  inherentSeverity: RiskSeverity;
  likelihood: number;
  impact: number;
  severity: RiskSeverity;
  status: RiskStatus;
  mitigationApproach: string;
  controlMeasures: ControlMeasureData[];
  mitigationActions: MitigationActionData[];
  dueDate: Date;
}

export interface IncidentFormData {
  title: string;
  description: string;
  severity: RiskSeverity;
  status: "Open" | "Investigating" | "Resolved" | "Closed";
  relatedRiskId?: string;
}

// Mock user data for testing
export const mockUser: User = {
  id: "test-user-id",
  name: "Test User",
  email: "<EMAIL>",
  role: "admin" as any,
  department: "IT",
  organizationId: "test-org-id",
};

export const mockOrganization: Organization = {
  id: "test-org-id",
  name: "Test Organization",
  slug: "test-org",
  domain: "test.com",
  subscriptionPlan: "professional",
  subscriptionStatus: "active",
  maxUsers: 100,
  maxRisks: 1000,
  createdAt: new Date("2024-01-01"),
  updatedAt: new Date("2024-01-01"),
};

// Mock risk data
export const mockRisk = {
  id: "test-risk-id",
  title: "Test Risk",
  description: "Test risk description",
  category: "Information Security",
  categoryId: "test-category-id",
  ownerId: "test-user-id",
  ownerName: "Test User",
  organizationId: "test-org-id",
  createdAt: new Date("2024-01-01"),
  updatedAt: new Date("2024-01-01"),
  inherentLikelihood: 4,
  inherentImpact: 3,
  inherentSeverity: RiskSeverity.HIGH,
  likelihood: 2,
  impact: 3,
  severity: RiskSeverity.MEDIUM,
  status: RiskStatus.IN_PROGRESS,
  currentControls: "Test controls",
  mitigationApproach: "Test mitigation",
  dueDate: new Date("2024-12-31"),
};

// Mock incident data
export const mockIncident = {
  id: "test-incident-id",
  title: "Test Incident",
  description: "Test incident description",
  reporterId: "test-user-id",
  reporterName: "Test User",
  organizationId: "test-org-id",
  date: new Date("2024-01-01"),
  status: "Open" as const,
  severity: RiskSeverity.HIGH,
  relatedRiskId: "test-risk-id",
  relatedRiskTitle: "Test Risk",
};

// Mock policy data
export const mockPolicy = {
  id: "test-policy-id",
  title: "Test Policy",
  description: "Test policy description",
  category: "Information Security",
  version: "1.0",
  status: "published",
  effectiveDate: "2024-01-01",
  documentUrl: "https://example.com/policy.pdf",
  createdBy: "test-user-id",
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z",
};

// Create a custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = createQueryClient();

  // Override settings for testing
  queryClient.setDefaultOptions({
    queries: {
      retry: false,
      staleTime: 0,
      gcTime: 0,
    },
    mutations: {
      retry: false,
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
          <TooltipProvider>
            <LoadingProvider>
              <AuthProvider>{children}</AuthProvider>
            </LoadingProvider>
          </TooltipProvider>
        </ThemeProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

const customRender = (ui: ReactElement, options?: Omit<RenderOptions, "wrapper">) =>
  render(ui, { wrapper: AllTheProviders, ...options });

// Re-export everything
export * from "@testing-library/react";
export { customRender as render };

// Helper functions for testing

export const createMockSupabaseResponse = (data: unknown, error: Error | unknown = null) => ({
  data,
  error,
  status: error ? 400 : 200,
  statusText: error ? "Bad Request" : "OK",
});

// Comprehensive Supabase mock chain builder

export const createMockSupabaseChain = (
  response: unknown = null,
  error: Error | unknown = null
) => {
  const mockResponse = createMockSupabaseResponse(response, error);

  // Create a mock chain that supports fluent API
  const createChainMethod = (methodName: string) => {
    if (["single", "maybeSingle"].includes(methodName)) {
      // Terminal methods that return promises
      return vi.fn().mockResolvedValue(mockResponse);
    } else if (methodName === "onAuthStateChange") {
      // Special auth method
      return vi.fn().mockReturnValue({ data: { subscription: { unsubscribe: vi.fn() } } });
    } else {
      // Chain methods that return the chain for fluent API
      return vi.fn().mockImplementation((...args) => chain);
    }
  };

  const chain = {
    // Query builder methods
    select: createChainMethod("select"),
    from: createChainMethod("from"),
    insert: createChainMethod("insert"),
    update: createChainMethod("update"),
    delete: createChainMethod("delete"),
    upsert: createChainMethod("upsert"),

    // Filter methods
    eq: createChainMethod("eq"),
    neq: createChainMethod("neq"),
    gt: createChainMethod("gt"),
    gte: createChainMethod("gte"),
    lt: createChainMethod("lt"),
    lte: createChainMethod("lte"),
    like: createChainMethod("like"),
    ilike: createChainMethod("ilike"),
    is: createChainMethod("is"),
    in: createChainMethod("in"),
    contains: createChainMethod("contains"),
    containedBy: createChainMethod("containedBy"),
    overlaps: createChainMethod("overlaps"),
    range: createChainMethod("range"),
    adjacent: createChainMethod("adjacent"),
    not: createChainMethod("not"),
    or: createChainMethod("or"),
    filter: createChainMethod("filter"),
    match: createChainMethod("match"),

    // Transform methods
    order: createChainMethod("order"),
    limit: createChainMethod("limit"),
    abortSignal: createChainMethod("abortSignal"),

    // Execution methods - these are terminal and return promises
    single: createChainMethod("single"),
    maybeSingle: createChainMethod("maybeSingle"),

    // Auth methods
    signUp: vi.fn().mockResolvedValue(mockResponse),
    signInWithPassword: vi.fn().mockResolvedValue(mockResponse),
    signOut: vi.fn().mockResolvedValue(mockResponse),
    getSession: vi.fn().mockResolvedValue(mockResponse),
    getUser: vi.fn().mockResolvedValue(mockResponse),
    onAuthStateChange: createChainMethod("onAuthStateChange"),

    // RPC methods
    rpc: vi.fn().mockResolvedValue(mockResponse),

    // Storage methods
    upload: vi.fn().mockResolvedValue(mockResponse),
    download: vi.fn().mockResolvedValue(mockResponse),
    remove: vi.fn().mockResolvedValue(mockResponse),
    list: vi.fn().mockResolvedValue(mockResponse),
  };

  return chain;
};

// Mock Supabase client factory

export const createMockSupabaseClient = () => {
  return {
    from: vi.fn().mockImplementation(() => createMockSupabaseChain()),
    auth: {
      signUp: vi.fn().mockResolvedValue(createMockSupabaseResponse(null)),
      signInWithPassword: vi.fn().mockResolvedValue(createMockSupabaseResponse(null)),
      signOut: vi.fn().mockResolvedValue(createMockSupabaseResponse(null)),
      getSession: vi.fn().mockResolvedValue(createMockSupabaseResponse({ session: null })),
      getUser: vi.fn().mockResolvedValue(createMockSupabaseResponse({ user: null })),
      onAuthStateChange: vi
        .fn()
        .mockReturnValue({ data: { subscription: { unsubscribe: vi.fn() } } }),
    },
    storage: {
      from: vi.fn().mockReturnValue({
        upload: vi.fn().mockResolvedValue(createMockSupabaseResponse(null)),
        download: vi.fn().mockResolvedValue(createMockSupabaseResponse(null)),
        remove: vi.fn().mockResolvedValue(createMockSupabaseResponse(null)),
        list: vi.fn().mockResolvedValue(createMockSupabaseResponse([])),
      }),
    },
    rpc: vi.fn().mockResolvedValue(createMockSupabaseResponse(null)),
  };
};

export const createMockQueryClient = () => {
  const queryClient = createQueryClient();

  // Override settings for testing
  queryClient.setDefaultOptions({
    queries: {
      retry: false,
      staleTime: 0,
      gcTime: 0,
    },
    mutations: {
      retry: false,
    },
  });

  return queryClient;
};

// Mock form data generators with proper typing

export const createMockRiskFormData = (overrides: Partial<RiskFormData> = {}): RiskFormData => ({
  title: "Test Risk",
  description: "Test risk description",
  category: "Information Security",
  categoryId: "test-category-id",
  ownerId: "test-user-id",
  inherentLikelihood: 4,
  inherentImpact: 3,
  inherentSeverity: RiskSeverity.HIGH,
  likelihood: 2,
  impact: 3,
  severity: RiskSeverity.MEDIUM,
  status: RiskStatus.IN_PROGRESS,
  mitigationApproach: "Test mitigation",
  controlMeasures: [],
  mitigationActions: [],
  dueDate: new Date("2024-12-31"),
  ...overrides,
});

export const createMockIncidentFormData = (
  overrides: Partial<IncidentFormData> = {
    // Implementation needed
  }
): IncidentFormData => ({
  title: "Test Incident",
  description: "Test incident description",
  severity: RiskSeverity.HIGH,
  status: "Open" as const,
  relatedRiskId: "test-risk-id",
  ...overrides,
});

// Wait for async operations

export const waitForLoadingToFinish = () => new Promise(resolve => setTimeout(resolve, 0));

// Mock API responses
export const mockApiResponses = {
  risks: [mockRisk],
  incidents: [mockIncident],
  policies: [mockPolicy],
  user: mockUser,
  organization: mockOrganization,
};

// Test data factories with proper typing

export const createTestRisk = (overrides: Partial<Risk> = {}): Risk => ({
  ...mockRisk,
  ...overrides,
});

export const createTestIncident = (overrides: Partial<Incident> = {}): Incident => ({
  ...mockIncident,
  ...overrides,
});

export const createTestPolicy = (overrides: Partial<Policy> = {}): Policy => ({
  ...mockPolicy,
  ...overrides,
});

export const createTestUser = (overrides: Partial<User> = {}): User => ({
  ...mockUser,
  ...overrides,
});

export const createTestOrganization = (overrides: Partial<Organization> = {}): Organization => ({
  ...mockOrganization,
  ...overrides,
});
