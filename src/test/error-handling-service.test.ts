/**
 * Tests for error handling service
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import {
  executeWithErrorHandling,
  transformError,
  createUserFriendlyMessage,
  logStructuredError,
  createFallbackResult,
  combineServiceResults
} from '@/services/errorHandlingService';
import { ServiceResult, ServiceError } from '@/types/api';
import { ErrorContext, ErrorCategory } from '@/types';
import { errorMonitoringService } from '@/services/errorMonitoringService';

// Mock console methods to avoid noise in tests
const mockConsole = {
  error: vi.fn(),
  warn: vi.fn(),
  info: vi.fn(),
  log: vi.fn(),
  debug: vi.fn()
};
vi.stubGlobal('console', mockConsole);

describe('Error Handling Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset error monitoring service for each test
    errorMonitoringService.reset();
  });

  describe('executeWithErrorHandling', () => {
    it('should return success result for successful operation', async () => {
      const operation = vi.fn().mockResolvedValue('success data');
      
      const result = await executeWithErrorHandling(operation);
      
      expect(result.success).toBe(true);
      expect(result.data).toBe('success data');
      expect(result.metadata?.timestamp).toBeDefined();
      expect(result.metadata?.retryCount).toBe(0);
    });

    it('should handle operation failure and return error result', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Operation failed'));
      
      const result = await executeWithErrorHandling(operation, { logError: false });
      
      expect(result.success).toBe(false);
      expect(result.error?.message).toBe('Operation failed');
      expect(result.error?.code).toBe('Error');
      expect(result.metadata?.timestamp).toBeDefined();
    });

    it('should retry recoverable errors', async () => {
      const networkError = new Error('Network timeout');
      (networkError as any).code = 'NETWORK_ERROR';
      
      const operation = vi.fn()
        .mockRejectedValueOnce(networkError)
        .mockRejectedValueOnce(networkError)
        .mockResolvedValue('success after retry');
      
      const result = await executeWithErrorHandling(operation, { logError: false });
      
      expect(result.success).toBe(true);
      expect(result.data).toBe('success after retry');
      expect(operation).toHaveBeenCalledTimes(3);
      expect(result.metadata?.retryCount).toBe(2);
    });

    it('should not retry non-recoverable errors', async () => {
      const validationError = new Error('Invalid input');
      (validationError as any).code = 'VALIDATION_ERROR';
      
      const operation = vi.fn().mockRejectedValue(validationError);
      
      const result = await executeWithErrorHandling(operation, { logError: false });
      
      expect(result.success).toBe(false);
      expect(operation).toHaveBeenCalledTimes(1);
      expect(result.metadata?.retryCount).toBe(0);
    });

    it('should respect maximum retry limit', async () => {
      // Mock setTimeout to avoid actual delays in tests
      const originalSetTimeout = global.setTimeout;
      global.setTimeout = vi.fn().mockImplementation((fn) => {
        fn();
        return 1;
      });

      const networkError = new Error('Network timeout');
      (networkError as any).code = 'NETWORK_ERROR';
      
      const operation = vi.fn().mockRejectedValue(networkError);
      
      const result = await executeWithErrorHandling(operation, { logError: false });
      
      expect(result.success).toBe(false);
      expect(operation).toHaveBeenCalledTimes(4); // Initial + 3 retries

      // Restore original setTimeout
      global.setTimeout = originalSetTimeout;
    });

    it('should use custom user message when provided', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Technical error'));
      
      const result = await executeWithErrorHandling(operation, { 
        logError: false,
        userMessage: 'Something went wrong. Please try again.'
      });
      
      expect(result.success).toBe(false);
      expect(result.error?.message).toBe('Something went wrong. Please try again.');
    });

    it('should track errors in monitoring service', async () => {
      const operation = vi.fn().mockRejectedValue(new Error('Monitoring test error'));
      const context: Partial<ErrorContext> = {
        userId: 'test-user',
        organizationId: 'test-org',
        component: 'test-component',
        action: 'test-operation'
      };
      
      await executeWithErrorHandling(operation, { logError: false }, context);
      
      const metrics = errorMonitoringService.getMetrics();
      expect(metrics.totalErrors).toBe(1);
      expect(metrics.errorsByComponent['test-component']).toBe(1);
    });

    it('should track retry attempts in monitoring service', async () => {
      const networkError = new Error('Network timeout');
      (networkError as any).code = 'NETWORK_ERROR';
      
      const operation = vi.fn()
        .mockRejectedValueOnce(networkError)
        .mockRejectedValueOnce(networkError)
        .mockResolvedValue('success');
      
      const context: Partial<ErrorContext> = {
        userId: 'test-user',
        organizationId: 'test-org',
        component: 'network-client'
      };
      
      await executeWithErrorHandling(operation, { logError: false }, context);
      
      const metrics = errorMonitoringService.getMetrics();
      // Should track each retry attempt as a separate error
      // Since the operation succeeds on the third attempt, we should have 2 errors tracked
      expect(metrics.totalErrors).toBe(2);
      expect(metrics.errorsByCategory[ErrorCategory.SYSTEM]).toBe(2); // Errors are converted to SystemError
    });
  });

  describe('transformError', () => {
    it('should transform Error objects correctly', () => {
      const error = new Error('Test error message');
      error.name = 'TestError';
      
      const serviceError = transformError(error);
      
      expect(serviceError.message).toBe('Test error message');
      expect(serviceError.code).toBe('TestError');
      expect(serviceError.category).toBe('system');
      expect(serviceError.details?.originalMessage).toBe('Test error message');
      expect(serviceError.details?.stack).toBeDefined();
    });

    it('should transform string errors correctly', () => {
      const serviceError = transformError('String error message');
      
      expect(serviceError.message).toBe('String error message');
      expect(serviceError.code).toBe('STRING_ERROR');
      expect(serviceError.category).toBe('system');
      expect(serviceError.recoverable).toBe(false);
    });

    it('should transform object errors correctly', () => {
      const errorObj = { message: 'Object error', code: 'OBJ_ERROR' };
      
      const serviceError = transformError(errorObj);
      
      expect(serviceError.message).toBe('Object error');
      expect(serviceError.code).toBe('OBJ_ERROR');
      expect(serviceError.details).toEqual(errorObj);
    });

    it('should handle unknown error types', () => {
      const serviceError = transformError(null);
      
      expect(serviceError.message).toBe('An unexpected error occurred');
      expect(serviceError.code).toBe('UNKNOWN_ERROR');
      expect(serviceError.category).toBe('system');
    });

    it('should use custom user message when provided', () => {
      const error = new Error('Technical message');
      
      const serviceError = transformError(error, 'User-friendly message');
      
      expect(serviceError.message).toBe('User-friendly message');
      expect(serviceError.details?.originalMessage).toBe('Technical message');
    });

    it('should categorize authentication errors correctly', () => {
      const authError = new Error('Unauthorized access');
      (authError as any).code = 'AUTH_ERROR';
      
      const serviceError = transformError(authError);
      
      expect(serviceError.category).toBe('authentication');
    });

    it('should categorize validation errors correctly', () => {
      const validationError = new Error('Invalid input format');
      
      const serviceError = transformError(validationError);
      
      expect(serviceError.category).toBe('validation');
    });

    it('should categorize network errors correctly', () => {
      const networkError = new Error('Network connection failed');
      
      const serviceError = transformError(networkError);
      
      expect(serviceError.category).toBe('network');
      expect(serviceError.recoverable).toBe(true);
    });
  });

  describe('createUserFriendlyMessage', () => {
    it('should create appropriate message for authentication errors', () => {
      const error: ServiceError = {
        message: 'Token expired',
        code: 'AUTH_ERROR',
        category: 'authentication',
        recoverable: false
      };
      
      const message = createUserFriendlyMessage(error);
      
      expect(message).toBe('Please log in again to continue.');
    });

    it('should create appropriate message for authorization errors', () => {
      const error: ServiceError = {
        message: 'Access denied',
        code: 'PERMISSION_ERROR',
        category: 'authorization',
        recoverable: false
      };
      
      const message = createUserFriendlyMessage(error);
      
      expect(message).toBe('You do not have permission to perform this action.');
    });

    it('should create appropriate message for validation errors', () => {
      const error: ServiceError = {
        message: 'Invalid email format',
        code: 'VALIDATION_ERROR',
        category: 'validation',
        recoverable: false
      };
      
      const message = createUserFriendlyMessage(error);
      
      expect(message).toBe('Please check your input and try again.');
    });

    it('should create appropriate message for network errors', () => {
      const error: ServiceError = {
        message: 'Connection timeout',
        code: 'NETWORK_ERROR',
        category: 'network',
        recoverable: true
      };
      
      const message = createUserFriendlyMessage(error);
      
      expect(message).toBe('Connection issue. Please check your internet connection and try again.');
    });

    it('should return original message for business logic errors', () => {
      const error: ServiceError = {
        message: 'Risk cannot be deleted because it has active incidents',
        code: 'BUSINESS_RULE_VIOLATION',
        category: 'business_logic',
        recoverable: false
      };
      
      const message = createUserFriendlyMessage(error);
      
      expect(message).toBe('Risk cannot be deleted because it has active incidents');
    });

    it('should create generic message for system errors', () => {
      const error: ServiceError = {
        message: 'Database connection failed',
        code: 'DB_ERROR',
        category: 'system',
        recoverable: false
      };
      
      const message = createUserFriendlyMessage(error);
      
      expect(message).toBe('An unexpected error occurred. Please try again later.');
    });
  });

  describe('createFallbackResult', () => {
    it('should create fallback result with provided value', () => {
      const fallbackValue = { id: 'fallback', name: 'Fallback Data' };
      const originalError: ServiceError = {
        message: 'Original operation failed',
        code: 'OPERATION_FAILED',
        category: 'network',
        recoverable: true
      };
      
      const result = createFallbackResult(fallbackValue, originalError);
      
      expect(result.success).toBe(true);
      expect(result.data).toEqual(fallbackValue);
      expect(result.metadata?.fallbackUsed).toBe(true);
      expect(result.metadata?.originalError).toBe('Original operation failed');
    });
  });

  describe('combineServiceResults', () => {
    it('should combine all successful results', () => {
      const results: ServiceResult<string>[] = [
        { success: true, data: 'result1' },
        { success: true, data: 'result2' },
        { success: true, data: 'result3' }
      ];
      
      const combined = combineServiceResults(results);
      
      expect(combined.success).toBe(true);
      expect(combined.data).toEqual(['result1', 'result2', 'result3']);
      expect(combined.metadata?.totalOperations).toBe(3);
      expect(combined.metadata?.successfulOperations).toBe(3);
    });

    it('should handle partial success', () => {
      const results: ServiceResult<string>[] = [
        { success: true, data: 'result1' },
        { 
          success: false, 
          error: { message: 'Error 1', code: 'ERR1', category: 'network', recoverable: true }
        },
        { success: true, data: 'result3' }
      ];
      
      const combined = combineServiceResults(results);
      
      expect(combined.success).toBe(true);
      expect(combined.data).toEqual(['result1', 'result3']);
      expect(combined.metadata?.totalOperations).toBe(3);
      expect(combined.metadata?.successfulOperations).toBe(2);
      expect(combined.metadata?.failedOperations).toBe(1);
      expect(combined.metadata?.partialSuccess).toBe(true);
    });

    it('should handle all failures', () => {
      const results: ServiceResult<string>[] = [
        { 
          success: false, 
          error: { message: 'Error 1', code: 'ERR1', category: 'network', recoverable: true }
        },
        { 
          success: false, 
          error: { message: 'Error 2', code: 'ERR2', category: 'validation', recoverable: false }
        }
      ];
      
      const combined = combineServiceResults(results);
      
      expect(combined.success).toBe(false);
      expect(combined.error?.message).toBe('All 2 operations failed');
      expect(combined.error?.code).toBe('BATCH_OPERATION_FAILED');
      expect(combined.error?.recoverable).toBe(true); // At least one error is recoverable
    });

    it('should handle empty results array', () => {
      const results: ServiceResult<string>[] = [];
      
      const combined = combineServiceResults(results);
      
      expect(combined.success).toBe(true);
      expect(combined.data).toEqual([]);
      expect(combined.metadata?.totalOperations).toBe(0);
      expect(combined.metadata?.successfulOperations).toBe(0);
    });
  });

  describe('logStructuredError', () => {
    it('should log structured error information', () => {
      const error: ServiceError = {
        message: 'Test error',
        code: 'TEST_ERROR',
        category: 'validation',
        recoverable: false
      };
      
      const context = {
        operation: 'createRisk',
        userId: 'user-123',
        organizationId: 'org-123',
        additionalData: { riskId: 'risk-456' }
      };
      
      logStructuredError(error, context);
      
      expect(mockConsole.error).toHaveBeenCalledWith(
        'Structured Error Log:',
        expect.stringContaining('"level": "warn"')
      );
      expect(mockConsole.error).toHaveBeenCalledWith(
        'Structured Error Log:',
        expect.stringContaining('"category": "validation"')
      );
      expect(mockConsole.error).toHaveBeenCalledWith(
        'Structured Error Log:',
        expect.stringContaining('"operation": "createRisk"')
      );
    });
  });
});