/**
 * Tests for API response validation and type safety
 */

import { describe, it, expect } from 'vitest';
import {
  isRiskApiResponse,
  isIncidentApiResponse,
  isPolicyApiResponse,
  isPolicyRequestApiResponse,
  isUserProfileApiResponse,
  isOrganizationApiResponse,
  isUserInvitationApiResponse,
  isControlMeasureApiResponse,
  isMitigationActionApiResponse,
  isCommentApiResponse,
  isRiskHistoryApiResponse,
  isRiskApiResponseArray,
  isIncidentApiResponseArray
} from '@/utils/api-validation';
import {
  RiskApiResponse,
  IncidentApiResponse,
  PolicyApiResponse,
  PolicyRequestApiResponse,
  UserProfileApiResponse,
  OrganizationApiResponse,
  UserInvitationApiResponse,
  ControlMeasureApiResponse,
  MitigationActionApiResponse,
  CommentApiResponse,
  RiskHistoryApiResponse
} from '@/types/api';

describe('API Response Validation', () => {
  describe('Risk API Response Validation', () => {
    const validRiskResponse: RiskApiResponse = {
      id: 'risk-123',
      title: 'Test Risk',
      description: 'Test Description',
      category_id: 'cat-123',
      owner_id: 'user-123',
      organization_id: 'org-123',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      inherent_likelihood: 3,
      inherent_impact: 4,
      inherent_severity: 'High',
      likelihood: 2,
      impact: 3,
      severity: 'Medium',
      status: 'In Progress',
      current_controls: 'Some controls',
      mitigation_approach: 'Some approach',
      due_date: '2024-12-31T00:00:00Z',
      created_by: 'user-123',
      template_id: 'template-123'
    };

    it('should validate a correct risk response', () => {
      expect(isRiskApiResponse(validRiskResponse)).toBe(true);
    });

    it('should reject invalid risk response - missing required fields', () => {
      const invalidResponse = { ...validRiskResponse };
      delete (invalidResponse as any).id;
      expect(isRiskApiResponse(invalidResponse)).toBe(false);
    });

    it('should reject invalid risk response - wrong types', () => {
      const invalidResponse = {
        ...validRiskResponse,
        likelihood: 'invalid' // should be number
      };
      expect(isRiskApiResponse(invalidResponse)).toBe(false);
    });

    it('should validate risk response with optional fields as undefined', () => {
      const responseWithUndefined = {
        ...validRiskResponse,
        category_id: undefined,
        current_controls: undefined,
        mitigation_approach: undefined,
        due_date: undefined,
        template_id: undefined
      };
      expect(isRiskApiResponse(responseWithUndefined)).toBe(true);
    });

    it('should validate array of risk responses', () => {
      const validArray = [validRiskResponse, { ...validRiskResponse, id: 'risk-456' }];
      expect(isRiskApiResponseArray(validArray)).toBe(true);
    });

    it('should reject array with invalid risk response', () => {
      const invalidArray = [validRiskResponse, { id: 'invalid' }];
      expect(isRiskApiResponseArray(invalidArray)).toBe(false);
    });
  });

  describe('Incident API Response Validation', () => {
    const validIncidentResponse: IncidentApiResponse = {
      id: 'incident-123',
      title: 'Test Incident',
      description: 'Test Description',
      reporter_id: 'user-123',
      organization_id: 'org-123',
      date: '2024-01-01T00:00:00Z',
      status: 'Open',
      severity: 'High',
      related_risk_id: 'risk-123',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    };

    it('should validate a correct incident response', () => {
      expect(isIncidentApiResponse(validIncidentResponse)).toBe(true);
    });

    it('should reject invalid incident response - missing required fields', () => {
      const invalidResponse = { ...validIncidentResponse };
      delete (invalidResponse as any).title;
      expect(isIncidentApiResponse(invalidResponse)).toBe(false);
    });

    it('should validate incident response with optional related_risk_id as undefined', () => {
      const responseWithUndefined = {
        ...validIncidentResponse,
        related_risk_id: undefined
      };
      expect(isIncidentApiResponse(responseWithUndefined)).toBe(true);
    });

    it('should validate array of incident responses', () => {
      const validArray = [validIncidentResponse, { ...validIncidentResponse, id: 'incident-456' }];
      expect(isIncidentApiResponseArray(validArray)).toBe(true);
    });
  });

  describe('Policy API Response Validation', () => {
    const validPolicyResponse: PolicyApiResponse = {
      id: 'policy-123',
      title: 'Test Policy',
      description: 'Test Description',
      category: 'Security',
      status: 'published',
      version: '1.0',
      created_by: 'user-123',
      organization_id: 'org-123',
      effective_date: '2024-01-01T00:00:00Z',
      document_url: 'https://example.com/doc.pdf',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    };

    it('should validate a correct policy response', () => {
      expect(isPolicyApiResponse(validPolicyResponse)).toBe(true);
    });

    it('should validate policy response with optional fields as undefined', () => {
      const responseWithUndefined = {
        ...validPolicyResponse,
        effective_date: undefined,
        document_url: undefined
      };
      expect(isPolicyApiResponse(responseWithUndefined)).toBe(true);
    });
  });

  describe('User Profile API Response Validation', () => {
    const validUserResponse: UserProfileApiResponse = {
      id: 'user-123',
      name: 'John Doe',
      email: '<EMAIL>',
      role: 'admin',
      department: 'IT',
      avatar_url: 'https://example.com/avatar.jpg',
      organization_id: 'org-123',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      deleted_at: undefined
    };

    it('should validate a correct user profile response', () => {
      expect(isUserProfileApiResponse(validUserResponse)).toBe(true);
    });

    it('should validate user response with optional fields as undefined', () => {
      const responseWithUndefined = {
        ...validUserResponse,
        department: undefined,
        avatar_url: undefined,
        organization_id: undefined,
        deleted_at: undefined
      };
      expect(isUserProfileApiResponse(responseWithUndefined)).toBe(true);
    });
  });

  describe('Organization API Response Validation', () => {
    const validOrgResponse: OrganizationApiResponse = {
      id: 'org-123',
      name: 'Test Organization',
      slug: 'test-org',
      domain: 'test.com',
      logo_url: 'https://example.com/logo.png',
      subscription_plan: 'professional',
      subscription_status: 'active',
      max_users: 100,
      max_risks: 1000,
      organization_size: 'medium',
      sector_type: 'technology',
      sector_description: 'Software Development',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z'
    };

    it('should validate a correct organization response', () => {
      expect(isOrganizationApiResponse(validOrgResponse)).toBe(true);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should reject null values', () => {
      expect(isRiskApiResponse(null)).toBe(false);
      expect(isIncidentApiResponse(null)).toBe(false);
      expect(isPolicyApiResponse(null)).toBe(false);
    });

    it('should reject undefined values', () => {
      expect(isRiskApiResponse(undefined)).toBe(false);
      expect(isIncidentApiResponse(undefined)).toBe(false);
      expect(isPolicyApiResponse(undefined)).toBe(false);
    });

    it('should reject primitive values', () => {
      expect(isRiskApiResponse('string')).toBe(false);
      expect(isRiskApiResponse(123)).toBe(false);
      expect(isRiskApiResponse(true)).toBe(false);
    });

    it('should reject arrays when expecting objects', () => {
      expect(isRiskApiResponse([])).toBe(false);
      expect(isIncidentApiResponse([1, 2, 3])).toBe(false);
    });

    it('should reject empty objects', () => {
      expect(isRiskApiResponse({})).toBe(false);
      expect(isIncidentApiResponse({})).toBe(false);
      expect(isPolicyApiResponse({})).toBe(false);
    });
  });

  describe('Complex Nested Validation', () => {
    it('should handle responses with nested objects', () => {
      const riskWithNested = {
        id: 'risk-123',
        title: 'Test Risk',
        description: 'Test Description',
        owner_id: 'user-123',
        organization_id: 'org-123',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        inherent_likelihood: 3,
        inherent_impact: 4,
        inherent_severity: 'High',
        likelihood: 2,
        impact: 3,
        severity: 'Medium',
        status: 'In Progress',
        created_by: 'user-123',
        profiles: { name: 'John Doe' }, // This is allowed but not validated deeply
        categories: { name: 'Security' } // This is allowed but not validated deeply
      };

      expect(isRiskApiResponse(riskWithNested)).toBe(true);
    });
  });
});