/**
 * Manual validation script for input sanitization integration
 */
import {
  sanitizeUserComment,
  sanitizeRiskDescription,
  sanitizeSearchQuery,
  sanitizeRiskFormData,
  sanitizeIncidentFormData,
  sanitizePolicyContent,
  sanitizeUserProfileData,
  sanitizeExternalUrl,
  sanitizeRiskList,
  sanitizeAndValidateApiResponse,
  sanitizeByFieldType,
  sanitizeCsvImportData,
  riskApiSanitizationMiddleware,
  incidentApiSanitizationMiddleware,
} from "../utils/input-sanitization-integration";
// Test 1: User Comments
const comment =
  '<p>This is a <strong>great</strong> risk assessment!</p><script>alert("xss")</script>';
const sanitizedComment = sanitizeUserComment(comment);
// Test 2: Risk Description
const riskDesc =
  '<h2>Risk Overview</h2><p>This risk involves <em>potential</em> data breach.</p><script>alert("xss")</script>';
const sanitizedRiskDesc = sanitizeRiskDescription(riskDesc);
// Test 3: Search Query
const searchQuery = '<script>alert("xss")</script>security risk';
const sanitizedQuery = sanitizeSearchQuery(searchQuery);
// Test 4: Risk Form Data
const riskFormData = {
  title: 'Data Breach Risk<script>alert("xss")</script>',
  description: "<p>Potential <strong>high impact</strong> risk</p>",
  mitigation_actions: "<ul><li>Implement encryption</li></ul>",
  owner_email: '<EMAIL><script>alert("xss")</script>',
};
const riskResults = sanitizeRiskFormData(riskFormData);
Object.entries(riskResults).forEach(([key, result]) => {
  const safe = result.isValid && !result.sanitizedValue.includes("script");
});
// Test 5: External URL Sanitization
const urls = [
  "http://example.com/page",
  "https://example.com/page",
  "/internal/page",
  "javascript:alert(1)",
];
urls.forEach(url => {
  const sanitized = sanitizeExternalUrl(url);
  const safe = !sanitized.includes("javascript:");
});
// Test 6: Field Type Sanitization
const fieldTests = [
  { value: '<EMAIL><script>alert("xss")</script>', type: "email" },
  { value: '+****************<script>alert("xss")</script>', type: "phone" },
  {
    value: '<p>Rich <strong>text</strong> content</p><script>alert("xss")</script>',
    type: "rich_text",
  },
  { value: '<p>Some text</p><script>alert("xss")</script>', type: "plain_text" },
];
fieldTests.forEach(({ value, type }) => {
  const sanitized = sanitizeByFieldType(value, type);
  const safe = !sanitized.includes("script");
});
// Test 7: API Response Validation
const apiData = {
  name: 'John Doe<script>alert("xss")</script>',
  email: "<EMAIL>",
  bio: "<p>Software developer with <strong>10 years</strong> experience</p>",
};
const apiResult = sanitizeAndValidateApiResponse(apiData);
Object.entries(apiResult.sanitizedData).forEach(([key, value]) => {
  const safe = typeof value === "string" && !value.includes("script");
});
// Test 8: CSV Import Data
const csvData = [
  {
    name: 'John Doe<script>alert("xss")</script>',
    email: "<EMAIL>",
    department: 'IT<img src="x" onerror="alert(1)">',
  },
  {
    name: "Jane Smith",
    email: '<EMAIL><script>alert("xss")</script>',
    department: "HR",
  },
];
const csvResults = sanitizeCsvImportData(csvData);
csvResults.forEach((row, index) => {
  Object.entries(row.data).forEach(([key, value]) => {
    const safe = !value.includes("script") && !value.includes("onerror");
  });
});
// Test 9: API Middleware
const middlewareData = {
  title: 'Security Risk<script>alert("xss")</script>',
  description: "<p>Risk description with <strong>formatting</strong></p>",
  notes: "<p>Additional notes</p>",
};
const middlewareResult = riskApiSanitizationMiddleware(middlewareData);
Object.entries(middlewareResult.sanitizedData).forEach(([key, value]) => {
  const safe = typeof value === "string" && !value.includes("script");
});
