/**
 * Error Monitoring Service Tests
 * Comprehensive tests for error tracking, categorization, and alerting
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ErrorMonitoringService } from '@/services/errorMonitoringService';
import { SystemError, ValidationError, NetworkError, AuthorizationError } from '@/utils/errors';
import { ErrorSeverity, ErrorCategory, ErrorContext } from '@/types';

describe('ErrorMonitoringService', () => {
  let errorMonitoringService: ErrorMonitoringService;
  let mockContext: ErrorContext;

  beforeEach(() => {
    // Reset singleton instance for each test
    (ErrorMonitoringService as any).instance = null;
    
    errorMonitoringService = ErrorMonitoringService.getInstance({
      enabled: true,
      trackingWindowMinutes: 60,
      alertThresholds: [
        {
          category: 'total',
          maxErrorsPerMinute: 5,
          maxErrorsPerHour: 50,
          alertOnCritical: true
        },
        {
          category: ErrorCategory.SYSTEM,
          maxErrorsPerMinute: 3,
          maxErrorsPerHour: 30,
          alertOnCritical: true
        }
      ],
      enablePatternDetection: true,
      enableRealTimeAlerts: true,
      maxStoredErrors: 100,
      alertCooldownMinutes: 1
    });

    mockContext = {
      userId: 'test-user-123',
      organizationId: 'test-org-456',
      component: 'test-component',
      action: 'test-action',
      timestamp: new Date()
    };

    // Mock console methods to avoid noise in tests
    vi.spyOn(console, 'error').mockImplementation(() => {});
    vi.spyOn(console, 'warn').mockImplementation(() => {});
    vi.spyOn(console, 'info').mockImplementation(() => {});
  });

  afterEach(() => {
    errorMonitoringService.destroy();
    vi.restoreAllMocks();
  });

  describe('Error Tracking', () => {
    it('should track errors and update metrics', () => {
      const error = new SystemError(
        'Test system error',
        'test-component',
        'TEST_ERROR',
        mockContext,
        ErrorSeverity.HIGH
      );

      errorMonitoringService.trackError(error);

      const metrics = errorMonitoringService.getMetrics();
      expect(metrics.totalErrors).toBe(1);
      expect(metrics.errorsByCategory[ErrorCategory.SYSTEM]).toBe(1);
      expect(metrics.errorsBySeverity[ErrorSeverity.HIGH]).toBe(1);
      expect(metrics.errorsByComponent['test-component']).toBe(1);
    });

    it('should track multiple errors and maintain accurate counts', () => {
      const systemError = new SystemError(
        'System error',
        'sys-component',
        'SYS_ERROR',
        { ...mockContext, component: 'component-a' },
        ErrorSeverity.HIGH
      );

      const validationError = new ValidationError(
        'Validation failed',
        'email',
        'VALIDATION_ERROR',
        { ...mockContext, component: 'component-b' }
      );

      const networkError = new NetworkError(
        'Network timeout',
        500,
        '/api/data',
        'GET',
        { ...mockContext, component: 'component-a' }
      );

      errorMonitoringService.trackError(systemError);
      errorMonitoringService.trackError(validationError);
      errorMonitoringService.trackError(networkError);

      const metrics = errorMonitoringService.getMetrics();
      expect(metrics.totalErrors).toBe(3);
      expect(metrics.errorsByCategory[ErrorCategory.SYSTEM]).toBe(1);
      expect(metrics.errorsByCategory[ErrorCategory.VALIDATION]).toBe(1);
      expect(metrics.errorsByCategory[ErrorCategory.NETWORK]).toBe(1);
      expect(metrics.errorsByComponent['component-a']).toBe(2);
      expect(metrics.errorsByComponent['component-b']).toBe(1);
    });

    it('should track critical errors separately', () => {
      const criticalError = new SystemError(
        'Critical system failure',
        'core-system',
        'CRITICAL_ERROR',
        mockContext,
        ErrorSeverity.CRITICAL
      );

      const regularError = new ValidationError(
        'Validation error',
        'email',
        'VALIDATION_ERROR',
        { ...mockContext, component: 'form-component' }
      );

      errorMonitoringService.trackError(criticalError);
      errorMonitoringService.trackError(regularError);

      const metrics = errorMonitoringService.getMetrics();
      expect(metrics.criticalErrors).toBe(1);
      expect(metrics.totalErrors).toBe(2);
    });

    it('should track recoverable errors', () => {
      const recoverableError = new NetworkError(
        'Network timeout',
        500,
        'api-client',
        'GET',
        mockContext
      );

      const nonRecoverableError = new AuthorizationError(
        'Access denied',
        'admin_access',
        'user',
        mockContext
      );

      errorMonitoringService.trackError(recoverableError);
      errorMonitoringService.trackError(nonRecoverableError);

      const metrics = errorMonitoringService.getMetrics();
      expect(metrics.recoverableErrors).toBe(1); // Network errors are recoverable, authorization errors are not
      expect(metrics.totalErrors).toBe(2);
    });
  });

  describe('Pattern Detection', () => {
    it('should detect error patterns', () => {
      const baseError = {
        message: 'Database connection failed',
        component: 'database-client',
        code: 'DB_CONNECTION_ERROR',
        context: mockContext,
        severity: ErrorSeverity.HIGH
      };

      // Create multiple similar errors
      for (let i = 0; i < 3; i++) {
        const error = new SystemError(
          baseError.message,
          baseError.component,
          baseError.code,
          { ...baseError.context, component: baseError.component },
          baseError.severity
        );
        errorMonitoringService.trackError(error);
      }

      const patterns = errorMonitoringService.getErrorPatterns();
      expect(patterns.length).toBeGreaterThan(0);
      
      const dbPattern = patterns.find(p => 
        p.pattern.includes('system') && 
        p.pattern.includes('database-client')
      );
      expect(dbPattern).toBeDefined();
      expect(dbPattern!.count).toBe(3);
    });

    it('should group patterns by component and error type', () => {
      // Create errors with same pattern
      for (let i = 0; i < 2; i++) {
        const error = new ValidationError(
          'Invalid email format',
          'email',
          'INVALID_EMAIL',
          { ...mockContext, component: 'email-validator' }
        );
        errorMonitoringService.trackError(error);
      }

      // Create errors with different pattern
      for (let i = 0; i < 3; i++) {
        const error = new NetworkError(
          'Request timeout',
          408,
          '/api/data',
          'GET',
          { ...mockContext, component: 'api-client' }
        );
        errorMonitoringService.trackError(error);
      }

      const patterns = errorMonitoringService.getErrorPatterns();
      expect(patterns.length).toBe(2);
      
      const validationPattern = patterns.find(p => p.pattern.includes('validation'));
      const networkPattern = patterns.find(p => p.pattern.includes('network'));
      
      expect(validationPattern?.count).toBe(2);
      expect(networkPattern?.count).toBe(3);
    });
  });

  describe('Alerting System', () => {
    it('should create threshold alerts when error rate exceeds limits', async () => {
      // Create errors rapidly to exceed threshold
      for (let i = 0; i < 6; i++) {
        const error = new SystemError(
          `Error ${i}`,
          'test-component',
          'TEST_ERROR',
          mockContext,
          ErrorSeverity.MEDIUM
        );
        errorMonitoringService.trackError(error);
      }

      const alerts = errorMonitoringService.getAlerts();
      const thresholdAlert = alerts.find(alert => alert.type === 'threshold');
      
      expect(thresholdAlert).toBeDefined();
      expect(thresholdAlert!.message).toContain('threshold exceeded');
    });

    it('should create critical error alerts', () => {
      const criticalError = new SystemError(
        'Critical system failure',
        'core-system',
        'CRITICAL_FAILURE',
        mockContext,
        ErrorSeverity.CRITICAL
      );

      errorMonitoringService.trackError(criticalError);

      const alerts = errorMonitoringService.getAlerts();
      const criticalAlert = alerts.find(alert => alert.type === 'critical');
      
      expect(criticalAlert).toBeDefined();
      expect(criticalAlert!.severity).toBe(ErrorSeverity.CRITICAL);
      expect(criticalAlert!.message).toContain('Critical error occurred');
    });

    it('should create pattern alerts for recurring errors', () => {
      // Create multiple similar errors to trigger pattern alert
      for (let i = 0; i < 5; i++) {
        const error = new SystemError(
          'Database connection failed',
          'database-client',
          'DB_ERROR',
          mockContext,
          ErrorSeverity.HIGH
        );
        errorMonitoringService.trackError(error);
      }

      const alerts = errorMonitoringService.getAlerts();
      const patternAlert = alerts.find(alert => alert.type === 'pattern');
      
      expect(patternAlert).toBeDefined();
      expect(patternAlert!.message).toContain('pattern detected');
    });

    it('should respect alert cooldown periods', async () => {
      // Create errors to trigger first alert
      for (let i = 0; i < 6; i++) {
        const error = new SystemError(
          `Error ${i}`,
          'test-component',
          'TEST_ERROR',
          mockContext,
          ErrorSeverity.MEDIUM
        );
        errorMonitoringService.trackError(error);
      }

      const initialAlerts = errorMonitoringService.getAlerts();
      const thresholdAlerts = initialAlerts.filter(alert => alert.type === 'threshold');

      // Create more errors immediately (should be in cooldown)
      for (let i = 0; i < 6; i++) {
        const error = new SystemError(
          `Error ${i + 6}`,
          'test-component',
          'TEST_ERROR',
          mockContext,
          ErrorSeverity.MEDIUM
        );
        errorMonitoringService.trackError(error);
      }

      const afterCooldownAlerts = errorMonitoringService.getAlerts();
      const afterThresholdAlerts = afterCooldownAlerts.filter(alert => alert.type === 'threshold');
      
      // Should not create new threshold alerts due to cooldown
      expect(afterThresholdAlerts.length).toBe(thresholdAlerts.length);
    });

    it('should allow resolving alerts', () => {
      const criticalError = new SystemError(
        'Critical error',
        'test-component',
        'CRITICAL_ERROR',
        mockContext,
        ErrorSeverity.CRITICAL
      );

      errorMonitoringService.trackError(criticalError);

      const alerts = errorMonitoringService.getAlerts();
      const alert = alerts[0];
      
      expect(alert.resolved).toBe(false);
      
      const resolved = errorMonitoringService.resolveAlert(alert.id);
      expect(resolved).toBe(true);
      
      const updatedAlerts = errorMonitoringService.getAlerts();
      const updatedAlert = updatedAlerts.find(a => a.id === alert.id);
      expect(updatedAlert?.resolved).toBe(true);
    });
  });

  describe('Error History and Cleanup', () => {
    it('should maintain error history within size limits', () => {
      const maxErrors = 5;
      errorMonitoringService.updateConfig({ maxStoredErrors: maxErrors });

      // Create more errors than the limit
      for (let i = 0; i < maxErrors + 3; i++) {
        const error = new SystemError(
          `Error ${i}`,
          'test-component',
          'TEST_ERROR',
          mockContext,
          ErrorSeverity.MEDIUM
        );
        errorMonitoringService.trackError(error);
      }

      const history = errorMonitoringService.getErrorHistory();
      expect(history.length).toBe(maxErrors);
    });

    it('should provide error history filtering by time', () => {
      const now = new Date();
      
      // Create an old error (simulate by manipulating the history)
      const oldError = new SystemError(
        'Old error',
        'test-component',
        'OLD_ERROR',
        mockContext,
        ErrorSeverity.MEDIUM
      );
      
      const recentError = new SystemError(
        'Recent error',
        'test-component',
        'RECENT_ERROR',
        mockContext,
        ErrorSeverity.MEDIUM
      );

      errorMonitoringService.trackError(oldError);
      errorMonitoringService.trackError(recentError);

      const recentHistory = errorMonitoringService.getErrorHistory(1); // Last 1 minute
      expect(recentHistory.length).toBeGreaterThan(0);
    });
  });

  describe('Configuration Management', () => {
    it('should allow updating configuration', () => {
      const newConfig = {
        maxStoredErrors: 200,
        alertCooldownMinutes: 10,
        enablePatternDetection: false
      };

      errorMonitoringService.updateConfig(newConfig);
      const config = errorMonitoringService.getConfig();

      expect(config.maxStoredErrors).toBe(200);
      expect(config.alertCooldownMinutes).toBe(10);
      expect(config.enablePatternDetection).toBe(false);
    });

    it('should respect disabled state', () => {
      errorMonitoringService.updateConfig({ enabled: false });

      const error = new SystemError(
        'Test error',
        'test-component',
        'TEST_ERROR',
        mockContext,
        ErrorSeverity.MEDIUM
      );

      errorMonitoringService.trackError(error);

      const metrics = errorMonitoringService.getMetrics();
      expect(metrics.totalErrors).toBe(0); // Should not track when disabled
    });
  });

  describe('Integration with Logging', () => {
    it('should log structured error information', () => {
      const consoleSpy = vi.spyOn(console, 'error');
      
      const error = new SystemError(
        'Integration test error',
        'test-component',
        'INTEGRATION_ERROR',
        mockContext,
        ErrorSeverity.HIGH
      );

      errorMonitoringService.trackError(error);

      // Should have logged the error
      expect(consoleSpy).toHaveBeenCalled();
    });
  });

  describe('Metrics Calculation', () => {
    it('should calculate error rates correctly', () => {
      // Create errors over time to test rate calculation
      for (let i = 0; i < 3; i++) {
        const error = new SystemError(
          `Rate test error ${i}`,
          'test-component',
          'RATE_ERROR',
          mockContext,
          ErrorSeverity.MEDIUM
        );
        errorMonitoringService.trackError(error);
      }

      const metrics = errorMonitoringService.getMetrics();
      expect(metrics.errorRate).toBeGreaterThan(0);
      expect(metrics.totalErrors).toBe(3);
    });

    it('should provide comprehensive metrics breakdown', () => {
      // Create diverse set of errors
      const errors = [
        new SystemError('System error', 'sys', 'SYS', { ...mockContext, component: 'sys' }, ErrorSeverity.HIGH),
        new ValidationError('Validation error', 'val', 'VAL', { ...mockContext, component: 'val' }),
        new NetworkError('Network error', 500, '/api/test', 'GET', { ...mockContext, component: 'net' }),
        new SystemError('Critical error', 'sys', 'CRIT', { ...mockContext, component: 'sys' }, ErrorSeverity.CRITICAL)
      ];

      errors.forEach(error => errorMonitoringService.trackError(error));

      const metrics = errorMonitoringService.getMetrics();
      
      expect(metrics.totalErrors).toBe(4);
      expect(metrics.criticalErrors).toBe(1);
      expect(metrics.recoverableErrors).toBe(3); // System (high severity), Validation, and Network errors are recoverable by default
      expect(Object.keys(metrics.errorsByCategory).length).toBeGreaterThan(0);
      expect(Object.keys(metrics.errorsBySeverity).length).toBeGreaterThan(0);
      expect(Object.keys(metrics.errorsByComponent).length).toBeGreaterThan(0);
    });
  });
});