/**
 * Performance Dashboard Test Suite
 * Tests for the performance dashboard component
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { PerformanceDashboard } from '../components/ui/performance-dashboard';

// Mock the memory monitor hook
vi.mock('../utils/memory-usage-monitor', () => ({
  useMemoryMonitor: () => ({
    memoryStats: {
      current: 10 * 1024 * 1024, // 10MB
      peak: 15 * 1024 * 1024, // 15MB
      average: 12 * 1024 * 1024, // 12MB
      budget: 50 * 1024 * 1024, // 50MB
      utilization: 0.2, // 20%
      trend: 'stable' as const,
    },
    startMonitoring: vi.fn(),
    stopMonitoring: vi.fn(),
    takeSnapshot: vi.fn(),
    generateReport: vi.fn(),
    forceGC: vi.fn(),
  }),
  formatMemorySize: (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },
  getMemoryUsageColor: (utilization: number) => {
    if (utilization < 0.5) return 'green';
    if (utilization < 0.75) return 'yellow';
    if (utilization < 0.9) return 'orange';
    return 'red';
  },
}));

// Mock the network condition tester
vi.mock('../utils/network-condition-tester', () => ({
  NetworkConditionTester: vi.fn().mockImplementation(() => ({
    generateNetworkPerformanceReport: () => ({
      summary: {
        totalConditions: 5,
        excellentCount: 4,
        goodCount: 1,
        fairCount: 0,
        poorCount: 0,
        overallScore: 95,
      },
      results: [
        {
          condition: { name: 'WiFi', description: 'WiFi connection (50 Mbps, 10ms RTT)' },
          metrics: {
            loadTime: 140,
            firstContentfulPaint: 20,
            largestContentfulPaint: 40,
            timeToInteractive: 640,
            cumulativeLayoutShift: 0.08,
            totalBlockingTime: 450,
          },
          estimatedUserExperience: 'excellent',
        },
        {
          condition: { name: 'Regular 4G', description: 'Regular 4G connection (10 Mbps, 40ms RTT)' },
          metrics: {
            loadTime: 560,
            firstContentfulPaint: 70,
            largestContentfulPaint: 160,
            timeToInteractive: 1060,
            cumulativeLayoutShift: 0.08,
            totalBlockingTime: 450,
          },
          estimatedUserExperience: 'excellent',
        },
      ],
      recommendations: [
        'Reduce JavaScript execution time',
        'Implement code splitting for non-critical features',
      ],
    }),
  })),
  formatPerformanceMetrics: (metrics: any) => ({
    loadTime: `${(metrics.loadTime / 1000).toFixed(2)}s`,
    firstContentfulPaint: `${(metrics.firstContentfulPaint / 1000).toFixed(2)}s`,
    largestContentfulPaint: `${(metrics.largestContentfulPaint / 1000).toFixed(2)}s`,
    timeToInteractive: `${(metrics.timeToInteractive / 1000).toFixed(2)}s`,
    cumulativeLayoutShift: metrics.cumulativeLayoutShift.toFixed(3),
    totalBlockingTime: `${metrics.totalBlockingTime.toFixed(0)}ms`,
  }),
  getPerformanceGrade: (userExperience: string) => {
    switch (userExperience) {
      case 'excellent':
        return { grade: 'A', color: 'green', emoji: '🟢' };
      case 'good':
        return { grade: 'B', color: 'lightgreen', emoji: '🟡' };
      case 'fair':
        return { grade: 'C', color: 'orange', emoji: '🟠' };
      case 'poor':
        return { grade: 'D', color: 'red', emoji: '🔴' };
      default:
        return { grade: 'F', color: 'darkred', emoji: '❌' };
    }
  },
  NETWORK_CONDITIONS: [
    { name: 'WiFi', rtt: 10, throughput: 50000, description: 'WiFi connection' },
    { name: 'Regular 4G', rtt: 40, throughput: 10000, description: '4G connection' },
  ],
}));

// Mock fetch for performance validation report
const mockValidationReport = {
  timestamp: '2025-07-20T03:40:22.999Z',
  bundleSizeReduction: {
    baseline: { total: 105750 },
    current: 107132,
    reduction: -0.013,
    targetMet: false,
  },
  webVitals: {
    firstContentfulPaint: { target: 1500, results: [] },
    largestContentfulPaint: { target: 2500, results: [] },
    timeToInteractive: { target: 3500, results: [] },
    cumulativeLayoutShift: { target: 0.1, results: [] },
  },
  memoryUsage: {
    estimatedUsage: 214264,
    threshold: 52428800,
    withinThreshold: true,
    leakChecks: [
      {
        name: 'useEffect Cleanup',
        passed: false,
        message: 'Found 0 cleanup functions in 0 useEffect hooks',
      },
      {
        name: 'Event Listener Cleanup',
        passed: false,
        message: 'Found 0 event listener cleanup patterns',
      },
      {
        name: 'Timer Cleanup',
        passed: true,
        message: 'Found 0 timers with 0 cleanup patterns',
      },
    ],
  },
  summary: {
    bundleSizeTargetMet: false,
    fcpTargetMet: true,
    lcpTargetMet: true,
    memoryTargetMet: true,
    overallScore: 75,
  },
};

global.fetch = vi.fn();

describe('Performance Dashboard', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (global.fetch as any).mockResolvedValue({
      ok: true,
      json: async () => mockValidationReport,
    });
  });

  it('should render loading state initially', () => {
    (global.fetch as any).mockResolvedValue({
      ok: false,
    });

    render(<PerformanceDashboard />);
    
    expect(screen.getByText('Loading performance validation data...')).toBeInTheDocument();
  });

  it('should render performance dashboard with validation data', async () => {
    render(<PerformanceDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Performance Dashboard')).toBeInTheDocument();
    });

    // Check overall score
    expect(screen.getByText('75')).toBeInTheDocument();
    expect(screen.getByText('/100')).toBeInTheDocument();
    
    // Check tabs
    expect(screen.getByText('Bundle Analysis')).toBeInTheDocument();
    expect(screen.getByText('Network Performance')).toBeInTheDocument();
    expect(screen.getByText('Memory Usage')).toBeInTheDocument();
    expect(screen.getByText('Web Vitals')).toBeInTheDocument();
  });

  it('should display bundle analysis information', async () => {
    render(<PerformanceDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Performance Dashboard')).toBeInTheDocument();
    });

    // Bundle analysis should be the default tab
    expect(screen.getByText('Current Bundle Size')).toBeInTheDocument();
    expect(screen.getByText('Size Reduction')).toBeInTheDocument();
    expect(screen.getByText('Bundle Status')).toBeInTheDocument();
    
    // Should show FAIL status for bundle size target
    expect(screen.getByText('FAIL')).toBeInTheDocument();
  });

  it('should switch between tabs correctly', async () => {
    render(<PerformanceDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Performance Dashboard')).toBeInTheDocument();
    });

    // Click on Network Performance tab
    fireEvent.click(screen.getByText('Network Performance'));
    
    await waitFor(() => {
      expect(screen.getByText('Network Condition Performance')).toBeInTheDocument();
    });

    // Click on Memory Usage tab
    fireEvent.click(screen.getByText('Memory Usage'));
    
    await waitFor(() => {
      expect(screen.getByText('Memory Leak Prevention')).toBeInTheDocument();
    });

    // Click on Web Vitals tab
    fireEvent.click(screen.getByText('Web Vitals'));
    
    await waitFor(() => {
      expect(screen.getByText('Web Vitals Data Unavailable')).toBeInTheDocument();
    });
  });

  it('should display network performance metrics', async () => {
    render(<PerformanceDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Performance Dashboard')).toBeInTheDocument();
    });

    // Switch to network tab
    fireEvent.click(screen.getByText('Network Performance'));
    
    await waitFor(() => {
      expect(screen.getByText('Network Condition Performance')).toBeInTheDocument();
    });

    // Should show network condition results
    expect(screen.getByText('WiFi')).toBeInTheDocument();
    expect(screen.getByText('Regular 4G')).toBeInTheDocument();
    
    // Should show recommendations
    expect(screen.getByText('Recommendations')).toBeInTheDocument();
    expect(screen.getByText('Reduce JavaScript execution time')).toBeInTheDocument();
  });

  it('should display memory usage information', async () => {
    render(<PerformanceDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Performance Dashboard')).toBeInTheDocument();
    });

    // Switch to memory tab
    fireEvent.click(screen.getByText('Memory Usage'));
    
    await waitFor(() => {
      expect(screen.getByText('Current Usage')).toBeInTheDocument();
    });

    // Should show memory metrics
    expect(screen.getByText('Utilization')).toBeInTheDocument();
    expect(screen.getByText('Trend')).toBeInTheDocument();
    expect(screen.getByText('20.0%')).toBeInTheDocument(); // Utilization percentage
    
    // Should show memory leak checks
    expect(screen.getByText('Memory Leak Prevention')).toBeInTheDocument();
    expect(screen.getByText('useEffect Cleanup')).toBeInTheDocument();
    expect(screen.getByText('Event Listener Cleanup')).toBeInTheDocument();
    expect(screen.getByText('Timer Cleanup')).toBeInTheDocument();
  });

  it('should display web vitals status', async () => {
    render(<PerformanceDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Performance Dashboard')).toBeInTheDocument();
    });

    // Switch to web vitals tab
    fireEvent.click(screen.getByText('Web Vitals'));
    
    await waitFor(() => {
      expect(screen.getByText('FCP')).toBeInTheDocument();
    });

    // Should show web vitals metrics
    expect(screen.getByText('LCP')).toBeInTheDocument();
    expect(screen.getByText('TTI')).toBeInTheDocument();
    expect(screen.getByText('CLS')).toBeInTheDocument();
    
    // Should show PASS/FAIL status
    const passElements = screen.getAllByText('PASS');
    expect(passElements.length).toBeGreaterThan(0);
    
    // Should show unavailable data warning
    expect(screen.getByText('Web Vitals Data Unavailable')).toBeInTheDocument();
  });

  it('should handle refresh button click', async () => {
    render(<PerformanceDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Performance Dashboard')).toBeInTheDocument();
    });

    const refreshButton = screen.getByText('Refresh Data');
    expect(refreshButton).toBeInTheDocument();
    
    fireEvent.click(refreshButton);
    
    // Should call fetch again
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(2); // Initial load + refresh
    });
  });

  it('should display appropriate score colors and badges', async () => {
    render(<PerformanceDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Performance Dashboard')).toBeInTheDocument();
    });

    // Should show score with appropriate styling
    const scoreElement = screen.getByText('75');
    expect(scoreElement).toBeInTheDocument();
    
    // Should show appropriate badge for score
    expect(screen.getByText('Good')).toBeInTheDocument();
  });

  it('should show bundle size optimization warning', async () => {
    render(<PerformanceDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Performance Dashboard')).toBeInTheDocument();
    });

    // Should show warning for bundle size not meeting target
    expect(screen.getByText('Bundle Size Optimization Needed')).toBeInTheDocument();
    expect(screen.getByText(/does not meet the 30% reduction target/)).toBeInTheDocument();
  });

  it('should handle memory monitoring actions', async () => {
    const mockTakeSnapshot = vi.fn();
    const mockForceGC = vi.fn();
    
    // Re-mock with our spy functions
    vi.doMock('../utils/memory-usage-monitor', () => ({
      useMemoryMonitor: () => ({
        memoryStats: {
          current: 10 * 1024 * 1024,
          peak: 15 * 1024 * 1024,
          average: 12 * 1024 * 1024,
          budget: 50 * 1024 * 1024,
          utilization: 0.2,
          trend: 'stable' as const,
        },
        startMonitoring: vi.fn(),
        stopMonitoring: vi.fn(),
        takeSnapshot: mockTakeSnapshot,
        generateReport: vi.fn(),
        forceGC: mockForceGC,
      }),
      formatMemorySize: (bytes: number) => `${(bytes / 1024 / 1024).toFixed(1)} MB`,
      getMemoryUsageColor: () => 'green',
    }));

    render(<PerformanceDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Performance Dashboard')).toBeInTheDocument();
    });

    // Switch to memory tab
    fireEvent.click(screen.getByText('Memory Usage'));
    
    await waitFor(() => {
      expect(screen.getByText('Take Snapshot')).toBeInTheDocument();
    });

    // Click memory monitoring buttons
    fireEvent.click(screen.getByText('Take Snapshot'));
    fireEvent.click(screen.getByText('Force GC'));
    
    expect(mockTakeSnapshot).toHaveBeenCalled();
    expect(mockForceGC).toHaveBeenCalled();
  });
});