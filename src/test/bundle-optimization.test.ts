/**
 * Bundle optimization tests
 * Validates that bundle size optimization features are working correctly
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { 
  monitorBundleOptimization,
  trackBundleSizeChanges,
  validateBundleOptimization
} from '@/utils/bundle-analyzer';

// Mock performance API
const mockPerformanceEntries = [
  {
    name: 'https://example.com/assets/vendor-react-abc123.js',
    transferSize: 400 * 1024, // 400KB
    entryType: 'resource'
  },
  {
    name: 'https://example.com/assets/vendor-ui-def456.js',
    transferSize: 300 * 1024, // 300KB
    entryType: 'resource'
  },
  {
    name: 'https://example.com/assets/main-ghi789.js',
    transferSize: 200 * 1024, // 200KB
    entryType: 'resource'
  },
  {
    name: 'https://example.com/assets/chunk-jkl012.js',
    transferSize: 150 * 1024, // 150KB
    entryType: 'resource'
  },
  {
    name: 'https://example.com/assets/chunk-mno345.js',
    transferSize: 100 * 1024, // 100KB
    entryType: 'resource'
  }
];

describe('Bundle Optimization', () => {
  let originalPerformance: Performance;
  let consoleSpy: ReturnType<typeof vi.spyOn>;

  beforeEach(() => {
    // Mock performance API
    originalPerformance = global.performance;
    global.performance = {
      ...originalPerformance,
      getEntriesByType: vi.fn().mockReturnValue(mockPerformanceEntries)
    } as any;

    // Mock console methods
    consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});
    vi.spyOn(console, 'group').mockImplementation(() => {});
    vi.spyOn(console, 'groupEnd').mockImplementation(() => {});

    // Mock import.meta.env
    vi.stubGlobal('import', {
      meta: {
        env: {
          MODE: 'development'
        }
      }
    });
  });

  afterEach(() => {
    global.performance = originalPerformance;
    vi.restoreAllMocks();
    vi.unstubAllGlobals();
  });

  describe('monitorBundleOptimization', () => {
    it('should analyze bundle composition correctly', () => {
      const result = monitorBundleOptimization();

      expect(result).toBeDefined();
      expect(result?.totalSize).toBe(1150 * 1024); // Sum of all chunks
      expect(result?.chunkCount).toBe(5);
      expect(result?.vendorChunks).toHaveLength(2);
      expect(result?.dynamicChunks).toHaveLength(2);
    });

    it('should identify large chunks', () => {
      const result = monitorBundleOptimization();

      expect(result?.largeChunks).toHaveLength(0); // No chunks over 500KB in mock data
    });

    it('should log optimization analysis', () => {
      monitorBundleOptimization();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Total JS Size:')
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Chunk Count: 5')
      );
    });
  });

  describe('validateBundleOptimization', () => {
    it('should validate optimization goals', () => {
      const result = validateBundleOptimization();

      expect(result).toBeDefined();
      expect(result?.goals.totalSizeUnder2MB).toBe(true);
      expect(result?.goals.hasVendorSeparation).toBe(true);
      expect(result?.goals.hasCodeSplitting).toBe(true);
      expect(result?.goals.multipleChunks).toBe(true);
    });

    it('should calculate optimization score', () => {
      const result = validateBundleOptimization();

      expect(result?.score).toBeGreaterThan(0);
      expect(result?.score).toBeLessThanOrEqual(1);
    });

    it('should log optimization goals', () => {
      validateBundleOptimization();

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Total size under 2MB: PASS')
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Vendor separation: PASS')
      );
    });
  });

  describe('trackBundleSizeChanges', () => {
    let mockLocalStorage: { [key: string]: string };

    beforeEach(() => {
      mockLocalStorage = {};
      
      // Mock localStorage
      Object.defineProperty(window, 'localStorage', {
        value: {
          getItem: vi.fn((key: string) => mockLocalStorage[key] || null),
          setItem: vi.fn((key: string, value: string) => {
            mockLocalStorage[key] = value;
          }),
          removeItem: vi.fn((key: string) => {
            delete mockLocalStorage[key];
          }),
          clear: vi.fn(() => {
            mockLocalStorage = {};
          })
        },
        writable: true
      });
    });

    it('should track bundle size changes', () => {
      trackBundleSizeChanges();

      expect(localStorage.setItem).toHaveBeenCalledWith(
        'bundle-size-history',
        expect.stringContaining('"size":')
      );
    });

    it('should detect size changes', () => {
      // Set initial history
      const initialHistory = [
        {
          timestamp: '2023-01-01T00:00:00.000Z',
          size: 1000 * 1024 // 1MB
        }
      ];
      mockLocalStorage['bundle-size-history'] = JSON.stringify(initialHistory);

      trackBundleSizeChanges();

      // Should log size change since current size (1150KB) > previous size (1000KB)
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Bundle size change:')
      );
    });

    it('should limit history entries', () => {
      // Create history with 15 entries (more than the 10 limit)
      const largeHistory = Array.from({ length: 15 }, (_, i) => ({
        timestamp: `2023-01-${String(i + 1).padStart(2, '0')}T00:00:00.000Z`,
        size: 1000 * 1024
      }));
      mockLocalStorage['bundle-size-history'] = JSON.stringify(largeHistory);

      trackBundleSizeChanges();

      const savedHistory = JSON.parse(mockLocalStorage['bundle-size-history']);
      expect(savedHistory).toHaveLength(10); // Should be limited to 10 entries
    });
  });

  describe('Dynamic Import Utilities', () => {
    it('should provide dynamic import functions', async () => {
      const { loadChartComponents, loadPDFLibrary, loadExcelLibrary } = await import('@/utils/dynamic-imports');

      expect(typeof loadChartComponents).toBe('function');
      expect(typeof loadPDFLibrary).toBe('function');
      expect(typeof loadExcelLibrary).toBe('function');
    });

    it('should create smart loaders', async () => {
      const { createSmartLoader } = await import('@/utils/dynamic-imports');
      
      const mockImport = vi.fn().mockResolvedValue({ test: 'data' });
      const loader = createSmartLoader(mockImport, { preload: false });

      expect(typeof loader).toBe('function');
      
      const result = await loader();
      expect(result).toEqual({ test: 'data' });
      expect(mockImport).toHaveBeenCalledTimes(1);
    });
  });

  describe('Lazy Chart Wrapper', () => {
    it('should create lazy chart components', async () => {
      const { LazyChartWrapper } = await import('@/components/charts/LazyChartWrapper');
      
      expect(LazyChartWrapper).toBeDefined();
      expect(typeof LazyChartWrapper).toBe('function');
    });
  });
});

describe('Bundle Size Monitoring Script', () => {
  it('should have bundle monitoring script file', async () => {
    // This test ensures the script file exists
    const fs = await import('fs');
    expect(fs.existsSync('scripts/bundle-size-monitor.js')).toBe(true);
  });

  it('should have bundle budget configuration', () => {
    // This test ensures the budget file exists
    expect(() => require('../../bundle-budget.json')).not.toThrow();
  });
});

describe('Vite Configuration', () => {
  it('should have optimized rollup configuration', async () => {
    // Read the vite config to ensure it has the optimization settings
    const fs = await import('fs');
    const viteConfig = fs.readFileSync('vite.config.ts', 'utf8');
    
    expect(viteConfig).toContain('manualChunks');
    expect(viteConfig).toContain('vendor-react');
    expect(viteConfig).toContain('vendor-ui');
    expect(viteConfig).toContain('vendor-charts');
    expect(viteConfig).toContain('treeshake');
  });

  it('should have proper chunk naming configuration', async () => {
    const fs = await import('fs');
    const viteConfig = fs.readFileSync('vite.config.ts', 'utf8');
    
    expect(viteConfig).toContain('chunkFileNames');
    expect(viteConfig).toContain('entryFileNames');
    expect(viteConfig).toContain('assetFileNames');
  });
});