/**
 * Performance Validation Test Suite
 * Tests for bundle size reduction, web vitals, memory usage, and network conditions
 */

import { describe, it, expect, beforeAll, afterAll } from "vitest";
import { execSync } from "child_process";
import fs from "fs";
import path from "path";

// Performance targets from requirements
const PERFORMANCE_TARGETS = {
  bundleSizeReduction: 0.3, // 30% reduction target
  firstContentfulPaint: 1500, // 1.5 seconds
  largestContentfulPaint: 2500, // 2.5 seconds
  timeToInteractive: 3500, // 3.5 seconds
  cumulativeLayoutShift: 0.1, // CLS score
  memoryUsageThreshold: 50 * 1024 * 1024, // 50MB
};

// Baseline bundle sizes (before optimization)
const BASELINE_BUNDLE_SIZES = {
  totalJS: 2.5 * 1024 * 1024, // 2.5MB baseline
  totalCSS: 300 * 1024, // 300KB baseline
  total: 2.8 * 1024 * 1024, // 2.8MB total baseline
};

interface BundleMetrics {
  jsFiles: Array<{ name: string; size: number; path: string }>;
  cssFiles: Array<{ name: string; size: number; path: string }>;
  totalJSSize: number;
  totalCSSSize: number;
  totalBundleSize: number;
  largestChunk: number;
}

interface PerformanceResult {
  networkCondition: string;
  firstContentfulPaint: number | null;
  largestContentfulPaint: number | null;
  cumulativeLayoutShift: number | null;
  timeToInteractive: number | null;
  performanceScore: number | null;
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return "0 Bytes";
  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
}

function formatTime(ms: number): string {
  if (ms < 1000) return `${ms.toFixed(0)}ms`;
  return `${(ms / 1000).toFixed(2)}s`;
}

function getCurrentBundleMetrics(): BundleMetrics {
  const distPath = "dist";
  const assetsPath = path.join(distPath, "assets");

  if (!fs.existsSync(distPath)) {
    throw new Error('Build output not found. Run "npm run build" first.');
  }

  if (!fs.existsSync(assetsPath)) {
    throw new Error("Assets directory not found in build output.");
  }

  const files = fs.readdirSync(assetsPath, { recursive: true });
  const jsFiles: Array<{ name: string; size: number; path: string }> = [];
  const cssFiles: Array<{ name: string; size: number; path: string }> = [];

  files.forEach(file => {
    if (typeof file !== "string") return;

    const filePath = path.join(assetsPath, file);
    const stats = fs.statSync(filePath);

    if (stats.isFile()) {
      const size = stats.size;
      const fileInfo = { name: file, size, path: filePath };

      if (file.endsWith(".js")) {
        jsFiles.push(fileInfo);
      } else if (file.endsWith(".css")) {
        cssFiles.push(fileInfo);
      }
    }
  });

  const totalJSSize = jsFiles.reduce((sum, file) => sum + file.size, 0);
  const totalCSSSize = cssFiles.reduce((sum, file) => sum + file.size, 0);
  const totalBundleSize = totalJSSize + totalCSSSize;

  return {
    jsFiles: jsFiles.sort((a, b) => b.size - a.size),
    cssFiles: cssFiles.sort((a, b) => b.size - a.size),
    totalJSSize,
    totalCSSSize,
    totalBundleSize,
    largestChunk: jsFiles[0]?.size ?? 0,
  };
}

function loadBundleHistory(): any[] {
  try {
    if (fs.existsSync("bundle-size-history.json")) {
      return JSON.parse(fs.readFileSync("bundle-size-history.json", "utf8"));
    }
  } catch (error) {
    console.warn("Could not load bundle history");
  }
  return [];
}

describe("Performance Validation and Benchmarking", () => {
  let bundleMetrics: BundleMetrics;
  let bundleHistory: any[];

  beforeAll(async () => {
    // Ensure we have a fresh build
    try {
      console.log("Building application for performance testing...");
      execSync("npm run build", { stdio: "pipe" });
    } catch (error) {
      console.error("Build failed:", error);
      throw error;
    }

    bundleMetrics = getCurrentBundleMetrics();
    bundleHistory = loadBundleHistory();
  });

  describe("Bundle Size Reduction Validation", () => {
    it("should achieve 30% bundle size reduction target", () => {
      // Get baseline from bundle history or use default
      let baseline = BASELINE_BUNDLE_SIZES;

      if (bundleHistory.length > 0) {
        const firstEntry = bundleHistory[0];
        baseline = {
          totalJS: firstEntry.totalJS || BASELINE_BUNDLE_SIZES.totalJS,
          totalCSS: firstEntry.totalCSS || BASELINE_BUNDLE_SIZES.totalCSS,
          total: firstEntry.total || BASELINE_BUNDLE_SIZES.total,
        };
      }

      const totalReduction = (baseline.total - bundleMetrics.totalBundleSize) / baseline.total;
      const targetReduction = PERFORMANCE_TARGETS.bundleSizeReduction;

      console.log(`Baseline Bundle Size: ${formatBytes(baseline.total)}`);
      console.log(`Current Bundle Size: ${formatBytes(bundleMetrics.totalBundleSize)}`);
      console.log(
        `Total Reduction: ${formatBytes(baseline.total - bundleMetrics.totalBundleSize)} (${(totalReduction * 100).toFixed(1)}%)`
      );

      expect(totalReduction).toBeGreaterThanOrEqual(targetReduction);
    });

    it("should have reasonable chunk sizes", () => {
      const maxChunkSize = 1 * 1024 * 1024; // 1MB

      bundleMetrics.jsFiles.forEach(file => {
        expect(file.size).toBeLessThanOrEqual(maxChunkSize);
      });

      expect(bundleMetrics.largestChunk).toBeLessThanOrEqual(maxChunkSize);
    });

    it("should have optimized CSS bundle size", () => {
      const maxCSSSize = 500 * 1024; // 500KB
      expect(bundleMetrics.totalCSSSize).toBeLessThanOrEqual(maxCSSSize);
    });

    it("should have proper code splitting", () => {
      // Should have multiple JS chunks for proper code splitting
      expect(bundleMetrics.jsFiles.length).toBeGreaterThan(1);

      // No single chunk should dominate the bundle
      const largestChunkRatio = bundleMetrics.largestChunk / bundleMetrics.totalJSSize;
      expect(largestChunkRatio).toBeLessThan(0.8); // Less than 80% of total JS
    });
  });

  describe("Bundle Composition Analysis", () => {
    it("should have reasonable JavaScript to CSS ratio", () => {
      const jsRatio = bundleMetrics.totalJSSize / bundleMetrics.totalBundleSize;
      const cssRatio = bundleMetrics.totalCSSSize / bundleMetrics.totalBundleSize;

      // JS should be majority but not overwhelming
      expect(jsRatio).toBeGreaterThan(0.3);
      expect(jsRatio).toBeLessThan(0.95);

      // CSS should be reasonable portion
      expect(cssRatio).toBeGreaterThan(0.05);
      expect(cssRatio).toBeLessThan(0.7);
    });

    it("should have vendor chunks separated", () => {
      const vendorChunks = bundleMetrics.jsFiles.filter(
        file => file.name.includes("vendor") || file.name.includes("chunk")
      );

      // Should have at least one vendor/chunk file
      expect(vendorChunks.length).toBeGreaterThan(0);
    });

    it("should not have excessively large individual files", () => {
      const maxIndividualFileSize = 2 * 1024 * 1024; // 2MB

      [...bundleMetrics.jsFiles, ...bundleMetrics.cssFiles].forEach(file => {
        expect(file.size).toBeLessThanOrEqual(maxIndividualFileSize);
      });
    });
  });

  describe("Memory Usage Validation", () => {
    it("should have estimated memory usage within threshold", () => {
      // Estimate memory usage based on bundle size
      const estimatedMemoryUsage = bundleMetrics.totalBundleSize * 2; // Rough estimate

      console.log(`Estimated Memory Usage: ${formatBytes(estimatedMemoryUsage)}`);
      console.log(`Memory Threshold: ${formatBytes(PERFORMANCE_TARGETS.memoryUsageThreshold)}`);

      expect(estimatedMemoryUsage).toBeLessThanOrEqual(PERFORMANCE_TARGETS.memoryUsageThreshold);
    });

    it("should have proper cleanup patterns in codebase", () => {
      // Check for useEffect cleanup patterns
      const srcFiles = getAllSourceFiles();
      let totalEffects = 0;
      let cleanupCount = 0;

      srcFiles.forEach(file => {
        const content = fs.readFileSync(file, "utf8");
        const useEffectMatches = content.match(/useEffect\s*\(/g) || [];
        totalEffects += useEffectMatches.length;

        // Look for return statements in useEffect (cleanup functions)
        const cleanupMatches = content.match(/useEffect[^}]*return\s*\([^}]*\)\s*=>/g) || [];
        cleanupCount += cleanupMatches.length;
      });

      console.log(`Found ${cleanupCount} cleanup functions in ${totalEffects} useEffect hooks`);

      // Should have some cleanup functions if there are effects
      if (totalEffects > 0) {
        expect(cleanupCount).toBeGreaterThan(0);
      }
    });

    it("should have event listener cleanup patterns", () => {
      const srcFiles = getAllSourceFiles();
      let addEventListenerCount = 0;
      let removeEventListenerCount = 0;

      srcFiles.forEach(file => {
        const content = fs.readFileSync(file, "utf8");
        addEventListenerCount += (content.match(/addEventListener/g) || []).length;
        removeEventListenerCount += (content.match(/removeEventListener/g) || []).length;
      });

      console.log(
        `Found ${addEventListenerCount} addEventListener calls and ${removeEventListenerCount} removeEventListener calls`
      );

      // Should have cleanup for event listeners if they exist
      if (addEventListenerCount > 0) {
        expect(removeEventListenerCount).toBeGreaterThan(0);
      }
    });

    it("should have timer cleanup patterns", () => {
      const srcFiles = getAllSourceFiles();
      let timerCount = 0;
      let cleanupCount = 0;

      srcFiles.forEach(file => {
        const content = fs.readFileSync(file, "utf8");
        timerCount += (content.match(/(setTimeout|setInterval)/g) || []).length;
        cleanupCount += (content.match(/(clearTimeout|clearInterval)/g) || []).length;
      });

      console.log(`Found ${timerCount} timers with ${cleanupCount} cleanup calls`);

      // Should have cleanup for timers if they exist
      if (timerCount > 0) {
        expect(cleanupCount).toBeGreaterThan(0);
      }
    });
  });

  describe("Performance Budget Compliance", () => {
    it("should meet total bundle size budget", () => {
      const totalBudget = 2 * 1024 * 1024; // 2MB
      expect(bundleMetrics.totalBundleSize).toBeLessThanOrEqual(totalBudget);
    });

    it("should meet JavaScript bundle size budget", () => {
      const jsBudget = 1.5 * 1024 * 1024; // 1.5MB
      expect(bundleMetrics.totalJSSize).toBeLessThanOrEqual(jsBudget);
    });

    it("should meet CSS bundle size budget", () => {
      const cssBudget = 500 * 1024; // 500KB
      expect(bundleMetrics.totalCSSSize).toBeLessThanOrEqual(cssBudget);
    });

    it("should meet largest chunk size budget", () => {
      const chunkBudget = 1 * 1024 * 1024; // 1MB
      expect(bundleMetrics.largestChunk).toBeLessThanOrEqual(chunkBudget);
    });
  });

  describe("Bundle Optimization Verification", () => {
    it("should have minified JavaScript files", () => {
      bundleMetrics.jsFiles.forEach(file => {
        // Minified files should have hash in name and be reasonably compressed
        expect(file.name).toMatch(/\.[a-f0-9]{8,}\./);
      });
    });

    it("should have minified CSS files", () => {
      bundleMetrics.cssFiles.forEach(file => {
        // Minified files should have hash in name
        expect(file.name).toMatch(/\.[a-f0-9]{8,}\./);
      });
    });

    it("should have reasonable compression ratios", () => {
      // Check if files appear to be compressed (rough heuristic)
      const avgJSFileSize = bundleMetrics.totalJSSize / bundleMetrics.jsFiles.length;
      const avgCSSFileSize = bundleMetrics.totalCSSSize / bundleMetrics.cssFiles.length;

      // Average file sizes should be reasonable for minified content
      expect(avgJSFileSize).toBeLessThan(500 * 1024); // 500KB average
      expect(avgCSSFileSize).toBeLessThan(200 * 1024); // 200KB average
    });
  });

  describe("Network Condition Simulation", () => {
    it("should validate bundle size for slow networks", () => {
      // For slow 3G (400 Kbps), bundle should load in reasonable time
      const slow3GBandwidth = (400 * 1024) / 8; // 400 Kbps in bytes per second
      const loadTime = bundleMetrics.totalBundleSize / slow3GBandwidth;

      console.log(`Estimated load time on Slow 3G: ${loadTime.toFixed(1)}s`);

      // Should load in under 10 seconds on slow 3G
      expect(loadTime).toBeLessThan(10);
    });

    it("should validate bundle size for fast 3G", () => {
      // For fast 3G (1.6 Mbps), bundle should load quickly
      const fast3GBandwidth = (1.6 * 1024 * 1024) / 8; // 1.6 Mbps in bytes per second
      const loadTime = bundleMetrics.totalBundleSize / fast3GBandwidth;

      console.log(`Estimated load time on Fast 3G: ${loadTime.toFixed(1)}s`);

      // Should load in under 5 seconds on fast 3G
      expect(loadTime).toBeLessThan(5);
    });

    it("should validate initial bundle for critical path", () => {
      // Initial bundle should be small enough for fast first paint
      const initialBundleSize = bundleMetrics.jsFiles[0]?.size ?? 0;
      const initialBudget = 500 * 1024; // 500KB

      expect(initialBundleSize).toBeLessThanOrEqual(initialBudget);
    });
  });
});

function getAllSourceFiles(): string[] {
  const files: string[] = [];

  function walkDir(dir: string) {
    try {
      const items = fs.readdirSync(dir);

      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);

        if (stat.isDirectory() && !item.startsWith(".") && item !== "node_modules") {
          walkDir(fullPath);
        } else if (stat.isFile() && (item.endsWith(".ts") || item.endsWith(".tsx"))) {
          files.push(fullPath);
        }
      });
    } catch (error) {
      // Skip directories we can't read
    }
  }

  walkDir("src");
  return files;
}
