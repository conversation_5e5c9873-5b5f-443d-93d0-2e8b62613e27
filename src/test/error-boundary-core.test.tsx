import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { vi, describe, it, expect, beforeEach, afterEach } from 'vitest';
import {
  BaseErrorBoundary,
  FormErrorBoundary,
  LazyLoadErrorBoundary,
  PageErrorBoundary,
  RiskErrorBoundary
} from '@/components/error-boundaries';

// Mock the services
vi.mock('@/services/errorMonitoringService', () => ({
  errorMonitoringService: {
    trackError: vi.fn()
  }
}));

vi.mock('@/services/loggingService', () => ({
  loggingService: {
    error: vi.fn(),
    info: vi.fn()
  }
}));

vi.mock('@/utils/errors', () => ({
  errorHandler: {
    handle: vi.fn()
  },
  logger: {
    info: vi.fn()
  },
  SystemError: class SystemError extends Error {
    constructor(message: string, public category: string, public code: string, public context: any, public severity: string) {
      super(message);
    }
  },
  ErrorSeverity: {
    HIGH: 'HIGH'
  }
}));

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    BrowserRouter: ({ children }: { children: React.ReactNode }) => <div>{children}</div>
  };
});

// Test component that throws errors
const ThrowError: React.FC<{ error?: Error; shouldThrow?: boolean }> = ({ 
  error = new Error('Test error'), 
  shouldThrow = true 
}) => {
  if (shouldThrow) {
    throw error;
  }
  return <div>No error</div>;
};

describe('Error Boundary Core Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock console.error to avoid noise in tests
    vi.spyOn(console, 'error').mockImplementation(() => {});
    // Mock window.location.reload
    Object.defineProperty(window, 'location', {
      value: { reload: vi.fn() },
      writable: true
    });
    // Mock window.history
    Object.defineProperty(window, 'history', {
      value: { back: vi.fn(), length: 2 },
      writable: true
    });
    // Mock navigator.onLine
    Object.defineProperty(navigator, 'onLine', {
      value: true,
      writable: true
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('BaseErrorBoundary', () => {
    it('should render error fallback when error occurs', () => {
      render(
        <BaseErrorBoundary>
          <ThrowError />
        </BaseErrorBoundary>
      );

      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      expect(screen.getByText(/try again/i)).toBeInTheDocument();
    });

    it('should sanitize sensitive error messages', () => {
      const sensitiveError = new Error('ChunkLoadError: Loading chunk 123 failed');
      
      render(
        <BaseErrorBoundary>
          <ThrowError error={sensitiveError} />
        </BaseErrorBoundary>
      );

      expect(screen.getByText(/unable to load page content/i)).toBeInTheDocument();
      expect(screen.queryByText(/ChunkLoadError/)).not.toBeInTheDocument();
    });

    it('should handle page reload', () => {
      render(
        <BaseErrorBoundary>
          <ThrowError />
        </BaseErrorBoundary>
      );

      const reloadButton = screen.getByText(/reload page/i);
      fireEvent.click(reloadButton);

      expect(window.location.reload).toHaveBeenCalled();
    });
  });

  describe('FormErrorBoundary', () => {
    it('should provide form-specific error messages', () => {
      const validationError = new Error('Validation failed: required field missing');
      
      render(
        <FormErrorBoundary formName="test-form">
          <ThrowError error={validationError} />
        </FormErrorBoundary>
      );

      expect(screen.getByText(/form validation error/i)).toBeInTheDocument();
      expect(screen.getByText(/check that all required fields/i)).toBeInTheDocument();
    });

    it('should offer local data saving functionality', () => {
      render(
        <FormErrorBoundary>
          <ThrowError />
        </FormErrorBoundary>
      );

      const saveLocallyButton = screen.getByText(/save locally/i);
      expect(saveLocallyButton).toBeInTheDocument();
      
      fireEvent.click(saveLocallyButton);
      // Should not throw error when clicking save locally
    });
  });

  describe('LazyLoadErrorBoundary', () => {
    it('should detect and handle chunk loading errors', () => {
      const chunkError = new Error('Loading chunk 5 failed');
      
      render(
        <LazyLoadErrorBoundary routeName="test-route">
          <ThrowError error={chunkError} />
        </LazyLoadErrorBoundary>
      );

      expect(screen.getByText(/page loading issue/i)).toBeInTheDocument();
      expect(screen.getByText(/network issue/i)).toBeInTheDocument();
    });

    it('should handle CSS loading failures', () => {
      const cssError = new Error('Loading CSS chunk failed');
      
      render(
        <LazyLoadErrorBoundary>
          <ThrowError error={cssError} />
        </LazyLoadErrorBoundary>
      );

      expect(screen.getByText(/style loading issue/i)).toBeInTheDocument();
    });
  });

  describe('PageErrorBoundary', () => {
    it('should analyze different types of page errors', () => {
      const notFoundError = new Error('Page not found - 404');
      
      render(
        <BrowserRouter>
          <PageErrorBoundary pageName="test-page">
            <ThrowError error={notFoundError} />
          </PageErrorBoundary>
        </BrowserRouter>
      );

      expect(screen.getByText(/check the url for any typos/i)).toBeInTheDocument();
      expect(screen.getByText(/we couldn't find the page/i)).toBeInTheDocument();
    });

    it('should provide navigation options', () => {
      render(
        <BrowserRouter>
          <PageErrorBoundary>
            <ThrowError />
          </PageErrorBoundary>
        </BrowserRouter>
      );

      expect(screen.getByRole('button', { name: /go back/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /dashboard/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /reload page/i })).toBeInTheDocument();
    });
  });

  describe('RiskErrorBoundary', () => {
    it('should analyze risk validation errors', () => {
      const validationError = new Error('Risk validation failed: invalid score');
      
      render(
        <BrowserRouter>
          <RiskErrorBoundary>
            <ThrowError error={validationError} />
          </RiskErrorBoundary>
        </BrowserRouter>
      );

      expect(screen.getByText(/risk scores are within valid ranges/i)).toBeInTheDocument();
    });

    it('should handle permission errors for risk access', () => {
      const permissionError = new Error('Permission denied for risk access');
      
      render(
        <BrowserRouter>
          <RiskErrorBoundary>
            <ThrowError error={permissionError} />
          </RiskErrorBoundary>
        </BrowserRouter>
      );

      expect(screen.getByText(/contact your administrator/i)).toBeInTheDocument();
      
      // Should not show retry button for permission errors
      expect(screen.queryByText(/try again/i)).not.toBeInTheDocument();
    });

    it('should offer data recovery options', () => {
      render(
        <BrowserRouter>
          <RiskErrorBoundary>
            <ThrowError />
          </RiskErrorBoundary>
        </BrowserRouter>
      );

      expect(screen.getByText(/data recovery options/i)).toBeInTheDocument();
      expect(screen.getByText(/export risk data/i)).toBeInTheDocument();
    });

    it('should toggle technical details visibility', () => {
      render(
        <BrowserRouter>
          <RiskErrorBoundary>
            <ThrowError />
          </RiskErrorBoundary>
        </BrowserRouter>
      );

      const toggleButton = screen.getByText(/show technical details/i);
      fireEvent.click(toggleButton);

      expect(screen.getByText(/hide technical details/i)).toBeInTheDocument();
      expect(screen.getByText(/technical details:/i)).toBeInTheDocument();
    });
  });

  describe('Error Message Sanitization', () => {
    it('should sanitize database error messages', () => {
      const dbError = new Error('SQL Error: Connection to database failed');
      
      render(
        <BaseErrorBoundary>
          <ThrowError error={dbError} />
        </BaseErrorBoundary>
      );

      expect(screen.queryByText(/SQL Error/)).not.toBeInTheDocument();
      expect(screen.getByText(/temporary issue occurred/i)).toBeInTheDocument();
    });

    it('should sanitize internal server error messages', () => {
      const serverError = new Error('Internal server error: Stack trace...');
      
      render(
        <BaseErrorBoundary>
          <ThrowError error={serverError} />
        </BaseErrorBoundary>
      );

      expect(screen.queryByText(/Internal server error/)).not.toBeInTheDocument();
      expect(screen.getByText(/temporary issue occurred/i)).toBeInTheDocument();
    });

    it('should preserve safe user-friendly messages', () => {
      const safeError = new Error('Please fill in all required fields');
      
      render(
        <BaseErrorBoundary>
          <ThrowError error={safeError} />
        </BaseErrorBoundary>
      );

      expect(screen.getByText(/please fill in all required fields/i)).toBeInTheDocument();
    });
  });
});