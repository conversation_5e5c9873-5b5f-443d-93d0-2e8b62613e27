import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import App from '@/App';

// Mock all the lazy-loaded components
vi.mock('@/routes/lazy-routes', () => ({
  LazyIndex: () => <div data-testid="lazy-index">Index Page</div>,
  LazyLogin: () => <div data-testid="lazy-login">Login Page</div>,
  LazySignup: () => <div data-testid="lazy-signup">Signup Page</div>,
  LazyDashboard: () => <div data-testid="lazy-dashboard">Dashboard Page</div>,
  LazyRiskRegister: () => <div data-testid="lazy-risk-register">Risk Register</div>,
  LazyRiskCreate: () => <div data-testid="lazy-risk-create">Risk Create</div>,
  LazyRiskDetails: () => <div data-testid="lazy-risk-details">Risk Details</div>,
  LazyRiskTemplates: () => <div data-testid="lazy-risk-templates">Risk Templates</div>,
  LazyIncidents: () => <div data-testid="lazy-incidents">Incidents</div>,
  LazyIncidentCreate: () => <div data-testid="lazy-incident-create">Incident Create</div>,
  LazyIncidentEdit: () => <div data-testid="lazy-incident-edit">Incident Edit</div>,
  LazyIncidentDetails: () => <div data-testid="lazy-incident-details">Incident Details</div>,
  LazyReports: () => <div data-testid="lazy-reports">Reports</div>,
  LazyAdministration: () => <div data-testid="lazy-administration">Administration</div>,
  LazyOrganizationManagement: () => <div data-testid="lazy-org-management">Org Management</div>,
  LazyOrganizationPage: () => <div data-testid="lazy-org-page">Organization</div>,
  LazyPolicies: () => <div data-testid="lazy-policies">Policies</div>,
  LazyPolicyEditDialog: () => <div data-testid="lazy-policy-edit">Policy Edit</div>,
  LazyProfile: () => <div data-testid="lazy-profile">Profile</div>,
  LazyNotFound: () => <div data-testid="lazy-not-found">Not Found</div>,
}));

// Mock the auth context
vi.mock('@/contexts/auth', () => ({
  AuthProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
  useAuth: () => ({
    user: null,
    isAuthenticated: false,
    isLoading: false,
    hasPermission: () => false,
    needsOrganizationSetup: false,
  }),
}));

// Mock other contexts and providers
vi.mock('@/contexts/loading-context', () => ({
  LoadingProvider: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

vi.mock('@/components/route/ProtectedRoute', () => ({
  default: ({ children }: { children: React.ReactNode }) => <div>{children}</div>,
}));

vi.mock('@/hooks/useRoutePreloader', () => ({
  useRoutePreloader: () => {},
}));

vi.mock('@/utils/bundle-analyzer', () => ({
  verifyCodeSplitting: vi.fn(() => true),
  analyzeBundleSizes: vi.fn(),
}));

// Mock error handling
vi.mock('@/utils/errors/examples/IntegrationExamples', () => ({
  initializeErrorHandling: vi.fn(),
}));

const TestWrapper = ({ children }: { children: React.ReactNode }) => (
  <BrowserRouter>{children}</BrowserRouter>
);

describe('App Component with Lazy Loading', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock development environment
    process.env.NODE_ENV = 'development';
  });

  it('should render the app without crashing', async () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    // App should render without throwing
    await waitFor(() => {
      expect(document.body).toBeInTheDocument();
    });
  });

  it('should initialize error handling on mount', async () => {
    const { initializeErrorHandling } = await import('@/utils/errors/examples/IntegrationExamples');

    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    expect(initializeErrorHandling).toHaveBeenCalled();
  });

  it('should verify code splitting in development', async () => {
    const { verifyCodeSplitting, analyzeBundleSizes } = await import('@/utils/bundle-analyzer');

    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    // Wait for the timeout to trigger verification
    await waitFor(() => {
      expect(verifyCodeSplitting).toHaveBeenCalled();
      expect(analyzeBundleSizes).toHaveBeenCalled();
    }, { timeout: 3000 });
  });

  it('should use route preloader hook', async () => {
    const { useRoutePreloader } = await import('@/hooks/useRoutePreloader');

    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    expect(useRoutePreloader).toHaveBeenCalled();
  });
});

describe('Route Structure', () => {
  it('should have all expected lazy routes imported', async () => {
    const lazyRoutes = await import('@/routes/lazy-routes');

    const expectedRoutes = [
      'LazyIndex',
      'LazyLogin',
      'LazySignup',
      'LazyDashboard',
      'LazyRiskRegister',
      'LazyRiskCreate',
      'LazyRiskDetails',
      'LazyRiskTemplates',
      'LazyIncidents',
      'LazyIncidentCreate',
      'LazyIncidentEdit',
      'LazyIncidentDetails',
      'LazyReports',
      'LazyAdministration',
      'LazyOrganizationManagement',
      'LazyOrganizationPage',
      'LazyPolicies',
      'LazyPolicyEditDialog',
      'LazyProfile',
      'LazyNotFound',
    ];

    expectedRoutes.forEach(routeName => {
      expect(lazyRoutes).toHaveProperty(routeName);
    });
  });
});
