/**
 * Network Condition Validation Test Suite
 * Tests for performance under various network conditions
 */

import { describe, it, expect, beforeAll } from 'vitest';
import { 
  NetworkConditionTester, 
  NETWORK_CONDITIONS, 
  PERFORMANCE_THRESHOLDS,
  formatPerformanceMetrics,
  getPerformanceGrade
} from '../utils/network-condition-tester';

describe('Network Condition Validation', () => {
  let tester: NetworkConditionTester;
  const bundleSize = 107132; // Current bundle size from validation report

  beforeAll(() => {
    tester = new NetworkConditionTester(bundleSize, 10);
  });

  describe('Bundle Load Time Estimation', () => {
    it('should estimate reasonable load times for Slow 3G', () => {
      const slow3G = NETWORK_CONDITIONS.find(c => c.name === 'Slow 3G')!;
      const loadTime = tester.estimateBundleLoadTime(slow3G);
      
      console.log(`Slow 3G load time: ${(loadTime / 1000).toFixed(2)}s`);
      
      // Should load in under 10 seconds on slow 3G
      expect(loadTime).toBeLessThan(10000);
      expect(loadTime).toBeGreaterThan(1000); // Should take at least 1 second
    });

    it('should estimate reasonable load times for Fast 3G', () => {
      const fast3G = NETWORK_CONDITIONS.find(c => c.name === 'Fast 3G')!;
      const loadTime = tester.estimateBundleLoadTime(fast3G);
      
      console.log(`Fast 3G load time: ${(loadTime / 1000).toFixed(2)}s`);
      
      // Should load in under 5 seconds on fast 3G
      expect(loadTime).toBeLessThan(5000);
      expect(loadTime).toBeGreaterThan(500); // Should take at least 0.5 seconds
    });

    it('should estimate reasonable load times for Regular 4G', () => {
      const regular4G = NETWORK_CONDITIONS.find(c => c.name === 'Regular 4G')!;
      const loadTime = tester.estimateBundleLoadTime(regular4G);
      
      console.log(`Regular 4G load time: ${(loadTime / 1000).toFixed(2)}s`);
      
      // Should load in under 2 seconds on 4G
      expect(loadTime).toBeLessThan(2000);
      expect(loadTime).toBeGreaterThan(100); // Should take at least 0.1 seconds
    });

    it('should estimate very fast load times for WiFi', () => {
      const wifi = NETWORK_CONDITIONS.find(c => c.name === 'WiFi')!;
      const loadTime = tester.estimateBundleLoadTime(wifi);
      
      console.log(`WiFi load time: ${(loadTime / 1000).toFixed(2)}s`);
      
      // Should load very quickly on WiFi
      expect(loadTime).toBeLessThan(1000);
      expect(loadTime).toBeGreaterThan(50); // Should take at least 50ms
    });

    it('should estimate instant load times for offline (cached)', () => {
      const offline = NETWORK_CONDITIONS.find(c => c.name === 'Offline')!;
      const loadTime = tester.estimateBundleLoadTime(offline);
      
      console.log(`Offline (cached) load time: ${loadTime}ms`);
      
      // Should load instantly from cache
      expect(loadTime).toBeLessThan(200);
      expect(loadTime).toBeGreaterThan(50); // Cache access time
    });
  });

  describe('Performance Metrics Estimation', () => {
    it('should estimate performance metrics for all network conditions', () => {
      NETWORK_CONDITIONS.forEach(condition => {
        const metrics = tester.estimatePerformanceMetrics(condition);
        
        console.log(`${condition.name} metrics:`, formatPerformanceMetrics(metrics));
        
        // All metrics should be positive numbers
        expect(metrics.loadTime).toBeGreaterThan(0);
        expect(metrics.firstContentfulPaint).toBeGreaterThan(0);
        expect(metrics.largestContentfulPaint).toBeGreaterThan(0);
        expect(metrics.timeToInteractive).toBeGreaterThan(0);
        expect(metrics.cumulativeLayoutShift).toBeGreaterThanOrEqual(0);
        expect(metrics.totalBlockingTime).toBeGreaterThanOrEqual(0);
        
        // LCP should be greater than FCP
        expect(metrics.largestContentfulPaint).toBeGreaterThan(metrics.firstContentfulPaint);
        
        // TTI should be greater than LCP
        expect(metrics.timeToInteractive).toBeGreaterThan(metrics.largestContentfulPaint);
      });
    });

    it('should have better performance metrics for faster networks', () => {
      const slow3G = tester.estimatePerformanceMetrics(
        NETWORK_CONDITIONS.find(c => c.name === 'Slow 3G')!
      );
      const wifi = tester.estimatePerformanceMetrics(
        NETWORK_CONDITIONS.find(c => c.name === 'WiFi')!
      );
      
      // WiFi should have better metrics than Slow 3G
      expect(wifi.firstContentfulPaint).toBeLessThan(slow3G.firstContentfulPaint);
      expect(wifi.largestContentfulPaint).toBeLessThan(slow3G.largestContentfulPaint);
      expect(wifi.timeToInteractive).toBeLessThan(slow3G.timeToInteractive);
      expect(wifi.loadTime).toBeLessThan(slow3G.loadTime);
    });
  });

  describe('User Experience Evaluation', () => {
    it('should evaluate user experience correctly', () => {
      const results = tester.testAllNetworkConditions();
      
      results.forEach(result => {
        const grade = getPerformanceGrade(result.estimatedUserExperience);
        
        console.log(`${result.condition.name}: ${result.estimatedUserExperience} (${grade.grade}) ${grade.emoji}`);
        
        // Should have valid user experience rating
        expect(['excellent', 'good', 'fair', 'poor']).toContain(result.estimatedUserExperience);
        
        // Should have valid grade
        expect(['A', 'B', 'C', 'D', 'F']).toContain(grade.grade);
      });
    });

    it('should have better user experience for faster networks', () => {
      const results = tester.testAllNetworkConditions();
      
      const slow3GResult = results.find(r => r.condition.name === 'Slow 3G')!;
      const wifiResult = results.find(r => r.condition.name === 'WiFi')!;
      
      const experienceOrder = ['poor', 'fair', 'good', 'excellent'];
      const slow3GIndex = experienceOrder.indexOf(slow3GResult.estimatedUserExperience);
      const wifiIndex = experienceOrder.indexOf(wifiResult.estimatedUserExperience);
      
      // WiFi should have better or equal user experience
      expect(wifiIndex).toBeGreaterThanOrEqual(slow3GIndex);
    });
  });

  describe('Performance Report Generation', () => {
    it('should generate comprehensive performance report', () => {
      const report = tester.generateNetworkPerformanceReport();
      
      console.log('Performance Report Summary:', report.summary);
      console.log('Recommendations:', report.recommendations);
      
      // Should have valid summary
      expect(report.summary.totalConditions).toBe(NETWORK_CONDITIONS.length);
      expect(report.summary.overallScore).toBeGreaterThanOrEqual(0);
      expect(report.summary.overallScore).toBeLessThanOrEqual(100);
      
      // Should have results for all conditions
      expect(report.results).toHaveLength(NETWORK_CONDITIONS.length);
      
      // Should have recommendations
      expect(Array.isArray(report.recommendations)).toBe(true);
      
      // Count totals should match
      const totalCount = report.summary.excellentCount + 
                        report.summary.goodCount + 
                        report.summary.fairCount + 
                        report.summary.poorCount;
      expect(totalCount).toBe(NETWORK_CONDITIONS.length);
    });

    it('should provide relevant recommendations based on performance', () => {
      const report = tester.generateNetworkPerformanceReport();
      
      // Should have at least some recommendations
      expect(report.recommendations.length).toBeGreaterThan(0);
      
      // Recommendations should be strings
      report.recommendations.forEach(rec => {
        expect(typeof rec).toBe('string');
        expect(rec.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Performance Thresholds Validation', () => {
    it('should meet performance thresholds for good network conditions', () => {
      const wifi = NETWORK_CONDITIONS.find(c => c.name === 'WiFi')!;
      const metrics = tester.estimatePerformanceMetrics(wifi);
      const thresholds = PERFORMANCE_THRESHOLDS.WiFi;
      
      // WiFi should meet all thresholds for our small bundle
      expect(metrics.firstContentfulPaint).toBeLessThanOrEqual(thresholds.firstContentfulPaint);
      expect(metrics.largestContentfulPaint).toBeLessThanOrEqual(thresholds.largestContentfulPaint);
      expect(metrics.timeToInteractive).toBeLessThanOrEqual(thresholds.timeToInteractive);
      expect(metrics.cumulativeLayoutShift).toBeLessThanOrEqual(thresholds.cumulativeLayoutShift);
    });

    it('should have reasonable performance even on slow networks', () => {
      const slow3G = NETWORK_CONDITIONS.find(c => c.name === 'Slow 3G')!;
      const metrics = tester.estimatePerformanceMetrics(slow3G);
      
      // Even on slow 3G, our small bundle should perform reasonably
      expect(metrics.firstContentfulPaint).toBeLessThan(5000); // Under 5 seconds
      expect(metrics.largestContentfulPaint).toBeLessThan(8000); // Under 8 seconds
      expect(metrics.timeToInteractive).toBeLessThan(12000); // Under 12 seconds
    });
  });

  describe('Bundle Size Impact Analysis', () => {
    it('should show performance impact of different bundle sizes', () => {
      const smallTester = new NetworkConditionTester(50 * 1024); // 50KB
      const largeTester = new NetworkConditionTester(2 * 1024 * 1024); // 2MB
      
      const slow3G = NETWORK_CONDITIONS.find(c => c.name === 'Slow 3G')!;
      
      const smallMetrics = smallTester.estimatePerformanceMetrics(slow3G);
      const largeMetrics = largeTester.estimatePerformanceMetrics(slow3G);
      
      console.log('Small bundle (50KB) on Slow 3G:', formatPerformanceMetrics(smallMetrics));
      console.log('Large bundle (2MB) on Slow 3G:', formatPerformanceMetrics(largeMetrics));
      
      // Smaller bundle should perform better
      expect(smallMetrics.loadTime).toBeLessThan(largeMetrics.loadTime);
      expect(smallMetrics.firstContentfulPaint).toBeLessThan(largeMetrics.firstContentfulPaint);
      expect(smallMetrics.largestContentfulPaint).toBeLessThan(largeMetrics.largestContentfulPaint);
      expect(smallMetrics.timeToInteractive).toBeLessThan(largeMetrics.timeToInteractive);
    });

    it('should validate current bundle size is optimal for performance', () => {
      const currentTester = new NetworkConditionTester(bundleSize);
      const report = currentTester.generateNetworkPerformanceReport();
      
      // With our current small bundle size, we should have good performance
      const goodOrBetterCount = report.summary.excellentCount + report.summary.goodCount;
      const totalConditions = report.summary.totalConditions;
      
      // At least 60% of conditions should have good or better performance
      expect(goodOrBetterCount / totalConditions).toBeGreaterThanOrEqual(0.6);
      
      // Overall score should be reasonable
      expect(report.summary.overallScore).toBeGreaterThanOrEqual(60);
    });
  });
});