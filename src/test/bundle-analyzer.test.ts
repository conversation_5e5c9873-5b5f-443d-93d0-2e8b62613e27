import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  verifyCodeSplitting,
  getRoutePerformanceMetrics,
  logBundleLoad,
  showLoadingDebugInfo
} from '@/utils/bundle-analyzer';

// Mock import.meta globally before importing the module
vi.stubGlobal('import', {
  meta: {
    env: { MODE: 'test' }
  }
});

// Mock DOM and performance APIs
const mockPerformance = {
  now: vi.fn(() => 1000),
  getEntriesByType: vi.fn(),
};

const mockDocument = {
  querySelectorAll: vi.fn(),
};

describe('Bundle Analyzer', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock global objects
    global.performance = mockPerformance as any;
    global.document = mockDocument as any;
    global.console = {
      ...console,
      log: vi.fn(),
    };
    
    // Mock window object with performance API
    global.window = {
      performance: mockPerformance,
    } as any;
  });
  
  afterEach(() => {
    vi.unstubAllGlobals();
  });

  describe('verifyCodeSplitting', () => {
    it('should detect code splitting when chunk files are present', () => {
      const mockScripts = [
        { src: 'http://localhost:3000/assets/index.abc123.js' },
        { src: 'http://localhost:3000/assets/chunk.def456.js' },
        { src: 'http://localhost:3000/assets/vendor.ghi789.js' },
      ];

      mockDocument.querySelectorAll.mockReturnValue(mockScripts);

      const result = verifyCodeSplitting();

      expect(result).toBe(true);
      expect(mockDocument.querySelectorAll).toHaveBeenCalledWith('script[src]');
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Code Splitting Analysis'),
        expect.objectContaining({
          isCodeSplitting: true,
          chunkFiles: expect.any(Number),
        })
      );
    });

    it('should return false when no chunk files are found', () => {
      const mockScripts = [
        { src: 'http://localhost:3000/main.js' },
        { src: 'http://localhost:3000/vendor.js' },
      ];

      mockDocument.querySelectorAll.mockReturnValue(mockScripts);

      const result = verifyCodeSplitting();

      expect(result).toBe(false);
    });
  });

  describe('getRoutePerformanceMetrics', () => {
    it('should return performance metrics when available', () => {
      const mockNavigationEntry = {
        navigationStart: 0,
        loadEventEnd: 1000,
      };

      const mockResourceEntries = [
        { name: 'http://localhost:3000/chunk.abc123.js' },
        { name: 'http://localhost:3000/styles.def456.css' },
        { name: 'http://localhost:3000/image.png' },
      ];

      mockPerformance.getEntriesByType
        .mockReturnValueOnce([mockNavigationEntry])
        .mockReturnValueOnce(mockResourceEntries);

      const metrics = getRoutePerformanceMetrics();

      expect(metrics).toEqual({
        navigation: mockNavigationEntry,
        resources: expect.arrayContaining([
          expect.objectContaining({ name: expect.stringContaining('.js') }),
          expect.objectContaining({ name: expect.stringContaining('.css') }),
        ]),
        totalLoadTime: 1000,
      });
    });

    it('should return null when performance API is not available', () => {
      global.performance = undefined as any;

      const metrics = getRoutePerformanceMetrics();

      expect(metrics).toBeNull();
    });

    it('should return null when no navigation entries are available', () => {
      mockPerformance.getEntriesByType
        .mockReturnValueOnce([]); // Empty navigation entries

      const metrics = getRoutePerformanceMetrics();

      expect(metrics).toBeNull();
    });
  });

  describe('logBundleLoad', () => {
    it.skip('should log bundle load time in development', () => {
      // Mock development environment
      Object.defineProperty(globalThis, 'import', {
        value: {
          meta: {
            env: { MODE: 'development' }
          }
        },
        writable: true,
        configurable: true
      });
      
      const startTime = 500;
      mockPerformance.now.mockReturnValue(1000);

      logBundleLoad('test-route', startTime);

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Route "test-route" loaded in 500.00ms')
      );
    });

    it('should not log in production', () => {
      // Mock production environment
      vi.stubGlobal('import.meta', {
        env: { MODE: 'production' }
      });
      
      logBundleLoad('test-route', 500);

      expect(console.log).not.toHaveBeenCalled();
    });
  });

  describe('showLoadingDebugInfo', () => {
    it.skip('should show loading state in development', () => {
      // Mock development environment
      Object.defineProperty(globalThis, 'import', {
        value: {
          meta: {
            env: { MODE: 'development' }
          }
        },
        writable: true,
        configurable: true
      });
      
      showLoadingDebugInfo('test-route', true);

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Loading route: test-route')
      );
    });

    it.skip('should show loaded state in development', () => {
      // Mock development environment
      Object.defineProperty(globalThis, 'import', {
        value: {
          meta: {
            env: { MODE: 'development' }
          }
        },
        writable: true,
        configurable: true
      });
      
      showLoadingDebugInfo('test-route', false);

      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Route loaded: test-route')
      );
    });
  });
});
