#!/usr/bin/env node
/**
 * Test Runner Script for RiskCompass
 *
 * This script provides various test running options:
 * - Run all tests
 * - Run tests by category (unit, integration, component)
 * - Run tests with coverage
 * - Run tests in watch mode
 * - Generate test reports
 */
import { execSync } from "child_process";
import { existsSync } from "fs";
import path from "path";
interface TestOptions {
  category?: "unit" | "integration" | "component" | "e2e";
  coverage?: boolean;
  watch?: boolean;
  ui?: boolean;
  reporter?: "default" | "verbose" | "json" | "html";
  pattern?: string;
  bail?: boolean;
  silent?: boolean;
}
class TestRunner {
  private baseCommand = "vitest";
  constructor(private options: TestOptions = {}) {
    // Implementation needed
  }
  /**
   * Build the vitest command based on options
   */
  private buildCommand(): string {
    let command = this.baseCommand;
    // Add coverage flag
    if (this.options.coverage) {
      command += " --coverage";
    }
    // Add watch mode
    if (this.options.watch) {
      command += " --watch";
    }
    // Add UI mode
    if (this.options.ui) {
      command += " --ui";
    }
    // Add reporter
    if (this.options.reporter && this.options.reporter !== "default") {
      command += ` --reporter=${this.options.reporter}`;
    }
    // Add bail option
    if (this.options.bail) {
      command += " --bail=1";
    }
    // Add silent option
    if (this.options.silent) {
      command += " --silent";
    }
    // Add test pattern based on category
    if (this.options.category) {
      const patterns = this.getCategoryPatterns(this.options.category);
      command += ` ${patterns.join(" ")}`;
    } else if (this.options.pattern) {
      command += ` ${this.options.pattern}`;
    }
    return command;
  }
  /**
   * Get test file patterns for different categories
   */
  private getCategoryPatterns(category: string): string[] {
    const patterns: Record<string, string[]> = {
      unit: [
        "src/utils/**/*.test.ts",
        "src/services/**/*.test.ts",
        "src/hooks/**/*.test.ts",
        "src/repositories/**/*.test.ts",
      ],
      component: ["src/components/**/*.test.tsx", "src/pages/**/*.test.tsx"],
      integration: ["src/integrations/**/*.test.ts", "src/contexts/**/*.test.tsx"],
      e2e: ["e2e/**/*.test.ts", "tests/**/*.e2e.ts"],
    };
    return patterns[category] ?? [];
  }
  /**
   * Run the tests
   */
  async run(): Promise<void> {
    const command = this.buildCommand();

    try {
      // Execute the test command
      execSync(command, { stdio: "inherit" });
    } catch (error) {
      console.error("Test execution failed:", error);
      process.exit(1);
    }
  }
}

/**
 * Parse command line arguments into TestOptions
 */
function parseArgs(args: string[]): TestOptions {
  const options: TestOptions = {};

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
      case "--coverage":
      case "-c":
        options.coverage = true;
        break;
      case "--watch":
      case "-w":
        options.watch = true;
        break;
      case "--ui":
        options.ui = true;
        break;
      case "--unit":
        options.category = "unit";
        break;
      case "--component":
        options.category = "component";
        break;
      case "--integration":
        options.category = "integration";
        break;
      case "--e2e":
        options.category = "e2e";
        break;
      case "--reporter":
        options.reporter = args[++i] as string;
        break;
      case "--pattern":
        options.pattern = args[++i];
        break;
      case "--bail":
        options.bail = true;
        break;
      case "--silent":
        options.silent = true;
        break;
      case "--help":
      case "-h":
        showHelp();
        process.exit(0);
        break;
    }
  }

  return options;
}

function showHelp(): void {
  console.log(`
RiskCompass Test Runner

Usage: node test-runner.js [options]

Options:
  --coverage, -c     Run tests with coverage reporting
  --watch, -w        Run tests in watch mode
  --ui               Run tests with UI interface
  --unit             Run only unit tests
  --component        Run only component tests
  --integration      Run only integration tests
  --e2e              Run only end-to-end tests
  --reporter <type>  Specify reporter (default, verbose, json, html)
  --pattern <glob>   Run tests matching pattern
  --bail             Stop on first test failure
  --silent           Suppress output
  --help, -h         Show this help message

Examples:
  node test-runner.js --unit --coverage
  node test-runner.js --watch --component
  node test-runner.js --pattern "**/Button.test.tsx"
`);
}

// CLI execution
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = parseArgs(args);
  const runner = new TestRunner(options);
  runner.run().catch(error => {
    console.error("Failed to run tests:", error);
    process.exit(1);
  });
}

export { TestRunner, TestOptions, parseArgs };
