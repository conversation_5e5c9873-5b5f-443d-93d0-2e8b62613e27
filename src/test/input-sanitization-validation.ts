/**
 * Manual validation script for input sanitization service
 * This script tests the sanitization service with various attack vectors
 */
import {
  sanitizeHtml,
  sanitizeText,
  validateAndSanitizeInput,
  sanitizeFileData,
  sanitizeUrl,
  testSanitization,
  SANITIZATION_PRESETS,
} from "../services/inputSanitizationService";
// Test 1: Basic XSS prevention
const xssTests = [
  '<script>alert("xss")</script>',
  '<img src="x" onerror="alert(1)">',
  '<div onclick="alert(1)">Click me</div>',
  "javascript:alert(1)",
  '<iframe src="javascript:alert(1)"></iframe>',
];
xssTests.forEach((test, index) => {
  const sanitized = sanitizeHtml(test);
  const safe = !sanitized.includes("alert") && !sanitized.includes("javascript:");
});
// Test 2: Text sanitization
const textTest = "<p>Hello <strong>World</strong></p>";
const sanitizedText = sanitizeText(textTest);
// Test 3: URL sanitization
const urlTests = [
  { url: "https://example.com", expected: true },
  { url: "javascript:alert(1)", expected: false },
  { url: "data:text/html,<script>alert(1)</script>", expected: false },
  { url: "/relative/path", expected: true },
];
urlTests.forEach(({ url, expected }) => {
  const sanitized = sanitizeUrl(url);
  const safe = expected ? sanitized === url : sanitized === "";
});
// Test 4: Form data sanitization
const formData = {
  name: "John Doe",
  message: '<script>alert("xss")</script>Hello World',
  description: "<p>Safe HTML content</p>",
};
const fieldOptions = {
  description: SANITIZATION_PRESETS.RICH_TEXT,
};
// Import the sanitizeFormData function
import { sanitizeFormData } from "../services/inputSanitizationService";
const formResults = sanitizeFormData(formData, fieldOptions);
Object.entries(formResults).forEach(([key, result]) => {
  const safe = result.isValid && !result.sanitizedValue.includes("alert");
  if (result.warnings.length > 0) {
    // Condition handled
  }
});
// Test 5: Built-in test suite
const builtInTestResult = testSanitization();
// Test 6: Advanced XSS vectors
const advancedXssVectors = [
  '<svg onload="alert(1)">',
  '<body onload="alert(1)">',
  '<style>body{background:url("javascript:alert(1)")}</style>',
  '<meta http-equiv="refresh" content="0;url=javascript:alert(1)">',
  "&lt;script&gt;alert(1)&lt;/script&gt;",
  "%3Cscript%3Ealert(1)%3C/script%3E",
];
advancedXssVectors.forEach((vector, index) => {
  const sanitized = sanitizeHtml(vector);
  const safe =
    !sanitized.toLowerCase().includes("alert") &&
    !sanitized.toLowerCase().includes("javascript:") &&
    !sanitized.toLowerCase().includes("<script");
});
// Test 7: File validation
// Create mock file for testing (Node.js compatible)
class MockFile {
  name: string;
  type: string;
  size: number;
  constructor(content: unknown[], name: string, options: { type: string }) {
    this.name = name;
    this.type = options.type;
    this.size = 1024; // Default size
  }
}
const createMockFile = (name: string, type: string, size: number): File => {
  const file = new MockFile([""], name, { type }) as unknown as File;
  (file as unknown as { size: number }).size = size;
  return file;
};
const fileTests = [
  { name: "document.pdf", type: "application/pdf", size: 1024, shouldPass: true },
  { name: "script.exe", type: "application/x-executable", size: 1024, shouldPass: false },
  { name: "large.pdf", type: "application/pdf", size: 15 * 1024 * 1024, shouldPass: false },
  { name: "<script>alert(1)</script>.pdf", type: "application/pdf", size: 1024, shouldPass: true },
];
fileTests.forEach(({ name, type, size, shouldPass }) => {
  const file = createMockFile(name, type, size);
  const result = sanitizeFileData(file);
  const passed = result.isValid === shouldPass;
  if (!result.isValid && result.errors.length > 0) {
    // Condition handled
  }
});
