import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { BrowserRouter, MemoryRouter } from 'react-router-dom';
import { useRoutePreloader, useInteractionPreloader } from '@/hooks/useRoutePreloader';

// Mock the preloadRoute function
vi.mock('@/utils/lazy-routes', () => ({
  preloadRoute: vi.fn(() => Promise.resolve()),
}));

const createTestWrapper = (initialPath: string) => ({ children }: { children: React.ReactNode }) => (
  <MemoryRouter initialEntries={[initialPath]}>{children}</MemoryRouter>
);

describe('useRoutePreloader', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should preload routes based on current location', async () => {
    const { preloadRoute } = await import('@/utils/lazy-routes');
    
    // Use MemoryRouter with dashboard path
    const TestWrapper = createTestWrapper('/dashboard');

    renderHook(() => useRoutePreloader(), {
      wrapper: TestWrapper,
    });

    // Fast-forward timers to trigger preloading
    vi.advanceTimersByTime(1000);
    vi.advanceTimersByTime(1000); // advance additional time for staggered preloads

    expect(preloadRoute).toHaveBeenCalled();
  });

  it('should preload different routes for different locations', async () => {
    const { preloadRoute } = await import('@/utils/lazy-routes');
    
    // Test risk pages
    const TestWrapper = createTestWrapper('/risks');

    renderHook(() => useRoutePreloader(), {
      wrapper: TestWrapper,
    });

    vi.advanceTimersByTime(1000);
    vi.advanceTimersByTime(1000); // advance additional time for staggered preloads
    
    expect(preloadRoute).toHaveBeenCalled();
  });
});

describe('useInteractionPreloader', () => {
  it('should return preload functions for hover and click', () => {
    const { result } = renderHook(() => useInteractionPreloader());
    
    expect(result.current.preloadOnHover).toBeInstanceOf(Function);
    expect(result.current.preloadOnClick).toBeInstanceOf(Function);
  });

  it('should create hover handlers that preload routes', async () => {
    const { preloadRoute } = await import('@/utils/lazy-routes');
    const { result } = renderHook(() => useInteractionPreloader());
    
    const mockImport = () => Promise.resolve({ default: () => null });
    const hoverProps = result.current.preloadOnHover(mockImport);
    
    expect(hoverProps).toHaveProperty('onMouseEnter');
    expect(hoverProps).toHaveProperty('onFocus');
    
    // Simulate hover
    hoverProps.onMouseEnter();
    expect(preloadRoute).toHaveBeenCalledWith(mockImport);
  });
});
