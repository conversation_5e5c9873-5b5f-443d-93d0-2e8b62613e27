/**
 * Integration tests for input sanitization examples
 */

import { describe, it, expect } from 'vitest';
import {
  sanitizeUserComment,
  sanitizeRiskDescription,
  sanitizeSearchQuery,
  sanitizeRiskFormData,
  sanitizeIncidentFormData,
  sanitizePolicyContent,
  sanitizeUserProfileData,
  sanitizeExternalUrl,
  sanitizeRiskList,
  sanitizeAndValidateApiResponse,
  sanitizeByFieldType,
  sanitizeCsvImportData,
  riskApiSanitizationMiddleware,
  incidentApiSanitizationMiddleware,
} from '../utils/input-sanitization-integration';

describe('Input Sanitization Integration', () => {
  describe('sanitizeUserComment', () => {
    it('should allow safe HTML in user comments', () => {
      const comment = '<p>This is a <strong>great</strong> risk assessment!</p>';
      const result = sanitizeUserComment(comment);
      expect(result).toBe('<p>This is a <strong>great</strong> risk assessment!</p>');
    });

    it('should remove dangerous content from user comments', () => {
      const comment = '<p>Good point!</p><script>alert("xss")</script>';
      const result = sanitizeUserComment(comment);
      expect(result).toBe('<p>Good point!</p>');
      expect(result).not.toContain('script');
    });
  });

  describe('sanitizeRiskDescription', () => {
    it('should preserve rich text formatting', () => {
      const description = '<h2>Risk Overview</h2><p>This risk involves <em>potential</em> data breach.</p>';
      const result = sanitizeRiskDescription(description);
      expect(result).toContain('<h2>');
      expect(result).toContain('<em>');
      expect(result).not.toContain('script');
    });
  });

  describe('sanitizeSearchQuery', () => {
    it('should remove all HTML from search queries', () => {
      const query = '<script>alert("xss")</script>security risk';
      const result = sanitizeSearchQuery(query);
      expect(result).toBe('security risk');
      expect(result).not.toContain('<');
    });

    it('should limit search query length', () => {
      const longQuery = 'a'.repeat(200);
      const result = sanitizeSearchQuery(longQuery);
      expect(result.length).toBe(100);
    });
  });

  describe('sanitizeRiskFormData', () => {
    it('should sanitize risk form data with appropriate presets', () => {
      const formData = {
        title: 'Data Breach Risk<script>alert("xss")</script>',
        description: '<p>Potential <strong>high impact</strong> risk</p>',
        mitigation_actions: '<ul><li>Implement encryption</li></ul>',
        owner_email: '<EMAIL><script>alert("xss")</script>',
      };

      const results = sanitizeRiskFormData(formData);
      
      expect(results.title.sanitizedValue).toBe('Data Breach Risk');
      expect(results.description.sanitizedValue).toContain('<p>');
      expect(results.description.sanitizedValue).toContain('<strong>');
      expect(results.mitigation_actions.sanitizedValue).toContain('<ul>');
      expect(results.owner_email.sanitizedValue).toBe('<EMAIL>');
      
      // Ensure no script tags remain
      Object.values(results).forEach(result => {
        expect(result.sanitizedValue).not.toContain('script');
      });
    });
  });

  describe('sanitizeIncidentFormData', () => {
    it('should sanitize incident form data appropriately', () => {
      const formData = {
        title: 'Security Incident<script>alert("xss")</script>',
        description: '<p>Incident details with <strong>important</strong> information</p>',
        resolution_notes: '<p>Resolved by implementing <em>additional controls</em></p>',
      };

      const results = sanitizeIncidentFormData(formData);
      
      expect(results.title.sanitizedValue).toBe('Security Incident');
      expect(results.description.sanitizedValue).toContain('<p>');
      expect(results.resolution_notes.sanitizedValue).toContain('<em>');
      
      Object.values(results).forEach(result => {
        expect(result.sanitizedValue).not.toContain('script');
      });
    });
  });

  describe('sanitizePolicyContent', () => {
    it('should allow comprehensive HTML for policy documents', () => {
      const content = `
        <h1>Security Policy</h1>
        <table>
          <thead>
            <tr><th>Rule</th><th>Description</th></tr>
          </thead>
          <tbody>
            <tr><td>Rule 1</td><td>No unauthorized access</td></tr>
          </tbody>
        </table>
        <script>alert("xss")</script>
      `;

      const result = sanitizePolicyContent(content);
      
      expect(result).toContain('<h1>');
      expect(result).toContain('<table>');
      expect(result).toContain('<thead>');
      expect(result).not.toContain('script');
    });
  });

  describe('sanitizeExternalUrl', () => {
    it('should upgrade HTTP to HTTPS for external URLs', () => {
      const url = 'http://example.com/page';
      const result = sanitizeExternalUrl(url);
      expect(result).toBe('https://example.com/page');
    });

    it('should preserve HTTPS URLs', () => {
      const url = 'https://example.com/page';
      const result = sanitizeExternalUrl(url);
      expect(result).toBe('https://example.com/page');
    });

    it('should preserve relative URLs', () => {
      const url = '/internal/page';
      const result = sanitizeExternalUrl(url);
      expect(result).toBe('/internal/page');
    });

    it('should block dangerous URLs', () => {
      const url = 'javascript:alert(1)';
      const result = sanitizeExternalUrl(url);
      expect(result).toBe('');
    });
  });

  describe('sanitizeRiskList', () => {
    it('should sanitize multiple risk objects', () => {
      const risks = [
        {
          title: 'Risk 1<script>alert("xss")</script>',
          description: '<p>First risk description</p>',
        },
        {
          title: 'Risk 2<img src="x" onerror="alert(1)">',
          description: '<p>Second risk description</p>',
        },
      ];

      const results = sanitizeRiskList(risks);
      
      expect(results).toHaveLength(2);
      expect(results[0].title).toBe('Risk 1');
      expect(results[1].title).toBe('Risk 2');
      
      results.forEach(risk => {
        expect(risk.title).not.toContain('script');
        expect(risk.title).not.toContain('onerror');
      });
    });
  });

  describe('sanitizeAndValidateApiResponse', () => {
    it('should sanitize and validate API response data', () => {
      const apiData = {
        name: 'John Doe<script>alert("xss")</script>',
        email: '<EMAIL>',
        bio: '<p>Software developer with <strong>10 years</strong> experience</p>',
      };

      const result = sanitizeAndValidateApiResponse(apiData);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedData.name).toBe('John Doe');
      expect(result.sanitizedData.email).toBe('<EMAIL>');
      expect(result.warnings.length).toBeGreaterThan(0); // Should warn about modified content
    });

    it('should handle invalid data gracefully', () => {
      const result = sanitizeAndValidateApiResponse(null);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Invalid data format');
    });
  });

  describe('sanitizeByFieldType', () => {
    it('should sanitize email fields appropriately', () => {
      const email = '<EMAIL><script>alert("xss")</script>';
      const result = sanitizeByFieldType(email, 'email');
      expect(result).toBe('<EMAIL>');
      expect(result.length).toBeLessThanOrEqual(254);
    });

    it('should sanitize phone fields', () => {
      const phone = '+****************<script>alert("xss")</script>';
      const result = sanitizeByFieldType(phone, 'phone');
      expect(result).toBe('+****************');
      expect(result).not.toContain('script');
    });

    it('should sanitize URL fields', () => {
      const url = 'https://example.com/page';
      const result = sanitizeByFieldType(url, 'url');
      expect(result).toBe('https://example.com/page');
    });

    it('should handle rich text fields', () => {
      const richText = '<p>Rich <strong>text</strong> content</p><script>alert("xss")</script>';
      const result = sanitizeByFieldType(richText, 'rich_text');
      expect(result).toContain('<p>');
      expect(result).toContain('<strong>');
      expect(result).not.toContain('script');
    });

    it('should default to plain text sanitization', () => {
      const text = '<p>Some text</p><script>alert("xss")</script>';
      const result = sanitizeByFieldType(text, 'unknown_type');
      expect(result).toBe('Some text');
      expect(result).not.toContain('<');
    });
  });

  describe('sanitizeCsvImportData', () => {
    it('should sanitize CSV import data', () => {
      const csvData = [
        {
          name: 'John Doe<script>alert("xss")</script>',
          email: '<EMAIL>',
          department: 'IT<img src="x" onerror="alert(1)">',
        },
        {
          name: 'Jane Smith',
          email: '<EMAIL><script>alert("xss")</script>',
          department: 'HR',
        },
      ];

      const results = sanitizeCsvImportData(csvData);
      
      expect(results).toHaveLength(2);
      expect(results[0].data.name).toBe('John Doe');
      expect(results[0].data.department).toBe('IT');
      expect(results[1].data.email).toBe('<EMAIL>');
      
      results.forEach(row => {
        Object.values(row.data).forEach(value => {
          expect(value).not.toContain('script');
          expect(value).not.toContain('onerror');
        });
      });
    });
  });

  describe('API Sanitization Middlewares', () => {
    it('should sanitize risk API data', () => {
      const riskData = {
        title: 'Security Risk<script>alert("xss")</script>',
        description: '<p>Risk description with <strong>formatting</strong></p>',
        notes: '<p>Additional notes</p>',
      };

      const result = riskApiSanitizationMiddleware(riskData);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedData.title).toBe('Security Risk');
      expect(result.sanitizedData.description).toContain('<p>');
      expect(result.sanitizedData.description).toContain('<strong>');
      
      Object.values(result.sanitizedData).forEach(value => {
        expect(value).not.toContain('script');
      });
    });

    it('should sanitize incident API data', () => {
      const incidentData = {
        title: 'Security Incident<script>alert("xss")</script>',
        description: '<p>Incident description</p>',
        resolution_notes: '<p>Resolution details</p>',
      };

      const result = incidentApiSanitizationMiddleware(incidentData);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedData.title).toBe('Security Incident');
      expect(result.sanitizedData.description).toContain('<p>');
      
      Object.values(result.sanitizedData).forEach(value => {
        expect(value).not.toContain('script');
      });
    });
  });
});