import { supabase } from "@/integrations/supabase/client";
import { RiskTemplate } from "@/types";
export const fetchRiskTemplates = async (): Promise<RiskTemplate[]> => {
  try {
    const { data, error } = await supabase
      .from("risk_templates")
      .select(
        `
        *,
        categories:risk_categories(name)
      `
      )
      .order("created_at", { ascending: false });
    if (error) {
      throw new Error(`Failed to fetch templates: ${error.message}`);
    }
    if (!data) {
      return [];
    }
    const formattedTemplates: RiskTemplate[] = data.map(template => ({
      id: template.id,
      name: template.name,
      description: template.description,
      categoryId: template.category_id,
      category: template.categories?.name ?? "Uncategorized",
      defaultLikelihood: template.default_likelihood,
      defaultImpact: template.default_impact,
      suggestedMitigationPlan: template.suggested_mitigation_plan ?? "",
      createdAt: new Date(template.created_at),
      createdBy: template.created_by,
      organizationId: template.organization_id,
    }));
    return formattedTemplates;
  } catch (error) {
    throw error;
  }
};
export const getTemplate = async (templateId: string): Promise<RiskTemplate | null> => {
  try {
    const { data, error } = await supabase
      .from("risk_templates")
      .select(
        `
        *,
        categories:risk_categories(name)
      `
      )
      .eq("id", templateId)
      .single();
    if (error) {
      throw new Error(`Failed to fetch template: ${error.message}`);
    }
    if (!data) {
      return null;
    }
    const formattedTemplate: RiskTemplate = {
      id: data.id,
      name: data.name,
      description: data.description,
      categoryId: data.category_id,
      category: data.categories?.name ?? "Uncategorized",
      defaultLikelihood: data.default_likelihood,
      defaultImpact: data.default_impact,
      suggestedMitigationPlan: data.suggested_mitigation_plan ?? "",
      createdAt: new Date(data.created_at),
      createdBy: data.created_by,
      organizationId: data.organization_id,
    };
    return formattedTemplate;
  } catch (error) {
    throw error;
  }
};
export const createTemplate = async (
  template: Omit<RiskTemplate, "id" | "createdAt">
): Promise<string | null> => {
  try {
    // Get current user and organization
    const {
      data: { session },
      error: sessionError,
    } = await supabase.auth.getSession();
    if (sessionError || !session) {
      throw new Error("Authentication required");
    }
    // Get user's organization
    const { data: profileData, error: profileError } = await supabase
      .from("profiles")
      .select("organization_id")
      .eq("id", session.user.id)
      .single();
    if (profileError || !profileData?.organization_id) {
      throw new Error("No organization found for user");
    }
    const { data, error } = await supabase
      .from("risk_templates")
      .insert({
        name: template.name,
        description: template.description,
        category_id: template.categoryId,
        default_likelihood: template.defaultLikelihood,
        default_impact: template.defaultImpact,
        suggested_mitigation_plan: template.suggestedMitigationPlan,
        created_by: session.user.id,
        organization_id: profileData.organization_id,
      })
      .select("id")
      .single();
    if (error) {
      throw new Error(`Failed to create template: ${error.message}`);
    }
    return data.id;
  } catch (error) {
    throw error;
  }
};
export const deleteTemplate = async (templateId: string): Promise<boolean> => {
  try {
    const { error } = await supabase.from("risk_templates").delete().eq("id", templateId);
    if (error) {
      throw new Error(`Failed to delete template: ${error.message}`);
    }
    return true;
  } catch (error) {
    throw error;
  }
};
