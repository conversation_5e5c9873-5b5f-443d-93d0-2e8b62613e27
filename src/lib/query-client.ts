import { QueryClient } from "@tanstack/react-query";

/**
 * Optimized QueryClient configuration for risk management application
 * Configured for large datasets and frequent updates
 */

export const createQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Cache data for 5 minutes before considering it stale
        staleTime: 5 * 60 * 1000, // 5 minutes

        // Keep data in cache for 10 minutes after last use
        gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)

        // Retry failed requests 3 times with exponential backoff
        retry: (failureCount, error: Error | unknown) => {
          // Don't retry on 4xx errors (client errors)
          if (error && typeof error === "object" && "status" in error) {
            const status = (error as { status: number }).status;
            if (status >= 400 && status < 500) {
              return false;
            }
          }
          // Retry up to 3 times for other errors
          return failureCount < 3;
        },

        // Exponential backoff delay
        retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),

        // Refetch on window focus for critical data
        refetchOnWindowFocus: true,

        // Don't refetch on reconnect by default (can be overridden per query)
        refetchOnReconnect: "always",

        // Enable background refetching
        refetchInterval: false, // Disabled by default, enabled per query as needed

        // Network mode - continue with cached data when offline
        networkMode: "online",

        // Structural sharing for performance
        structuralSharing: true,
      },
      mutations: {
        // Retry mutations once on network errors
        retry: (failureCount, error: Error | unknown) => {
          if (error && typeof error === "object" && "status" in error) {
            const status = (error as { status: number }).status;
            if (status >= 400 && status < 500) {
              return false;
            }
          }
          return failureCount < 1;
        },

        // Network mode for mutations
        networkMode: "online",
      },
    },
  });
};

/**
 * Query key factory for consistent cache management
 */
export const queryKeys = {
  // Risk-related queries
  risks: {
    all: ["risks"] as const,
    lists: () => [...queryKeys.risks.all, "list"] as const,
    list: (filters: Record<string, unknown>) => [...queryKeys.risks.lists(), filters] as const,
    details: () => [...queryKeys.risks.all, "detail"] as const,
    detail: (id: string) => [...queryKeys.risks.details(), id] as const,
    history: (id: string) => [...queryKeys.risks.detail(id), "history"] as const,
    templates: () => [...queryKeys.risks.all, "templates"] as const,
    categories: () => [...queryKeys.risks.all, "categories"] as const,
  },

  // Organization-scoped queries
  organization: {
    all: (orgId: string) => ["organization", orgId] as const,
    risks: (orgId: string) => [...queryKeys.organization.all(orgId), "risks"] as const,
    users: (orgId: string) => [...queryKeys.organization.all(orgId), "users"] as const,
    dashboard: (orgId: string) => [...queryKeys.organization.all(orgId), "dashboard"] as const,
  },

  // User-related queries
  users: {
    all: ["users"] as const,
    lists: () => [...queryKeys.users.all, "list"] as const,
    list: (filters: Record<string, unknown>) => [...queryKeys.users.lists(), filters] as const,
    details: () => [...queryKeys.users.all, "detail"] as const,
    detail: (id: string) => [...queryKeys.users.details(), id] as const,
  },

  // Incident-related queries
  incidents: {
    all: ["incidents"] as const,
    lists: () => [...queryKeys.incidents.all, "list"] as const,
    list: (filters: Record<string, unknown>) => [...queryKeys.incidents.lists(), filters] as const,
    details: () => [...queryKeys.incidents.all, "detail"] as const,
    detail: (id: string) => [...queryKeys.incidents.details(), id] as const,
  },
} as const;

/**
 * Cache invalidation helpers
 */
export const cacheUtils = {
  // Invalidate all risk-related queries
  invalidateRisks: (queryClient: QueryClient) => {
    return queryClient.invalidateQueries({ queryKey: queryKeys.risks.all });
  },

  // Invalidate organization-specific data
  invalidateOrganization: (queryClient: QueryClient, orgId: string) => {
    return queryClient.invalidateQueries({ queryKey: queryKeys.organization.all(orgId) });
  },

  // Prefetch critical data
  prefetchRisks: async (
    queryClient: QueryClient,
    orgId: string,
    fetchFn: () => Promise<unknown>
  ) => {
    return queryClient.prefetchQuery({
      queryKey: queryKeys.organization.risks(orgId),
      queryFn: fetchFn,
      staleTime: 2 * 60 * 1000, // 2 minutes for prefetched data
    });
  },
};
