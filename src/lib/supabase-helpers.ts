import { supabase } from "@/integrations/supabase/client";
import { RiskTemplate } from "@/types";
export const transformRiskTemplate = (data: unknown): RiskTemplate => {
  return {
    id: data.id,
    name: data.name,
    category: data.categories?.name ?? "Uncategorized",
    categoryId: data.category_id,
    organizationId: data.organization_id,
    description: data.description,
    defaultLikelihood: data.default_likelihood,
    defaultImpact: data.default_impact,
    suggestedMitigationPlan: data.suggested_mitigation_plan,
    createdAt: new Date(data.created_at),
    createdBy: data.created_by,
  };
};
export const createRiskFromTemplate = async (
  templateId: string,
  customData: {
    title: string;
    description?: string;
    ownerId: string;
    categoryId?: string;
    organizationId: string;
  }
) => {
  try {
    const { data: template, error: templateError } = await supabase
      .from("risk_templates")
      .select("*")
      .eq("id", templateId)
      .single();
    if (templateError) throw templateError;
    // Create risk based on template
    const { data: risk, error: riskError } = await supabase
      .from("risks")
      .insert({
        title: customData.title,
        description: customData.description || template.description,
        category_id: customData.categoryId || template.category_id,
        owner_id: customData.ownerId,
        organization_id: customData.organizationId,
        likelihood: template.default_likelihood,
        impact: template.default_impact,
        inherent_likelihood: template.default_likelihood,
        inherent_impact: template.default_impact,
        severity: "Medium", // Will be calculated
        inherent_severity: "Medium", // Will be calculated
        status: "Identified",
        template_id: templateId,
        created_by: customData.ownerId,
      })
      .select()
      .single();
    if (riskError) throw riskError;
    return { success: true, data: risk };
  } catch (error) {
    return { success: false, error };
  }
};
