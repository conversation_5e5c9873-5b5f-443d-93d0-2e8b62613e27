import { supabase } from "@/integrations/supabase/client";
import { UserRole } from "@/types";

export interface InviteCodeData {
  id: string;
  code: string;
  role: UserRole;
  organizationId: string;
  invitedEmail: string;
  expiresAt?: string;
  isValid: boolean;
}

/**
 * Generate a cryptographically secure invite code
 */

export const generateInviteCode = (): string => {
  // Use crypto.getRandomValues for secure random generation
  const array = new Uint8Array(8);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(36))
    .join("")
    .substring(0, 8)
    .toUpperCase();
};

/**
 * Create an invite code in the database
 */
interface InviteCodeInsertData {
  code: string;
  role: UserRole;
  created_by: string;
  organization_id: string;
  invited_email: string;
  is_valid: boolean;
  expires_at?: string;
}

export const createInviteCode = async (
  email: string,
  role: UserRole,
  organizationId: string,
  createdBy: string,
  expiresAt?: Date
): Promise<{ data: InviteCodeData | null; error: string | null }> => {
  try {
    const code = generateInviteCode();

    const insertData: InviteCodeInsertData = {
      code,
      role,
      created_by: createdBy,
      organization_id: organizationId,
      invited_email: email,
      is_valid: true,
    };

    if (expiresAt) {
      insertData.expires_at = expiresAt.toISOString();
    }

    const { data, error } = await supabase
      .from("invite_codes")
      .insert(insertData)
      .select()
      .single();

    if (error) {
      return { data: null, error: error.message };
    }

    return {
      data: {
        id: data.id,
        code: data.code,
        role: data.role as UserRole,
        organizationId: data.organization_id,
        invitedEmail: data.invited_email,
        expiresAt: data.expires_at,
        isValid: data.is_valid,
      },
      error: null,
    };
  } catch (error) {
    return {
      data: null,
      error: error instanceof Error ? error.message : "Failed to create invite code",
    };
  }
};

/**
 * Validate an invite code
 */

export const validateInviteCode = async (
  code: string
): Promise<{ valid: boolean; data?: InviteCodeData; error?: string }> => {
  try {
    const { data, error } = await supabase
      .from("invite_codes")
      .select("*")
      .eq("code", code)
      .eq("is_valid", true)
      .single();

    if (error) {
      return { valid: false, error: "Invalid invite code" };
    }

    // Check if expired
    if (data.expires_at && new Date(data.expires_at) < new Date()) {
      return { valid: false, error: "Invite code has expired" };
    }

    return {
      valid: true,
      data: {
        id: data.id,
        code: data.code,
        role: data.role as UserRole,
        organizationId: data.organization_id,
        invitedEmail: data.invited_email,
        expiresAt: data.expires_at,
        isValid: data.is_valid,
      },
    };
  } catch (error) {
    return {
      valid: false,
      error: error instanceof Error ? error.message : "Failed to validate invite code",
    };
  }
};

/**
 * Mark an invite code as used
 */

export const markInviteCodeAsUsed = async (
  codeId: string,
  usedBy: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { error } = await supabase
      .from("invite_codes")
      .update({
        is_valid: false,
        used_by: usedBy,
        used_at: new Date().toISOString(),
      })
      .eq("id", codeId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to mark invite code as used",
    };
  }
};
