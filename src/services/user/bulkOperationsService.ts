import { UserRole } from "@/types";
import { createInviteCode } from "./inviteCodeManagementService";
import { createUserInvitation } from "./userInvitationManagementService";

export interface InviteResult {
  email: string;
  success: boolean;
  inviteCode?: string;
  error?: string;
}

export interface BulkInviteRequest {
  emails: string[];
  role: UserRole;
  organizationId: string;
  invitedBy: string;
}

/**
 * Create bulk invitations with invite codes
 */

export const createBulkInvitations = async (
  emails: string[],
  role: UserRole,
  organizationId: string,
  invitedBy: string
): Promise<{ results: InviteResult[]; error?: string }> => {
  try {
    const results: InviteResult[] = [];

    for (const email of emails) {
      try {
        // Create invite code
        const { data: inviteCodeData, error: inviteError } = await createInviteCode(
          email,
          role,
          organizationId,
          invitedBy
        );

        if (inviteError || !inviteCodeData) {
          results.push({
            email,
            success: false,
            error: `Failed to create invite code: ${inviteError}`,
          });
          continue;
        }

        // Create user invitation record
        const { success, error: invitationError } = await createUserInvitation({
          email,
          role,
          inviteCodeId: inviteCodeData.id,
          organizationId,
          invitedBy,
        });

        if (!success) {
          results.push({
            email,
            success: false,
            error: `Failed to create invitation record: ${invitationError}`,
          });
          continue;
        }

        results.push({
          email,
          success: true,
          inviteCode: inviteCodeData.code,
        });
      } catch (error) {
        results.push({
          email,
          success: false,
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    return { results };
  } catch (error) {
    return {
      results: [],
      error: error instanceof Error ? error.message : "Failed to create bulk invitations",
    };
  }
};

/**
 * Process bulk operations with progress tracking
 */

export const processBulkOperation = async <T, R>(
  items: T[],
  processor: (item: T) => Promise<R>,
  onProgress?: (completed: number, total: number) => void
): Promise<R[]> => {
  const results: R[] = [];

  for (let i = 0; i < items.length; i++) {
    const result = await processor(items[i]);
    results.push(result);

    if (onProgress) {
      onProgress(i + 1, items.length);
    }
  }

  return results;
};

/**
 * Batch items into chunks for processing
 */

export const batchItems = <T>(items: T[], batchSize: number = 10): T[][] => {
  const batches: T[][] = [];

  for (let i = 0; i < items.length; i += batchSize) {
    batches.push(items.slice(i, i + batchSize));
  }

  return batches;
};
