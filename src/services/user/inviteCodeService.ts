import { UserR<PERSON> } from "@/types";
import { supabase } from "@/integrations/supabase/client";
/**
 * Generates an invite code for a new user
 */
export const generateInviteCode = async (
  role: UserRole,
  createdBy: string,
  expiresAt?: Date
): Promise<{ code: string | null; error: Error | null }> => {
  try {
    // Get the current user's organization ID
    const { data: profile, error: profileError } = await supabase
      .from("profiles")
      .select("organization_id")
      .eq("id", createdBy)
      .single();
    if (profileError || !profile?.organization_id) {
      return {
        code: null,
        error: new Error("User must be part of an organization to generate invite codes"),
      };
    }
    // Generate a cryptographically secure random code
    const array = new Uint8Array(8);
    crypto.getRandomValues(array);
    const code = Array.from(array, byte => byte.toString(36))
      .join("")
      .substring(0, 8)
      .toUpperCase();
    interface InviteCodeInsertData {
      code: string;
      role: UserRole;
      created_by: string;
      organization_id: string;
      is_valid: boolean;
      expires_at?: string;
    }
    // Prepare the insert data
    const insertData: InviteCodeInsertData = {
      code,
      role,
      created_by: createdBy,
      organization_id: profile.organization_id,
      is_valid: true,
    };
    // Add expiration date if provided
    if (expiresAt) {
      insertData.expires_at = expiresAt.toISOString();
    }
    // Store the invite code in the database
    const { error } = await supabase.from("invite_codes").insert(insertData);
    if (error) {
      return { code: null, error };
    }
    return { code, error: null };
  } catch (error) {
    return { code: null, error: error as Error };
  }
};
