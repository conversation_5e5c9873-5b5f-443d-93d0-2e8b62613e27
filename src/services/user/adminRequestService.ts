import { supabase } from "@/integrations/supabase/client";
import { UserRole } from "@/types";
/**
 * Fetches admin requests from the database
 */
export const fetchAdminRequests = async (): Promise<{
  data: unknown[] | null;
  error: Error | null;
}> => {
  try {
    // Fetch the admin requests
    const { data: requestsData, error: requestsError } = await supabase
      .from("admin_requests")
      .select("*")
      .order("created_at", { ascending: false });
    if (requestsError) {
      return { data: null, error: requestsError };
    }
    if (!requestsData || requestsData.length === 0) {
      return { data: [], error: null };
    }
    // Fetch user profiles for each request
    const requestsWithProfiles = await Promise.all(
      requestsData.map(async (request: Record<string, unknown>) => {
        const { data: profileData } = await supabase
          .from("profiles")
          .select("name, email")
          .eq("id", request.user_id)
          .single();
        return {
          ...request,
          user_name: profileData?.name ?? "Unknown User",
          user_email: profileData?.email ?? "No email",
        };
      })
    );
    return { data: requestsWithProfiles, error: null };
  } catch (error) {
    return { data: null, error: error as Error };
  }
};
/**
 * Approves an admin request
 */
export const approveAdminRequest = async (
  requestId: string,
  userId: string,
  reviewerId?: string
): Promise<{ success: boolean; error: Error | null }> => {
  try {
    // 1. Update the admin request status
    const { error: requestError } = await supabase
      .from("admin_requests")
      .update({
        status: "approved",
        reviewer_id: reviewerId,
        feedback: "Your request for admin access has been approved.",
      })
      .eq("id", requestId);
    if (requestError) {
      return { success: false, error: requestError };
    }
    // 2. Update the user role to admin
    const { error: profileError } = await supabase
      .from("profiles")
      .update({ role: UserRole.ADMIN })
      .eq("id", userId);
    if (profileError) {
      return { success: false, error: profileError };
    }
    // 3. Create a notification for the user
    await supabase.from("notifications").insert({
      user_id: userId,
      title: "Admin Request Approved",
      message: "Your request for administrator privileges has been approved.",
      type: "admin_request_approved",
    });
    return { success: true, error: null };
  } catch (error) {
    return { success: false, error: error as Error };
  }
};
/**
 * Rejects an admin request
 */
export const rejectAdminRequest = async (
  requestId: string,
  userId: string,
  reviewerId?: string
): Promise<{ success: boolean; error: Error | null }> => {
  try {
    // Update the admin request status
    const { error: requestError } = await supabase
      .from("admin_requests")
      .update({
        status: "rejected",
        reviewer_id: reviewerId,
        feedback: "Your request for admin access has been rejected.",
      })
      .eq("id", requestId);
    if (requestError) {
      return { success: false, error: requestError };
    }
    // Create a notification for the user
    await supabase.from("notifications").insert({
      user_id: userId,
      title: "Admin Request Rejected",
      message: "Your request for administrator privileges has been rejected.",
      type: "admin_request_rejected",
    });
    return { success: true, error: null };
  } catch (error) {
    return { success: false, error: error as Error };
  }
};
/**
 * Submits a request for admin privileges
 */
export const requestAdminAccess = async (
  userId: string,
  justification: string
): Promise<{ success: boolean; error: Error | null }> => {
  try {
    // Store the admin request in the database
    const { error } = await supabase.from("admin_requests").insert({
      user_id: userId,
      justification,
      status: "pending",
    });
    if (error) {
      return { success: false, error };
    }
    // Get all admin users
    const { data: admins, error: adminsError } = await supabase
      .from("profiles")
      .select("id")
      .eq("role", UserRole.ADMIN)
      .is("deleted_at", null);
    if (!adminsError && admins?.length > 0) {
      // Create notifications for each admin
      const notifications = admins.map(admin => ({
        user_id: admin.id,
        title: "New Admin Request",
        message: `A user has requested admin privileges. Please review the pending requests.`,
        type: "admin_request",
        metadata: { requesterId: userId },
      }));
      await supabase.from("notifications").insert(notifications);
    }
    return { success: true, error: null };
  } catch (error) {
    return { success: false, error: error as Error };
  }
};
