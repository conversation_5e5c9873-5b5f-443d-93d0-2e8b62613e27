import { supabase } from "@/integrations/supabase/client";
import { UserRole } from "@/types";

export interface UserInvitation {
  id: string;
  email: string;
  role: string;
  status: string;
  inviteCodeId?: string;
  emailSentAt?: string;
  createdAt: string;
  invitedBy?: string;
  organizationId: string;
}

export interface CreateInvitationData {
  email: string;
  role: UserRole;
  inviteCodeId: string;
  organizationId: string;
  invitedBy: string;
}

/**
 * Create a user invitation record
 */

export const createUserInvitation = async (
  data: CreateInvitationData
): Promise<{ success: boolean; invitation?: UserInvitation; error?: string }> => {
  try {
    const { data: invitation, error } = await supabase
      .from("user_invitations")
      .insert({
        email: data.email,
        role: data.role,
        invite_code_id: data.inviteCodeId,
        organization_id: data.organizationId,
        invited_by: data.invitedBy,
        status: "pending",
      })
      .select()
      .single();

    if (error) {
      return { success: false, error: error.message };
    }

    return {
      success: true,
      invitation: {
        id: invitation.id,
        email: invitation.email,
        role: invitation.role,
        status: invitation.status,
        inviteCodeId: invitation.invite_code_id,
        emailSentAt: invitation.email_sent_at,
        createdAt: invitation.created_at,
        invitedBy: invitation.invited_by,
        organizationId: invitation.organization_id,
      },
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create user invitation",
    };
  }
};

/**
 * Fetch user invitations for an organization
 */

export const fetchUserInvitations = async (
  organizationId: string
): Promise<{ data: UserInvitation[] | null; error: Error | null }> => {
  try {
    const { data, error } = await supabase
      .from("user_invitations")
      .select(
        `
        id,
        email,
        role,
        status,
        invite_code_id,
        email_sent_at,
        created_at,
        invited_by,
        organization_id
      `
      )
      .eq("organization_id", organizationId)
      .order("created_at", { ascending: false });

    if (error) {
      return { data: null, error };
    }

    const mappedData: UserInvitation[] = (data ?? []).map(item => ({
      id: item.id,
      email: item.email,
      role: item.role,
      status: item.status,
      inviteCodeId: item.invite_code_id,
      emailSentAt: item.email_sent_at,
      createdAt: item.created_at,
      invitedBy: item.invited_by,
      organizationId: item.organization_id,
    }));

    return { data: mappedData, error: null };
  } catch (error) {
    return { data: null, error: error as Error };
  }
};

/**
 * Update invitation status
 */

export const updateInvitationStatus = async (
  invitationId: string,
  status: string,
  additionalData?: Partial<UserInvitation>
): Promise<{ success: boolean; error?: string }> => {
  try {
    const updateData = {
      status,
      updated_at: new Date().toISOString(),
      ...additionalData,
    };

    const { error } = await supabase
      .from("user_invitations")
      .update(updateData)
      .eq("id", invitationId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update invitation",
    };
  }
};

/**
 * Delete an invitation
 */

export const deleteInvitation = async (
  invitationId: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { error } = await supabase.from("user_invitations").delete().eq("id", invitationId);

    if (error) {
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete invitation",
    };
  }
};
