

// Barrel file to re-export all user-related services

// Export everything from userProfileService except transferRiskOwnership
export { 
  fetchUsers, 
  updateUserRole, 
  deleteUser, 
  getUserRisks, 
  getRiskOwnerCandidates 
} from './userProfileService';

export * from './adminRequestService';

// Export everything from riskTransferService (including transferRiskOwnership)
export * from './riskTransferService';

export * from './bulkInviteService';

// New focused services
export * from './emailValidationService';
export * from './inviteCodeManagementService';
export * from './userInvitationManagementService';
export * from './bulkOperationsService';

// Note: inviteCodeService is not exported to avoid conflict with inviteCodeManagementService
// Note: We export transferRiskOwnership from riskTransferService to avoid conflicts

