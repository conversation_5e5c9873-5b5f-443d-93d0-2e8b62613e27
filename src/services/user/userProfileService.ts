import { supabase } from "@/integrations/supabase/client";
import { User, UserRole } from "@/types";
/**
 * Get the current user's organization ID
 */
const getUserOrganizationId = async (): Promise<string | null> => {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) return null;
  const { data: profile } = await supabase
    .from("profiles")
    .select("organization_id")
    .eq("id", user.id)
    .single();
  return profile?.organization_id ?? null;
};
/**
 * Fetch all users in the current user's organization
 */
export const fetchUsers = async () => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("organization_id", organizationId)
      .is("deleted_at", null)
      .order("name");
    if (error) {
      return { data: null, error };
    }
    const users: User[] = data.map(profile => ({
      id: profile.id,
      name: profile.name,
      email: profile.email,
      role: profile.role as UserRole,
      department: profile.department,
      organizationId: profile.organization_id,
      createdAt: new Date(profile.created_at),
      updatedAt: new Date(profile.updated_at),
    }));
    return { data: users, error: null };
  } catch (error: unknown) {
    return { data: null, error };
  }
};
/**
 * Update user role (with organization verification)
 */
export const updateUserRole = async (userId: string, newRole: UserRole) => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    // First verify the user belongs to the same organization
    const { data: userCheck, error: checkError } = await supabase
      .from("profiles")
      .select("organization_id")
      .eq("id", userId)
      .single();
    if (checkError || !userCheck || userCheck.organization_id !== organizationId) {
      throw new Error("User not found in your organization or access denied");
    }
    const { data, error } = await supabase
      .from("profiles")
      .update({
        role: newRole,
        updated_at: new Date().toISOString(),
      })
      .eq("id", userId)
      .eq("organization_id", organizationId)
      .select()
      .single();
    if (error) {
      return { success: false, error };
    }
    return { success: true, data, error: null };
  } catch (error: unknown) {
    return { success: false, error };
  }
};
/**
 * Delete user (with organization verification)
 */
export const deleteUser = async (userId: string) => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    // First verify the user belongs to the same organization
    const { data: userCheck, error: checkError } = await supabase
      .from("profiles")
      .select("organization_id")
      .eq("id", userId)
      .single();
    if (checkError || !userCheck || userCheck.organization_id !== organizationId) {
      throw new Error("User not found in your organization or access denied");
    }
    // Soft delete by setting deleted_at timestamp
    const { data, error } = await supabase
      .from("profiles")
      .update({
        deleted_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq("id", userId)
      .eq("organization_id", organizationId)
      .select()
      .single();
    if (error) {
      return { success: false, error };
    }
    return { success: true, data, error: null };
  } catch (error: unknown) {
    return { success: false, error };
  }
};
/**
 * Get risks owned by a specific user (organization scoped)
 */
export const getUserRisks = async (userId: string) => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    // First verify the user belongs to the same organization
    const { data: userCheck, error: checkError } = await supabase
      .from("profiles")
      .select("organization_id")
      .eq("id", userId)
      .single();
    if (checkError || !userCheck || userCheck.organization_id !== organizationId) {
      throw new Error("User not found in your organization or access denied");
    }
    const { data, error } = await supabase
      .from("risks")
      .select("id, title, owner_id")
      .eq("owner_id", userId)
      .eq("organization_id", organizationId);
    if (error) {
      return { data: null, error };
    }
    return { data, error: null };
  } catch (error: unknown) {
    return { data: null, error };
  }
};
/**
 * Get candidates for risk ownership transfer (organization scoped)
 */
export const getRiskOwnerCandidates = async (excludeUserId: string) => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("organization_id", organizationId)
      .neq("id", excludeUserId)
      .in("role", ["admin", "risk_owner"])
      .is("deleted_at", null)
      .order("name");
    if (error) {
      return { data: null, error };
    }
    const candidates: User[] = data.map(profile => ({
      id: profile.id,
      name: profile.name,
      email: profile.email,
      role: profile.role as UserRole,
      department: profile.department,
      organizationId: profile.organization_id,
      createdAt: new Date(profile.created_at),
      updatedAt: new Date(profile.updated_at),
    }));
    return { data: candidates, error: null };
  } catch (error: unknown) {
    return { data: null, error };
  }
};
/**
 * Transfer risk ownership (organization scoped)
 */
export const transferRiskOwnership = async (
  oldOwnerId: string,
  newOwnerId: string,
  riskIds: string[]
) => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    // Verify both users belong to the same organization
    const { data: usersCheck, error: checkError } = await supabase
      .from("profiles")
      .select("id, organization_id")
      .in("id", [oldOwnerId, newOwnerId]);
    if (checkError || !usersCheck || usersCheck.length !== 2) {
      throw new Error("Users not found or access denied");
    }
    const invalidUsers = usersCheck.filter(user => user.organization_id !== organizationId);
    if (invalidUsers.length > 0) {
      throw new Error("Users not found in your organization");
    }
    // Transfer the risks
    const { data, error } = await supabase
      .from("risks")
      .update({
        owner_id: newOwnerId,
        updated_at: new Date().toISOString(),
      })
      .in("id", riskIds)
      .eq("organization_id", organizationId)
      .eq("owner_id", oldOwnerId);
    if (error) {
      return { success: false, error };
    }
    return { success: true, data, error: null };
  } catch (error: unknown) {
    return { success: false, error };
  }
};
