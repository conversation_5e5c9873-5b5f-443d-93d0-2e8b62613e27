import { User, UserRole } from "@/types";
import { supabase } from "@/integrations/supabase/client";
/**
 * Transfers ownership of multiple risks from one user to another
 */
export const transferRiskOwnership = async (
  oldOwnerId: string,
  newOwnerId: string,
  riskIds: string[]
): Promise<{ success: boolean; error: Error | null }> => {
  try {
    // Get the current user's session to record who made the transfer
    const { data: sessionData } = await supabase.auth.getSession();
    const transferredBy = sessionData.session?.user.id;
    // Update all the specified risks with the new owner
    const { error: updateError } = await supabase
      .from("risks")
      .update({ owner_id: newOwnerId })
      .in("id", riskIds);
    if (updateError) {
      return { success: false, error: updateError };
    }
    // Record the transfers in the risk_ownership_transfers table
    const transferRecords = riskIds.map(riskId => ({
      risk_id: riskId,
      old_owner_id: oldOwnerId,
      new_owner_id: newOwnerId,
      transferred_by: transferredBy,
    }));
    const { error: transferError } = await supabase
      .from("risk_ownership_transfers")
      .upsert(transferRecords);
    if (transferError) {
      // Condition handled
    }
    return { success: true, error: null };
  } catch (error) {
    return { success: false, error: error as Error };
  }
};
/**
 * Fetches pending risk ownership transfers
 */
export const getPendingRiskTransfers = async (): Promise<{
  data: unknown[] | null;
  error: Error | null;
  owners: User[] | null;
}> => {
  try {
    // Get transfers where new_owner_id is null (not yet assigned)
    const { data, error } = await supabase
      .from("risk_ownership_transfers")
      .select("*")
      .is("new_owner_id", null);
    if (error) {
      return { data: null, error, owners: null };
    }
    if (!data || data.length === 0) {
      return { data: [], error: null, owners: [] };
    }
    // Get risk titles
    const enhancedData = await Promise.all(
      data.map(async (transfer: Record<string, unknown>) => {
        // Get risk details
        const { data: riskData } = await supabase
          .from("risks")
          .select("title")
          .eq("id", transfer.risk_id)
          .single();
        // Get old owner details
        const { data: ownerData } = await supabase
          .from("profiles")
          .select("name")
          .eq("id", transfer.old_owner_id)
          .single();
        return {
          ...transfer,
          risk_title: riskData?.title ?? "Unknown Risk",
          old_owner_name: ownerData?.name ?? "Unknown User",
        };
      })
    );
    // Get eligible owners for the dropdown
    const { data: ownersData } = await supabase
      .from("profiles")
      .select("id, name, email, role, avatar_url, department")
      .in("role", [UserRole.ADMIN, UserRole.RISK_OWNER])
      .is("deleted_at", null);
    const formattedOwners =
      ownersData?.map(profile => ({
        id: profile.id,
        name: profile.name,
        email: profile.email,
        role: profile.role as UserRole,
        department: profile.department,
        avatar: profile.avatar_url,
      })) || [];
    return {
      data: enhancedData,
      error: null,
      owners: formattedOwners,
    };
  } catch (error) {
    return { data: null, error: error as Error, owners: null };
  }
};
/**
 * Completes a risk transfer by assigning a new owner
 */
export const completeRiskTransfer = async (
  transferId: string,
  riskId: string,
  newOwnerId: string
): Promise<{ success: boolean; error: Error | null }> => {
  try {
    // Get the current user's session
    const { data: sessionData } = await supabase.auth.getSession();
    const transferredBy = sessionData.session?.user.id;
    // Update the risk with the new owner
    const { error: updateError } = await supabase
      .from("risks")
      .update({ owner_id: newOwnerId })
      .eq("id", riskId);
    if (updateError) {
      return { success: false, error: updateError };
    }
    // Update the transfer record
    const { error: transferError } = await supabase
      .from("risk_ownership_transfers")
      .update({
        new_owner_id: newOwnerId,
        transferred_by: transferredBy,
        transferred_at: new Date().toISOString(),
      })
      .eq("id", transferId);
    if (transferError) {
      // Condition handled
    }
    return { success: true, error: null };
  } catch (error) {
    return { success: false, error: error as Error };
  }
};
