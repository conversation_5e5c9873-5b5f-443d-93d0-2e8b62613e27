/**
 * Email validation and parsing service
 * Handles email address validation, parsing, and normalization
 */

export interface EmailValidationResult {
  valid: string[];
  invalid: string[];
}

/**
 * Parse emails from a text input (comma, semicolon, or newline separated)
 */

export const parseEmails = (emailText: string): string[] => {
  if (!emailText.trim()) return [];

  // Split by common separators and clean up
  const emails = emailText
    .split(/[,;\n\r\t]+/)
    .map(email => email.trim())
    .filter(email => email.length > 0);

  return [...new Set(emails)]; // Remove duplicates
};

/**
 * Validate email addresses using RFC 5322 compliant regex
 */

export const validateEmails = (emails: string[]): EmailValidationResult => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  const valid: string[] = [];
  const invalid: string[] = [];

  emails.forEach(email => {
    if (emailRegex.test(email)) {
      valid.push(email);
    } else {
      invalid.push(email);
    }
  });

  return { valid, invalid };
};

/**
 * Normalize email address (lowercase, trim)
 */

export const normalizeEmail = (email: string): string => {
  return email.trim().toLowerCase();
};

/**
 * Validate and parse emails in one operation
 */

export const validateAndParseEmails = (emailText: string): EmailValidationResult => {
  const parsed = parseEmails(emailText);
  const normalized = parsed.map(normalizeEmail);
  return validateEmails(normalized);
};
