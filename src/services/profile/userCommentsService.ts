import { supabase } from "@/integrations/supabase/client";
/**
 * Fetch comments related to risks owned by the user or incidents reported by the user
 */
export const fetchUserRelatedComments = async (userId: string) => {
  // First, get all risks owned by this user
  const { data: userRisks, error: risksError } = await supabase
    .from("risks")
    .select("id, title")
    .eq("owner_id", userId);
  if (risksError) {
    throw risksError;
  }
  // Next, get all incidents reported by this user
  const { data: userIncidents, error: incidentsError } = await supabase
    .from("incidents")
    .select("id, title")
    .eq("reporter_id", userId);
  if (incidentsError) {
    throw incidentsError;
  }
  const riskIds = userRisks.map(risk => risk.id);
  const incidentIds = userIncidents.map(incident => incident.id);
  // No risks or incidents, return empty array
  if (riskIds.length === 0 && incidentIds.length === 0) {
    return [];
  }
  // Fetch risk comments and incident comments separately, then combine the results
  let allComments = [];
  if (riskIds.length > 0) {
    const { data: riskComments, error: riskCommentsError } = await supabase
      .from("comments")
      .select(
        `
        *,
        profiles:user_id (name, avatar_url)
      `
      )
      .eq("entity_type", "risk")
      .in("entity_id", riskIds)
      .is("deleted_at", null)
      .order("created_at", { ascending: false });
    if (riskCommentsError) {
      throw riskCommentsError;
    }
    // Format risk comments with additional entity information
    const formattedRiskComments = riskComments.map(comment => {
      const relatedRisk = userRisks.find(risk => risk.id === comment.entity_id);
      return {
        id: comment.id,
        content: comment.content,
        entityType: comment.entity_type,
        entityId: comment.entity_id,
        entityTitle: relatedRisk?.title ?? "Unknown Risk",
        userId: comment.user_id,
        userName: comment.profiles?.name ?? "Unknown User",
        userAvatar: comment.profiles?.avatar_url,
        createdAt: new Date(comment.created_at),
      };
    });
    allComments = allComments.concat(formattedRiskComments);
  }
  if (incidentIds.length > 0) {
    const { data: incidentComments, error: incidentCommentsError } = await supabase
      .from("comments")
      .select(
        `
        *,
        profiles:user_id (name, avatar_url)
      `
      )
      .eq("entity_type", "incident")
      .in("entity_id", incidentIds)
      .is("deleted_at", null)
      .order("created_at", { ascending: false });
    if (incidentCommentsError) {
      throw incidentCommentsError;
    }
    // Format incident comments with additional entity information
    const formattedIncidentComments = incidentComments.map(comment => {
      const relatedIncident = userIncidents.find(incident => incident.id === comment.entity_id);
      return {
        id: comment.id,
        content: comment.content,
        entityType: comment.entity_type,
        entityId: comment.entity_id,
        entityTitle: relatedIncident?.title ?? "Unknown Incident",
        userId: comment.user_id,
        userName: comment.profiles?.name ?? "Unknown User",
        userAvatar: comment.profiles?.avatar_url,
        createdAt: new Date(comment.created_at),
      };
    });
    allComments = allComments.concat(formattedIncidentComments);
  }
  // Sort all comments by creation date (newest first)
  allComments.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  return allComments;
};
