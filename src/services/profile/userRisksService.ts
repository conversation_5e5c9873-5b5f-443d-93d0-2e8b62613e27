import { supabase } from "@/integrations/supabase/client";
/**
 * Fetch summary of risks owned by the current user
 */
export const fetchUserRisksSummary = async (userId: string) => {
  const { data, error } = await supabase
    .from("risks")
    .select(
      `
      id,
      title,
      status,
      severity,
      updated_at,
      owner_id,
      categories:risk_categories(name)
    `
    )
    .eq("owner_id", userId)
    .order("updated_at", { ascending: false });
  if (error) {
    throw error;
  }
  return data;
};
/**
 * Calculate risk statistics for the user
 */
export const calculateRiskStats = (risks: unknown[]) => {
  const statsByStatus = risks.reduce(
    (acc, risk) => {
      acc[risk.status] = (acc[risk.status] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );
  const statsBySeverity = risks.reduce(
    (acc, risk) => {
      acc[risk.severity] = (acc[risk.severity] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );
  return {
    totalRisks: risks.length,
    byStatus: statsByStatus,
    bySeverity: statsBySeverity,
  };
};
