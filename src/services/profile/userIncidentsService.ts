import { supabase } from "@/integrations/supabase/client";
/**
 * Fetch summary of incidents reported by the current user
 */
export const fetchUserIncidentsSummary = async (userId: string) => {
  const { data, error } = await supabase
    .from("incidents")
    .select(
      `
      id,
      title, 
      status,
      severity,
      date,
      reporter_id,
      risks:related_risk_id(title)
    `
    )
    .eq("reporter_id", userId)
    .order("date", { ascending: false });
  if (error) {
    throw error;
  }
  return data;
};
/**
 * Calculate incident statistics for the user
 */
export const calculateIncidentStats = (incidents: unknown[]) => {
  const statsByStatus = incidents.reduce(
    (acc, incident) => {
      acc[incident.status] = (acc[incident.status] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );
  const statsBySeverity = incidents.reduce(
    (acc, incident) => {
      acc[incident.severity] = (acc[incident.severity] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );
  return {
    totalIncidents: incidents.length,
    byStatus: statsByStatus,
    bySeverity: statsBySeverity,
  };
};
