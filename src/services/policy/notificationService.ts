import { supabase } from "@/integrations/supabase/client";
interface NotificationPayload {
  type: string;
  title: string;
  message: string;
  metadata?: Record<string, unknown>;
}
/**
 * Send a notification to admin users
 */
export const notifyAdmins = async (payload: NotificationPayload): Promise<boolean> => {
  try {
    // Get all admin users
    const { data: admins, error: adminsError } = await supabase
      .from("profiles")
      .select("id, name, email")
      .eq("role", "admin");
    if (adminsError) throw adminsError;
    if (!admins || admins.length === 0) {
      return false;
    }
    interface NotificationInsert {
      user_id: string;
      type: string;
      title: string;
      message: string;
      metadata?: Record<string, unknown>;
      read: boolean;
    }
    // Create notifications for each admin
    const notifications: NotificationInsert[] = admins.map(admin => ({
      user_id: admin.id,
      type: payload.type,
      title: payload.title,
      message: payload.message,
      metadata: payload.metadata,
      read: false,
    }));
    const { error } = await supabase.from("notifications").insert(notifications);
    if (error) throw error;
    // Broadcast to real-time channel for immediate updates
    const channel = supabase.channel("admin-notifications");
    await channel.send({
      type: "broadcast",
      event: "policy_request_notification",
      payload,
    });
    return true;
  } catch (error: unknown) {
    return false;
  }
};
/**
 * Get all notifications for a user
 */
export const getUserNotifications = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from("notifications")
      .select("*")
      .eq("user_id", userId)
      .order("created_at", { ascending: false });
    if (error) throw error;
    return data ?? [];
  } catch (error: unknown) {
    return [];
  }
};
/**
 * Mark a notification as read
 */
export const markNotificationAsRead = async (notificationId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("notifications")
      .update({ read: true })
      .eq("id", notificationId);
    if (error) throw error;
    return true;
  } catch (error: unknown) {
    return false;
  }
};
/**
 * Mark all notifications as read for a user
 */
export const markAllNotificationsAsRead = async (userId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from("notifications")
      .update({ read: true })
      .eq("user_id", userId)
      .eq("read", false);
    if (error) throw error;
    return true;
  } catch (error: unknown) {
    return false;
  }
};
