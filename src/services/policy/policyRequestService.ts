import { supabase } from "@/integrations/supabase/client";
import { PolicyRequest, PolicyRequestFromDB } from "@/types/policy";
import { PolicyRequestApiResponse } from "@/types/api";
import { mapPolicyRequestFromDB } from "@/utils/policyMappers";
/**
 * Get the current user's organization ID
 */
const getUserOrganizationId = async (): Promise<string | null> => {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) return null;
  const { data: profile } = await supabase
    .from("profiles")
    .select("organization_id")
    .eq("id", user.id)
    .single();
  return profile?.organization_id ?? null;
};
/**
 * Fetch all policy requests for the user's organization
 */
export const fetchPolicyRequests = async (): Promise<PolicyRequest[]> => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    const { data, error } = await supabase
      .from("policy_requests")
      .select("*")
      .eq("organization_id", organizationId)
      .order("created_at", { ascending: false });
    if (error) throw error;
    return (data as PolicyRequestFromDB[]).map(mapPolicyRequestFromDB);
  } catch (error: unknown) {
    throw error;
  }
};
/**
 * Create a new policy request (ensures organization isolation)
 */
export const createPolicyRequest = async (
  request: {
    title: string;
    description: string;
    category: string;
    justification: string;
    reference_document_url?: string;
  },
  userId: string
): Promise<PolicyRequest> => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    const requestForDB = {
      title: request.title,
      description: request.description,
      reason: request.justification,
      status: "pending",
      requester_id: userId,
      organization_id: organizationId,
      reference_document_url: request.reference_document_url,
    };
    const { data, error } = await supabase
      .from("policy_requests")
      .insert(requestForDB)
      .select()
      .single();
    if (error) throw error;
    return mapPolicyRequestFromDB(data as PolicyRequestFromDB);
  } catch (error: unknown) {
    throw error;
  }
};
/**
 * Update policy request status (with organization verification)
 */
export const updatePolicyRequestStatus = async (
  requestId: string,
  status: string,
  feedback?: string,
  reviewerId?: string
): Promise<PolicyRequest> => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    // First verify the request belongs to the user's organization
    const { data: existingRequest, error: fetchError } = await supabase
      .from("policy_requests")
      .select("id")
      .eq("id", requestId)
      .eq("organization_id", organizationId)
      .single();
    if (fetchError || !existingRequest) {
      throw new Error("Policy request not found or access denied");
    }
    const updates: Partial<PolicyRequestApiResponse> = {
      status,
      updated_at: new Date().toISOString(),
    };
    if (feedback) updates.feedback = feedback;
    if (reviewerId) updates.reviewer_id = reviewerId;
    const { data, error } = await supabase
      .from("policy_requests")
      .update(updates)
      .eq("id", requestId)
      .eq("organization_id", organizationId)
      .select()
      .single();
    if (error) throw error;
    return mapPolicyRequestFromDB(data as PolicyRequestFromDB);
  } catch (error: unknown) {
    throw error;
  }
};
/**
 * Delete policy request (with organization verification)
 */
export const deletePolicyRequest = async (requestId: string): Promise<void> => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    const { error } = await supabase
      .from("policy_requests")
      .delete()
      .eq("id", requestId)
      .eq("organization_id", organizationId);
    if (error) throw error;
  } catch (error: unknown) {
    throw error;
  }
};
