import { supabase } from "@/integrations/supabase/client";
import { Policy, PolicyFromDB, PolicyFormValues } from "@/types/policy";
import { PolicyApiResponse } from "@/types/api";
import { mapPolicyFromDB } from "@/utils/policyMappers";
import { auditLog, AuditEventType } from "@/services/auditLoggingService";
/**
 * Get the current user's organization ID
 */
const getUserOrganizationId = async (): Promise<string | null> => {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) return null;
  const { data: profile } = await supabase
    .from("profiles")
    .select("organization_id")
    .eq("id", user.id)
    .single();
  return profile?.organization_id ?? null;
};
/**
 * Fetch all published policies for the user's organization
 */
export const fetchPublishedPolicies = async (): Promise<Policy[]> => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    const { data, error } = await supabase
      .from("policies")
      .select("*")
      .eq("status", "published")
      .eq("organization_id", organizationId)
      .order("created_at", { ascending: false });
    if (error) throw error;
    return (data as PolicyFromDB[]).map(mapPolicyFromDB);
  } catch (error: unknown) {
    throw error;
  }
};
/**
 * Fetch all policies for the user's organization (for admin)
 */
export const fetchAllPolicies = async (): Promise<Policy[]> => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    const { data, error } = await supabase
      .from("policies")
      .select("*")
      .eq("organization_id", organizationId)
      .order("created_at", { ascending: false });
    if (error) throw error;
    return (data as PolicyFromDB[]).map(mapPolicyFromDB);
  } catch (error: unknown) {
    throw error;
  }
};
/**
 * Fetch a specific policy by ID (with organization verification)
 */
export const fetchPolicyById = async (id: string): Promise<Policy | null> => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    const { data, error } = await supabase
      .from("policies")
      .select("*")
      .eq("id", id)
      .eq("organization_id", organizationId)
      .single();
    if (error) {
      if (error.code === "PGRST116") {
        return null; // No rows returned
      }
      throw error;
    }
    return mapPolicyFromDB(data as PolicyFromDB);
  } catch (error: unknown) {
    throw error;
  }
};
/**
 * Create a new policy
 */
export const createPolicy = async (policy: PolicyFormValues, userId: string): Promise<Policy> => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    const policyForDB = {
      title: policy.title,
      description: policy.description,
      category: policy.category,
      status: policy.status,
      version: "1.0", // Default for new policies
      created_by: userId,
      organization_id: organizationId,
      effective_date: policy.effectiveDate?.toISOString(),
      document_url: null, // Will be updated separately if needed
    };
    const { data, error } = await supabase.from("policies").insert(policyForDB).select().single();
    if (error) throw error;
    const createdPolicy = mapPolicyFromDB(data as PolicyFromDB);
    // Log successful policy creation
    await auditLog.policy(AuditEventType.POLICY_CREATED, userId, createdPolicy.id, {
      title: policy.title,
      category: policy.category,
      status: policy.status,
      organizationId,
      timestamp: new Date().toISOString(),
    });
    return createdPolicy;
  } catch (error: unknown) {
    throw error;
  }
};
/**
 * Update an existing policy
 * Now accepts partial updates to a policy
 */
export const updatePolicy = async (
  id: string,
  updates: Partial<PolicyFormValues>,
  updatedBy?: string
): Promise<Policy> => {
  try {
    const organizationId = await getUserOrganizationId();
    const policyUpdatesForDB: Partial<PolicyApiResponse> = {};
    const changes: string[] = [];
    if (updates.title) {
      policyUpdatesForDB.title = updates.title;
      changes.push("title");
    }
    if (updates.description) {
      policyUpdatesForDB.description = updates.description;
      changes.push("description");
    }
    if (updates.category) {
      policyUpdatesForDB.category = updates.category;
      changes.push("category");
    }
    if (updates.status) {
      policyUpdatesForDB.status = updates.status;
      changes.push("status");
    }
    if (updates.documentUrl) {
      policyUpdatesForDB.document_url = updates.documentUrl;
      changes.push("document");
    }
    // If updating a policy, increment the version number
    const { data: currentPolicy } = await supabase
      .from("policies")
      .select("version, title")
      .eq("id", id)
      .single();
    if (currentPolicy) {
      const currentVersion = currentPolicy.version;
      const versionParts = currentVersion.split(".");
      const minorVersion = parseInt(versionParts[1] ?? "0", 10) + 1;
      policyUpdatesForDB.version = `${versionParts[0]}.${minorVersion}`;
      changes.push("version");
    }
    if (updates.effectiveDate) {
      policyUpdatesForDB.effective_date = updates.effectiveDate.toISOString();
      changes.push("effective_date");
    } else if (updates.effectiveDate === null) {
      policyUpdatesForDB.effective_date = null;
      changes.push("effective_date");
    }
    const { data, error } = await supabase
      .from("policies")
      .update(policyUpdatesForDB)
      .eq("id", id)
      .select()
      .single();
    if (error) throw error;
    const updatedPolicy = mapPolicyFromDB(data as PolicyFromDB);
    // Log successful policy update
    if (updatedBy) {
      await auditLog.policy(AuditEventType.POLICY_UPDATED, updatedBy, id, {
        title: currentPolicy?.title || updatedPolicy.title,
        changes,
        newValues: {
          title: updates.title,
          category: updates.category,
          status: updates.status,
          version: policyUpdatesForDB.version,
        },
        organizationId,
        timestamp: new Date().toISOString(),
      });
    }
    return updatedPolicy;
  } catch (error: unknown) {
    throw error;
  }
};
/**
 * Delete a policy
 */
export const deletePolicy = async (id: string, deletedBy?: string): Promise<void> => {
  try {
    const organizationId = await getUserOrganizationId();
    // Get policy details before deletion for audit logging
    let policyTitle: string | undefined;
    if (deletedBy) {
      try {
        const { data: policyData } = await supabase
          .from("policies")
          .select("title, category, status")
          .eq("id", id)
          .single();
        policyTitle = policyData?.title;
      } catch (error) {
        // Continue with deletion even if we can't get policy details
      }
    }
    const { error } = await supabase.from("policies").delete().eq("id", id);
    if (error) throw error;
    // Log successful policy deletion
    if (deletedBy) {
      await auditLog.policy(AuditEventType.POLICY_DELETED, deletedBy, id, {
        title: policyTitle,
        organizationId,
        timestamp: new Date().toISOString(),
      });
    }
  } catch (error: unknown) {
    throw error;
  }
};
