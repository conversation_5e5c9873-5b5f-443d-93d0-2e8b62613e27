import { supabase } from "@/integrations/supabase/client";
/**
 * Get the current user's organization ID
 */
const getUserOrganizationId = async (): Promise<string | null> => {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) return null;
  const { data: profile } = await supabase
    .from("profiles")
    .select("organization_id")
    .eq("id", user.id)
    .single();
  return profile?.organization_id ?? null;
};
/**
 * Get signed URL for policy documents (organization scoped)
 */
export const getSignedUrl = async (
  documentPath: string,
  expiresIn: number = 3600
): Promise<string> => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    // Verify the document belongs to the user's organization
    if (!documentPath.startsWith(`${organizationId}/`)) {
      throw new Error("Access denied - document not in user's organization");
    }
    // For now, return a placeholder signed URL - this would need proper file storage implementation
    return `/uploads/${documentPath}?signed=true&expires=${Date.now() + expiresIn * 1000}`;
  } catch (error: unknown) {
    throw error;
  }
};
/**
 * Get upload URL for policy documents (organization scoped)
 */
export const getUploadUrl = async (fileName: string): Promise<string> => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    // Create organization-scoped path
    const filePath = `${organizationId}/policies/${fileName}`;
    // For now, return a placeholder - this would need proper file storage implementation
    return `/uploads/${filePath}`;
  } catch (error: unknown) {
    throw error;
  }
};
/**
 * Upload policy document (organization scoped)
 */
export const uploadPolicyDocument = async (file: File, policyId?: string): Promise<string> => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    // Create organization-scoped path
    const basePath = policyId
      ? `${organizationId}/policies/${policyId}`
      : `${organizationId}/policies`;
    const filePath = `${basePath}/${file.name}`;
    // For now, return a placeholder URL - this would need proper file storage implementation
    const documentUrl = `/uploads/${filePath}`;
    return documentUrl;
  } catch (error: unknown) {
    throw error;
  }
};
/**
 * Get public URL for policy document (organization scoped)
 */
export const getPublicUrl = async (documentPath: string): Promise<string> => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    // Verify the document belongs to the user's organization
    if (!documentPath.startsWith(`${organizationId}/`)) {
      throw new Error("Access denied - document not in user's organization");
    }
    return `/uploads/${documentPath}`;
  } catch (error: unknown) {
    throw error;
  }
};
/**
 * Download policy document (organization scoped)
 */
export const downloadPolicyDocument = async (documentPath: string): Promise<Blob> => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    // Verify the document belongs to the user's organization
    if (!documentPath.startsWith(`${organizationId}/`)) {
      throw new Error("Access denied - document not in user's organization");
    }
    // For now, return empty blob - this would need proper file storage implementation
    return new Blob([""], { type: "application/pdf" });
  } catch (error: unknown) {
    throw error;
  }
};
/**
 * Delete policy document (organization scoped)
 */
export const deletePolicyDocument = async (documentPath: string): Promise<void> => {
  try {
    const organizationId = await getUserOrganizationId();
    if (!organizationId) {
      throw new Error("User organization not found");
    }
    // Verify the document belongs to the user's organization
    if (!documentPath.startsWith(`${organizationId}/`)) {
      throw new Error("Access denied - document not in user's organization");
    }
  } catch (error: unknown) {
    throw error;
  }
};
