/**
 * Code Quality Metrics Service
 * Collects and tracks code quality metrics including TypeScript errors,
 * test coverage, code complexity, and quality trends over time.
 */

import { Logger } from "../utils/errors/Logger";

export interface CodeQualityMetrics {
  timestamp: string;
  typeScriptErrors: number;
  eslintWarnings: number;
  eslintErrors: number;
  testCoverage: CoverageMetrics;
  codeComplexity: ComplexityMetrics;
  maintainabilityIndex: number;
  bundleSize: BundleSizeMetrics;
  qualityScore: number;
}

export interface CoverageMetrics {
  lines: number;
  functions: number;
  branches: number;
  statements: number;
  threshold: {
    lines: number;
    functions: number;
    branches: number;
    statements: number;
  };
}

export interface ComplexityMetrics {
  cyclomaticComplexity: number;
  cognitiveComplexity: number;
  maintainabilityIndex: number;
  linesOfCode: number;
  technicalDebt: number;
}

export interface BundleSizeMetrics {
  totalSize: number;
  gzippedSize: number;
  chunkCount: number;
  duplicateDependencies: number;
  unusedCode: number;
}

export interface QualityTrend {
  date: string;
  metrics: CodeQualityMetrics;
  regression: boolean;
  improvements: string[];
  regressions: string[];
}

export interface QualityAlert {
  id: string;
  type: "error" | "warning" | "info";
  severity: "low" | "medium" | "high" | "critical";
  message: string;
  metric: string;
  threshold: number;
  currentValue: number;
  timestamp: string;
  resolved: boolean;
}

class CodeQualityMetricsService {
  private logger = Logger.getInstance();
  private metricsHistory: QualityTrend[] = [];
  private alerts: QualityAlert[] = [];
  private thresholds = {
    typeScriptErrors: 0,
    eslintErrors: 0,
    testCoverage: 80,
    maintainabilityIndex: 70,
    bundleSize: 500 * 1024, // 500KB
    qualityScore: 85,
  };

  /**
   * Collect current code quality metrics
   */
  async collectMetrics(): Promise<CodeQualityMetrics> {
    try {
      this.logger.info("Collecting code quality metrics");

      const [typeScriptMetrics, eslintMetrics, coverageMetrics, complexityMetrics, bundleMetrics] =
        await Promise.all([
          this.collectTypeScriptMetrics(),
          this.collectESLintMetrics(),
          this.collectCoverageMetrics(),
          this.collectComplexityMetrics(),
          this.collectBundleMetrics(),
        ]);

      const qualityScore = this.calculateQualityScore({
        typeScriptErrors: typeScriptMetrics.errors,
        eslintErrors: eslintMetrics.errors,
        testCoverage: coverageMetrics.lines,
        maintainabilityIndex: complexityMetrics.maintainabilityIndex,
        bundleSize: bundleMetrics.totalSize,
      });

      const metrics: CodeQualityMetrics = {
        timestamp: new Date().toISOString(),
        typeScriptErrors: typeScriptMetrics.errors,
        eslintWarnings: eslintMetrics.warnings,
        eslintErrors: eslintMetrics.errors,
        testCoverage: coverageMetrics,
        codeComplexity: complexityMetrics,
        maintainabilityIndex: complexityMetrics.maintainabilityIndex,
        bundleSize: bundleMetrics,
        qualityScore,
      };

      this.logger.info("Code quality metrics collected", undefined, { metrics });
      return metrics;
    } catch (error) {
      const typedError = error instanceof Error ? error : new Error("Unknown error occurred");
      this.logger.error("Failed to collect code quality metrics", typedError);
      throw typedError;
    }
  }

  /**
   * Store metrics and track trends
   */
  async storeMetrics(metrics: CodeQualityMetrics): Promise<void> {
    try {
      const previousMetrics = this.getLatestMetrics();
      const trend = this.analyzeTrend(metrics, previousMetrics);

      this.metricsHistory.push(trend);

      // Keep only last 30 days of history
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      this.metricsHistory = this.metricsHistory.filter(t => new Date(t.date) >= thirtyDaysAgo);

      // Check for quality regressions and generate alerts
      await this.checkQualityThresholds(metrics);

      this.logger.info("Metrics stored and analyzed", undefined, { trend });
    } catch (error) {
      const typedError = error instanceof Error ? error : new Error("Unknown error occurred");
      this.logger.error("Failed to store metrics", typedError);
      throw typedError;
    }
  }

  /**
   * Get quality trends over time
   */
  getQualityTrends(days: number = 7): QualityTrend[] {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    return this.metricsHistory
      .filter(trend => new Date(trend.date) >= cutoffDate)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }

  /**
   * Get active quality alerts
   */
  getActiveAlerts(): QualityAlert[] {
    return this.alerts.filter(alert => !alert.resolved);
  }

  /**
   * Get latest metrics
   */
  getLatestMetrics(): CodeQualityMetrics | null {
    const latest = this.metricsHistory[this.metricsHistory.length - 1];
    return latest?.metrics || null;
  }

  /**
   * Resolve quality alert
   */
  resolveAlert(alertId: string): void {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      this.logger.info("Quality alert resolved", undefined, { alertId });
    }
  }

  private async collectTypeScriptMetrics(): Promise<{ errors: number; warnings: number }> {
    // In a real implementation, this would run tsc --noEmit and parse output
    // For now, return mock data
    return { errors: 0, warnings: 2 };
  }

  private async collectESLintMetrics(): Promise<{ errors: number; warnings: number }> {
    // In a real implementation, this would run eslint and parse output
    // For now, return mock data
    return { errors: 0, warnings: 5 };
  }

  private async collectCoverageMetrics(): Promise<CoverageMetrics> {
    // In a real implementation, this would parse coverage reports
    // For now, return mock data
    return {
      lines: 85.5,
      functions: 82.3,
      branches: 78.9,
      statements: 86.1,
      threshold: {
        lines: 80,
        functions: 80,
        branches: 75,
        statements: 80,
      },
    };
  }

  private async collectComplexityMetrics(): Promise<ComplexityMetrics> {
    // In a real implementation, this would analyze code complexity
    // For now, return mock data
    return {
      cyclomaticComplexity: 12.5,
      cognitiveComplexity: 8.3,
      maintainabilityIndex: 78.2,
      linesOfCode: 15420,
      technicalDebt: 2.5,
    };
  }

  private async collectBundleMetrics(): Promise<BundleSizeMetrics> {
    // In a real implementation, this would analyze bundle output
    // For now, return mock data
    return {
      totalSize: 450 * 1024, // 450KB
      gzippedSize: 125 * 1024, // 125KB
      chunkCount: 8,
      duplicateDependencies: 0,
      unusedCode: 5,
    };
  }

  private calculateQualityScore(metrics: {
    typeScriptErrors: number;
    eslintErrors: number;
    testCoverage: number;
    maintainabilityIndex: number;
    bundleSize: number;
  }): number {
    let score = 100;

    // Deduct points for errors
    score -= metrics.typeScriptErrors * 10;
    score -= metrics.eslintErrors * 5;

    // Deduct points for low coverage
    if (metrics.testCoverage < 80) {
      score -= (80 - metrics.testCoverage) * 2;
    }

    // Deduct points for low maintainability
    if (metrics.maintainabilityIndex < 70) {
      score -= 70 - metrics.maintainabilityIndex;
    }

    // Deduct points for large bundle size
    if (metrics.bundleSize > this.thresholds.bundleSize) {
      const excessKB = (metrics.bundleSize - this.thresholds.bundleSize) / 1024;
      score -= excessKB * 0.1;
    }

    return Math.max(0, Math.min(100, score));
  }

  private analyzeTrend(
    current: CodeQualityMetrics,
    previous: CodeQualityMetrics | null
  ): QualityTrend {
    const improvements: string[] = [];
    const regressions: string[] = [];
    let regression = false;

    if (previous) {
      // Check for improvements and regressions
      if (current.typeScriptErrors < previous.typeScriptErrors) {
        improvements.push(
          `TypeScript errors reduced by ${previous.typeScriptErrors - current.typeScriptErrors}`
        );
      } else if (current.typeScriptErrors > previous.typeScriptErrors) {
        regressions.push(
          `TypeScript errors increased by ${current.typeScriptErrors - previous.typeScriptErrors}`
        );
        regression = true;
      }

      if (current.testCoverage.lines > previous.testCoverage.lines) {
        improvements.push(
          `Test coverage improved by ${(current.testCoverage.lines - previous.testCoverage.lines).toFixed(1)}%`
        );
      } else if (current.testCoverage.lines < previous.testCoverage.lines) {
        regressions.push(
          `Test coverage decreased by ${(previous.testCoverage.lines - current.testCoverage.lines).toFixed(1)}%`
        );
        regression = true;
      }

      if (current.qualityScore > previous.qualityScore) {
        improvements.push(
          `Quality score improved by ${(current.qualityScore - previous.qualityScore).toFixed(1)} points`
        );
      } else if (current.qualityScore < previous.qualityScore) {
        regressions.push(
          `Quality score decreased by ${(previous.qualityScore - current.qualityScore).toFixed(1)} points`
        );
        regression = true;
      }
    }

    return {
      date: current.timestamp,
      metrics: current,
      regression,
      improvements,
      regressions,
    };
  }

  private async checkQualityThresholds(metrics: CodeQualityMetrics): Promise<void> {
    const newAlerts: QualityAlert[] = [];

    // Check TypeScript errors
    if (metrics.typeScriptErrors > this.thresholds.typeScriptErrors) {
      newAlerts.push({
        id: `ts-errors-${Date.now()}`,
        type: "error",
        severity: "high",
        message: `TypeScript errors detected: ${metrics.typeScriptErrors}`,
        metric: "typeScriptErrors",
        threshold: this.thresholds.typeScriptErrors,
        currentValue: metrics.typeScriptErrors,
        timestamp: new Date().toISOString(),
        resolved: false,
      });
    }

    // Check test coverage
    if (metrics.testCoverage.lines < this.thresholds.testCoverage) {
      newAlerts.push({
        id: `coverage-${Date.now()}`,
        type: "warning",
        severity: "medium",
        message: `Test coverage below threshold: ${metrics.testCoverage.lines.toFixed(1)}%`,
        metric: "testCoverage",
        threshold: this.thresholds.testCoverage,
        currentValue: metrics.testCoverage.lines,
        timestamp: new Date().toISOString(),
        resolved: false,
      });
    }

    // Check quality score
    if (metrics.qualityScore < this.thresholds.qualityScore) {
      newAlerts.push({
        id: `quality-score-${Date.now()}`,
        type: "warning",
        severity: "medium",
        message: `Quality score below threshold: ${metrics.qualityScore.toFixed(1)}`,
        metric: "qualityScore",
        threshold: this.thresholds.qualityScore,
        currentValue: metrics.qualityScore,
        timestamp: new Date().toISOString(),
        resolved: false,
      });
    }

    this.alerts.push(...newAlerts);

    if (newAlerts.length > 0) {
      this.logger.warn("Quality threshold violations detected", undefined, { alerts: newAlerts });
    }
  }
}

export const codeQualityMetricsService = new CodeQualityMetricsService();
