import { describe, it, expect } from 'vitest'
import {
  calculateSeverity,
  calculateRiskAssessment,
  getSeverityColor,
  getSeverityPriority,
  validateAssessmentValues,
  getAssessmentDescription,
} from '../riskCalculationService'
import { RiskSeverity } from '@/types'

describe('Risk Calculation Service', () => {
  describe('calculateSeverity', () => {
    it('should calculate LOW severity correctly', () => {
      expect(calculateSeverity(1, 1)).toBe(RiskSeverity.LOW)
      expect(calculateSeverity(1, 2)).toBe(RiskSeverity.LOW)
      expect(calculateSeverity(1, 3)).toBe(RiskSeverity.LOW)
      expect(calculateSeverity(2, 1)).toBe(RiskSeverity.LOW)
      expect(calculateSeverity(2, 2)).toBe(RiskSeverity.LOW)
      expect(calculateSeverity(3, 1)).toBe(RiskSeverity.LOW)
    })

    it('should calculate MEDIUM severity correctly', () => {
      expect(calculateSeverity(1, 4)).toBe(RiskSeverity.MEDIUM)
      expect(calculateSeverity(1, 5)).toBe(RiskSeverity.MEDIUM)
      expect(calculateSeverity(2, 3)).toBe(RiskSeverity.MEDIUM)
      expect(calculateSeverity(2, 4)).toBe(RiskSeverity.MEDIUM)
      expect(calculateSeverity(3, 2)).toBe(RiskSeverity.MEDIUM)
      expect(calculateSeverity(3, 3)).toBe(RiskSeverity.MEDIUM)
      expect(calculateSeverity(4, 1)).toBe(RiskSeverity.MEDIUM)
      expect(calculateSeverity(4, 2)).toBe(RiskSeverity.MEDIUM)
      expect(calculateSeverity(5, 1)).toBe(RiskSeverity.MEDIUM)
    })

    it('should calculate HIGH severity correctly', () => {
      expect(calculateSeverity(2, 5)).toBe(RiskSeverity.HIGH)
      expect(calculateSeverity(3, 4)).toBe(RiskSeverity.HIGH)
      expect(calculateSeverity(3, 5)).toBe(RiskSeverity.HIGH)
      expect(calculateSeverity(4, 3)).toBe(RiskSeverity.HIGH)
      expect(calculateSeverity(4, 4)).toBe(RiskSeverity.HIGH)
      expect(calculateSeverity(5, 2)).toBe(RiskSeverity.HIGH)
      expect(calculateSeverity(5, 3)).toBe(RiskSeverity.HIGH)
    })

    it('should calculate CRITICAL severity correctly', () => {
      expect(calculateSeverity(4, 5)).toBe(RiskSeverity.CRITICAL)
      expect(calculateSeverity(5, 4)).toBe(RiskSeverity.CRITICAL)
      expect(calculateSeverity(5, 5)).toBe(RiskSeverity.CRITICAL)
    })

    it('should default to LOW for invalid combinations', () => {
      expect(calculateSeverity(0, 0)).toBe(RiskSeverity.LOW)
      expect(calculateSeverity(6, 6)).toBe(RiskSeverity.LOW)
      expect(calculateSeverity(-1, 3)).toBe(RiskSeverity.LOW)
    })
  })

  describe('calculateRiskAssessment', () => {
    it('should return complete risk assessment', () => {
      const assessment = calculateRiskAssessment(3, 4)

      expect(assessment).toEqual({
        likelihood: 3,
        impact: 4,
        severity: RiskSeverity.HIGH
      })
    })

    it('should handle edge cases', () => {
      const assessment = calculateRiskAssessment(1, 1)
      
      expect(assessment).toEqual({
        likelihood: 1,
        impact: 1,
        severity: RiskSeverity.LOW
      })
    })
  })

  describe('getSeverityColor', () => {
    it('should return correct colors for each severity', () => {
      expect(getSeverityColor(RiskSeverity.LOW))
        .toBe('bg-green-100 text-green-800 border-green-200')
      
      expect(getSeverityColor(RiskSeverity.MEDIUM))
        .toBe('bg-yellow-100 text-yellow-800 border-yellow-200')
      
      expect(getSeverityColor(RiskSeverity.HIGH))
        .toBe('bg-orange-100 text-orange-800 border-orange-200')
      
      expect(getSeverityColor(RiskSeverity.CRITICAL))
        .toBe('bg-red-100 text-red-800 border-red-200')
    })

    it('should default to LOW color for invalid severity', () => {
      expect(getSeverityColor('INVALID' as RiskSeverity))
        .toBe('bg-green-100 text-green-800 border-green-200')
    })
  })

  describe('getSeverityPriority', () => {
    it('should return correct priority numbers', () => {
      expect(getSeverityPriority(RiskSeverity.LOW)).toBe(2)
      expect(getSeverityPriority(RiskSeverity.MEDIUM)).toBe(3)
      expect(getSeverityPriority(RiskSeverity.HIGH)).toBe(4)
      expect(getSeverityPriority(RiskSeverity.CRITICAL)).toBe(5)
    })

    it('should default to LOW priority for invalid severity', () => {
      expect(getSeverityPriority('INVALID' as RiskSeverity)).toBe(2)
    })
  })

  describe('validateAssessmentValues', () => {
    it('should validate correct values', () => {
      expect(validateAssessmentValues(1, 1)).toBe(true)
      expect(validateAssessmentValues(3, 3)).toBe(true)
      expect(validateAssessmentValues(5, 5)).toBe(true)
    })

    it('should reject invalid values', () => {
      expect(validateAssessmentValues(0, 3)).toBe(false)
      expect(validateAssessmentValues(3, 0)).toBe(false)
      expect(validateAssessmentValues(6, 3)).toBe(false)
      expect(validateAssessmentValues(3, 6)).toBe(false)
      expect(validateAssessmentValues(-1, 3)).toBe(false)
      expect(validateAssessmentValues(3, -1)).toBe(false)
    })
  })

  describe('getAssessmentDescription', () => {
    it('should return correct descriptions', () => {
      expect(getAssessmentDescription(1, 1))
        .toBe('Very Unlikely occurrence with Insignificant impact')
      
      expect(getAssessmentDescription(3, 3))
        .toBe('Possible occurrence with Moderate impact')
      
      expect(getAssessmentDescription(5, 5))
        .toBe('Very Likely occurrence with Catastrophic impact')
    })

    it('should handle edge cases gracefully', () => {
      // Test that function doesn't crash with invalid inputs
      expect(() => getAssessmentDescription(0, 0)).not.toThrow()
      expect(() => getAssessmentDescription(6, 6)).not.toThrow()
      expect(() => getAssessmentDescription(-1, 3)).not.toThrow()
    })
  })

  describe('Risk Matrix Integration', () => {
    it('should maintain consistency across all functions', () => {
      const testCases = [
        { likelihood: 1, impact: 1, expectedSeverity: RiskSeverity.LOW },
        { likelihood: 2, impact: 3, expectedSeverity: RiskSeverity.MEDIUM },
        { likelihood: 4, impact: 3, expectedSeverity: RiskSeverity.HIGH },
        { likelihood: 5, impact: 5, expectedSeverity: RiskSeverity.CRITICAL },
      ]

      testCases.forEach(({ likelihood, impact, expectedSeverity }) => {
        const severity = calculateSeverity(likelihood, impact)
        const assessment = calculateRiskAssessment(likelihood, impact)
        const color = getSeverityColor(severity)
        const priority = getSeverityPriority(severity)
        const description = getAssessmentDescription(likelihood, impact)

        expect(severity).toBe(expectedSeverity)
        expect(assessment.severity).toBe(expectedSeverity)
        expect(color).toContain('bg-')
        expect(priority).toBeGreaterThan(0)
        expect(description).toContain('occurrence with')
      })
    })
  })
})
