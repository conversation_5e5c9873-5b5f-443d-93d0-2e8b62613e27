import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createRisk, updateRisk, deleteRisk, fetchRiskById } from '../riskCRUDService'
import { supabase } from '@/integrations/supabase/client'
import { RiskSeverity, RiskStatus } from '@/types'
import { createMockSupabaseResponse } from '@/test/test-utils'

// Mock the supabase client
vi.mock('@/integrations/supabase/client')

describe('Risk CRUD Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('createRisk', () => {
    const mockRiskData = {
      title: 'Test Risk',
      description: 'Test risk description',
      categoryId: 'test-category-id',
      inherentLikelihood: 4,
      inherentImpact: 3,
      inherentSeverity: RiskSeverity.HIGH,
      likelihood: 2,
      impact: 3,
      severity: RiskSeverity.MEDIUM,
      status: RiskStatus.IN_PROGRESS,
      mitigationApproach: 'Test mitigation',
      dueDate: new Date('2024-12-31'),
      ownerId: 'test-user-id',
      organizationId: 'test-org-id',
    }

    it('should create a risk successfully', async () => {
      const mockResponse = {
        id: 'new-risk-id',
        ...mockRiskData,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        profiles: { name: 'Test User' },
        categories: { name: 'Test Category' }
      }

      const mockSupabaseChain = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue(createMockSupabaseResponse(mockResponse)),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await createRisk(mockRiskData, 'test-user-id')

      expect(result.success).toBe(true)
      expect(result.error).toBeUndefined()
      expect(result.data).toBeTruthy()
      expect(result.data?.id).toBe('new-risk-id')
      expect(mockSupabaseChain.insert).toHaveBeenCalledWith({
        title: mockRiskData.title,
        description: mockRiskData.description,
        category_id: mockRiskData.categoryId,
        inherent_likelihood: mockRiskData.inherentLikelihood,
        inherent_impact: mockRiskData.inherentImpact,
        inherent_severity: mockRiskData.inherentSeverity,
        likelihood: mockRiskData.likelihood,
        impact: mockRiskData.impact,
        severity: mockRiskData.severity,
        status: mockRiskData.status,
        mitigation_approach: mockRiskData.mitigationApproach,
        due_date: mockRiskData.dueDate.toISOString(),
        created_by: 'test-user-id',
        owner_id: mockRiskData.ownerId,
        organization_id: mockRiskData.organizationId,
        template_id: null,
      })
    })

    it('should handle creation errors', async () => {
      const mockError = { message: 'Database error' }
      const mockSupabaseChain = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue(createMockSupabaseResponse(null, mockError)),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await createRisk(mockRiskData, 'test-user-id')

      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
      expect(result.error?.message).toContain('Database error')
      expect(result.data).toBeUndefined()
    })

    it('should handle missing optional fields', async () => {
      const minimalRiskData = {
        title: 'Test Risk',
        description: 'Test risk description',
        inherentLikelihood: 4,
        inherentImpact: 3,
        inherentSeverity: RiskSeverity.HIGH,
        likelihood: 2,
        impact: 3,
        severity: RiskSeverity.MEDIUM,
        status: RiskStatus.IN_PROGRESS,
        ownerId: 'test-user-id',
        organizationId: 'test-org-id',
      }

      const mockResponse = {
        id: 'new-risk-id',
        profiles: { name: 'Test User' },
        categories: null
      }

      const mockSupabaseChain = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue(createMockSupabaseResponse(mockResponse)),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await createRisk(minimalRiskData, 'test-user-id')

      expect(result.success).toBe(true)
      expect(result.error).toBeUndefined()
      expect(mockSupabaseChain.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          category_id: undefined,
          mitigation_approach: null,
          due_date: null,
        })
      )
    })
  })

  describe('updateRisk', () => {
    const mockUpdateData = {
      id: 'test-risk-id',
      title: 'Updated Risk',
      description: 'Updated description',
      severity: RiskSeverity.HIGH,
    }

    it('should update a risk successfully', async () => {
      const mockSupabaseChain = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue(createMockSupabaseResponse(null)), // No error = successful update
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await updateRisk(mockUpdateData)

      expect(result.success).toBe(true)
      expect(result.error).toBeUndefined()
      expect(mockSupabaseChain.eq).toHaveBeenCalledWith('id', 'test-risk-id')
    })

    it('should handle update errors', async () => {
      const mockError = { message: 'Update failed' }
      const mockSupabaseChain = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue(createMockSupabaseResponse(null, mockError)),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await updateRisk(mockUpdateData)

      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
      expect(result.error?.message).toContain('Update failed')
    })
  })

  describe('deleteRisk', () => {
    it('should delete a risk successfully', async () => {
      const mockSupabaseChain = {
        delete: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue(createMockSupabaseResponse(null)),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await deleteRisk('test-risk-id')

      expect(result.success).toBe(true)
      expect(result.error).toBeUndefined()
      expect(mockSupabaseChain.eq).toHaveBeenCalledWith('id', 'test-risk-id')
    })

    it('should handle deletion errors', async () => {
      const mockError = { message: 'Deletion failed' }
      const mockSupabaseChain = {
        delete: vi.fn().mockReturnThis(),
        eq: vi.fn().mockResolvedValue(createMockSupabaseResponse(null, mockError)),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await deleteRisk('test-risk-id')

      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
      expect(result.error?.message).toContain('Deletion failed')
    })
  })

  describe('fetchRiskById', () => {
    it('should fetch a risk successfully', async () => {
      const mockRisk = {
        id: 'test-risk-id',
        title: 'Test Risk',
        description: 'Test description',
        severity: RiskSeverity.HIGH,
        status: RiskStatus.IN_PROGRESS,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        profiles: { name: 'Test User' },
        categories: { name: 'Test Category' }
      }

      const mockSupabaseChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue(createMockSupabaseResponse(mockRisk)),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await fetchRiskById('test-risk-id')

      expect(result.success).toBe(true)
      expect(result.error).toBeUndefined()
      expect(result.data).toBeTruthy()
      expect(result.data?.id).toBe('test-risk-id')
      expect(mockSupabaseChain.eq).toHaveBeenCalledWith('id', 'test-risk-id')
    })

    it('should handle fetch errors', async () => {
      const mockError = { message: 'Risk not found' }
      const mockSupabaseChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue(createMockSupabaseResponse(null, mockError)),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await fetchRiskById('non-existent-id')

      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
      expect(result.error?.message).toContain('Risk not found')
      expect(result.data).toBeUndefined()
    })
  })
})
