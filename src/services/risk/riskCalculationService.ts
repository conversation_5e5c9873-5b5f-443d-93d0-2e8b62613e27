import { RiskSeverity } from "@/types";

export interface RiskAssessment {
  likelihood: number;
  impact: number;
  severity: RiskSeverity;
}

export interface SeverityMatrix {
  [key: string]: RiskSeverity;
}

/**
 * Risk severity calculation matrix
 */
const SEVERITY_MATRIX: SeverityMatrix = {
  "1-1": RiskSeverity.LOW,
  "1-2": RiskSeverity.LOW,
  "1-3": RiskSeverity.LOW,
  "1-4": RiskSeverity.MEDIUM,
  "1-5": RiskSeverity.MEDIUM,
  "2-1": RiskSeverity.LOW,
  "2-2": RiskSeverity.LOW,
  "2-3": RiskSeverity.MEDIUM,
  "2-4": RiskSeverity.MEDIUM,
  "2-5": RiskSeverity.HIGH,
  "3-1": RiskSeverity.LOW,
  "3-2": RiskSeverity.MEDIUM,
  "3-3": RiskSeverity.MEDIUM,
  "3-4": RiskSeverity.HIGH,
  "3-5": RiskSeverity.HIGH,
  "4-1": RiskSeverity.MEDIUM,
  "4-2": RiskSeverity.MEDIUM,
  "4-3": RiskSeverity.HIGH,
  "4-4": RiskSeverity.HIGH,
  "4-5": RiskSeverity.CRITICAL,
  "5-1": RiskSeverity.MEDIUM,
  "5-2": RiskSeverity.HIGH,
  "5-3": RiskSeverity.HIGH,
  "5-4": RiskSeverity.CRITICAL,
  "5-5": RiskSeverity.CRITICAL,
};

/**
 * Calculate risk severity based on likelihood and impact
 */

export const calculateSeverity = (likelihood: number, impact: number): RiskSeverity => {
  const key = `${likelihood}-${impact}`;
  return SEVERITY_MATRIX[key] || RiskSeverity.LOW;
};

/**
 * Calculate risk assessment including severity
 */

export const calculateRiskAssessment = (likelihood: number, impact: number): RiskAssessment => {
  return {
    likelihood,
    impact,
    severity: calculateSeverity(likelihood, impact),
  };
};

/**
 * Get severity color for UI display - updated to use dark text on light backgrounds
 */

export const getSeverityColor = (severity: RiskSeverity): string => {
  const colors = {
    [RiskSeverity.LOW]: "bg-green-100 text-green-800 border-green-200",
    [RiskSeverity.MEDIUM]: "bg-yellow-100 text-yellow-800 border-yellow-200",
    [RiskSeverity.HIGH]: "bg-orange-100 text-orange-800 border-orange-200",
    [RiskSeverity.CRITICAL]: "bg-red-100 text-red-800 border-red-200",
  };

  return colors[severity] || colors[RiskSeverity.LOW];
};

/**
 * Get severity priority number for sorting
 */

export const getSeverityPriority = (severity: RiskSeverity): number => {
  const priorities = {
    [RiskSeverity.LOW]: 2,
    [RiskSeverity.MEDIUM]: 3,
    [RiskSeverity.HIGH]: 4,
    [RiskSeverity.CRITICAL]: 5,
  };

  return priorities[severity] ?? 2;
};

/**
 * Validate likelihood and impact values
 */

export const validateAssessmentValues = (likelihood: number, impact: number): boolean => {
  return likelihood >= 1 && likelihood <= 5 && impact >= 1 && impact <= 5;
};

/**
 * Get assessment description based on values
 */

export const getAssessmentDescription = (likelihood: number, impact: number): string => {
  const likelihoodDesc = ["", "Very Unlikely", "Unlikely", "Possible", "Likely", "Very Likely"];
  const impactDesc = ["", "Insignificant", "Minor", "Moderate", "Major", "Catastrophic"];

  return `${likelihoodDesc[likelihood]} occurrence with ${impactDesc[impact]} impact`;
};
