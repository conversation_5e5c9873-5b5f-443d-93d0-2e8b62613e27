import { supabase } from "@/integrations/supabase/client";
import { Risk, RiskSeverity, RiskStatus } from "@/types";
import {
  ServiceResult,
  RiskApiResponse,
  CreateRiskRequest,
  transformSupabaseError,
} from "@/types/api";
import { isRiskApiResponse } from "@/utils/api-validation";
import { mapDatabaseRiskToRisk } from "@/utils/typeMappers";
import { auditLog, AuditEventType } from "@/services/auditLoggingService";
export interface CreateRiskData {
  title: string;
  description: string;
  categoryId?: string;
  inherentLikelihood: number;
  inherentImpact: number;
  inherentSeverity: RiskSeverity;
  likelihood: number;
  impact: number;
  severity: RiskSeverity;
  status: RiskStatus;
  mitigationApproach?: string;
  dueDate?: Date;
  ownerId: string;
  organizationId: string;
  templateId?: string;
}
export interface UpdateRiskData extends Partial<CreateRiskData> {
  id: string;
}
/**
 * Create a new risk
 */
export const createRisk = async (
  data: CreateRiskData,
  createdBy: string
): Promise<ServiceResult<Risk>> => {
  try {
    const requestData: CreateRiskRequest = {
      title: data.title,
      description: data.description,
      category_id: data.categoryId,
      inherent_likelihood: data.inherentLikelihood,
      inherent_impact: data.inherentImpact,
      inherent_severity: data.inherentSeverity,
      likelihood: data.likelihood,
      impact: data.impact,
      severity: data.severity,
      status: data.status,
      mitigation_approach: data.mitigationApproach,
      due_date: data.dueDate ? data.dueDate.toISOString() : undefined,
      owner_id: data.ownerId,
      organization_id: data.organizationId,
      template_id: data.templateId,
    };
    const { data: riskData, error } = await supabase
      .from("risks")
      .insert({
        ...requestData,
        created_by: createdBy,
        mitigation_approach: requestData.mitigation_approach ?? null,
        due_date: requestData.due_date ?? null,
        template_id: requestData.template_id ?? null,
      })
      .select(
        `
        *,
        profiles!risks_owner_id_fkey(name),
        categories:risk_categories(name)
      `
      )
      .single();
    if (error) {
      return {
        success: false,
        error: transformSupabaseError(error),
      };
    }
    // Validate the response structure
    if (!isRiskApiResponse(riskData)) {
      return {
        success: false,
        error: {
          message: "Invalid risk response format",
          code: "INVALID_RESPONSE",
          category: "validation",
          recoverable: false,
        },
      };
    }
    const risk = mapDatabaseRiskToRisk(riskData);
    // Log successful risk creation
    await auditLog.risk(AuditEventType.RISK_CREATED, createdBy, risk.id, {
      title: data.title,
      severity: data.severity,
      status: data.status,
      organizationId: data.organizationId,
      ownerId: data.ownerId,
      timestamp: new Date().toISOString(),
    });
    return {
      success: true,
      data: risk,
      metadata: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : "Failed to create risk",
        code: "UNEXPECTED_ERROR",
        category: "system",
        recoverable: false,
      },
    };
  }
};
/**
 * Update an existing risk
 */
interface DatabaseUpdateData {
  title?: string;
  description?: string;
  category_id?: string;
  likelihood?: number;
  impact?: number;
  severity?: RiskSeverity;
  status?: RiskStatus;
  mitigation_approach?: string | null;
  due_date?: string | null;
  owner_id?: string;
  updated_at?: string;
}
export const updateRisk = async (
  data: UpdateRiskData,
  updatedBy?: string
): Promise<ServiceResult<void>> => {
  try {
    const updateData: DatabaseUpdateData = {};
    const changes: string[] = [];
    if (data.title !== undefined) {
      updateData.title = data.title;
      changes.push("title");
    }
    if (data.description !== undefined) {
      updateData.description = data.description;
      changes.push("description");
    }
    if (data.categoryId !== undefined) {
      updateData.category_id = data.categoryId;
      changes.push("category");
    }
    if (data.likelihood !== undefined) {
      updateData.likelihood = data.likelihood;
      changes.push("likelihood");
    }
    if (data.impact !== undefined) {
      updateData.impact = data.impact;
      changes.push("impact");
    }
    if (data.severity !== undefined) {
      updateData.severity = data.severity;
      changes.push("severity");
    }
    if (data.status !== undefined) {
      updateData.status = data.status;
      changes.push("status");
    }
    if (data.mitigationApproach !== undefined) {
      updateData.mitigation_approach = data.mitigationApproach;
      changes.push("mitigation_approach");
    }
    if (data.dueDate !== undefined) {
      updateData.due_date = data.dueDate ? data.dueDate.toISOString() : null;
      changes.push("due_date");
    }
    if (data.ownerId !== undefined) {
      updateData.owner_id = data.ownerId;
      changes.push("owner");
    }
    updateData.updated_at = new Date().toISOString();
    const { error } = await supabase.from("risks").update(updateData).eq("id", data.id);
    if (error) {
      return {
        success: false,
        error: transformSupabaseError(error),
      };
    }
    // Log successful risk update
    if (updatedBy) {
      await auditLog.risk(AuditEventType.RISK_UPDATED, updatedBy, data.id, {
        changes,
        newValues: {
          title: data.title,
          severity: data.severity,
          status: data.status,
          ownerId: data.ownerId,
        },
        timestamp: new Date().toISOString(),
      });
    }
    return {
      success: true,
      data: undefined,
      metadata: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : "Failed to update risk",
        code: "UNEXPECTED_ERROR",
        category: "system",
        recoverable: false,
      },
    };
  }
};
/**
 * Delete a risk
 */
export const deleteRisk = async (
  riskId: string,
  deletedBy?: string
): Promise<ServiceResult<void>> => {
  try {
    // Get risk details before deletion for audit logging
    let riskTitle: string | undefined;
    if (deletedBy) {
      try {
        const { data: riskData } = await supabase
          .from("risks")
          .select("title, status, severity")
          .eq("id", riskId)
          .single();
        riskTitle = riskData?.title;
      } catch (error) {
        // Continue with deletion even if we can't get risk details
      }
    }
    const { error } = await supabase.from("risks").delete().eq("id", riskId);
    if (error) {
      return {
        success: false,
        error: transformSupabaseError(error),
      };
    }
    // Log successful risk deletion
    if (deletedBy) {
      await auditLog.risk(AuditEventType.RISK_DELETED, deletedBy, riskId, {
        title: riskTitle,
        timestamp: new Date().toISOString(),
      });
    }
    return {
      success: true,
      data: undefined,
      metadata: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : "Failed to delete risk",
        code: "UNEXPECTED_ERROR",
        category: "system",
        recoverable: false,
      },
    };
  }
};
/**
 * Fetch a risk by ID
 */
export const fetchRiskById = async (riskId: string): Promise<ServiceResult<Risk>> => {
  try {
    const { data, error } = await supabase
      .from("risks")
      .select(
        `
        *,
        profiles!risks_owner_id_fkey(name),
        categories:risk_categories(name)
      `
      )
      .eq("id", riskId)
      .single();
    if (error) {
      return {
        success: false,
        error: transformSupabaseError(error),
      };
    }
    // Validate the response structure
    if (!isRiskApiResponse(data)) {
      return {
        success: false,
        error: {
          message: "Invalid risk response format",
          code: "INVALID_RESPONSE",
          category: "validation",
          recoverable: false,
        },
      };
    }
    return {
      success: true,
      data: mapDatabaseRiskToRisk(data),
      metadata: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : "Failed to fetch risk",
        code: "UNEXPECTED_ERROR",
        category: "system",
        recoverable: false,
      },
    };
  }
};
