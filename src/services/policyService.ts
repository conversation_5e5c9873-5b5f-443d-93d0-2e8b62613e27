// Legacy export file for backward compatibility
// This file re-exports all policy-related services from the new structure
// It should be considered deprecated and will be removed in the future
import * as PolicyServices from "./policy";
// Re-export everything that actually exists
export const {
  // Policy Data Services
  fetchPublishedPolicies,
  fetchAllPolicies,
  fetchPolicyById,
  createPolicy,
  updatePolicy,
  deletePolicy,
  // Policy Request Services
  createPolicyRequest,
  fetchPolicyRequests,
  updatePolicyRequestStatus,
  deletePolicyRequest,
  // Policy Document Services (if they exist)
  getUploadUrl,
  uploadPolicyDocument,
  getPublicUrl,
  downloadPolicyDocument,
  deletePolicyDocument,
} = PolicyServices;
// Note: fetchPolicyRequestsByUser and fetchPolicyRequestById were removed
// as they don't exist in the current policy services implementation
// Utility function to update types in policy.ts - this is kept for backward compatibility
export const updatePolicyMappers = () => {};
