import { supabase } from "@/integrations/supabase/client";
import { Comment } from "@/types";
export const fetchComments = async (entityType: "risk" | "incident", entityId: string) => {
  const { data, error } = await supabase
    .from("comments")
    .select(
      `
      *,
      profiles:user_id (name, avatar_url)
    `
    )
    .eq("entity_type", entityType)
    .eq("entity_id", entityId)
    .is("deleted_at", null)
    .order("created_at", { ascending: true });
  if (error) {
    throw error;
  }
  return data.map(comment => ({
    id: comment.id,
    content: comment.content,
    entityType: comment.entity_type as "risk" | "incident",
    entityId: comment.entity_id,
    userId: comment.user_id,
    userName: comment.profiles?.name ?? "Unknown User",
    userAvatar: comment.profiles?.avatar_url,
    createdAt: new Date(comment.created_at),
    updatedAt: new Date(comment.updated_at),
    deletedAt: comment.deleted_at ? new Date(comment.deleted_at) : null,
  }));
};
export const addComment = async (
  entityType: "risk" | "incident",
  entityId: string,
  content: string,
  userId: string
) => {
  const commentData = {
    content,
    entity_type: entityType,
    entity_id: entityId,
    user_id: userId,
  };
  const { data, error } = await supabase
    .from("comments")
    .insert(commentData)
    .select(
      `
      *,
      profiles:user_id (name, avatar_url)
    `
    )
    .single();
  if (error) {
    throw error;
  }
  return {
    id: data.id,
    content: data.content,
    entityType: data.entity_type as "risk" | "incident",
    entityId: data.entity_id,
    userId: data.user_id,
    userName: data.profiles?.name ?? "Unknown User",
    userAvatar: data.profiles?.avatar_url,
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
    deletedAt: data.deleted_at ? new Date(data.deleted_at) : null,
  };
};
export const updateComment = async (commentId: string, content: string) => {
  const { data, error } = await supabase
    .from("comments")
    .update({
      content,
      updated_at: new Date().toISOString(),
    })
    .eq("id", commentId)
    .select()
    .single();
  if (error) {
    throw error;
  }
  return data;
};
export const deleteComment = async (commentId: string) => {
  // Soft delete by setting deleted_at timestamp
  const { error } = await supabase
    .from("comments")
    .update({
      deleted_at: new Date().toISOString(),
    })
    .eq("id", commentId);
  if (error) {
    throw error;
  }
  return true;
};
