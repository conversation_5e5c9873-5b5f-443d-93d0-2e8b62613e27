import { supabase } from '@/integrations/supabase/client';
import { Risk, RiskSeverity, RiskStatus } from '@/types';
import { formatRisksData, formatRiskData } from '@/utils/riskTransformations';

export interface RiskFilters {
  severities?: RiskSeverity[];
  statuses?: RiskStatus[];
  categoryIds?: string[];
  ownerIds?: string[];
  searchTerm?: string;
}

export interface RiskSortOptions {
  field: string;
  order: 'asc' | 'desc';
}

export interface PaginationOptions {
  page: number;
  pageSize: number;
}

export interface RiskQueryResult {
  risks: Risk[];
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

/**
 * Optimized risk service with efficient database queries
 * Designed for handling large datasets with minimal memory footprint
 */
export class RiskService {
  /**
   * Fetch risks with advanced filtering, sorting, and pagination
   */
  static async getRisks(
    organizationId: string,
    filters: RiskFilters = {},
    sort: RiskSortOptions = { field: 'created_at', order: 'desc' },
    pagination: PaginationOptions = { page: 1, pageSize: 50 }
  ): Promise<RiskQueryResult> {
    const { page, pageSize } = pagination;
    const offset = (page - 1) * pageSize;

    // Build optimized query with selective field loading
    let query = supabase
      .from('risks')
      .select(`
        id,
        title,
        description,
        category_id,
        owner_id,
        organization_id,
        severity,
        status,
        likelihood,
        impact,
        inherent_likelihood,
        inherent_impact,
        inherent_severity,
        current_controls,
        mitigation_approach,
        due_date,
        created_at,
        updated_at,
        profiles!risks_owner_id_fkey(name),
        categories:risk_categories(name)
      `, { count: 'exact' })
      .eq('organization_id', organizationId);

    // Apply filters with database-level filtering for performance
    if (filters.severities?.length) {
      query = query.in('severity', filters.severities);
    }

    if (filters.statuses?.length) {
      query = query.in('status', filters.statuses);
    }

    if (filters.categoryIds?.length) {
      query = query.in('category_id', filters.categoryIds);
    }

    if (filters.ownerIds?.length) {
      query = query.in('owner_id', filters.ownerIds);
    }

    // Full-text search optimization
    if (filters.searchTerm?.trim()) {
      const searchTerm = filters.searchTerm.trim();
      // Use PostgreSQL's full-text search capabilities
      query = query.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%,current_controls.ilike.%${searchTerm}%,mitigation_approach.ilike.%${searchTerm}%`);
    }

    // Apply sorting
    query = query.order(sort.field, { ascending: sort.order === 'asc' });

    // Apply pagination
    query = query.range(offset, offset + pageSize - 1);

    const { data, error, count } = await query;

    if (error) {
      throw new Error(`Failed to fetch risks: ${error.message}`);
    }

    const risks = data ? formatRisksData(data) : [];
    const totalCount = count ?? 0;

    return {
      risks,
      totalCount,
      hasNextPage: offset + pageSize < totalCount,
      hasPreviousPage: page > 1,
    };
  }

  /**
   * Get a single risk by ID with related data
   */
  static async getRiskById(riskId: string, organizationId: string): Promise<Risk | null> {
    const { data, error } = await supabase
      .from('risks')
      .select(`
        *,
        profiles!risks_owner_id_fkey(name),
        categories:risk_categories(name)
      `)
      .eq('id', riskId)
      .eq('organization_id', organizationId)
      .maybeSingle();

    if (error) {
      throw new Error(`Failed to fetch risk: ${error.message}`);
    }

    return data ? formatRiskData(data) : null;
  }

  /**
   * Get risk statistics for dashboard (optimized aggregation)
   */
  static async getRiskStatistics(organizationId: string) {
    // Use database aggregation for better performance
    const [severityStats, statusStats, categoryStats] = await Promise.all([
      // Severity distribution
      supabase
        .from('risks')
        .select('severity')
        .eq('organization_id', organizationId),
      
      // Status distribution
      supabase
        .from('risks')
        .select('status')
        .eq('organization_id', organizationId),
      
      // Category distribution with names
      supabase
        .from('risks')
        .select(`
          category_id,
          categories:risk_categories(name)
        `)
        .eq('organization_id', organizationId)
    ]);

    if (severityStats.error || statusStats.error || categoryStats.error) {
      throw new Error('Failed to fetch risk statistics');
    }

    // Process aggregations client-side (could be moved to database functions for even better performance)
    const severityDistribution = severityStats.data?.reduce((acc, risk) => {
      acc[risk.severity] = (acc[risk.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    const statusDistribution = statusStats.data?.reduce((acc, risk) => {
      acc[risk.status] = (acc[risk.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    const categoryDistribution = categoryStats.data?.reduce((acc, risk) => {
      const categoryName = risk.categories?.name ?? 'Uncategorized';
      acc[categoryName] = (acc[categoryName] ?? 0) + 1;
      return acc;
    }, {} as Record<string, number>) || {};

    return {
      totalRisks: severityStats.data?.length ?? 0,
      severityDistribution,
      statusDistribution,
      categoryDistribution,
    };
  }

  /**
   * Bulk operations for large datasets
   */
  static async bulkUpdateRisks(
    riskIds: string[],
    updates: Partial<Risk>,
    organizationId: string
  ): Promise<void> {
    // Process in batches to avoid timeout issues
    const batchSize = 50;
    const batches = [];
    
    for (let i = 0; i < riskIds.length; i += batchSize) {
      batches.push(riskIds.slice(i, i + batchSize));
    }

    for (const batch of batches) {
      const { error } = await supabase
        .from('risks')
        .update(updates)
        .in('id', batch)
        .eq('organization_id', organizationId);

      if (error) {
        throw new Error(`Bulk update failed: ${error.message}`);
      }
    }
  }

  /**
   * Get risks that need attention (due soon, high severity, etc.)
   */
  static async getCriticalRisks(organizationId: string): Promise<Risk[]> {
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

    const { data, error } = await supabase
      .from('risks')
      .select(`
        *,
        profiles!risks_owner_id_fkey(name),
        categories:risk_categories(name)
      `)
      .eq('organization_id', organizationId)
      .or(`severity.eq.critical,severity.eq.high,and(due_date.lte.${thirtyDaysFromNow.toISOString()},status.neq.closed)`)
      .order('severity', { ascending: false })
      .order('due_date', { ascending: true })
      .limit(20);

    if (error) {
      throw new Error(`Failed to fetch critical risks: ${error.message}`);
    }

    return data ? formatRisksData(data) : [];
  }
}
