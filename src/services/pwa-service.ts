/**
 * PWA Service - Manages Progressive Web App functionality
 * Handles service worker registration, offline detection, and app installation
 */
interface BeforeInstallPromptEvent extends Event {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: "accepted" | "dismissed" }>;
}
export interface PWAInstallPrompt {
  prompt: () => Promise<void>;
  userChoice: Promise<{ outcome: "accepted" | "dismissed" }>;
}
export interface OfflineData {
  type: "risk" | "incident" | "comment";
  data: unknown;
  timestamp: number;
  id: string;
}
class PWAService {
  private serviceWorker: ServiceWorkerRegistration | null = null;
  private installPrompt: PWAInstallPrompt | null = null;
  private isOnline = navigator.onLine;
  private offlineQueue: OfflineData[] = [];
  constructor() {
    this.initializeEventListeners();
  }
  /**
   * Initialize PWA service and register service worker
   */
  async initialize(): Promise<void> {
    try {
      // Register service worker
      await this.registerServiceWorker();
      // Setup offline data management
      this.setupOfflineDataManagement();
      // Setup install prompt handling
      this.setupInstallPrompt();
    } catch (error) {
      // Error caught and handled
    }
  }
  /**
   * Register service worker
   */
  private async registerServiceWorker(): Promise<void> {
    if ("serviceWorker" in navigator) {
      try {
        this.serviceWorker = await navigator.serviceWorker.register("/sw.js", {
          scope: "/",
        });
        // Handle service worker updates
        this.serviceWorker.addEventListener("updatefound", () => {
          const newWorker = this.serviceWorker?.installing;
          if (newWorker) {
            newWorker.addEventListener("statechange", () => {
              if (newWorker.state === "installed" && navigator.serviceWorker.controller) {
                // New service worker is available
                this.notifyServiceWorkerUpdate();
              }
            });
          }
        });
      } catch (error) {
        throw error;
      }
    } else {
      // Else case handled
    }
  }
  /**
   * Setup offline data management
   */
  private setupOfflineDataManagement(): void {
    // Load offline queue from localStorage
    const savedQueue = localStorage.getItem("pwa-offline-queue");
    if (savedQueue) {
      try {
        this.offlineQueue = JSON.parse(savedQueue);
      } catch (error) {
        this.offlineQueue = [];
      }
    }
    // Sync offline data when connection is restored
    window.addEventListener("online", () => {
      this.syncOfflineData();
    });
  }
  /**
   * Setup install prompt handling
   */
  private setupInstallPrompt(): void {
    window.addEventListener("beforeinstallprompt", e => {
      e.preventDefault();
      this.installPrompt = e as BeforeInstallPromptEvent;
    });
    window.addEventListener("appinstalled", () => {
      this.installPrompt = null;
    });
  }
  /**
   * Initialize event listeners
   */
  private initializeEventListeners(): void {
    // Online/offline status
    window.addEventListener("online", () => {
      this.isOnline = true;
    });
    window.addEventListener("offline", () => {
      this.isOnline = false;
    });
  }
  /**
   * Check if app can be installed
   */
  canInstall(): boolean {
    return this.installPrompt !== null;
  }
  /**
   * Trigger app installation
   */
  async install(): Promise<boolean> {
    if (!this.installPrompt) {
      return false;
    }
    try {
      await this.installPrompt.prompt();
      const choiceResult = await this.installPrompt.userChoice;
      if (choiceResult.outcome === "accepted") {
        this.installPrompt = null;
        return true;
      } else {
        return false;
      }
    } catch (error) {
      return false;
    }
  }
  /**
   * Check if user is online
   */
  isUserOnline(): boolean {
    return this.isOnline;
  }
  /**
   * Add data to offline queue
   */
  addToOfflineQueue(data: Omit<OfflineData, "timestamp" | "id">): void {
    const offlineData: OfflineData = {
      ...data,
      timestamp: Date.now(),
      id: crypto.randomUUID(),
    };
    this.offlineQueue.push(offlineData);
    this.saveOfflineQueue();
  }
  /**
   * Get offline queue
   */
  getOfflineQueue(): OfflineData[] {
    return [...this.offlineQueue];
  }
  /**
   * Clear offline queue
   */
  clearOfflineQueue(): void {
    this.offlineQueue = [];
    this.saveOfflineQueue();
  }
  /**
   * Save offline queue to localStorage
   */
  private saveOfflineQueue(): void {
    try {
      localStorage.setItem("pwa-offline-queue", JSON.stringify(this.offlineQueue));
    } catch (error) {
      // Error caught and handled
    }
  }
  /**
   * Sync offline data when connection is restored
   */
  private async syncOfflineData(): Promise<void> {
    if (this.offlineQueue.length === 0) {
      return;
    }
    const itemsToSync = [...this.offlineQueue];
    const syncedItems: string[] = [];
    for (const item of itemsToSync) {
      try {
        await this.syncOfflineItem(item);
        syncedItems.push(item.id);
      } catch (error) {
        // Error caught and handled
      }
    }
    // Remove successfully synced items
    this.offlineQueue = this.offlineQueue.filter(item => !syncedItems.includes(item.id));
    this.saveOfflineQueue();
    if (syncedItems.length > 0) {
      // Condition handled
    }
  }
  /**
   * Sync individual offline item
   */
  private async syncOfflineItem(item: OfflineData): Promise<void> {
    const endpoint = this.getEndpointForType(item.type);
    const response = await fetch(endpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(item.data),
    });
    if (!response.ok) {
      throw new Error(`Sync failed: ${response.status} ${response.statusText}`);
    }
  }
  /**
   * Get API endpoint for data type
   */
  private getEndpointForType(type: OfflineData["type"]): string {
    switch (type) {
      case "risk":
        return "/api/risks";
      case "incident":
        return "/api/incidents";
      case "comment":
        return "/api/comments";
      default:
        throw new Error(`Unknown data type: ${type}`);
    }
  }
  /**
   * Notify user about service worker update
   */
  private notifyServiceWorkerUpdate(): void {
    // This could trigger a toast notification or modal
    // You could dispatch a custom event here for the UI to handle
    window.dispatchEvent(new CustomEvent("pwa-update-available"));
  }
  /**
   * Update service worker
   */
  async updateServiceWorker(): Promise<void> {
    if (this.serviceWorker?.waiting) {
      this.serviceWorker.waiting.postMessage({ type: "SKIP_WAITING" });
      window.location.reload();
    }
  }
  /**
   * Request notification permission
   */
  async requestNotificationPermission(): Promise<NotificationPermission> {
    if ("Notification" in window) {
      return await Notification.requestPermission();
    }
    return "denied";
  }
  /**
   * Show notification
   */
  async showNotification(title: string, options?: NotificationOptions): Promise<void> {
    if (this.serviceWorker && "Notification" in window && Notification.permission === "granted") {
      await this.serviceWorker.showNotification(title, {
        icon: "/icons/icon-192x192.png",
        badge: "/icons/badge-72x72.png",
        ...options,
      });
    }
  }
}
// Create singleton instance
export const pwaService = new PWAService();
// Auto-initialize when module is imported
if (typeof window !== "undefined") {
  pwaService.initialize().catch(() => {
    // PWA initialization failed - handled silently
  });
}
