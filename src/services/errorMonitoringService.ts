/**
 * Enhanced Error Monitoring Service
 * Provides comprehensive error tracking, categorization, and alerting
 */

import { ErrorContext, ErrorSeverity, ErrorCategory } from "@/types";
import { AppError } from "@/utils/errors/AppError";
import { logger } from "@/utils/errors/Logger";
import { loggingService } from "./loggingService";

export interface ErrorMetrics {
  totalErrors: number;
  errorsByCategory: Record<ErrorCategory, number>;
  errorsBySeverity: Record<ErrorSeverity, number>;
  errorsByComponent: Record<string, number>;
  errorRate: number; // errors per minute
  criticalErrors: number;
  recoverableErrors: number;
  lastErrorTime?: Date;
}

export interface ErrorAlert {
  id: string;
  type: "threshold" | "critical" | "pattern" | "spike";
  severity: ErrorSeverity;
  message: string;
  timestamp: Date;
  context: ErrorContext;
  threshold?: number;
  currentValue?: number;
  resolved: boolean;
}

export interface ErrorPattern {
  pattern: string;
  count: number;
  firstSeen: Date;
  lastSeen: Date;
  components: string[];
  severity: ErrorSeverity;
}

export interface ErrorThreshold {
  category: ErrorCategory | "total";
  severity?: ErrorSeverity;
  maxErrorsPerMinute: number;
  maxErrorsPerHour: number;
  alertOnCritical: boolean;
}

export interface ErrorMonitoringConfig {
  enabled: boolean;
  trackingWindowMinutes: number;
  alertThresholds: ErrorThreshold[];
  enablePatternDetection: boolean;
  enableRealTimeAlerts: boolean;
  maxStoredErrors: number;
  alertCooldownMinutes: number;
}

/**
 * Comprehensive error monitoring and alerting service
 */
export class ErrorMonitoringService {
  private static instance: ErrorMonitoringService;
  private config: ErrorMonitoringConfig;
  private errorHistory: Array<{ error: AppError; timestamp: Date }> = [];
  private alerts: ErrorAlert[] = [];
  private patterns: Map<string, ErrorPattern> = new Map();
  private alertCooldowns: Map<string, Date> = new Map();
  private metrics: ErrorMetrics;
  private metricsUpdateInterval: NodeJS.Timeout | null = null;

  private constructor(config: Partial<ErrorMonitoringConfig> = {}) {
    this.config = {
      enabled: true,
      trackingWindowMinutes: 60,
      alertThresholds: [
        {
          category: "total",
          maxErrorsPerMinute: 10,
          maxErrorsPerHour: 100,
          alertOnCritical: true,
        },
        {
          category: ErrorCategory.SYSTEM,
          maxErrorsPerMinute: 5,
          maxErrorsPerHour: 50,
          alertOnCritical: true,
        },
        {
          category: ErrorCategory.NETWORK,
          maxErrorsPerMinute: 15,
          maxErrorsPerHour: 200,
          alertOnCritical: false,
        },
        {
          category: ErrorCategory.AUTHENTICATION,
          maxErrorsPerMinute: 3,
          maxErrorsPerHour: 20,
          alertOnCritical: true,
        },
      ],
      enablePatternDetection: true,
      enableRealTimeAlerts: true,
      maxStoredErrors: 1000,
      alertCooldownMinutes: 5,
      ...config,
    };

    this.metrics = this.initializeMetrics();
    this.startMetricsUpdater();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(config?: Partial<ErrorMonitoringConfig>): ErrorMonitoringService {
    if (!ErrorMonitoringService.instance) {
      ErrorMonitoringService.instance = new ErrorMonitoringService(config);
    }
    return ErrorMonitoringService.instance;
  }

  /**
   * Initialize metrics structure
   */
  private initializeMetrics(): ErrorMetrics {
    return {
      totalErrors: 0,
      errorsByCategory: {
        [ErrorCategory.VALIDATION]: 0,
        [ErrorCategory.NETWORK]: 0,
        [ErrorCategory.AUTHENTICATION]: 0,
        [ErrorCategory.AUTHORIZATION]: 0,
        [ErrorCategory.BUSINESS_LOGIC]: 0,
        [ErrorCategory.SYSTEM]: 0,
        [ErrorCategory.USER_INPUT]: 0,
        [ErrorCategory.DATA_PROCESSING]: 0,
        [ErrorCategory.EXTERNAL_SERVICE]: 0,
      },
      errorsBySeverity: {
        [ErrorSeverity.LOW]: 0,
        [ErrorSeverity.MEDIUM]: 0,
        [ErrorSeverity.HIGH]: 0,
        [ErrorSeverity.CRITICAL]: 0,
      },
      errorsByComponent: {},
      errorRate: 0,
      criticalErrors: 0,
      recoverableErrors: 0,
    };
  }

  /**
   * Track an error occurrence
   */
  public trackError(error: AppError, additionalContext?: Partial<ErrorContext>): void {
    if (!this.config.enabled) return;

    const timestamp = new Date();
    const errorEntry = { error, timestamp };

    // Add to history
    this.errorHistory.push(errorEntry);
    this.maintainHistorySize();

    // Update metrics
    this.updateMetrics(error);

    // Detect patterns
    if (this.config.enablePatternDetection) {
      this.detectErrorPattern(error);
    }

    // Check thresholds and generate alerts
    if (this.config.enableRealTimeAlerts) {
      this.checkThresholds(error);
    }

    // Log structured error
    this.logStructuredError(error, additionalContext);

    // Log monitoring event
    loggingService.audit(
      "error_tracked",
      error.context.userId || "anonymous",
      {
        errorId: error.id,
        category: error.category,
        severity: error.severity,
        component: error.context.component,
        recoverable: error.isRecoverable(),
      },
      error.context
    );
  }

  /**
   * Maintain error history size limit
   */
  private maintainHistorySize(): void {
    if (this.errorHistory.length > this.config.maxStoredErrors) {
      this.errorHistory = this.errorHistory.slice(-this.config.maxStoredErrors);
    }
  }

  /**
   * Update error metrics
   */
  private updateMetrics(error: AppError): void {
    this.metrics.totalErrors++;
    this.metrics.errorsByCategory[error.category]++;
    this.metrics.errorsBySeverity[error.severity]++;

    const component = error.context.component || "unknown";
    this.metrics.errorsByComponent[component] =
      (this.metrics.errorsByComponent[component] || 0) + 1;

    if (error.severity === ErrorSeverity.CRITICAL) {
      this.metrics.criticalErrors++;
    }

    if (error.isRecoverable()) {
      this.metrics.recoverableErrors++;
    }

    this.metrics.lastErrorTime = new Date();

    // Update error rate immediately
    this.metrics.errorRate = this.calculateErrorRate();
  }

  /**
   * Calculate error rate (errors per minute)
   */
  private calculateErrorRate(): number {
    const now = new Date();
    const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);

    const recentErrors = this.errorHistory.filter(entry => entry.timestamp >= oneMinuteAgo);

    return recentErrors.length;
  }

  /**
   * Detect error patterns
   */
  private detectErrorPattern(error: AppError): void {
    const patternKey = this.generatePatternKey(error);
    const existing = this.patterns.get(patternKey);

    if (existing) {
      existing.count++;
      existing.lastSeen = new Date();
      if (!existing.components.includes(error.context.component || "unknown")) {
        existing.components.push(error.context.component || "unknown");
      }
    } else {
      this.patterns.set(patternKey, {
        pattern: patternKey,
        count: 1,
        firstSeen: new Date(),
        lastSeen: new Date(),
        components: [error.context.component || "unknown"],
        severity: error.severity,
      });
    }

    // Alert on pattern if it's becoming frequent
    const pattern = this.patterns.get(patternKey)!;
    if (pattern.count >= 5 && pattern.count % 5 === 0) {
      this.createAlert({
        type: "pattern",
        severity: pattern.severity,
        message: `Error pattern detected: ${patternKey} (${pattern.count} occurrences)`,
        context: error.context,
        currentValue: pattern.count,
      });
    }
  }

  /**
   * Generate pattern key for error grouping
   */
  private generatePatternKey(error: AppError): string {
    // Group by error category, component, and error code/type
    const category = error.category.toLowerCase();
    const component = error.context.component || "unknown";
    const errorCode =
      (error as any).errorCode || (error as any).code || error.constructor.name.toLowerCase();
    return `${category}_${component}_${errorCode}`;
  }

  /**
   * Check error thresholds and create alerts
   */
  private checkThresholds(error: AppError): void {
    const now = new Date();

    for (const threshold of this.config.alertThresholds) {
      // Check if we're in cooldown for this threshold
      const cooldownKey = `${threshold.category}_${threshold.severity || "all"}`;
      const lastAlert = this.alertCooldowns.get(cooldownKey);

      if (
        lastAlert &&
        now.getTime() - lastAlert.getTime() < this.config.alertCooldownMinutes * 60 * 1000
      ) {
        continue;
      }

      // Check minute threshold
      const minuteRate = this.calculateErrorRateForThreshold(threshold, 1);
      if (minuteRate >= threshold.maxErrorsPerMinute) {
        this.createAlert({
          type: "threshold",
          severity: ErrorSeverity.HIGH,
          message: `Error rate threshold exceeded: ${minuteRate} errors/minute (limit: ${threshold.maxErrorsPerMinute})`,
          context: error.context,
          threshold: threshold.maxErrorsPerMinute,
          currentValue: minuteRate,
        });
        this.alertCooldowns.set(cooldownKey, now);
      }

      // Check hour threshold
      const hourRate = this.calculateErrorRateForThreshold(threshold, 60);
      if (hourRate >= threshold.maxErrorsPerHour) {
        this.createAlert({
          type: "threshold",
          severity: ErrorSeverity.MEDIUM,
          message: `Hourly error threshold exceeded: ${hourRate} errors/hour (limit: ${threshold.maxErrorsPerHour})`,
          context: error.context,
          threshold: threshold.maxErrorsPerHour,
          currentValue: hourRate,
        });
        this.alertCooldowns.set(cooldownKey, now);
      }

      // Check critical error alert
      if (threshold.alertOnCritical && error.severity === ErrorSeverity.CRITICAL) {
        this.createAlert({
          type: "critical",
          severity: ErrorSeverity.CRITICAL,
          message: `Critical error occurred in ${error.context.component || "unknown component"}`,
          context: error.context,
        });
      }
    }
  }

  /**
   * Calculate error rate for specific threshold
   */
  private calculateErrorRateForThreshold(threshold: ErrorThreshold, minutes: number): number {
    const now = new Date();
    const timeAgo = new Date(now.getTime() - minutes * 60 * 1000);

    return this.errorHistory.filter(entry => {
      if (entry.timestamp < timeAgo) return false;

      if (threshold.category === "total") return true;

      if (threshold.category !== entry.error.category) return false;

      if (threshold.severity && threshold.severity !== entry.error.severity) return false;

      return true;
    }).length;
  }

  /**
   * Create and store alert
   */
  private createAlert(alertData: {
    type: ErrorAlert["type"];
    severity: ErrorSeverity;
    message: string;
    context: ErrorContext;
    threshold?: number;
    currentValue?: number;
  }): void {
    const alert: ErrorAlert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      timestamp: new Date(),
      resolved: false,
      ...alertData,
    };

    this.alerts.push(alert);

    // Log alert
    logger.error(`Error Alert: ${alert.message}`, undefined, alert.context, {
      alertId: alert.id,
      alertType: alert.type,
      threshold: alert.threshold,
      currentValue: alert.currentValue,
    });

    // Notify via logging service
    loggingService.error(`Error monitoring alert: ${alert.message}`, undefined, alert.context);

    // In a real implementation, this could trigger:
    // - Email notifications
    // - Slack/Teams messages
    // - PagerDuty alerts
    // - Dashboard notifications
  }

  /**
   * Log structured error with full context
   */
  private logStructuredError(error: AppError, additionalContext?: Partial<ErrorContext>): void {
    const fullContext = {
      ...error.context,
      ...additionalContext,
    };

    const structuredLog = {
      errorId: error.id,
      category: error.category,
      severity: error.severity,
      code: error.code,
      userMessage: error.userMessage,
      technicalMessage: error.technicalMessage,
      recoverable: error.isRecoverable(),
      critical: error.isCritical(),
      context: fullContext,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      sessionId: logger.getCurrentCorrelationId(),
      metadata: error.toMetadata(),
    };

    // Log based on severity
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        logger.critical("Critical error occurred", error, fullContext, structuredLog);
        break;
      case ErrorSeverity.HIGH:
        logger.error("High severity error occurred", error, fullContext, structuredLog);
        break;
      case ErrorSeverity.MEDIUM:
        logger.warn("Medium severity error occurred", fullContext, structuredLog);
        break;
      case ErrorSeverity.LOW:
        logger.info("Low severity error occurred", fullContext, structuredLog);
        break;
    }
  }

  /**
   * Start metrics updater
   */
  private startMetricsUpdater(): void {
    if (this.metricsUpdateInterval) {
      clearInterval(this.metricsUpdateInterval);
    }

    this.metricsUpdateInterval = setInterval(() => {
      this.metrics.errorRate = this.calculateErrorRate();
      this.cleanupOldData();
    }, 30000); // Update every 30 seconds
  }

  /**
   * Cleanup old data based on tracking window
   */
  private cleanupOldData(): void {
    const cutoffTime = new Date(Date.now() - this.config.trackingWindowMinutes * 60 * 1000);

    // Clean error history
    this.errorHistory = this.errorHistory.filter(entry => entry.timestamp >= cutoffTime);

    // Clean old patterns
    for (const [key, pattern] of this.patterns.entries()) {
      if (pattern.lastSeen < cutoffTime) {
        this.patterns.delete(key);
      }
    }

    // Clean old alert cooldowns
    for (const [key, timestamp] of this.alertCooldowns.entries()) {
      if (timestamp < cutoffTime) {
        this.alertCooldowns.delete(key);
      }
    }
  }

  /**
   * Get current error metrics
   */
  public getMetrics(): ErrorMetrics {
    return { ...this.metrics };
  }

  /**
   * Get recent alerts
   */
  public getAlerts(limit: number = 50): ErrorAlert[] {
    return this.alerts
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * Get unresolved alerts
   */
  public getUnresolvedAlerts(): ErrorAlert[] {
    return this.alerts.filter(alert => !alert.resolved);
  }

  /**
   * Resolve an alert
   */
  public resolveAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      loggingService.info(`Alert resolved: ${alertId}`, { alertId });
      return true;
    }
    return false;
  }

  /**
   * Get error patterns
   */
  public getErrorPatterns(): ErrorPattern[] {
    return Array.from(this.patterns.values()).sort((a, b) => b.count - a.count);
  }

  /**
   * Get error history for analysis
   */
  public getErrorHistory(minutes?: number): Array<{ error: AppError; timestamp: Date }> {
    if (!minutes) return [...this.errorHistory];

    const cutoffTime = new Date(Date.now() - minutes * 60 * 1000);
    return this.errorHistory.filter(entry => entry.timestamp >= cutoffTime);
  }

  /**
   * Update configuration
   */
  public updateConfig(config: Partial<ErrorMonitoringConfig>): void {
    this.config = { ...this.config, ...config };

    if (config.trackingWindowMinutes) {
      this.startMetricsUpdater();
    }
  }

  /**
   * Get current configuration
   */
  public getConfig(): ErrorMonitoringConfig {
    return { ...this.config };
  }

  /**
   * Reset all data (useful for testing)
   */
  public reset(): void {
    this.errorHistory = [];
    this.alerts = [];
    this.patterns.clear();
    this.alertCooldowns.clear();
    this.metrics = this.initializeMetrics();
  }

  /**
   * Cleanup resources
   */
  public destroy(): void {
    if (this.metricsUpdateInterval) {
      clearInterval(this.metricsUpdateInterval);
      this.metricsUpdateInterval = null;
    }
  }
}

// Export singleton instance

export const errorMonitoringService = ErrorMonitoringService.getInstance();
