import { Risk, RiskStatus } from "@/types";
import { riskRepository } from "@/repositories/riskRepository";
import { supabase } from "@/integrations/supabase/client";

/**
 * Get the current user's organization ID
 */
const getCurrentUserOrganizationId = async (): Promise<string> => {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) {
    throw new Error("User not authenticated");
  }

  const { data: userProfile } = await supabase
    .from("profiles")
    .select("organization_id")
    .eq("id", user.id)
    .single();

  if (!userProfile?.organization_id) {
    throw new Error("User organization not found");
  }

  return userProfile.organization_id;
};

/**
 * Reopens a closed risk by updating its status
 */

export const reopenRisk = async (riskId: string, organizationId?: string): Promise<Risk | null> => {
  try {
    // Get organization ID from user context if not provided
    const orgId = organizationId || (await getCurrentUserOrganizationId());

    // Validate if the risk exists before attempting to update
    const existingRisk = await riskRepository.getRiskById(riskId, orgId);
    if (!existingRisk) {
      throw new Error("Risk not found or access denied");
    }

    // Check if the risk is already in the appropriate status
    if (existingRisk.status === RiskStatus.IN_PROGRESS) {
      import("../services/loggingService").then(({ log }) => {
        log.info("Risk is already in IN_PROGRESS status", undefined, {
          component: "risk_service",
          action: "reopen_risk",
          additionalData: { riskId, status: existingRisk.status },
        });
      });
      return existingRisk;
    }

    const success = await riskRepository.updateRiskStatus(riskId, RiskStatus.IN_PROGRESS, orgId);

    if (!success) {
      throw new Error("Failed to reopen risk");
    }

    // Return the updated risk
    return await riskRepository.getRiskById(riskId, orgId);
  } catch (error) {
    import("../services/loggingService").then(({ log }) => {
      log.error("Error reopening risk", error as Error, {
        component: "risk_service",
        action: "reopen_risk",
        additionalData: { riskId, orgId },
      });
    });
    throw error;
  }
};

/**
 * Closes a risk by updating its status
 */

export const closeRisk = async (riskId: string, organizationId?: string): Promise<Risk | null> => {
  try {
    // Get organization ID from user context if not provided
    const orgId = organizationId || (await getCurrentUserOrganizationId());

    // Validate if the risk exists before attempting to update
    const existingRisk = await riskRepository.getRiskById(riskId, orgId);
    if (!existingRisk) {
      throw new Error("Risk not found or access denied");
    }

    // Check if the risk is already closed
    if (existingRisk.status === RiskStatus.CLOSED) {
      import("../services/loggingService").then(({ log }) => {
        log.info("Risk is already in CLOSED status", undefined, {
          component: "risk_service",
          action: "close_risk",
          additionalData: { riskId, status: existingRisk.status },
        });
      });
      return existingRisk;
    }

    const success = await riskRepository.updateRiskStatus(riskId, RiskStatus.CLOSED, orgId);

    if (!success) {
      throw new Error("Failed to close risk");
    }

    // Return the updated risk
    return await riskRepository.getRiskById(riskId, orgId);
  } catch (error) {
    import("../services/loggingService").then(({ log }) => {
      log.error("Error closing risk", error as Error, {
        component: "risk_service",
        action: "close_risk",
        additionalData: { riskId, orgId },
      });
    });
    throw error;
  }
};
