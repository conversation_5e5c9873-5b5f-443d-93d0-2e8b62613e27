import { supabase } from "@/integrations/supabase/client";
import { IncidentFormValues } from "@/hooks/useIncidentForm";
import { User } from "@/types";
import {
  ServiceResult,
  IncidentApiResponse,
  CreateIncidentRequest,
  transformSupabaseError,
} from "@/types/api";
import { isIncidentApiResponse } from "@/utils/api-validation";

export const createIncident = async (
  values: IncidentFormValues,
  user: User
): Promise<ServiceResult<IncidentApiResponse>> => {
  try {
    if (!user.organizationId) {
      return {
        success: false,
        error: {
          message: "User organization not found",
          code: "MISSING_ORGANIZATION",
          category: "validation",
          recoverable: false,
        },
      };
    }

    // Prepare the data for insertion
    const incidentData: CreateIncidentRequest = {
      title: values.title,
      description: values.description,
      severity: values.severity,
      status: values.status,
      reporter_id: user.id,
      organization_id: user.organizationId,
      related_risk_id: values.relatedRiskId ?? undefined,
      date: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from("incidents")
      .insert(incidentData)
      .select("*")
      .single();

    if (error) {
      return {
        success: false,
        error: transformSupabaseError(error),
      };
    }

    if (!isIncidentApiResponse(data)) {
      return {
        success: false,
        error: {
          message: "Invalid incident response format",
          code: "INVALID_RESPONSE",
          category: "validation",
          recoverable: false,
        },
      };
    }

    return {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : "Unknown error occurred",
        code: "UNEXPECTED_ERROR",
        category: "system",
        recoverable: false,
      },
    };
  }
};

export const updateIncident = async (
  id: string,
  values: IncidentFormValues
): Promise<ServiceResult<IncidentApiResponse>> => {
  try {
    // Prepare the data for update
    const incidentData: Partial<CreateIncidentRequest> = {
      title: values.title,
      description: values.description,
      severity: values.severity,
      status: values.status,
      related_risk_id: values.relatedRiskId ?? undefined,
    };

    const { data, error } = await supabase
      .from("incidents")
      .update({
        ...incidentData,
        updated_at: new Date().toISOString(),
      })
      .eq("id", id)
      .select("*")
      .single();

    if (error) {
      return {
        success: false,
        error: transformSupabaseError(error),
      };
    }

    if (!isIncidentApiResponse(data)) {
      return {
        success: false,
        error: {
          message: "Invalid incident response format",
          code: "INVALID_RESPONSE",
          category: "validation",
          recoverable: false,
        },
      };
    }

    return {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : "Unknown error occurred",
        code: "UNEXPECTED_ERROR",
        category: "system",
        recoverable: false,
      },
    };
  }
};

export const reopenIncident = async (
  incidentId: string
): Promise<ServiceResult<IncidentApiResponse>> => {
  try {
    // Prepare the data for update
    const updateData = {
      status: "Open",
      updated_at: new Date().toISOString(),
    };

    const { data, error } = await supabase
      .from("incidents")
      .update(updateData)
      .eq("id", incidentId)
      .select("*")
      .single();

    if (error) {
      return {
        success: false,
        error: transformSupabaseError(error),
      };
    }

    if (!isIncidentApiResponse(data)) {
      return {
        success: false,
        error: {
          message: "Invalid incident response format",
          code: "INVALID_RESPONSE",
          category: "validation",
          recoverable: false,
        },
      };
    }

    return {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      success: false,
      error: {
        message: error instanceof Error ? error.message : "Unknown error occurred",
        code: "UNEXPECTED_ERROR",
        category: "system",
        recoverable: false,
      },
    };
  }
};
