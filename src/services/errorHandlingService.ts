/**
 * Centralized error handling service for API calls
 * Provides type-safe error handling and recovery mechanisms
 */
import { ServiceResult, ServiceError, ServiceMetadata } from "@/types/api";
import { ErrorContext, ErrorSeverity, ErrorCategory } from "@/types";
import { AppError, SystemError } from "@/utils/errors";
import { errorMonitoringService } from "./errorMonitoringService";
import { loggingService } from "./loggingService";
export interface ErrorHandlingOptions {
  retryCount?: number;
  retryDelay?: number;
  fallbackValue?: unknown;
  logError?: boolean;
  userMessage?: string;
}
export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffMultiplier: 2,
};
/**
 * Execute an async operation with error handling and retry logic
 */
export async function executeWithErrorHandling<T>(
  operation: () => Promise<T>,
  options: ErrorHandlingOptions = {},
  context?: Partial<ErrorContext>
): Promise<ServiceResult<T>> {
  const { retryCount = 0, retryDelay = 1000, logError = true, userMessage } = options;
  const operationContext: ErrorContext = {
    userId: context?.userId || "anonymous",
    organizationId: context?.organizationId || "unknown",
    component: context?.component || "service",
    action: context?.action || "operation",
    timestamp: new Date(),
    ...context,
  };
  try {
    const data = await operation();
    return {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
        retryCount,
      },
    };
  } catch (error) {
    const serviceError = transformError(error, userMessage);
    // Convert ServiceError to AppError for monitoring
    const appError = createAppErrorFromServiceError(serviceError, operationContext);
    // Track error in monitoring system
    errorMonitoringService.trackError(appError, operationContext);
    if (logError) {
      loggingService.error(
        `Service operation failed: ${serviceError.message}`,
        error instanceof Error ? error : undefined,
        operationContext
      );
    }
    // Check if error is recoverable and we haven't exceeded retry limit
    if (serviceError.recoverable && retryCount < DEFAULT_RETRY_CONFIG.maxRetries) {
      const delay = Math.min(
        retryDelay * Math.pow(DEFAULT_RETRY_CONFIG.backoffMultiplier, retryCount),
        DEFAULT_RETRY_CONFIG.maxDelay
      );
      loggingService.info(
        `Retrying operation (attempt ${retryCount + 1}/${DEFAULT_RETRY_CONFIG.maxRetries})`,
        { delay, retryCount },
        operationContext
      );
      await new Promise(resolve => setTimeout(resolve, delay));
      return executeWithErrorHandling(
        operation,
        {
          ...options,
          retryCount: retryCount + 1,
          retryDelay: delay,
        },
        context
      );
    }
    return {
      success: false,
      error: serviceError,
      metadata: {
        timestamp: new Date().toISOString(),
        retryCount,
      },
    };
  }
}
/**
 * Convert ServiceError to AppError for monitoring
 */
function createAppErrorFromServiceError(
  serviceError: ServiceError,
  context: ErrorContext
): AppError {
  const severity = mapCategoryToSeverity(serviceError.category);
  const category = mapServiceCategoryToErrorCategory(serviceError.category);
  return new SystemError(
    serviceError.message,
    serviceError.code || "UNKNOWN",
    serviceError.code,
    context,
    severity
  );
}
/**
 * Map service error category to error severity
 */
function mapCategoryToSeverity(category: ServiceError["category"]): ErrorSeverity {
  switch (category) {
    case "authentication":
    case "authorization":
      return ErrorSeverity.HIGH;
    case "validation":
    case "business_logic":
      return ErrorSeverity.MEDIUM;
    case "network":
      return ErrorSeverity.LOW;
    case "system":
      return ErrorSeverity.HIGH;
    default:
      return ErrorSeverity.MEDIUM;
  }
}
/**
 * Map service error category to ErrorCategory enum
 */
function mapServiceCategoryToErrorCategory(category: ServiceError["category"]): ErrorCategory {
  switch (category) {
    case "validation":
      return ErrorCategory.VALIDATION;
    case "network":
      return ErrorCategory.NETWORK;
    case "authentication":
      return ErrorCategory.AUTHENTICATION;
    case "authorization":
      return ErrorCategory.AUTHORIZATION;
    case "business_logic":
      return ErrorCategory.BUSINESS_LOGIC;
    case "system":
      return ErrorCategory.SYSTEM;
    default:
      return ErrorCategory.SYSTEM;
  }
}
/**
 * Transform various error types into standardized ServiceError
 */
export function transformError(error: unknown, userMessage?: string): ServiceError {
  if (error instanceof Error) {
    return {
      message: userMessage || error.message,
      code: getErrorCode(error),
      category: categorizeError(error),
      details: {
        originalMessage: error.message,
        stack: error.stack,
      },
      recoverable: isRecoverableError(error),
    };
  }
  if (typeof error === "string") {
    return {
      message: userMessage || error,
      code: "STRING_ERROR",
      category: "system",
      recoverable: false,
    };
  }
  if (typeof error === "object" && error !== null) {
    const errorObj = error as Record<string, unknown>;
    return {
      message: userMessage || String(errorObj.message || "Unknown error"),
      code: String(errorObj.code || "OBJECT_ERROR"),
      category: "system",
      details: errorObj,
      recoverable: false,
    };
  }
  return {
    message: userMessage || "An unexpected error occurred",
    code: "UNKNOWN_ERROR",
    category: "system",
    recoverable: false,
  };
}
/**
 * Extract error code from Error object
 */
function getErrorCode(error: Error): string {
  // Check for common error code properties
  const errorWithCode = error as Error & { code?: string };
  if (errorWithCode.code) {
    return errorWithCode.code;
  }
  // Extract code from error message patterns
  if (error.message.includes("PGRST")) {
    const match = error.message.match(/PGRST\d+/);
    return match ? match[0] : "DATABASE_ERROR";
  }
  if (error.message.includes("auth")) {
    return "AUTH_ERROR";
  }
  if (error.message.includes("network") || error.message.includes("fetch")) {
    return "NETWORK_ERROR";
  }
  return error.name || "GENERIC_ERROR";
}
/**
 * Categorize error based on its characteristics
 */
function categorizeError(error: Error): ServiceError["category"] {
  const message = error.message.toLowerCase();
  const code = getErrorCode(error).toLowerCase();
  if (code.includes("auth") || message.includes("unauthorized") || message.includes("forbidden")) {
    return "authentication";
  }
  if (code.includes("permission") || message.includes("access denied")) {
    return "authorization";
  }
  if (
    code.includes("validation") ||
    message.includes("invalid") ||
    message.includes("constraint")
  ) {
    return "validation";
  }
  if (
    code.includes("network") ||
    message.includes("fetch") ||
    message.includes("connection") ||
    message.includes("network")
  ) {
    return "network";
  }
  if (message.includes("business") || message.includes("rule")) {
    return "business_logic";
  }
  return "system";
}
/**
 * Determine if an error is recoverable (can be retried)
 */
function isRecoverableError(error: Error): boolean {
  const message = error.message.toLowerCase();
  const code = getErrorCode(error).toLowerCase();
  // Network errors are usually recoverable
  if (
    code.includes("network") ||
    message.includes("fetch") ||
    message.includes("timeout") ||
    message.includes("connection")
  ) {
    return true;
  }
  // Temporary database issues
  if (code === "pgrst301" || message.includes("temporary")) {
    return true;
  }
  // Rate limiting
  if (code.includes("rate") || message.includes("too many requests")) {
    return true;
  }
  // Server errors (5xx) are potentially recoverable
  if (message.includes("server error") || message.includes("internal error")) {
    return true;
  }
  // Authentication and validation errors are not recoverable
  if (code.includes("auth") || code.includes("validation") || message.includes("invalid")) {
    return false;
  }
  return false;
}
/**
 * Create a user-friendly error message from ServiceError
 */
export function createUserFriendlyMessage(error: ServiceError): string {
  switch (error.category) {
    case "authentication":
      return "Please log in again to continue.";
    case "authorization":
      return "You do not have permission to perform this action.";
    case "validation":
      return "Please check your input and try again.";
    case "network":
      return "Connection issue. Please check your internet connection and try again.";
    case "business_logic":
      return error.message; // Business logic errors usually have meaningful messages
    default:
      return "An unexpected error occurred. Please try again later.";
  }
}
/**
 * Log error with structured information
 */
export function logStructuredError(
  error: ServiceError,
  context: {
    operation: string;
    userId?: string;
    organizationId?: string;
    additionalData?: Record<string, unknown>;
  }
): void {
  const logEntry = {
    timestamp: new Date().toISOString(),
    level: getLogLevel(error),
    category: error.category,
    code: error.code,
    message: error.message,
    context,
    details: error.details,
    recoverable: error.recoverable,
  };
  // In production, this would be sent to a logging service
}
/**
 * Determine log level based on error characteristics
 */
function getLogLevel(error: ServiceError): "debug" | "info" | "warn" | "error" | "fatal" {
  if (error.category === "validation") {
    return "warn";
  }
  if (error.category === "authentication" || error.category === "authorization") {
    return "warn";
  }
  if (error.category === "network" && error.recoverable) {
    return "warn";
  }
  if (error.category === "business_logic") {
    return "info";
  }
  return "error";
}
/**
 * Create a fallback result when primary operation fails
 */
export function createFallbackResult<T>(
  fallbackValue: T,
  originalError: ServiceError
): ServiceResult<T> {
  return {
    success: true,
    data: fallbackValue,
    metadata: {
      timestamp: new Date().toISOString(),
      fallbackUsed: true,
      originalError: originalError.message,
    },
  };
}
/**
 * Combine multiple service results into a single result
 */
export function combineServiceResults<T>(results: ServiceResult<T>[]): ServiceResult<T[]> {
  const successfulResults = results.filter(result => result.success);
  const failedResults = results.filter(result => !result.success);
  if (failedResults.length === 0) {
    return {
      success: true,
      data: successfulResults.map(result => result.data!),
      metadata: {
        timestamp: new Date().toISOString(),
        totalOperations: results.length,
        successfulOperations: successfulResults.length,
      },
    };
  }
  // If some operations succeeded, return partial success
  if (successfulResults.length > 0) {
    return {
      success: true,
      data: successfulResults.map(result => result.data!),
      metadata: {
        timestamp: new Date().toISOString(),
        totalOperations: results.length,
        successfulOperations: successfulResults.length,
        failedOperations: failedResults.length,
        partialSuccess: true,
      },
    };
  }
  // All operations failed
  return {
    success: false,
    error: {
      message: `All ${results.length} operations failed`,
      code: "BATCH_OPERATION_FAILED",
      category: "system",
      details: {
        errors: failedResults.map(result => result.error),
      },
      recoverable: failedResults.some(result => result.error?.recoverable),
    },
  };
}
