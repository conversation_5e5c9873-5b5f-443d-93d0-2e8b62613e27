import { loggingService } from "./loggingService";
import { ErrorContext } from "@/types";

/**
 * Audit event types for security-sensitive operations
 */
export enum AuditEventType {
  // Authentication events
  USER_LOGIN = "user_login",
  USER_LOGOUT = "user_logout",
  USER_SIGNUP = "user_signup",
  LOGIN_FAILED = "login_failed",
  PASSWORD_RESET = "password_reset",

  // User management events
  USER_CREATED = "user_created",
  USER_UPDATED = "user_updated",
  USER_DELETED = "user_deleted",
  USER_ROLE_CHANGED = "user_role_changed",
  USER_PERMISSIONS_CHANGED = "user_permissions_changed",

  // Admin access events
  ADMIN_ACCESS_REQUESTED = "admin_access_requested",
  ADMIN_ACCESS_GRANTED = "admin_access_granted",
  ADMIN_ACCESS_DENIED = "admin_access_denied",
  ADMIN_ACTION_PERFORMED = "admin_action_performed",

  // Risk management events
  RISK_CREATED = "risk_created",
  RISK_UPDATED = "risk_updated",
  RISK_DELETED = "risk_deleted",
  RISK_STATUS_CHANGED = "risk_status_changed",
  RISK_ASSESSMENT_CHANGED = "risk_assessment_changed",

  // Policy management events
  POLICY_CREATED = "policy_created",
  POLICY_UPDATED = "policy_updated",
  POLICY_DELETED = "policy_deleted",
  POLICY_PUBLISHED = "policy_published",
  POLICY_ARCHIVED = "policy_archived",

  // Organization events
  ORGANIZATION_CREATED = "organization_created",
  ORGANIZATION_UPDATED = "organization_updated",
  ORGANIZATION_DELETED = "organization_deleted",
  ORGANIZATION_SETTINGS_CHANGED = "organization_settings_changed",

  // Data access events
  SENSITIVE_DATA_ACCESSED = "sensitive_data_accessed",
  BULK_DATA_EXPORT = "bulk_data_export",
  DATA_IMPORT = "data_import",

  // Security events
  SECURITY_VIOLATION_DETECTED = "security_violation_detected",
  UNAUTHORIZED_ACCESS_ATTEMPT = "unauthorized_access_attempt",
  SUSPICIOUS_ACTIVITY = "suspicious_activity",

  // System events
  SYSTEM_CONFIGURATION_CHANGED = "system_configuration_changed",
  BACKUP_CREATED = "backup_created",
  BACKUP_RESTORED = "backup_restored",
}

/**
 * Audit event severity levels
 */
export enum AuditSeverity {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  CRITICAL = "critical",
}

/**
 * Audit event interface
 */
export interface AuditEvent {
  id: string;
  timestamp: string;
  eventType: AuditEventType;
  severity: AuditSeverity;
  userId: string;
  userEmail?: string;
  organizationId?: string;
  resourceId?: string;
  resourceType?: string;
  action: string;
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  correlationId?: string;
  outcome: "success" | "failure" | "partial";
  errorMessage?: string;
  metadata?: Record<string, any>;
}

/**
 * Audit log retention policy
 */
export interface AuditRetentionPolicy {
  defaultRetentionDays: number;
  criticalRetentionDays: number;
  archiveAfterDays: number;
  purgeAfterDays: number;
  compressionEnabled: boolean;
  encryptionEnabled: boolean;
}

/**
 * Audit logging configuration
 */
export interface AuditLoggingConfig {
  enabled: boolean;
  retentionPolicy: AuditRetentionPolicy;
  integrityCheckEnabled: boolean;
  realTimeAlertsEnabled: boolean;
  batchSize: number;
  flushInterval: number;
  remoteStorageEnabled: boolean;
  remoteStorageEndpoint?: string;
}

/**
 * Default audit logging configuration
 */
const DEFAULT_AUDIT_CONFIG: AuditLoggingConfig = {
  enabled: true,
  retentionPolicy: {
    defaultRetentionDays: 90,
    criticalRetentionDays: 365,
    archiveAfterDays: 30,
    purgeAfterDays: 2555, // 7 years for compliance
    compressionEnabled: true,
    encryptionEnabled: true,
  },
  integrityCheckEnabled: true,
  realTimeAlertsEnabled: true,
  batchSize: 50,
  flushInterval: 5000, // 5 seconds
  remoteStorageEnabled: import.meta.env.PROD,
  remoteStorageEndpoint: import.meta.env.VITE_AUDIT_ENDPOINT || "/api/audit",
};

/**
 * Centralized audit logging service for security-sensitive operations
 */
export class AuditLoggingService {
  private static instance: AuditLoggingService;
  private config: AuditLoggingConfig;
  private auditBuffer: AuditEvent[] = [];
  private flushTimer: NodeJS.Timeout | null = null;
  private integrityHashes: Map<string, string> = new Map();

  private constructor(config?: Partial<AuditLoggingConfig>) {
    this.config = { ...DEFAULT_AUDIT_CONFIG, ...config };
    this.initializeFlushTimer();
  }

  public static getInstance(config?: Partial<AuditLoggingConfig>): AuditLoggingService {
    if (!AuditLoggingService.instance) {
      AuditLoggingService.instance = new AuditLoggingService(config);
    }
    return AuditLoggingService.instance;
  }

  /**
   * Log an audit event
   */
  public async logEvent(
    eventType: AuditEventType,
    userId: string,
    action: string,
    details: Record<string, any> = {},
    options: {
      severity?: AuditSeverity;
      resourceId?: string;
      resourceType?: string;
      outcome?: "success" | "failure" | "partial";
      errorMessage?: string;
      userEmail?: string;
      organizationId?: string;
      metadata?: Record<string, any>;
    } = {
      // Implementation needed
    }
  ): Promise<void> {
    if (!this.config.enabled) {
      return;
    }

    const auditEvent: AuditEvent = {
      id: this.generateEventId(),
      timestamp: new Date().toISOString(),
      eventType,
      severity: options.severity || this.determineSeverity(eventType),
      userId,
      userEmail: options.userEmail,
      organizationId: options.organizationId,
      resourceId: options.resourceId,
      resourceType: options.resourceType,
      action,
      details: this.sanitizeDetails(details),
      ipAddress: this.getClientIP(),
      userAgent: this.getUserAgent(),
      sessionId: this.getSessionId(),
      correlationId: loggingService.getCorrelationId() || undefined,
      outcome: options.outcome || "success",
      errorMessage: options.errorMessage,
      metadata: options.metadata,
    };

    // Add integrity hash
    if (this.config.integrityCheckEnabled) {
      const hash = await this.generateIntegrityHash(auditEvent);
      this.integrityHashes.set(auditEvent.id, hash);
    }

    // Add to buffer
    this.auditBuffer.push(auditEvent);

    // Log to centralized logging service
    const context: ErrorContext = {
      userId,
      component: "audit",
      action: eventType,
      organizationId: options.organizationId,
    } as ErrorContext;

    loggingService.audit(
      action,
      userId,
      {
        eventType,
        severity: auditEvent.severity,
        resourceId: options.resourceId,
        resourceType: options.resourceType,
        outcome: auditEvent.outcome,
        auditEventId: auditEvent.id,
        ...details,
      },
      context
    );

    // Check for real-time alerts
    if (this.config.realTimeAlertsEnabled && this.shouldTriggerAlert(auditEvent)) {
      await this.triggerRealTimeAlert(auditEvent);
    }

    // Flush if buffer is full
    if (this.auditBuffer.length >= this.config.batchSize) {
      await this.flush();
    }
  }

  /**
   * Log authentication events
   */
  public async logAuthEvent(
    eventType:
      | AuditEventType.USER_LOGIN
      | AuditEventType.USER_LOGOUT
      | AuditEventType.USER_SIGNUP
      | AuditEventType.LOGIN_FAILED,
    userId: string,
    userEmail?: string,
    details: Record<string, any> = {
      // Implementation needed
    }
  ): Promise<void> {
    await this.logEvent(eventType, userId, eventType, details, {
      severity:
        eventType === AuditEventType.LOGIN_FAILED ? AuditSeverity.MEDIUM : AuditSeverity.LOW,
      userEmail,
      outcome: eventType === AuditEventType.LOGIN_FAILED ? "failure" : "success",
    });
  }

  /**
   * Log user management events
   */
  public async logUserManagementEvent(
    eventType:
      | AuditEventType.USER_CREATED
      | AuditEventType.USER_UPDATED
      | AuditEventType.USER_DELETED
      | AuditEventType.USER_ROLE_CHANGED,
    performedBy: string,
    targetUserId: string,
    details: Record<string, any> = {
      // Implementation needed
    }
  ): Promise<void> {
    await this.logEvent(eventType, performedBy, eventType, details, {
      severity: AuditSeverity.MEDIUM,
      resourceId: targetUserId,
      resourceType: "user",
    });
  }

  /**
   * Log risk management events
   */
  public async logRiskEvent(
    eventType:
      | AuditEventType.RISK_CREATED
      | AuditEventType.RISK_UPDATED
      | AuditEventType.RISK_DELETED,
    userId: string,
    riskId: string,
    details: Record<string, any> = {
      // Implementation needed
    }
  ): Promise<void> {
    await this.logEvent(eventType, userId, eventType, details, {
      severity: AuditSeverity.LOW,
      resourceId: riskId,
      resourceType: "risk",
    });
  }

  /**
   * Log policy management events
   */
  public async logPolicyEvent(
    eventType:
      | AuditEventType.POLICY_CREATED
      | AuditEventType.POLICY_UPDATED
      | AuditEventType.POLICY_DELETED,
    userId: string,
    policyId: string,
    details: Record<string, any> = {
      // Implementation needed
    }
  ): Promise<void> {
    await this.logEvent(eventType, userId, eventType, details, {
      severity: AuditSeverity.MEDIUM,
      resourceId: policyId,
      resourceType: "policy",
    });
  }

  /**
   * Log admin access events
   */
  public async logAdminEvent(
    eventType:
      | AuditEventType.ADMIN_ACCESS_REQUESTED
      | AuditEventType.ADMIN_ACCESS_GRANTED
      | AuditEventType.ADMIN_ACCESS_DENIED
      | AuditEventType.ADMIN_ACTION_PERFORMED,
    userId: string,
    details: Record<string, any> = {
      // Implementation needed
    }
  ): Promise<void> {
    await this.logEvent(eventType, userId, eventType, details, {
      severity: AuditSeverity.HIGH,
      outcome: eventType === AuditEventType.ADMIN_ACCESS_DENIED ? "failure" : "success",
    });
  }

  /**
   * Log security events
   */
  public async logSecurityEvent(
    eventType:
      | AuditEventType.SECURITY_VIOLATION_DETECTED
      | AuditEventType.UNAUTHORIZED_ACCESS_ATTEMPT
      | AuditEventType.SUSPICIOUS_ACTIVITY,
    userId: string,
    details: Record<string, any> = {
      // Implementation needed
    }
  ): Promise<void> {
    await this.logEvent(eventType, userId, eventType, details, {
      severity: AuditSeverity.CRITICAL,
      outcome: "failure",
    });
  }

  /**
   * Flush audit events to remote storage
   */
  public async flush(): Promise<void> {
    if (this.auditBuffer.length === 0) {
      return;
    }

    const eventsToFlush = [...this.auditBuffer];
    this.auditBuffer = [];

    try {
      if (this.config.remoteStorageEnabled && this.config.remoteStorageEndpoint) {
        await this.sendToRemoteStorage(eventsToFlush);
      }

      // Log successful flush
      loggingService.info(`Flushed ${eventsToFlush.length} audit events`, {
        component: "audit",
        action: "flush",
      } as ErrorContext);
    } catch (error) {
      // Re-add events to buffer on failure
      this.auditBuffer.unshift(...eventsToFlush);

      loggingService.error(
        "Failed to flush audit events",
        error as Error,
        {
          component: "audit",
          action: "flush",
        } as ErrorContext
      );
    }
  }

  /**
   * Verify audit log integrity
   */
  public async verifyIntegrity(eventId: string): Promise<boolean> {
    if (!this.config.integrityCheckEnabled) {
      return true;
    }

    const storedHash = this.integrityHashes.get(eventId);
    if (!storedHash) {
      return false;
    }

    // In a real implementation, you would retrieve the event and recalculate the hash
    // For now, we'll assume integrity is maintained
    return true;
  }

  /**
   * Get audit statistics
   */
  public getAuditStats(): {
    bufferedEvents: number;
    totalEventsLogged: number;
    integrityChecksEnabled: boolean;
    lastFlushTime: string | null;
  } {
    return {
      bufferedEvents: this.auditBuffer.length,
      totalEventsLogged: this.integrityHashes.size,
      integrityChecksEnabled: this.config.integrityCheckEnabled,
      lastFlushTime: null, // Would track this in a real implementation
    };
  }

  /**
   * Update audit configuration
   */
  public updateConfig(newConfig: Partial<AuditLoggingConfig>): void {
    this.config = { ...this.config, ...newConfig };

    // Restart flush timer if interval changed
    if (newConfig.flushInterval) {
      this.initializeFlushTimer();
    }
  }

  // Private helper methods

  private generateEventId(): string {
    return `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private determineSeverity(eventType: AuditEventType): AuditSeverity {
    const criticalEvents = [
      AuditEventType.SECURITY_VIOLATION_DETECTED,
      AuditEventType.UNAUTHORIZED_ACCESS_ATTEMPT,
      AuditEventType.SUSPICIOUS_ACTIVITY,
      AuditEventType.SYSTEM_CONFIGURATION_CHANGED,
    ];

    const highSeverityEvents = [
      AuditEventType.ADMIN_ACCESS_GRANTED,
      AuditEventType.ADMIN_ACCESS_DENIED,
      AuditEventType.ADMIN_ACTION_PERFORMED,
      AuditEventType.USER_DELETED,
      AuditEventType.ORGANIZATION_DELETED,
      AuditEventType.BULK_DATA_EXPORT,
    ];

    const mediumSeverityEvents = [
      AuditEventType.USER_ROLE_CHANGED,
      AuditEventType.USER_PERMISSIONS_CHANGED,
      AuditEventType.POLICY_CREATED,
      AuditEventType.POLICY_UPDATED,
      AuditEventType.POLICY_DELETED,
      AuditEventType.LOGIN_FAILED,
    ];

    if (criticalEvents.includes(eventType)) {
      return AuditSeverity.CRITICAL;
    } else if (highSeverityEvents.includes(eventType)) {
      return AuditSeverity.HIGH;
    } else if (mediumSeverityEvents.includes(eventType)) {
      return AuditSeverity.MEDIUM;
    } else {
      return AuditSeverity.LOW;
    }
  }

  private sanitizeDetails(details: Record<string, any>): Record<string, any> {
    const sanitized = { ...details };

    // Remove sensitive fields
    const sensitiveFields = ["password", "token", "secret", "key", "credential"];

    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = "[REDACTED]";
      }
    }

    return sanitized;
  }

  private async generateIntegrityHash(event: AuditEvent): Promise<string> {
    // In a real implementation, you would use a cryptographic hash
    // For now, we'll use a simple hash based on event data
    const eventString = JSON.stringify(event);
    let hash = 0;
    for (let i = 0; i < eventString.length; i++) {
      const char = eventString.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(16);
  }

  private getClientIP(): string | undefined {
    // In a browser environment, we can't directly get the client IP
    // This would typically be handled by the server
    return undefined;
  }

  private getUserAgent(): string | undefined {
    return typeof navigator !== "undefined" ? navigator.userAgent : undefined;
  }

  private getSessionId(): string | undefined {
    // Get session ID from current session storage or generate one
    if (typeof sessionStorage !== "undefined") {
      let sessionId = sessionStorage.getItem("audit_session_id");
      if (!sessionId) {
        sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        sessionStorage.setItem("audit_session_id", sessionId);
      }
      return sessionId;
    }
    return undefined;
  }

  private shouldTriggerAlert(event: AuditEvent): boolean {
    return (
      event.severity === AuditSeverity.CRITICAL ||
      (event.outcome === "failure" && event.severity === AuditSeverity.HIGH)
    );
  }

  private async triggerRealTimeAlert(event: AuditEvent): Promise<void> {
    // In a real implementation, this would send alerts to monitoring systems
    loggingService.error(
      `AUDIT ALERT: ${event.eventType}`,
      undefined,
      {
        component: "audit-alert",
        userId: event.userId,
        action: event.action,
      } as ErrorContext,
      {
        auditEvent: event,
        alertType: "real-time",
        severity: event.severity,
      }
    );
  }

  private async sendToRemoteStorage(events: AuditEvent[]): Promise<void> {
    if (!this.config.remoteStorageEndpoint) {
      throw new Error("Remote storage endpoint not configured");
    }

    const response = await fetch(this.config.remoteStorageEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "X-Correlation-ID": loggingService.getCorrelationId() || "",
      },
      body: JSON.stringify({
        events,
        timestamp: new Date().toISOString(),
        source: "audit-service",
      }),
    });

    if (!response.ok) {
      throw new Error(`Failed to send audit events: ${response.status} ${response.statusText}`);
    }
  }

  private initializeFlushTimer(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }

    this.flushTimer = setInterval(() => {
      this.flush().catch(error => {
        loggingService.error(
          "Scheduled audit flush failed",
          error as Error,
          {
            component: "audit",
            action: "scheduled-flush",
          } as ErrorContext
        );
      });
    }, this.config.flushInterval);
  }
}

// Export singleton instance

export const auditLoggingService = AuditLoggingService.getInstance();

// Export convenience functions for common audit operations
export const auditLog = {
  auth: auditLoggingService.logAuthEvent.bind(auditLoggingService),
  user: auditLoggingService.logUserManagementEvent.bind(auditLoggingService),
  risk: auditLoggingService.logRiskEvent.bind(auditLoggingService),
  policy: auditLoggingService.logPolicyEvent.bind(auditLoggingService),
  admin: auditLoggingService.logAdminEvent.bind(auditLoggingService),
  security: auditLoggingService.logSecurityEvent.bind(auditLoggingService),
  custom: auditLoggingService.logEvent.bind(auditLoggingService),
};
