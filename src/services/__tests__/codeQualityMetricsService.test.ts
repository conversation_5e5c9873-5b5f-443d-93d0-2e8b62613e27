/**
 * Tests for Code Quality Metrics Service
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { 
  codeQualityMetricsService,
  CodeQualityMetrics,
  QualityAlert
} from '../codeQualityMetricsService';

// Mock the Logger
vi.mock('../../utils/errors/Logger', () => ({
  Logger: vi.fn().mockImplementation(() => ({
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn()
  }))
}));

describe('CodeQualityMetricsService', () => {
  beforeEach(() => {
    // Reset service state
    vi.clearAllMocks();
  });

  describe('collectMetrics', () => {
    it('should collect and return quality metrics', async () => {
      const metrics = await codeQualityMetricsService.collectMetrics();
      
      expect(metrics).toBeDefined();
      expect(metrics.timestamp).toBeDefined();
      expect(typeof metrics.typeScriptErrors).toBe('number');
      expect(typeof metrics.eslintWarnings).toBe('number');
      expect(typeof metrics.eslintErrors).toBe('number');
      expect(typeof metrics.qualityScore).toBe('number');
      
      expect(metrics.testCoverage).toBeDefined();
      expect(typeof metrics.testCoverage.lines).toBe('number');
      expect(typeof metrics.testCoverage.functions).toBe('number');
      expect(typeof metrics.testCoverage.branches).toBe('number');
      expect(typeof metrics.testCoverage.statements).toBe('number');
      
      expect(metrics.codeComplexity).toBeDefined();
      expect(typeof metrics.codeComplexity.cyclomaticComplexity).toBe('number');
      expect(typeof metrics.codeComplexity.cognitiveComplexity).toBe('number');
      expect(typeof metrics.codeComplexity.maintainabilityIndex).toBe('number');
      
      expect(metrics.bundleSize).toBeDefined();
      expect(typeof metrics.bundleSize.totalSize).toBe('number');
      expect(typeof metrics.bundleSize.gzippedSize).toBe('number');
      expect(typeof metrics.bundleSize.chunkCount).toBe('number');
    });

    it('should calculate quality score correctly', async () => {
      const metrics = await codeQualityMetricsService.collectMetrics();
      
      expect(metrics.qualityScore).toBeGreaterThanOrEqual(0);
      expect(metrics.qualityScore).toBeLessThanOrEqual(100);
    });
  });

  describe('storeMetrics', () => {
    it('should store metrics and analyze trends', async () => {
      const metrics = await codeQualityMetricsService.collectMetrics();
      
      await expect(codeQualityMetricsService.storeMetrics(metrics)).resolves.not.toThrow();
      
      const trends = codeQualityMetricsService.getQualityTrends(1);
      expect(trends).toBeDefined();
      expect(Array.isArray(trends)).toBe(true);
    });
  });

  describe('getQualityTrends', () => {
    it('should return trends for specified number of days', async () => {
      const metrics = await codeQualityMetricsService.collectMetrics();
      await codeQualityMetricsService.storeMetrics(metrics);
      
      const trends = codeQualityMetricsService.getQualityTrends(7);
      
      expect(Array.isArray(trends)).toBe(true);
      trends.forEach(trend => {
        expect(trend.date).toBeDefined();
        expect(trend.metrics).toBeDefined();
        expect(typeof trend.regression).toBe('boolean');
        expect(Array.isArray(trend.improvements)).toBe(true);
        expect(Array.isArray(trend.regressions)).toBe(true);
      });
    });

    it('should filter trends by date range', async () => {
      const metrics = await codeQualityMetricsService.collectMetrics();
      await codeQualityMetricsService.storeMetrics(metrics);
      
      const trends1Day = codeQualityMetricsService.getQualityTrends(1);
      const trends7Days = codeQualityMetricsService.getQualityTrends(7);
      
      expect(trends1Day.length).toBeLessThanOrEqual(trends7Days.length);
    });
  });

  describe('getActiveAlerts', () => {
    it('should return only unresolved alerts', async () => {
      const alerts = codeQualityMetricsService.getActiveAlerts();
      
      expect(Array.isArray(alerts)).toBe(true);
      alerts.forEach(alert => {
        expect(alert.resolved).toBe(false);
        expect(alert.id).toBeDefined();
        expect(alert.type).toMatch(/^(error|warning|info)$/);
        expect(alert.severity).toMatch(/^(low|medium|high|critical)$/);
        expect(alert.message).toBeDefined();
        expect(alert.timestamp).toBeDefined();
      });
    });
  });

  describe('resolveAlert', () => {
    it('should mark alert as resolved', async () => {
      // First, create some metrics that might generate alerts
      const metrics: CodeQualityMetrics = {
        timestamp: new Date().toISOString(),
        typeScriptErrors: 5, // This should trigger an alert
        eslintWarnings: 2,
        eslintErrors: 1,
        testCoverage: {
          lines: 60, // Below threshold
          functions: 65,
          branches: 55,
          statements: 62,
          threshold: {
            lines: 80,
            functions: 80,
            branches: 75,
            statements: 80
          }
        },
        codeComplexity: {
          cyclomaticComplexity: 10,
          cognitiveComplexity: 8,
          maintainabilityIndex: 75,
          linesOfCode: 1000,
          technicalDebt: 2
        },
        maintainabilityIndex: 75,
        bundleSize: {
          totalSize: 400 * 1024,
          gzippedSize: 120 * 1024,
          chunkCount: 5,
          duplicateDependencies: 0,
          unusedCode: 0
        },
        qualityScore: 70
      };

      await codeQualityMetricsService.storeMetrics(metrics);
      
      const activeAlerts = codeQualityMetricsService.getActiveAlerts();
      
      if (activeAlerts.length > 0) {
        const alertId = activeAlerts[0].id;
        codeQualityMetricsService.resolveAlert(alertId);
        
        const updatedAlerts = codeQualityMetricsService.getActiveAlerts();
        const resolvedAlert = updatedAlerts.find(a => a.id === alertId);
        
        expect(resolvedAlert).toBeUndefined();
      }
    });
  });

  describe('getLatestMetrics', () => {
    it('should return the most recent metrics', async () => {
      const metrics = await codeQualityMetricsService.collectMetrics();
      await codeQualityMetricsService.storeMetrics(metrics);
      
      const latest = codeQualityMetricsService.getLatestMetrics();
      
      expect(latest).toBeDefined();
      expect(latest?.timestamp).toBeDefined();
      expect(latest?.qualityScore).toBeDefined();
    });

    it('should return null when no metrics exist', () => {
      // This test assumes a fresh service instance
      const freshService = new (codeQualityMetricsService.constructor as any)();
      const latest = freshService.getLatestMetrics();
      
      expect(latest).toBeNull();
    });
  });

  describe('quality score calculation', () => {
    it('should penalize TypeScript errors heavily', async () => {
      // Mock methods to return specific values
      const originalCollectTypeScript = codeQualityMetricsService['collectTypeScriptMetrics'];
      
      // Test with no errors
      codeQualityMetricsService['collectTypeScriptMetrics'] = vi.fn().mockResolvedValue({
        errors: 0,
        warnings: 0
      });
      
      const metricsNoErrors = await codeQualityMetricsService.collectMetrics();
      
      // Test with errors
      codeQualityMetricsService['collectTypeScriptMetrics'] = vi.fn().mockResolvedValue({
        errors: 5,
        warnings: 0
      });
      
      const metricsWithErrors = await codeQualityMetricsService.collectMetrics();
      
      expect(metricsNoErrors.qualityScore).toBeGreaterThan(metricsWithErrors.qualityScore);
      
      // Restore original method
      codeQualityMetricsService['collectTypeScriptMetrics'] = originalCollectTypeScript;
    });

    it('should reward high test coverage', async () => {
      const originalCollectCoverage = codeQualityMetricsService['collectCoverageMetrics'];
      
      // Test with high coverage
      codeQualityMetricsService['collectCoverageMetrics'] = vi.fn().mockResolvedValue({
        lines: 95,
        functions: 90,
        branches: 85,
        statements: 92,
        threshold: { lines: 80, functions: 80, branches: 75, statements: 80 }
      });
      
      const metricsHighCoverage = await codeQualityMetricsService.collectMetrics();
      
      // Test with low coverage
      codeQualityMetricsService['collectCoverageMetrics'] = vi.fn().mockResolvedValue({
        lines: 60,
        functions: 55,
        branches: 50,
        statements: 58,
        threshold: { lines: 80, functions: 80, branches: 75, statements: 80 }
      });
      
      const metricsLowCoverage = await codeQualityMetricsService.collectMetrics();
      
      expect(metricsHighCoverage.qualityScore).toBeGreaterThan(metricsLowCoverage.qualityScore);
      
      // Restore original method
      codeQualityMetricsService['collectCoverageMetrics'] = originalCollectCoverage;
    });
  });
});