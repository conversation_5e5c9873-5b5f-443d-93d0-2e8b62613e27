import { describe, it, expect, vi, beforeEach } from 'vitest'
import { createIncident, updateIncident, reopenIncident } from '../incidentService'
import { supabase } from '@/integrations/supabase/client'
import { RiskSeverity } from '@/types'
import { createMockSupabaseResponse, mockUser } from '@/test/test-utils'

// Mock the supabase client
vi.mock('@/integrations/supabase/client')

describe('Incident Service', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('createIncident', () => {
    const mockIncidentValues = {
      title: 'Test Incident',
      description: 'Test incident description',
      severity: RiskSeverity.HIGH,
      status: 'Open' as const,
      relatedRiskId: 'test-risk-id',
    }

    it('should create an incident successfully', async () => {
      const mockResponse = {
        id: 'new-incident-id',
      }

      const mockSupabaseChain = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue(createMockSupabaseResponse(mockResponse)),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await createIncident(mockIncidentValues, mockUser)

      expect(result.success).toBe(true)
      expect(result.data?.id).toBe('new-incident-id')
      expect(mockSupabaseChain.insert).toHaveBeenCalledWith({
        title: mockIncidentValues.title,
        description: mockIncidentValues.description,
        severity: mockIncidentValues.severity,
        status: mockIncidentValues.status,
        reporter_id: mockUser.id,
        organization_id: mockUser.organizationId,
        related_risk_id: mockIncidentValues.relatedRiskId,
        date: expect.any(String),
      })
    })

    it('should handle creation without related risk', async () => {
      const incidentWithoutRisk = {
        ...mockIncidentValues,
        relatedRiskId: undefined,
      }

      const mockSupabaseChain = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue(createMockSupabaseResponse({ id: 'new-incident-id' })),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await createIncident(incidentWithoutRisk, mockUser)

      expect(result.success).toBe(true)
      expect(result.data?.id).toBe('new-incident-id')
      expect(mockSupabaseChain.insert).toHaveBeenCalledWith(
        expect.objectContaining({
          related_risk_id: undefined,
        })
      )
    })

    it('should handle creation errors', async () => {
      const mockError = { message: 'Database error', code: 'DB_ERROR' }
      const mockSupabaseChain = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue(createMockSupabaseResponse(null, mockError)),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await createIncident(mockIncidentValues, mockUser)
      
      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
      expect(result.error?.message).toContain('Database error')
    })

    it('should set correct timestamp for incident date', async () => {
      const mockSupabaseChain = {
        insert: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue(createMockSupabaseResponse({ id: 'new-incident-id' })),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const beforeCall = new Date()
      await createIncident(mockIncidentValues, mockUser)
      const afterCall = new Date()

      // Check if mock.calls exists and has at least one element
      expect(mockSupabaseChain.insert.mock?.calls?.length).toBeGreaterThan(0);
      
      // Now that we've verified calls exist, we can safely access them
      // Use type assertion to tell TypeScript that we've already checked for existence
      const mockCalls = mockSupabaseChain.insert.mock!.calls as any[][];
      // Add non-null assertion for the array access
      const insertCall = mockCalls[0]![0];
      const incidentDate = new Date(insertCall.date);

      expect(incidentDate.getTime()).toBeGreaterThanOrEqual(beforeCall.getTime());
      expect(incidentDate.getTime()).toBeLessThanOrEqual(afterCall.getTime());
    })
  })

  describe('updateIncident', () => {
    const mockUpdateValues = {
      title: 'Updated Incident',
      description: 'Updated incident description',
      severity: RiskSeverity.MEDIUM,
      status: 'Investigating' as const,
      relatedRiskId: 'updated-risk-id',
    }

    it('should update an incident successfully', async () => {
      const mockResponse = {
        id: 'test-incident-id',
        title: 'Updated Incident',
        description: 'Updated incident description',
        severity: RiskSeverity.MEDIUM,
        status: 'Investigating',
        updated_at: '2024-01-01T00:00:00Z',
      }

      const mockSupabaseChain = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue(createMockSupabaseResponse(mockResponse)),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await updateIncident('test-incident-id', mockUpdateValues)

      expect(result.success).toBe(true)
      expect(result.data?.id).toBe('test-incident-id')
      expect(result.data?.title).toBe('Updated Incident')
      expect(mockSupabaseChain.eq).toHaveBeenCalledWith('id', 'test-incident-id')
      expect(mockSupabaseChain.update).toHaveBeenCalledWith({
        title: mockUpdateValues.title,
        description: mockUpdateValues.description,
        severity: mockUpdateValues.severity,
        status: mockUpdateValues.status,
        related_risk_id: mockUpdateValues.relatedRiskId,
        updated_at: expect.any(String),
      })
    })

    it('should handle update without related risk', async () => {
      const updateWithoutRisk = {
        ...mockUpdateValues,
        relatedRiskId: undefined,
      }

      const mockSupabaseChain = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue(createMockSupabaseResponse({ id: 'test-incident-id' })),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await updateIncident('test-incident-id', updateWithoutRisk)

      expect(result.success).toBe(true)
      expect(mockSupabaseChain.update).toHaveBeenCalledWith(
        expect.objectContaining({
          related_risk_id: undefined,
        })
      )
    })

    it('should handle update errors', async () => {
      const mockError = { message: 'Update failed', code: 'UPDATE_ERROR' }
      const mockSupabaseChain = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue(createMockSupabaseResponse(null, mockError)),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await updateIncident('test-incident-id', mockUpdateValues)
      
      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
      expect(result.error?.message).toContain('Update failed')
    })

    it('should set correct timestamp for updated_at', async () => {
      const mockSupabaseChain = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue(createMockSupabaseResponse({ id: 'test-incident-id' })),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const beforeCall = new Date()
      await updateIncident('test-incident-id', mockUpdateValues)
      const afterCall = new Date()

      // Check if mock.calls exists and has at least one element
      expect(mockSupabaseChain.update.mock?.calls?.length).toBeGreaterThan(0);
      
      // Now that we've verified calls exist, we can safely access them
      // Use type assertion to tell TypeScript that we've already checked for existence
      const mockCalls = mockSupabaseChain.update.mock!.calls as any[][];
      // Add non-null assertion for the array access
      const updateCall = mockCalls[0]![0];
      const updatedAt = new Date(updateCall.updated_at);

      expect(updatedAt.getTime()).toBeGreaterThanOrEqual(beforeCall.getTime());
      expect(updatedAt.getTime()).toBeLessThanOrEqual(afterCall.getTime());
    })
  })

  describe('reopenIncident', () => {
    it('should reopen an incident successfully', async () => {
      const mockResponse = {
        id: 'test-incident-id',
        status: 'Open',
        updated_at: '2024-01-01T00:00:00Z',
      }

      const mockSupabaseChain = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue(createMockSupabaseResponse(mockResponse)),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await reopenIncident('test-incident-id')

      expect(result.success).toBe(true)
      expect(result.data?.id).toBe('test-incident-id')
      expect(result.data?.status).toBe('Open')
      expect(mockSupabaseChain.eq).toHaveBeenCalledWith('id', 'test-incident-id')
      expect(mockSupabaseChain.update).toHaveBeenCalledWith({
        status: 'Open',
        updated_at: expect.any(String),
      })
    })

    it('should handle reopen errors', async () => {
      const mockError = { message: 'Reopen failed', code: 'REOPEN_ERROR' }
      const mockSupabaseChain = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue(createMockSupabaseResponse(null, mockError)),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await reopenIncident('test-incident-id')
      
      expect(result.success).toBe(false)
      expect(result.error).toBeDefined()
      expect(result.error?.message).toContain('Reopen failed')
    })

    it('should set correct timestamp when reopening', async () => {
      const mockSupabaseChain = {
        update: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        select: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue(createMockSupabaseResponse({ id: 'test-incident-id' })),
      }

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const beforeCall = new Date()
      await reopenIncident('test-incident-id')
      const afterCall = new Date()

      // Check if mock.calls exists and has at least one element
      expect(mockSupabaseChain.update.mock?.calls?.length).toBeGreaterThan(0);
      
      // Now that we've verified calls exist, we can safely access them
      // Use type assertion to tell TypeScript that we've already checked for existence
      const mockCalls = mockSupabaseChain.update.mock!.calls as any[][];
      // Add non-null assertion for the array access
      const updateCall = mockCalls[0]![0];
      const updatedAt = new Date(updateCall.updated_at);

      expect(updatedAt.getTime()).toBeGreaterThanOrEqual(beforeCall.getTime());
      expect(updatedAt.getTime()).toBeLessThanOrEqual(afterCall.getTime());
    })
  })
})
