import DOMPurify from "dompurify";
// Configure DOMPurify for different environments
let purify: DOMPurify.DOMPurifyI;
if (typeof window !== "undefined") {
  // Browser environment
  purify = DOMPurify;
} else {
  // Node.js environment - use a fallback or mock
  // For testing purposes, we'll create a minimal mock
  purify = {
    sanitize: (input: string, config?: DOMPurify.Config) => {
      // Enhanced fallback sanitization for Node.js testing
      let sanitized = input;
      // Remove dangerous tags completely
      sanitized = sanitized.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "");
      sanitized = sanitized.replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, "");
      sanitized = sanitized.replace(/<object\b[^>]*>.*?<\/object>/gi, "");
      sanitized = sanitized.replace(/<embed\b[^>]*>/gi, "");
      sanitized = sanitized.replace(/<form\b[^>]*>.*?<\/form>/gi, "");
      sanitized = sanitized.replace(/<input\b[^>]*>/gi, "");
      sanitized = sanitized.replace(/<svg\b[^>]*>.*?<\/svg>/gi, "");
      sanitized = sanitized.replace(/<style\b[^>]*>.*?<\/style>/gi, "");
      sanitized = sanitized.replace(/<meta\b[^>]*>/gi, "");
      // Remove dangerous protocols
      sanitized = sanitized.replace(/javascript:/gi, "");
      sanitized = sanitized.replace(/data:text\/html/gi, "");
      sanitized = sanitized.replace(/vbscript:/gi, "");
      // Remove event handlers
      sanitized = sanitized.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, "");
      sanitized = sanitized.replace(/\s*on\w+\s*=\s*[^>\s]+/gi, "");
      // If stripTags is requested, remove all HTML tags
      if (config?.stripTags || (config?.ALLOWED_TAGS && config.ALLOWED_TAGS.length === 0)) {
        sanitized = sanitized.replace(/<[^>]*>/g, "");
      }
      return sanitized;
    },
    addHook: () => {},
    removeHook: () => {},
    removeHooks: () => {},
    removeAllHooks: () => {},
    isValidAttribute: () => true,
    setConfig: () => {},
    clearConfig: () => {},
    isSupported: true,
    version: "3.2.6",
  } as DOMPurify.DOMPurifyI;
}
/**
 * Input Sanitization Service
 *
 * Centralized service for sanitizing user input to prevent XSS attacks
 * and ensure data integrity across the application.
 */
export interface SanitizationOptions {
  allowedTags?: string[] | undefined;
  allowedAttributes?: string[] | undefined;
  stripTags?: boolean | undefined;
  maxLength?: number | undefined;
  allowHtml?: boolean | undefined;
}
export interface ValidationResult {
  isValid: boolean;
  sanitizedValue: string;
  errors: string[];
  warnings: string[];
}
/**
 * Default sanitization configurations for different use cases
 */
export const SANITIZATION_PRESETS: Record<string, SanitizationOptions> = {
  // For plain text input (forms, comments, etc.)
  TEXT_ONLY: {
    allowedTags: [],
    allowedAttributes: [],
    stripTags: true,
    allowHtml: false,
  },
  // For rich text content (descriptions, articles)
  RICH_TEXT: {
    allowedTags: [
      "p",
      "br",
      "strong",
      "em",
      "u",
      "ol",
      "ul",
      "li",
      "h1",
      "h2",
      "h3",
      "h4",
      "h5",
      "h6",
      "blockquote",
    ],
    allowedAttributes: ["class", "id"],
    allowHtml: true,
  },
  // For user-generated content with links
  USER_CONTENT: {
    allowedTags: ["p", "br", "strong", "em", "u", "ol", "ul", "li", "a"],
    allowedAttributes: ["href", "title", "class", "id"],
    allowHtml: true,
  },
  // For file names and paths
  FILE_NAME: {
    allowedTags: [],
    allowedAttributes: [],
    stripTags: true,
    maxLength: 255,
    allowHtml: false,
  },
  // For URLs
  URL: {
    allowedTags: [],
    allowedAttributes: [],
    stripTags: true,
    maxLength: 2048,
    allowHtml: false,
  },
};
/**
 * Sanitize HTML content using DOMPurify
 */
export function sanitizeHtml(
  input: string,
  options: SanitizationOptions = {
    // Implementation needed
  }
): string {
  if (!input || typeof input !== "string") {
    return "";
  }
  const config: DOMPurify.Config = {
    // Always forbid dangerous tags and attributes
    FORBID_TAGS: ["script", "object", "embed", "form", "input", "iframe"],
    FORBID_ATTR: [
      "onerror",
      "onload",
      "onclick",
      "onmouseover",
      "onfocus",
      "onblur",
      "onsubmit",
      "onchange",
      "onkeydown",
      "onkeyup",
      "onkeypress",
      "onmousedown",
      "onmouseup",
      "onmousemove",
      "onmouseout",
      "onmousein",
    ],
  };
  // Apply custom allowed tags if provided
  if (options.allowedTags) {
    config.ALLOWED_TAGS = options.allowedTags;
  }
  // Apply custom allowed attributes if provided
  if (options.allowedAttributes) {
    config.ALLOWED_ATTR = options.allowedAttributes;
  }
  // Strip all tags if requested
  if (options.stripTags) {
    config.ALLOWED_TAGS = [];
    config.ALLOWED_ATTR = [];
  }
  let sanitized = purify.sanitize(input, config);
  // Apply length limit if specified
  if (options.maxLength && sanitized.length > options.maxLength) {
    sanitized = sanitized.substring(0, options.maxLength);
  }
  return sanitized;
}
/**
 * Sanitize plain text input (removes all HTML)
 */
export function sanitizeText(input: string, maxLength?: number): string {
  return sanitizeHtml(input, {
    ...SANITIZATION_PRESETS.TEXT_ONLY,
    maxLength,
  });
}
/**
 * Sanitize and validate user input with comprehensive checks
 */
export function validateAndSanitizeInput(
  input: unknown,
  options: SanitizationOptions = {
    // Implementation needed
  }
): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    sanitizedValue: "",
    errors: [],
    warnings: [],
  };
  // Type validation
  if (input === null || input === undefined) {
    result.sanitizedValue = "";
    return result;
  }
  if (typeof input !== "string") {
    result.errors.push("Input must be a string");
    result.isValid = false;
    return result;
  }
  const originalLength = input.length;
  // Sanitize the input
  try {
    result.sanitizedValue = sanitizeHtml(input, options);
  } catch (error) {
    result.errors.push("Failed to sanitize input");
    result.isValid = false;
    return result;
  }
  // Check if content was modified during sanitization
  if (result.sanitizedValue !== input) {
    result.warnings.push("Input was modified during sanitization");
  }
  // Length validation
  if (options.maxLength && originalLength > options.maxLength) {
    result.warnings.push(
      `Input was truncated from ${originalLength} to ${options.maxLength} characters`
    );
  }
  // Check for potentially dangerous patterns
  const dangerousPatterns = [
    /javascript:/i,
    /data:text\/html/i,
    /vbscript:/i,
    /<script/i,
    /on\w+\s*=/i,
  ];
  for (const pattern of dangerousPatterns) {
    if (pattern.test(input)) {
      result.warnings.push("Potentially dangerous content detected and removed");
      break;
    }
  }
  return result;
}
/**
 * Sanitize file upload data
 */
export function sanitizeFileData(file: File): ValidationResult {
  const result: ValidationResult = {
    isValid: true,
    sanitizedValue: file.name,
    errors: [],
    warnings: [],
  };
  // Enhanced file name sanitization
  let sanitizedFileName = file.name;
  // Remove path traversal patterns
  sanitizedFileName = sanitizedFileName.replace(/\.\.\//g, "");
  sanitizedFileName = sanitizedFileName.replace(/\.\.\\/g, "");
  // Remove dangerous characters
  sanitizedFileName = sanitizedFileName.replace(/[<>:"'|?*\x00-\x1f]/g, "");
  // Remove script tags and other dangerous content
  sanitizedFileName = sanitizedFileName.replace(
    /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    ""
  );
  sanitizedFileName = sanitizedFileName.replace(/javascript:/gi, "");
  // Apply standard sanitization
  const fileNameResult = validateAndSanitizeInput(
    sanitizedFileName,
    SANITIZATION_PRESETS.FILE_NAME
  );
  result.sanitizedValue = fileNameResult.sanitizedValue;
  result.errors.push(...fileNameResult.errors);
  result.warnings.push(...fileNameResult.warnings);
  // Check if file name was modified
  if (sanitizedFileName !== file.name) {
    result.warnings.push("Input was modified during sanitization");
  }
  // Check file size (10MB limit)
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    result.errors.push("File size exceeds maximum allowed size (10MB)");
    result.isValid = false;
  }
  // Check file type against allowed types
  const allowedTypes = [
    "application/pdf",
    "image/jpeg",
    "image/png",
    "image/gif",
    "text/plain",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  ];
  if (!allowedTypes.includes(file.type)) {
    result.errors.push(`File type ${file.type} is not allowed`);
    result.isValid = false;
  }
  return result;
}
/**
 * Sanitize form data object
 */
export function sanitizeFormData(
  formData: Record<string, unknown>,
  fieldOptions: Record<string, SanitizationOptions> = {
    // Implementation needed
  }
): Record<string, ValidationResult> {
  const results: Record<string, ValidationResult> = {};
  for (const [key, value] of Object.entries(formData)) {
    const options = fieldOptions[key] || SANITIZATION_PRESETS.TEXT_ONLY;
    results[key] = validateAndSanitizeInput(value, options);
  }
  return results;
}
/**
 * Sanitize URL to prevent XSS in href attributes
 */
export function sanitizeUrl(url: string): string {
  if (!url || typeof url !== "string") {
    return "";
  }
  // Remove dangerous protocols
  const dangerousProtocols = ["javascript:", "data:", "vbscript:", "file:"];
  const lowerUrl = url.toLowerCase().trim();
  for (const protocol of dangerousProtocols) {
    if (lowerUrl.startsWith(protocol)) {
      return "";
    }
  }
  // Allow only safe protocols
  const safeProtocols = ["http:", "https:", "mailto:", "tel:"];
  const hasProtocol = safeProtocols.some(protocol => lowerUrl.startsWith(protocol));
  // If no protocol, assume relative URL (safe)
  if (!hasProtocol && !lowerUrl.includes(":")) {
    return url;
  }
  // If has protocol, ensure it's safe
  if (hasProtocol) {
    return url;
  }
  // If has unknown protocol, remove it
  return "";
}
/**
 * Create a sanitization middleware for API endpoints
 */
export function createSanitizationMiddleware(
  fieldOptions: Record<string, SanitizationOptions> = {
    // Implementation needed
  }
) {
  return (data: Record<string, unknown>) => {
    const sanitizedData: Record<string, unknown> = {};
    const validationResults: Record<string, ValidationResult> = {};
    for (const [key, value] of Object.entries(data)) {
      const options = fieldOptions[key] || SANITIZATION_PRESETS.TEXT_ONLY;
      const result = validateAndSanitizeInput(value, options);
      validationResults[key] = result;
      sanitizedData[key] = result.sanitizedValue;
    }
    return {
      sanitizedData,
      validationResults,
      isValid: Object.values(validationResults).every(result => result.isValid),
    };
  };
}
/**
 * Utility to escape HTML entities as a fallback
 */
export function escapeHtml(text: string): string {
  if (typeof window !== "undefined") {
    // Browser environment
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  } else {
    // Node.js environment - manual escaping
    return text
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#x27;");
  }
}
/**
 * Test function to validate sanitization with known attack vectors
 */
export function testSanitization(): boolean {
  const testVectors = [
    '<script>alert("xss")</script>',
    '<img src="x" onerror="alert(1)">',
    '<iframe src="javascript:alert(1)"></iframe>',
    '<svg onload="alert(1)">',
    '<div onclick="alert(1)">Click me</div>',
  ];
  for (const vector of testVectors) {
    const sanitized = sanitizeHtml(vector);
    if (sanitized.includes("alert") || sanitized.includes("javascript:")) {
      return false;
    }
  }
  // Test plain text javascript: URLs separately since they're not HTML
  const plainTextVector = "javascript:alert(1)";
  const sanitizedUrl = sanitizeUrl(plainTextVector);
  if (sanitizedUrl.includes("alert") || sanitizedUrl.includes("javascript:")) {
    return false;
  }
  return true;
}
export default {
  sanitizeHtml,
  sanitizeText,
  validateAndSanitizeInput,
  sanitizeFileData,
  sanitizeFormData,
  sanitizeUrl,
  createSanitizationMiddleware,
  escapeHtml,
  testSanitization,
  SANITIZATION_PRESETS,
};
