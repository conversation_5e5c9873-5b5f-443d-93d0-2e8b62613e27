import { Logger, logger, LogLevel } from "@/utils/errors/Logger";
import { ErrorContext } from "@/types";

/**
 * Centralized logging service that replaces console statements
 * with structured logging capabilities
 */
export class LoggingService {
  private static instance: LoggingService;
  private logger: Logger;

  private constructor(loggerInstance?: Logger) {
    this.logger = loggerInstance || logger;
  }

  public static getInstance(loggerInstance?: Logger): LoggingService {
    if (!LoggingService.instance) {
      LoggingService.instance = new LoggingService(loggerInstance);
    } else if (loggerInstance) {
      LoggingService.instance.logger = loggerInstance;
    }
    return LoggingService.instance;
  }

  public static resetInstance(): void {
    LoggingService.instance = null as any;
  }

  /**
   * Replace console.log with structured info logging
   */
  public log(message: string, data?: any, context?: Partial<ErrorContext>): void {
    const metadata = data ? { data } : undefined;
    this.logger.info(message, context as ErrorContext, metadata);
  }

  /**
   * Replace console.info with structured info logging
   */
  public info(message: string, data?: any, context?: Partial<ErrorContext>): void {
    const metadata = data ? { data } : undefined;
    this.logger.info(message, context as ErrorContext, metadata);
  }

  /**
   * Replace console.warn with structured warning logging
   */
  public warn(message: string, data?: any, context?: Partial<ErrorContext>): void {
    const metadata = data ? { data } : undefined;
    this.logger.warn(message, context as ErrorContext, metadata);
  }

  /**
   * Replace console.error with structured error logging
   */
  public error(message: string, error?: Error | any, context?: Partial<ErrorContext>): void {
    const errorObj = error instanceof Error ? error : undefined;
    const metadata = error && !(error instanceof Error) ? { error } : undefined;
    this.logger.error(message, errorObj, context as ErrorContext, metadata);
  }

  /**
   * Replace console.debug with structured debug logging
   */
  public debug(message: string, data?: any, context?: Partial<ErrorContext>): void {
    const metadata = data ? { data } : undefined;
    this.logger.debug(message, context as ErrorContext, metadata);
  }

  /**
   * Log performance metrics
   */
  public performance(
    message: string,
    metrics: Record<string, number>,
    context?: Partial<ErrorContext>
  ): void {
    this.logger.info(message, context as ErrorContext, { performance: metrics });
  }

  /**
   * Log user actions for audit purposes
   */
  public audit(
    action: string,
    userId: string,
    details?: Record<string, any>,
    context?: Partial<ErrorContext>
  ): void {
    const auditContext: ErrorContext = {
      ...context,
      userId,
      action,
      component: context?.component || "audit",
    } as ErrorContext;

    this.logger.info(`User action: ${action}`, auditContext, { auditDetails: details });
  }

  /**
   * Log API requests and responses
   */
  public api(
    method: string,
    url: string,
    status: number,
    duration: number,
    context?: Partial<ErrorContext>
  ): void {
    const apiContext: ErrorContext = {
      ...context,
      component: context?.component || "api",
      action: `${method} ${url}`,
    } as ErrorContext;

    const metadata = {
      method,
      url,
      status,
      duration,
      success: status >= 200 && status < 300,
    };

    if (status >= 400) {
      this.logger.error(`API request failed: ${method} ${url}`, undefined, apiContext, metadata);
    } else {
      this.logger.info(`API request: ${method} ${url}`, apiContext, metadata);
    }
  }

  /**
   * Start a correlation context for request tracking
   */
  public startCorrelation(correlationId?: string): string {
    return this.logger.startCorrelation(correlationId);
  }

  /**
   * End current correlation context
   */
  public endCorrelation(): void {
    this.logger.endCorrelation();
  }

  /**
   * Get current correlation ID
   */
  public getCorrelationId(): string | null {
    return this.logger.getCurrentCorrelationId();
  }

  /**
   * Get logging statistics
   */
  public getStats() {
    return this.logger.getStats();
  }

  /**
   * Force flush logs (useful for testing or before page unload)
   */
  public async flush(): Promise<void> {
    await this.logger.flush();
  }
}

// Export singleton instance

export const loggingService = LoggingService.getInstance();

// Export convenience functions for easy migration from console statements
export const log = {
  debug: (message: string, data?: any, context?: Partial<ErrorContext>) =>
    loggingService.debug(message, data, context),

  info: (message: string, data?: any, context?: Partial<ErrorContext>) =>
    loggingService.info(message, data, context),

  warn: (message: string, data?: any, context?: Partial<ErrorContext>) =>
    loggingService.warn(message, data, context),

  error: (message: string, error?: Error | any, context?: Partial<ErrorContext>) =>
    loggingService.error(message, error, context),

  performance: (
    message: string,
    metrics: Record<string, number>,
    context?: Partial<ErrorContext>
  ) => loggingService.performance(message, metrics, context),

  audit: (
    action: string,
    userId: string,
    details?: Record<string, any>,
    context?: Partial<ErrorContext>
  ) => loggingService.audit(action, userId, details, context),

  api: (
    method: string,
    url: string,
    status: number,
    duration: number,
    context?: Partial<ErrorContext>
  ) => loggingService.api(method, url, status, duration, context),
};

/**
 * Console replacement object for development environments
 * This can be used to gradually replace console statements
 */
export const structuredConsole = {
  log: loggingService.log.bind(loggingService),
  info: loggingService.info.bind(loggingService),
  warn: loggingService.warn.bind(loggingService),
  error: loggingService.error.bind(loggingService),
  debug: loggingService.debug.bind(loggingService),
};
