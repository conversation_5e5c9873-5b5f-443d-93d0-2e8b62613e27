
import { Layout } from "@/components/layout/Layout";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { RiskTemplateSheet } from "@/components/risk/template/RiskTemplateSheet";
import { RiskTemplateCard } from "@/components/risk/template/RiskTemplateCard";
import { useRiskTemplates } from "@/hooks/useRiskTemplates";

const RiskTemplates = () => {
  const [createSheetOpen, setCreateSheetOpen] = useState(false);
  const { templates, loading, fetchTemplates } = useRiskTemplates();

  const handleDeleteTemplate = (templateId: string) => {
    // Handle template deletion
    fetchTemplates();
  };

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex justify-between items-start">
          <div className="space-y-1.5">
            <p className="text-muted-foreground">
              Pre-configured risk templates to speed up risk assessment
            </p>
          </div>
          <Button onClick={() => setCreateSheetOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Template
          </Button>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="h-48 bg-muted animate-pulse rounded-lg" />
            ))}
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {templates.map((template) => (
              <RiskTemplateCard 
                key={template.id} 
                template={template}
                onDelete={() => handleDeleteTemplate(template.id)}
              />
            ))}
          </div>
        )}

        <RiskTemplateSheet
          isOpen={createSheetOpen}
          onOpenChange={setCreateSheetOpen}
        />
      </div>
    </Layout>
  );
};

export default RiskTemplates;
