import { Layout } from "@/components/layout/Layout";
import { UserManagementTabs } from "@/components/user/UserManagementTabs";
import { useAuth } from "@/contexts/auth";
import { useQuery } from "@tanstack/react-query";
import { fetchUsers } from "@/services/user";
import { User } from "@/types";
const UserManagement = () => {
  const { user } = useAuth();
  // Fetch users data
  const {
    data: usersData,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["users"],
    queryFn: async () => {
      const { data, error } = await fetchUsers();
      if (error) {
        throw error;
      }
      return data ?? [];
    },
  });
  const handleUserUpdated = () => {
    refetch();
  };
  return (
    <Layout>
      <div className="space-y-6">
        <div className="space-y-1.5">
          <h2 className="text-2xl font-bold tracking-tight">User Management</h2>
          <p className="text-muted-foreground">Manage users, roles, and administrative requests</p>
        </div>
        <UserManagementTabs
          users={(usersData as User[]) || []}
          loading={isLoading}
          currentUserId={user?.id ?? ""}
          onUserUpdated={handleUserUpdated}
        />
      </div>
    </Layout>
  );
};
export default UserManagement;
