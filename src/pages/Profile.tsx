
import { useAuth } from "@/contexts/auth";
import { Navigate, Link } from "react-router-dom";
import { useProfileData } from "@/hooks/useProfileData";
import ProfileHeader from "@/components/profile/ProfileHeader";
import ProfileActivitySummary from "@/components/profile/ProfileActivitySummary";
import ProfileCommentsTable from "@/components/profile/ProfileCommentsTable";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import Layout from "@/components/layout/Layout";

const Profile = () => {
  const { user } = useAuth();
  const {
    loading,
    userRisks: risks,
    userComments: comments,
  } = useProfileData();

  // If not authenticated, redirect to login
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Calculate stats from the data
  const riskStats = {
    totalRisks: risks.length,
    openRisks: risks.filter(risk => risk.status !== 'Closed').length,
    criticalRisks: risks.filter(risk => risk.severity === 'Critical').length,
    overdueRisks: risks.filter(risk => 
      risk.dueDate && new Date(risk.dueDate) < new Date() && risk.status !== 'Closed'
    ).length,
    byStatus: risks.reduce((acc, risk) => {
      acc[risk.status] = (acc[risk.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
    bySeverity: risks.reduce((acc, risk) => {
      acc[risk.severity] = (acc[risk.severity] || 0) + 1;
      return acc;
    }, {} as Record<string, number>),
  };

  const incidentStats = {
    totalIncidents: 0, // We don't fetch incidents for profile yet
    openIncidents: 0,
    criticalIncidents: 0,
    byStatus: {} as Record<string, number>,
    bySeverity: {} as Record<string, number>,
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Breadcrumb and header */}
        <div>
          <nav className="text-sm text-muted-foreground mb-2">
            <ol className="flex items-center">
              <li>
                <span>Dashboard</span>
              </li>
              <li>
                <span className="mx-2">›</span>
              </li>
              <li>
                <span className="font-medium text-foreground">User Profile</span>
              </li>
            </ol>
          </nav>
          <h1 className="text-3xl font-bold tracking-tight">Your Profile</h1>
        </div>

        {loading ? (
          <div className="space-y-6">
            <div className="flex flex-col lg:flex-row gap-6 mb-8">
              <div className="flex flex-col md:flex-row gap-4 items-start md:items-center flex-grow">
                <Skeleton className="h-20 w-20 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-8 w-48" />
                  <Skeleton className="h-4 w-72" />
                </div>
              </div>
            </div>
            <div className="grid gap-4 md:grid-cols-2">
              <Skeleton className="h-64" />
              <Skeleton className="h-64" />
            </div>
            <Skeleton className="h-96" />
          </div>
        ) : (
          <>
            <ProfileHeader 
              user={user} 
              riskCount={riskStats.totalRisks}
              incidentCount={incidentStats.totalIncidents} 
            />
            
            <Tabs defaultValue="activity">
              <TabsList className="w-full max-w-md mb-2">
                <TabsTrigger value="activity" className="flex-1">Activity Summary</TabsTrigger>
                <TabsTrigger value="comments" className="flex-1">Related Comments</TabsTrigger>
              </TabsList>
              
              <TabsContent value="activity" className="space-y-6">
                <ProfileActivitySummary 
                  riskStats={riskStats}
                  incidentStats={incidentStats}
                />
                
                <Separator className="my-6" />
                
                <div className="space-y-4">
                  <h2 className="text-xl font-semibold">Recent Activity</h2>
                  
                  {risks.length === 0 ? (
                    <div className="text-muted-foreground text-center p-8 border rounded-md">
                      No recent activity found.
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {risks.slice(0, 3).length > 0 && (
                        <div>
                          <h3 className="text-md font-medium mb-2">Recent Risks</h3>
                          <div className="grid gap-2">
                            {risks.slice(0, 3).map((risk) => (
                              <Link 
                                to={`/risks/${risk.id}`} 
                                key={risk.id}
                                className="p-3 border rounded-md hover:bg-muted/50 transition-colors"
                              >
                                <div className="flex justify-between">
                                  <span className="font-medium">{risk.title}</span>
                                  <span className="text-xs text-muted-foreground">
                                    {new Date(risk.updatedAt).toLocaleDateString()}
                                  </span>
                                </div>
                                <div className="flex gap-2 mt-1">
                                  <span className="text-xs bg-muted px-2 py-0.5 rounded-full">
                                    {risk.status}
                                  </span>
                                  <span className="text-xs bg-muted px-2 py-0.5 rounded-full">
                                    {risk.severity}
                                  </span>
                                </div>
                              </Link>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </TabsContent>
              
              <TabsContent value="comments">
                <ProfileCommentsTable comments={comments} loading={loading} />
              </TabsContent>
            </Tabs>
          </>
        )}
      </div>
    </Layout>
  );
};

export default Profile;
