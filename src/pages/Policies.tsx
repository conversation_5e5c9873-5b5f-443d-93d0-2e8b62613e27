
import { Layout } from "@/components/layout/Layout";
import { useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { PolicyListView } from "@/components/policy/list/PolicyListView";
import { PolicyDetailView } from "@/components/policy/detail/PolicyDetailView";
import { PolicyListHeader } from "@/components/policy/list/PolicyListHeader";
import { PolicyRequestDialog } from "@/components/policy/PolicyRequestDialog";
import { usePolicy, usePolicies } from "@/hooks/usePolicies";

const Policies = () => {
  const { policyId } = useParams();
  const navigate = useNavigate();
  const [requestDialogOpen, setRequestDialogOpen] = useState(false);
  const [activeCategory, setActiveCategory] = useState("all");
  const { policy, loading: policyLoading } = usePolicy(policyId);
  const { policies, loading: policiesLoading } = usePolicies();

  const handleBackToList = () => {
    navigate('/policies');
  };

  const handleViewPolicy = (selectedPolicy: Record<string, unknown>) => {
    navigate(`/policies/${selectedPolicy.id}`);
  };

  const handleRequestPolicy = () => {
    setRequestDialogOpen(true);
  };

  if (policyId) {
    return (
      <Layout>
        <PolicyDetailView 
          policy={policy}
          loading={policyLoading}
          handleBackToList={handleBackToList}
        />
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        <PolicyListHeader 
          onRequestPolicy={handleRequestPolicy} 
          activeCategory={activeCategory}
          setActiveCategory={setActiveCategory}
        />

        <PolicyListView 
          policies={policies}
          loading={policiesLoading}
          handleViewPolicy={handleViewPolicy}
          activeCategory={activeCategory}
          setActiveCategory={setActiveCategory}
        />

        <PolicyRequestDialog
          open={requestDialogOpen}
          onOpenChange={setRequestDialogOpen}
        />
      </div>
    </Layout>
  );
};

export default Policies;
