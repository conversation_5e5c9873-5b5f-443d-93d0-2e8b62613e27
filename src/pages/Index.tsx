import { useEffect } from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, ShieldCheck, Users, AlertTriangle } from "lucide-react";
import { cleanupAuthState } from "@/integrations/supabase/client";
const Index = () => {
  // Clean up any stuck auth state when landing on main page
  useEffect(() => {
    const isStuck = localStorage.getItem("authStuckState");
    if (isStuck) {
      cleanupAuthState();
      localStorage.removeItem("authStuckState");
    }
  }, []);
  return (
    <div className="min-h-screen bg-background overflow-auto">
      <div className="w-full px-4 py-8 sm:py-12">
        <div className="container mx-auto max-w-4xl">
          <div className="space-y-8 sm:space-y-12">
            <div className="space-y-6 text-center">
              <ShieldCheck className="h-12 w-12 sm:h-16 sm:w-16 mx-auto text-primary" />
              <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight text-foreground px-4">
                Welcome to RiskCompass
              </h1>
              <p className="text-lg sm:text-xl text-muted-foreground max-w-2xl mx-auto px-4">
                Your complete solution for enterprise risk management, incident tracking, and
                compliance policy administration.
              </p>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 px-4">
              <div className="bg-card text-card-foreground p-4 sm:p-6 rounded-lg shadow border">
                <AlertTriangle className="h-6 w-6 sm:h-8 sm:w-8 text-amber-500 mb-3 mx-auto" />
                <h3 className="text-base sm:text-lg font-medium mb-2 text-center">
                  Risk Management
                </h3>
                <p className="text-sm text-muted-foreground text-center">
                  Identify, assess, and mitigate risks across your organization.
                </p>
              </div>
              <div className="bg-card text-card-foreground p-4 sm:p-6 rounded-lg shadow border">
                <Users className="h-6 w-6 sm:h-8 sm:w-8 text-blue-500 mb-3 mx-auto" />
                <h3 className="text-base sm:text-lg font-medium mb-2 text-center">
                  Role-Based Access
                </h3>
                <p className="text-sm text-muted-foreground text-center">
                  Secure, role-based permissions for your entire team.
                </p>
              </div>
              <div className="bg-card text-card-foreground p-4 sm:p-6 rounded-lg shadow border sm:col-span-2 lg:col-span-1">
                <ShieldCheck className="h-6 w-6 sm:h-8 sm:w-8 text-green-500 mb-3 mx-auto" />
                <h3 className="text-base sm:text-lg font-medium mb-2 text-center">
                  Policy Management
                </h3>
                <p className="text-sm text-muted-foreground text-center">
                  Create, manage, and distribute compliance policies.
                </p>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row justify-center gap-4 pt-4 px-4">
              <Button asChild size="lg" className="gap-2 w-full sm:w-auto">
                <Link to="/login">
                  Log In <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
              <Button asChild variant="outline" size="lg" className="w-full sm:w-auto">
                <Link to="/signup">Create Account</Link>
              </Button>
            </div>
            <p className="text-xs sm:text-sm text-muted-foreground pt-4 text-center px-4">
              New users will be granted Staff access by default.
              <br className="hidden sm:block" />
              <span className="block sm:inline">
                {" "}
                Administrator access requires an invite code or approval.
              </span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
export default Index;
