
import { Link } from "react-router-dom";
import { 
  Card,
  CardContent,
  CardFooter
} from "@/components/ui/card";
import { LoadingScreen } from "@/components/auth/signup/LoadingScreen";
import { EmailConfirmationView } from "@/components/auth/signup/EmailConfirmationView";
import { SignupForm } from "@/components/auth/signup/SignupForm";
import { SignupHeader } from "@/components/auth/signup/SignupHeader";
import { useSignupForm } from "@/hooks/useSignupForm";

const Signup = () => {
  const {
    inviteParam,
    error,
    loading,
    signupSuccess,
    loginLoading,
    handleSignup,
    handleLogin,
    setSignupSuccess
  } = useSignupForm();

  if (loading) {
    return <LoadingScreen message="Creating your account..." />;
  }

  if (loginLoading) {
    return <LoadingScreen message="Signing you in..." />;
  }

  // Show email confirmation and login form after successful signup
  if (signupSuccess) {
    return (
      <EmailConfirmationView
        email=""
        onLogin={handleLogin}
        onBack={() => setSignupSuccess(false)}
        loading={loginLoading}
        error={error}
      />
    );
  }

  // Initial signup form
  return (
    <div className="auth-page">
      <div className="min-h-screen w-full flex items-center justify-center bg-background p-4">
        <Card className="w-full max-w-md">
          <SignupHeader />
          
          <CardContent>
            <SignupForm
              onSubmit={handleSignup}
              loading={loading}
              error={error}
              initialInviteCode={inviteParam}
            />
          </CardContent>

          <CardFooter className="flex justify-center">
            <p className="text-sm text-muted-foreground">
              Already have an account?{" "}
              <Link 
                to="/login" 
                className="font-medium text-primary hover:underline"
              >
                Sign in
              </Link>
            </p>
          </CardFooter>
        </Card>
      </div>
    </div>
  );
};

export default Signup;
