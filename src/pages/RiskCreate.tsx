import { useLocation, useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import RiskForm from "@/components/risk/RiskForm";
import { useEffect, useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { Skeleton } from "@/components/ui/skeleton";
const RiskCreate = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isLoadingTemplate, setIsLoadingTemplate] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // Extract templateId from location state, with improved validation
  const templateId =
    location.state?.templateId && typeof location.state.templateId === "string"
      ? location.state.templateId
      : undefined;
  // Set page title when component mounts
  useEffect(() => {
    document.title = "Create New Risk | Risk Management";
    // Show toast if templateId is present in state but invalid
    if (location.state?.templateId && typeof location.state.templateId !== "string") {
      toast({
        title: "Invalid template",
        description: "The template identifier is not valid.",
        variant: "destructive",
      });
      // Clear the invalid state
      navigate("/risks/create", { replace: true, state: {} });
    }
    // Debug logging to help identify issues
    if (templateId) {
      // Condition handled
    }
  }, [templateId, toast, location.state, navigate]);
  const handleSuccess = () => {
    toast({
      title: "Risk created",
      description: "Your risk has been successfully added to the register.",
    });
    navigate("/risks");
  };
  // Handle template loading state changes from the form
  const handleTemplateLoadingChange = (loading: boolean) => {
    setIsLoadingTemplate(loading);
  };
  // Handle template loading errors from the form
  const handleTemplateError = (errorMessage: string | null) => {
    setError(errorMessage);
  };
  if (isLoadingTemplate) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-48" />
        </div>
        <div className="bg-background rounded-lg border shadow-sm p-6">
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-10 w-2/3" />
          </div>
        </div>
      </div>
    );
  }
  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Button variant="outline" size="icon" onClick={() => navigate("/risks")}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold">Error Loading Template</h1>
          </div>
        </div>
        <div className="bg-red-50 p-4 rounded-md border border-red-200">
          <p className="text-red-800">{error}</p>
          <Button
            onClick={() => navigate("/risks/create", { replace: true, state: {} })}
            variant="outline"
            className="mt-4"
          >
            Create Risk Without Template
          </Button>
        </div>
      </div>
    );
  }
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-2">
          <Button variant="outline" size="icon" onClick={() => navigate("/risks")}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <h1 className="text-2xl font-bold">Create New Risk</h1>
        </div>
      </div>
      <div className="bg-background rounded-lg border shadow-sm p-6">
        <RiskForm
          onSuccess={handleSuccess}
          onCancel={() => navigate("/risks")}
          templateId={templateId}
        />
      </div>
    </div>
  );
};
export default RiskCreate;
