
import { Layout } from "@/components/layout/Layout";
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import OverviewTab from "@/components/reports/OverviewTab";
import MatrixTab from "@/components/reports/MatrixTab";
import HistoricalTab from "@/components/reports/HistoricalTab";
import AdvancedReportsTab from "@/components/reports/AdvancedReportsTab";
import ExportsTab from "@/components/reports/ExportsTab";
import { useRiskData } from "@/hooks/useRiskData";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { BarChart3, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const Reports = () => {
  const [activeTab, setActiveTab] = useState("overview");
  const { risks, isLoading: loading } = useRiskData();
  const navigate = useNavigate();

  // Show empty state when no risks exist
  if (!loading && risks.length === 0) {
    return (
      <Layout>
        <div className="space-y-6">
          <Card className="border-dashed">
            <CardHeader className="text-center pb-4">
              <div className="flex justify-center mb-4">
                <div className="rounded-full bg-primary/10 p-6">
                  <BarChart3 className="h-12 w-12 text-primary" />
                </div>
              </div>
              <CardTitle className="text-2xl">No Reports Available</CardTitle>
              <CardDescription className="text-lg max-w-2xl mx-auto">
                Reports and analytics will be available once you start creating and managing risks. 
                Get started by adding your first risk assessment.
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              <Button 
                onClick={() => navigate('/risks/create')}
                size="lg"
                className="flex items-center gap-2"
              >
                <Plus className="h-5 w-5" />
                Create Your First Risk
              </Button>
            </CardContent>
          </Card>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-5 mb-8">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="matrix">Risk Matrix</TabsTrigger>
            <TabsTrigger value="historical">Historical</TabsTrigger>
            <TabsTrigger value="advanced">Advanced</TabsTrigger>
            <TabsTrigger value="exports">Exports</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <OverviewTab risks={risks} loading={loading} />
          </TabsContent>

          <TabsContent value="matrix" className="space-y-4">
            <MatrixTab risks={risks} loading={loading} />
          </TabsContent>

          <TabsContent value="historical" className="space-y-4">
            <HistoricalTab risks={risks} loading={loading} />
          </TabsContent>

          <TabsContent value="advanced" className="space-y-4">
            <AdvancedReportsTab risks={risks} loading={loading} />
          </TabsContent>

          <TabsContent value="exports" className="space-y-4">
            <ExportsTab risks={risks} loading={loading} />
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default Reports;
