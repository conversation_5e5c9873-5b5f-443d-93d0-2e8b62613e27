
import { useIncidentDetails } from "@/hooks/useIncidentDetails";
import { useIsMobile } from "@/hooks/use-mobile";
import IncidentDetailsSkeleton from "@/components/incident/detail/IncidentDetailsSkeleton";
import IncidentNotFound from "@/components/incident/detail/IncidentNotFound";
import IncidentDetailsHeader from "@/components/incident/detail/IncidentDetailsHeader";
import IncidentDetailsContent from "@/components/incident/detail/IncidentDetailsContent";
import CommentsSection from "@/components/comments/CommentsSection";
import { Layout } from "@/components/layout/Layout";
import { Lock } from "lucide-react";

const IncidentDetails = () => {
  const { incident, loading, error, id } = useIncidentDetails();
  const isMobile = useIsMobile();
  const isClosed = incident?.status === "Closed";

  if (loading) {
    return (
      <Layout>
        <IncidentDetailsSkeleton />
      </Layout>
    );
  }

  if (!incident) {
    return (
      <Layout>
        <IncidentNotFound />
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {isClosed && (
          <div className="bg-gray-100 border border-gray-300 p-4 rounded-lg flex items-center gap-2">
            <Lock className="h-5 w-5 text-gray-600" />
            <div>
              <p className="font-medium text-gray-700">
                This incident is currently closed
              </p>
              <p className="text-sm text-gray-500">
                Use the "Reopen" button to make changes
              </p>
            </div>
          </div>
        )}
        
        <IncidentDetailsHeader 
          incident={incident}
        />
        
        <IncidentDetailsContent incident={incident} />

        {/* Add comments section */}
        <CommentsSection entityType="incident" entityId={id ?? ""} />
      </div>
    </Layout>
  );
};

export default IncidentDetails;
