
// src/pages/Dashboard.tsx
// Enhanced Mobile Dashboard Component with Linear styling

import { But<PERSON> } from "@/components/ui/button";
import { AlertCircle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";
import { DashboardLoading } from "@/components/dashboard/DashboardLoading";
import { FeedbackMessage } from "@/components/ui/feedback-message";
import { useDashboardData } from "@/hooks/useDashboardData";
import DashboardContent from "@/components/dashboard/DashboardContent";
import { useAuth } from "@/contexts/auth";
import { Layout } from "@/components/layout/Layout";
import { formatRisksData } from "@/utils/riskTransformations";

const Dashboard = () => {
  const {
    dashboardMetrics,
    allRisks,
    criticalRisks,
    recentIncidents,
    isLoading: loading,
    isError,
    errors,
    refetchAll
  } = useDashboardData();

  // Extract data for compatibility with existing components
  // Use all risks for dashboard display to show charts even with non-critical risks
  // Transform the raw data to proper Risk type
  const risks = allRisks ? formatRisksData(allRisks) : [];
  const incidents = recentIncidents ?? [];
  const error = isError ? (errors[0]?.message ?? 'Failed to load dashboard data') : null;
  const handleRetry = refetchAll;
  const { organization, user, isLoading: authLoading } = useAuth();
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  // Show loading while auth is still loading
  if (authLoading) {
    return <DashboardLoading />;
  }

  // If no user (shouldn't happen due to ProtectedRoute, but just in case)
  if (!user) {
    return (
      <Layout>
        <div className="space-y-6">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h2 className="text-2xl font-bold mb-2">Authentication Required</h2>
            <p className="text-muted-foreground mb-4">
              Please sign in to access your dashboard.
            </p>
            <Button onClick={() => navigate('/login')}>
              Sign In
            </Button>
          </div>
        </div>
      </Layout>
    );
  }

  const DashboardPage = () => (
    <div className={isMobile ? 'mobile-section' : 'space-y-6'}>
      {/* Mobile-specific header section - simplified without action buttons */}
      {isMobile && (
        <div className="mobile-section">
          <div className="text-center space-y-4">
            <h2 className="text-xl font-semibold">Welcome back</h2>
            <p className="text-sm text-muted-foreground">
              Here's your risk management overview
            </p>
          </div>
        </div>
      )}

      {/* Error handling */}
      {error && (
        <div className={isMobile ? 'mobile-section' : 'space-y-4'}>
          <FeedbackMessage type="error" message={error} />
          <Button 
            onClick={handleRetry} 
            variant="outline" 
            className={isMobile ? 'btn-mobile-secondary' : 'w-full'}
          >
            Retry Loading Dashboard
          </Button>
        </div>
      )}

      {loading ? (
        <DashboardLoading />
      ) : (
        <DashboardContent
          risks={risks}
          incidents={incidents}
          onDataChange={refetchAll}
        />
      )}
    </div>
  );

  return (
    <Layout>
      <DashboardPage />
    </Layout>
  );
};

export default Dashboard;
