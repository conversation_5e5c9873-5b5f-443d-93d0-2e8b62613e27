
import { Layout } from "@/components/layout/Layout";
import { useIsMobile } from "@/hooks/use-mobile";
import { useIncidents } from "@/hooks/useIncidents";
import IncidentsHeader from "@/components/incident/IncidentsHeader";
import IncidentsContent from "@/components/incident/IncidentsContent";
import IncidentFilters from "@/components/incident/IncidentFilters";

const Incidents = () => {
  const { 
    incidents, 
    loading, 
    error, 
    filters,
    handleFilterChange,
    resetFilters
  } = useIncidents();

  return (
    <Layout>
      <div className="space-y-6">
        <IncidentsHeader />

        <IncidentFilters 
          filters={filters}
          onFilterChange={handleFilterChange}
          onResetFilters={resetFilters}
        />

        <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
          <IncidentsContent 
            incidents={incidents}
            loading={loading}
            error={error}
          />
        </div>
      </div>
    </Layout>
  );
};

export default Incidents;
