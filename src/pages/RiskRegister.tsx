import { useState, useEffect, useMemo } from "react";
import { useSearchParams } from "react-router-dom";
import { Layout } from "@/components/layout/Layout";
import RiskRegisterHeader from "@/components/risk/register/RiskRegisterHeader";
import RiskRegisterContent from "@/components/risk/register/RiskRegisterContent";
import { useRiskData } from "@/hooks/useRiskData";
import { Risk, RiskSeverity, RiskStatus } from "@/types";
import { useFilterData } from "@/components/risk/register/filters/useFilterData";
const RiskRegister = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const { categories, owners, loading: filtersLoading } = useFilterData();
  const [filtersVisible, setFiltersVisible] = useState(false);
  // Initialize filters from URL parameters
  const [activeFilters, setActiveFilters] = useState<{
    severities: RiskSeverity[];
    statuses: RiskStatus[];
    categories: string[];
    ownerIds: string[];
  }>({
    severities: [],
    statuses: [],
    categories: [],
    ownerIds: [],
  });
  const [sortOrder, setSortOrder] = useState<"asc" | "desc" | undefined>();
  // Convert activeFilters to the new API format - moved after state declarations
  const riskDataParams = useMemo(
    () => ({
      severities: activeFilters.severities,
      statuses: activeFilters.statuses,
      categoryIds: activeFilters.categories,
      ownerIds: activeFilters.ownerIds,
      sortField: sortOrder ? "due_date" : undefined,
      sortOrder: sortOrder,
      page: 1,
      pageSize: 100, // Large page size for now, can be made configurable
    }),
    [activeFilters, sortOrder]
  );
  const {
    risks,
    isLoading: loading,
    refetch: fetchRisks,
    totalCount,
    hasNextPage,
  } = useRiskData(riskDataParams);
  // Read URL parameters and set initial filters
  useEffect(() => {
    const severityParams = searchParams.getAll("severity") as RiskSeverity[];
    const statusParams = searchParams.getAll("status") as RiskStatus[];
    const categoryParams = searchParams.getAll("category");
    const ownerParams = searchParams.getAll("owner");
    const sortParam = searchParams.get("sortOrder") as "asc" | "desc" | null;
    setActiveFilters({
      severities: severityParams,
      statuses: statusParams,
      categories: categoryParams,
      ownerIds: ownerParams,
    });
    if (sortParam) {
      setSortOrder(sortParam);
    }
  }, [searchParams]);
  // Risks are now filtered and sorted server-side
  const sortedAndFilteredRisks = risks;
  const handleFilterChange = (filterType: string, value: string | null) => {
    const newParams = new URLSearchParams(searchParams);
    if (value === null) {
      // Clear all filters of this type
      newParams.delete(filterType);
    } else {
      // Check if this value is already in the filters
      const existingValues = newParams.getAll(filterType);
      if (existingValues.includes(value)) {
        // Remove this specific value
        newParams.delete(filterType);
        existingValues.filter(v => v !== value).forEach(v => newParams.append(filterType, v));
      } else {
        // Add this value
        newParams.append(filterType, value);
      }
    }
    setSearchParams(newParams);
  };
  const handleSortChange = (newSortOrder: "asc" | "desc" | null) => {
    const newParams = new URLSearchParams(searchParams);
    if (newSortOrder === null) {
      newParams.delete("sortOrder");
    } else {
      newParams.set("sortOrder", newSortOrder);
    }
    setSearchParams(newParams);
    setSortOrder(newSortOrder ?? undefined);
  };
  const handleImportSuccess = () => {
    // Refresh risks after import or risk creation
    fetchRisks();
  };
  const handleToggleFilters = () => {
    setFiltersVisible(!filtersVisible);
  };
  return (
    <Layout>
      <div className="space-y-6">
        <RiskRegisterHeader
          onImportSuccess={handleImportSuccess}
          onToggleFilters={handleToggleFilters}
          filtersVisible={filtersVisible}
        />
        <RiskRegisterContent
          risks={sortedAndFilteredRisks}
          isLoading={loading}
          categories={categories}
          owners={owners}
          activeFilters={activeFilters}
          sortOrder={sortOrder}
          onFilterChange={handleFilterChange}
          onSortChange={handleSortChange}
          isLoadingFilters={filtersLoading}
        />
      </div>
    </Layout>
  );
};
export default RiskRegister;
