
import { useState } from "react";
import { Tabs, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { PolicyRequestsAdmin } from "@/components/policy/admin/PolicyRequestsAdmin";
import { PolicyManagement } from "@/components/policy/admin/PolicyManagement";
import { PolicyVersionHistory } from "@/components/policy/admin/PolicyVersionHistory";
import { Layout } from "@/components/layout/Layout";

const PolicyAdmin = () => {
  const [activeTab, setActiveTab] = useState("requests");
  
  return (
    <Layout>
      <div className="space-y-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold tracking-tight">Policy Administration</h1>
          <p className="text-muted-foreground">
            Manage policy requests, approve documents, and control version history
          </p>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 mb-8">
            <TabsTrigger value="requests">Policy Requests</TabsTrigger>
            <TabsTrigger value="management">Policy Management</TabsTrigger>
            <TabsTrigger value="history">Version History</TabsTrigger>
          </TabsList>
          
          <TabsContent value="requests" className="space-y-4">
            <PolicyRequestsAdmin />
          </TabsContent>
          
          <TabsContent value="management" className="space-y-4">
            <PolicyManagement />
          </TabsContent>
          
          <TabsContent value="history" className="space-y-4">
            <PolicyVersionHistory />
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default PolicyAdmin;
