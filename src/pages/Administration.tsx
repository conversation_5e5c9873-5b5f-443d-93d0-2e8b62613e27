
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { PolicyRequestsAdmin } from "@/components/policy/admin/PolicyRequestsAdmin";
import { PolicyManagement } from "@/components/policy/admin/PolicyManagement";
import { PolicyVersionHistory } from "@/components/policy/admin/PolicyVersionHistory";
import { RiskCategoriesAdmin } from "@/components/administration/RiskCategoriesAdmin";
import { Layout } from "@/components/layout/Layout";

const Administration = () => {
  const [activeTab, setActiveTab] = useState("policies");
  
  return (
    <Layout>
      <div className="container mx-auto p-4 md:p-6 space-y-6">
        <div className="mb-6">
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight">Risk Framework</h1>
          <p className="text-muted-foreground mt-2">
            Manage policies, risk categories, and framework settings
          </p>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6 md:mb-8 max-w-md">
            <TabsTrigger value="policies" className="text-sm">Policies</TabsTrigger>
            <TabsTrigger value="categories" className="text-sm">Risk Categories</TabsTrigger>
          </TabsList>
          
          <TabsContent value="policies" className="space-y-6">
            <div className="space-y-6">
              <div className="space-y-4">
                <h2 className="text-xl font-semibold">Policy Requests</h2>
                <PolicyRequestsAdmin />
              </div>
              
              <div className="space-y-4">
                <h2 className="text-xl font-semibold">Policy Management</h2>
                <PolicyManagement />
              </div>
              
              <div className="space-y-4">
                <h2 className="text-xl font-semibold">Policy History</h2>
                <PolicyVersionHistory />
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="categories" className="space-y-4">
            <RiskCategoriesAdmin />
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default Administration;
