
import { useState } from "react";
import { Layout } from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { PolicyRequestDialog } from "@/components/policy/PolicyRequestDialog";

const PolicyManagement = () => {
  const [requestDialogOpen, setRequestDialogOpen] = useState(false);

  return (
    <Layout>
      <div className="space-y-6">
        <div className="flex justify-between items-start">
          <div className="space-y-1.5">
            <h1 className="text-2xl font-bold tracking-tight">Policy Management</h1>
            <p className="text-muted-foreground">
              Manage organizational policies and policy requests
            </p>
          </div>
          <Button onClick={() => setRequestDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Request Policy
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="p-6 border rounded-lg">
            <h3 className="font-semibold mb-2">Active Policies</h3>
            <p className="text-sm text-muted-foreground mb-4">
              View and manage currently active organizational policies
            </p>
            <Button variant="outline" className="w-full">
              View Policies
            </Button>
          </div>
          
          <div className="p-6 border rounded-lg">
            <h3 className="font-semibold mb-2">Policy Requests</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Review and manage incoming policy requests
            </p>
            <Button variant="outline" className="w-full">
              View Requests
            </Button>
          </div>
          
          <div className="p-6 border rounded-lg">
            <h3 className="font-semibold mb-2">Draft Policies</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Work on policies currently in development
            </p>
            <Button variant="outline" className="w-full">
              View Drafts
            </Button>
          </div>
        </div>

        <PolicyRequestDialog
          open={requestDialogOpen}
          onOpenChange={setRequestDialogOpen}
        />
      </div>
    </Layout>
  );
};

export default PolicyManagement;
