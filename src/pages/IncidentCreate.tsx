import { useNavigate, useParams, useSearchParams } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { IncidentForm } from "@/components/incident/IncidentForm";
import { useIncidentForm, IncidentFormValues } from "@/hooks/useIncidentForm";
import { createIncident } from "@/services/incidentService";
import { useIsMobile } from "@/hooks/use-mobile";
import { Layout } from "@/components/layout/Layout";
const IncidentCreate = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [searchParams] = useSearchParams();
  const riskId = searchParams.get("riskId"); // Get risk ID from URL search params
  const isMobile = useIsMobile();
  const { form, risks, loading, setLoading, loadingRisks, user } = useIncidentForm(
    riskId ?? undefined
  );
  const onSubmit = async (values: IncidentFormValues) => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "You must be logged in to report an incident.",
        variant: "destructive",
      });
      return;
    }
    setLoading(true);
    try {
      const data = await createIncident(values, user);
      toast({
        title: "Incident Reported",
        description: "Your incident has been successfully recorded.",
      });
      // Redirect to the incident details page
      navigate(`/incidents/${data.id}`);
    } catch (error: unknown) {
      toast({
        title: "Error Reporting Incident",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  const handleBackToRisk = () => {
    if (riskId) {
      navigate(`/risks/${riskId}`);
    }
  };
  return (
    <Layout>
      <div className="space-y-6">
        {/* Show back to risk button when coming from a risk */}
        {riskId && (
          <Button variant="outline" onClick={handleBackToRisk} className="mb-4">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Risk
          </Button>
        )}
        {isMobile && (
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Report New Incident</h2>
            <p className="text-muted-foreground">
              Fill in the details below to report a new incident.
            </p>
          </div>
        )}
        <Card>
          <CardHeader>
            <CardTitle>Incident Details</CardTitle>
          </CardHeader>
          <CardContent>
            <IncidentForm
              form={form}
              risks={risks}
              loadingRisks={loadingRisks}
              loading={loading}
              onSubmit={onSubmit}
              onCancel={() => navigate("/incidents")}
            />
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};
export default IncidentCreate;
