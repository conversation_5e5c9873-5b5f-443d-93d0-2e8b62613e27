
import { Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { IncidentForm } from "@/components/incident/IncidentForm";
import { useIncidentEditForm } from "@/hooks/useIncidentEditForm";

const IncidentEdit = () => {
  const {
    form,
    risks,
    loading,
    loadingRisks,
    initialLoading,
    onSubmit,
    user
  } = useIncidentEditForm();

  if (initialLoading) {
    return (
      <div className="space-y-6">
        <Button variant="ghost" size="sm" asChild>
          <Link to="/incidents">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Incidents
          </Link>
        </Button>
        <div className="space-y-4">
          <Skeleton className="h-8 w-3/4" />
          <Skeleton className="h-24 w-full" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
          <Skeleton className="h-12 w-full" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col space-y-2">
        <Button variant="ghost" size="sm" asChild>
          <Link to="/incidents">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Incidents
          </Link>
        </Button>
        <h2 className="text-2xl font-bold tracking-tight">Edit Incident</h2>
        <p className="text-muted-foreground">
          Update incident details and status
        </p>
      </div>

      <div className="rounded-lg border bg-card p-6">
        {user && (
          <IncidentForm
            form={form}
            risks={risks}
            loadingRisks={loadingRisks}
            loading={loading}
            onSubmit={onSubmit}
            onCancel={() => window.history.back()}
            submitButtonText="Update Incident"
          />
        )}
      </div>
    </div>
  );
};

export default IncidentEdit;
