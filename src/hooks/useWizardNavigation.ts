
import { useState } from "react";

export interface UseWizardNavigationOptions {
  totalSteps: number;
  initialStep?: number;
  onComplete?: () => void;
  validateStep?: (step: number) => boolean;
}

export interface UseWizardNavigationResult {
  currentStep: number;
  canGoNext: boolean;
  canGoPrevious: boolean;
  isFirstStep: boolean;
  isLastStep: boolean;
  goToNext: () => void;
  goToPrevious: () => void;
  goToStep: (step: number) => void;
  reset: () => void;
}

export function useWizardNavigation({
  totalSteps,
  initialStep = 1,
  onComplete,
  validateStep
}: UseWizardNavigationOptions): UseWizardNavigationResult {
  const [currentStep, setCurrentStep] = useState(initialStep);

  const isFirstStep = currentStep === 1;
  const isLastStep = currentStep === totalSteps;
  const canGoPrevious = !isFirstStep;
  const canGoNext = validateStep ? validateStep(currentStep) : true;

  const goToNext = () => {
    if (isLastStep) {
      onComplete?.();
    } else if (canGoNext && currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const goToPrevious = () => {
    if (canGoPrevious && currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (step: number) => {
    if (step >= 1 && step <= totalSteps) {
      setCurrentStep(step);
    }
  };

  const reset = () => {
    setCurrentStep(initialStep);
  };

  return {
    currentStep,
    canGoNext,
    canGoPrevious,
    isFirstStep,
    isLastStep,
    goToNext,
    goToPrevious,
    goToStep,
    reset
  };
}
