import { useState, useCallback, useMemo } from "react";
import { useIncidents as useOptimizedIncidents } from "./useOptimizedIncidents";
import type { IncidentDataParams } from "./useOptimizedIncidents";
import type { IncidentFilters } from "@/components/incident/IncidentFilters";

// Re-export types
export type { IncidentDataParams, IncidentDataResponse } from "./useOptimizedIncidents";

/**
 * Enhanced incidents hook with filter management
 * Wraps the optimized incidents hook and adds filter state management
 */

export const useIncidents = () => {
  // Filter state management
  const [filters, setFilters] = useState<IncidentFilters>({
    search: "",
    status: undefined,
    severity: undefined,
    sortBy: "date",
    sortOrder: "desc",
  });

  // Convert IncidentFilters to IncidentDataParams
  const queryParams = useMemo((): IncidentDataParams => {
    return {
      searchTerm: filters.search ?? undefined,
      status: filters.status as IncidentDataParams["status"],
      severity: filters.severity as IncidentDataParams["severity"],
      // Note: sortBy and sortOrder are handled in the component for now
      // The optimized hook currently only supports created_at sorting
    };
  }, [filters]);

  // Use the optimized incidents hook with converted params
  const incidentsQuery = useOptimizedIncidents(queryParams);

  // Filter change handler
  const handleFilterChange = useCallback((newFilters: IncidentFilters) => {
    setFilters(newFilters);
  }, []);

  // Reset filters handler
  const resetFilters = useCallback(() => {
    setFilters({
      search: "",
      status: undefined,
      severity: undefined,
      sortBy: "date",
      sortOrder: "desc",
    });
  }, []);

  return {
    // Data from optimized hook
    ...incidentsQuery,

    // Filter management
    filters,
    handleFilterChange,
    resetFilters,
  };
};
