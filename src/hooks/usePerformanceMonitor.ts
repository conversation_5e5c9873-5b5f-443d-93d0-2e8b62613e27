import { useEffect, useRef, useState } from "react";
import { useLocation } from "react-router-dom";
import {
  PerformanceMetricsCollector,
  getGlobalPerformanceCollector,
  PerformanceMetrics as CollectorMetrics,
  PerformanceAlert,
  DEFAULT_PERFORMANCE_BUDGET,
} from "../utils/performance-metrics-collector";
/**
 * Performance monitoring metrics (legacy interface for backward compatibility)
 */
interface PerformanceMetrics {
  routeLoadTime: number;
  chunkLoadTimes: Record<string, number>;
  memoryUsage: number;
  renderTime: number;
  bundleSize: number;
  cacheHitRate: number;
}
/**
 * Enhanced performance monitoring with real-time metrics collection
 */
interface EnhancedPerformanceMetrics extends PerformanceMetrics {
  // Core Web Vitals
  firstContentfulPaint: number | null;
  largestContentfulPaint: number | null;
  cumulativeLayoutShift: number | null;
  firstInputDelay: number | null;
  timeToInteractive: number | null;
  // Additional metrics
  navigationTiming: {
    domContentLoaded: number;
    loadComplete: number;
    timeToFirstByte: number;
  };
  // Performance alerts
  alerts: PerformanceAlert[];
  performanceScore: number;
}
/**
 * Performance thresholds for warnings
 */
const PERFORMANCE_THRESHOLDS = {
  ROUTE_LOAD_WARNING: 2000, // 2 seconds
  ROUTE_LOAD_ERROR: 5000, // 5 seconds
  CHUNK_LOAD_WARNING: 1000, // 1 second
  MEMORY_WARNING: 50, // 50MB
  RENDER_WARNING: 16, // 16ms (60fps)
};
/**
 * Hook to monitor application performance metrics
 */
export function usePerformanceMonitor() {
  const location = useLocation();
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    routeLoadTime: 0,
    chunkLoadTimes: {},
    memoryUsage: 0,
    renderTime: 0,
    bundleSize: 0,
    cacheHitRate: 0,
  });
  const routeStartTime = useRef<number>(0);
  const renderStartTime = useRef<number>(0);
  // Monitor route changes
  useEffect(() => {
    routeStartTime.current = performance.now();
    // Monitor when route is fully loaded
    const handleLoad = () => {
      const loadTime = performance.now() - routeStartTime.current;
      setMetrics(prev => ({
        ...prev,
        routeLoadTime: loadTime,
      }));
      // Log performance warnings
      if (loadTime > PERFORMANCE_THRESHOLDS.ROUTE_LOAD_ERROR) {
        // Condition handled
      } else if (loadTime > PERFORMANCE_THRESHOLDS.ROUTE_LOAD_WARNING) {
        // Condition handled
      } else {
        // Else case handled
      }
    };
    // Use requestIdleCallback if available, otherwise setTimeout
    if ("requestIdleCallback" in window) {
      requestIdleCallback(handleLoad);
    } else {
      setTimeout(handleLoad, 0);
    }
  }, [location.pathname]);
  // Monitor render performance - runs on every render to measure render time
  useEffect(() => {
    renderStartTime.current = performance.now();
    const measureRenderTime = () => {
      const renderTime = performance.now() - renderStartTime.current;
      setMetrics(prev => ({
        ...prev,
        renderTime,
      }));
      if (renderTime > PERFORMANCE_THRESHOLDS.RENDER_WARNING) {
        // Condition handled
      }
    };
    // Measure after render is complete
    if ("requestIdleCallback" in window) {
      requestIdleCallback(measureRenderTime);
    } else {
      setTimeout(measureRenderTime, 0);
    }
  }); // No dependency array - runs on every render to measure render time
  // Monitor memory usage
  useEffect(() => {
    const monitorMemory = () => {
      // Robust feature detection for Chrome's performance.memory API
      if (
        "memory" in performance?.memory &&
        typeof performance.memory === "object" &&
        "usedJSHeapSize" in performance.memory &&
        typeof performance.memory.usedJSHeapSize === "number"
      ) {
        const memory = performance.memory as {
          usedJSHeapSize: number;
          totalJSHeapSize: number;
          jsHeapSizeLimit: number;
        };
        const usedMB = memory.usedJSHeapSize / 1024 / 1024;
        setMetrics(prev => ({
          ...prev,
          memoryUsage: usedMB,
        }));
        if (usedMB > PERFORMANCE_THRESHOLDS.MEMORY_WARNING) {
          // Condition handled
        }
      } else {
        // Fallback for browsers without performance.memory
        if (import.meta.env.MODE === "development") {
          // Condition handled
        }
      }
    };
    // Monitor memory every 30 seconds
    const interval = setInterval(monitorMemory, 30000);
    monitorMemory(); // Initial check
    return () => clearInterval(interval);
  }, []);
  // Monitor chunk loading
  useEffect(() => {
    if (typeof window === "undefined") return;
    const observer = new PerformanceObserver(list => {
      const chunkTimes: Record<string, number> = {};
      let cacheHits = 0;
      let totalRequests = 0;
      for (const entry of list.getEntries()) {
        if (entry.entryType === "resource" && entry.name.includes(".js")) {
          const resource = entry as PerformanceResourceTiming;
          const chunkName = resource.name.split("/").pop() ?? "unknown";
          const loadTime = resource.responseEnd - resource.requestStart;
          chunkTimes[chunkName] = loadTime;
          totalRequests++;
          // Check cache hit
          if (resource.transferSize === 0 && resource.decodedBodySize > 0) {
            cacheHits++;
          }
          // Warn about slow chunks
          if (loadTime > PERFORMANCE_THRESHOLDS.CHUNK_LOAD_WARNING) {
            // Condition handled
          }
        }
      }
      if (Object.keys(chunkTimes).length > 0) {
        setMetrics(prev => ({
          ...prev,
          chunkLoadTimes: { ...prev.chunkLoadTimes, ...chunkTimes },
          cacheHitRate: totalRequests > 0 ? (cacheHits / totalRequests) * 100 : 0,
        }));
      }
    });
    observer.observe({ entryTypes: ["resource"] });
    return () => observer.disconnect();
  }, []);
  // Monitor bundle size
  useEffect(() => {
    const calculateBundleSize = () => {
      if (typeof window === "undefined") return;
      const resources = performance.getEntriesByType("resource") as PerformanceResourceTiming[];
      const jsResources = resources.filter(r => r.name.endsWith(".js"));
      const totalSize = jsResources.reduce((sum, r) => sum + (r.transferSize ?? 0), 0);
      setMetrics(prev => ({
        ...prev,
        bundleSize: totalSize,
      }));
    };
    // Calculate bundle size after initial load
    setTimeout(calculateBundleSize, 2000);
  }, [location.pathname]);
  return metrics;
}
/**
 * Hook to monitor specific component performance
 */
export function useComponentPerformanceMonitor(componentName: string) {
  const startTime = useRef<number>(0);
  const [renderCount, setRenderCount] = useState(0);
  const [averageRenderTime, setAverageRenderTime] = useState(0);
  useEffect(() => {
    startTime.current = performance.now();
    setRenderCount(prev => prev + 1);
    return () => {
      const renderTime = performance.now() - startTime.current;
      setAverageRenderTime(prev => {
        const newAverage = (prev * (renderCount - 1) + renderTime) / renderCount;
        if (import.meta.env.MODE === "development") {
          if (renderTime > PERFORMANCE_THRESHOLDS.RENDER_WARNING) {
            // Condition handled
          }
          if (renderCount % 10 === 0) {
            // Condition handled
          }
        }
        return newAverage;
      });
    };
  }); // No dependency array - runs on every render to measure component performance
  return {
    renderCount,
    averageRenderTime,
  };
}
/**
 * Hook to monitor bundle loading performance using PerformanceObserver
 * Avoids modifying global window properties
 */
export function useBundleLoadMonitor() {
  const [loadingChunks, setLoadingChunks] = useState<Set<string>>(new Set());
  const [loadedChunks, setLoadedChunks] = useState<Record<string, number>>({});
  const [failedChunks, setFailedChunks] = useState<Set<string>>(new Set());
  useEffect(() => {
    if (typeof window === "undefined") return;
    // Use PerformanceObserver to monitor resource loading without modifying globals
    const observer = new PerformanceObserver(list => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === "resource") {
          const resource = entry as PerformanceResourceTiming;
          // Focus on JavaScript chunks (dynamic imports)
          if (resource.name.includes(".js") && resource.name.includes("chunk")) {
            const chunkName = resource.name.split("/").pop() ?? "unknown";
            const loadTime = resource.responseEnd - resource.requestStart;
            // Determine if this was a successful load or failure
            // Note: PerformanceResourceTiming doesn't have responseStatus, so we check if the resource loaded successfully
            const isSuccessful = resource.responseEnd > 0 && resource.transferSize >= 0;
            if (isSuccessful) {
              // Successful load
              setLoadedChunks(prev => ({ ...prev, [chunkName]: loadTime }));
              setLoadingChunks(prev => {
                const newSet = new Set(prev);
                newSet.delete(chunkName);
                return newSet;
              });
              if (import.meta.env.MODE === "development") {
                // Condition handled
              }
            } else {
              // Failed load
              setFailedChunks(prev => new Set(prev).add(chunkName));
              setLoadingChunks(prev => {
                const newSet = new Set(prev);
                newSet.delete(chunkName);
                return newSet;
              });
              if (import.meta.env.MODE === "development") {
                // Condition handled
              }
            }
          }
        }
      }
    });
    // Monitor resource loading
    try {
      observer.observe({ entryTypes: ["resource"] });
    } catch (error) {
      if (import.meta.env.MODE === "development") {
        // Condition handled
      }
    }
    // Also monitor navigation entries for initial chunk loads
    const navigationObserver = new PerformanceObserver(list => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === "navigation") {
          // Reset state on navigation
          setLoadingChunks(new Set());
          setFailedChunks(new Set());
        }
      }
    });
    try {
      navigationObserver.observe({ entryTypes: ["navigation"] });
    } catch (error) {
      if (import.meta.env.MODE === "development") {
        // Condition handled
      }
    }
    return () => {
      observer.disconnect();
      navigationObserver.disconnect();
    };
  }, []);
  return {
    loadingChunks: Array.from(loadingChunks),
    loadedChunks,
    failedChunks: Array.from(failedChunks),
    isLoading: loadingChunks.size > 0,
  };
}
/**
 * Enhanced performance monitoring hook with real-time metrics collection
 */
export function useEnhancedPerformanceMonitor() {
  const location = useLocation();
  const [enhancedMetrics, setEnhancedMetrics] = useState<EnhancedPerformanceMetrics>({
    routeLoadTime: 0,
    chunkLoadTimes: {},
    memoryUsage: 0,
    renderTime: 0,
    bundleSize: 0,
    cacheHitRate: 0,
    firstContentfulPaint: null,
    largestContentfulPaint: null,
    cumulativeLayoutShift: null,
    firstInputDelay: null,
    timeToInteractive: null,
    navigationTiming: {
      domContentLoaded: 0,
      loadComplete: 0,
      timeToFirstByte: 0,
    },
    alerts: [],
    performanceScore: 100,
  });
  const collectorRef = useRef<PerformanceMetricsCollector | null>(null);
  useEffect(() => {
    // Initialize performance collector
    collectorRef.current = getGlobalPerformanceCollector();
    const updateMetrics = (metrics: Partial<CollectorMetrics>) => {
      setEnhancedMetrics(prev => ({
        ...prev,
        firstContentfulPaint: metrics.firstContentfulPaint ?? prev.firstContentfulPaint,
        largestContentfulPaint: metrics.largestContentfulPaint ?? prev.largestContentfulPaint,
        cumulativeLayoutShift: metrics.cumulativeLayoutShift ?? prev.cumulativeLayoutShift,
        firstInputDelay: metrics.firstInputDelay ?? prev.firstInputDelay,
        timeToInteractive: metrics.timeToInteractive ?? prev.timeToInteractive,
        navigationTiming: metrics.navigationTiming ?? prev.navigationTiming,
        bundleSize: metrics.bundleSize?.totalJS ?? prev.bundleSize,
        chunkLoadTimes: metrics.chunkLoadTimes ?? prev.chunkLoadTimes,
        cacheHitRate: metrics.cacheHitRate ?? prev.cacheHitRate,
        memoryUsage: metrics.memoryUsage?.usedJSHeapSize
          ? metrics.memoryUsage.usedJSHeapSize / 1024 / 1024
          : prev.memoryUsage,
      }));
    };
    const handleAlert = (alert: PerformanceAlert) => {
      setEnhancedMetrics(prev => ({
        ...prev,
        alerts: [...prev.alerts, alert],
      }));
    };
    // Create new collector with callbacks
    const collector = new PerformanceMetricsCollector(DEFAULT_PERFORMANCE_BUDGET, {
      onMetricUpdate: updateMetrics,
      onAlert: handleAlert,
    });
    collectorRef.current = collector;
    return () => {
      collector.destroy();
    };
  }, []);
  // Update route load time on location change
  const routeStartTime = useRef<number>(0);
  useEffect(() => {
    routeStartTime.current = performance.now();
    const handleRouteLoad = () => {
      const loadTime = performance.now() - routeStartTime.current;
      setEnhancedMetrics(prev => ({
        ...prev,
        routeLoadTime: loadTime,
      }));
    };
    if ("requestIdleCallback" in window) {
      requestIdleCallback(handleRouteLoad);
    } else {
      setTimeout(handleRouteLoad, 0);
    }
  }, [location.pathname]);
  // Calculate performance score based on metrics and alerts
  useEffect(() => {
    let score = 100;
    const criticalAlerts = enhancedMetrics.alerts.filter(a => a.type === "critical").length;
    const errorAlerts = enhancedMetrics.alerts.filter(a => a.type === "error").length;
    const warningAlerts = enhancedMetrics.alerts.filter(a => a.type === "warning").length;
    score -= criticalAlerts * 30;
    score -= errorAlerts * 15;
    score -= warningAlerts * 5;
    score = Math.max(0, score);
    setEnhancedMetrics(prev => ({
      ...prev,
      performanceScore: score,
    }));
  }, [enhancedMetrics.alerts]);
  return enhancedMetrics;
}
/**
 * Performance monitoring context provider
 */
export function usePerformanceReport() {
  const metrics = usePerformanceMonitor();
  const bundleMonitor = useBundleLoadMonitor();
  const enhancedMetrics = useEnhancedPerformanceMonitor();
  const generateReport = () => {
    const collector = getGlobalPerformanceCollector();
    const fullReport = collector.generateReport();
    const report = {
      timestamp: new Date().toISOString(),
      route: window.location.pathname,
      metrics: {
        ...metrics,
        ...enhancedMetrics,
      },
      bundleStatus: bundleMonitor,
      recommendations: [...generateRecommendations(metrics), ...fullReport.summary.recommendations],
      performanceScore: enhancedMetrics.performanceScore,
      alerts: enhancedMetrics.alerts,
      fullMetrics: fullReport.metrics,
    };
    if (import.meta.env.MODE === "development") {
      // Condition handled
    }
    return report;
  };
  const clearAlerts = () => {
    const collector = getGlobalPerformanceCollector();
    collector.clearAlerts();
  };
  return {
    metrics,
    enhancedMetrics,
    bundleMonitor,
    generateReport,
    clearAlerts,
  };
}
/**
 * Generate performance recommendations based on metrics
 */
function generateRecommendations(metrics: PerformanceMetrics): string[] {
  const recommendations: string[] = [];
  if (metrics.routeLoadTime > PERFORMANCE_THRESHOLDS.ROUTE_LOAD_WARNING) {
    recommendations.push("Consider implementing route preloading");
    recommendations.push("Optimize component lazy loading");
  }
  if (metrics.memoryUsage > PERFORMANCE_THRESHOLDS.MEMORY_WARNING) {
    recommendations.push("Check for memory leaks");
    recommendations.push("Implement component cleanup in useEffect");
  }
  if (metrics.renderTime > PERFORMANCE_THRESHOLDS.RENDER_WARNING) {
    recommendations.push("Use React.memo for expensive components");
    recommendations.push("Optimize re-renders with useMemo and useCallback");
  }
  if (metrics.cacheHitRate < 80) {
    recommendations.push("Improve chunk caching strategy");
    recommendations.push("Consider using service worker for caching");
  }
  if (metrics.bundleSize > 1024 * 1024) {
    // 1MB
    recommendations.push("Implement more aggressive code splitting");
    recommendations.push("Consider using CDN for vendor libraries");
  }
  return recommendations;
}
export default {
  usePerformanceMonitor,
  useComponentPerformanceMonitor,
  useBundleLoadMonitor,
  usePerformanceReport,
};
