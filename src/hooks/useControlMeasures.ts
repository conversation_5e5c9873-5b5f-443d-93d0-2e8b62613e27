import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import { ControlMeasure } from "@/types";
import { controlMeasuresRepository } from "@/repositories/controlMeasuresRepository";
import { validateEffectiveness } from "@/utils/typeValidation";
import { useAuth } from "@/contexts/auth";
/**
 * Custom hook for managing control measures
 * Uses repository pattern for data access and consistent error handling
 */
export const useControlMeasures = (riskId: string, onControlsUpdated?: () => void) => {
  const { toast } = useToast();
  const { organization } = useAuth();
  const [loading, setLoading] = useState(false);
  const toggleControlImplementation = async (controlId: string, implemented: boolean) => {
    try {
      setLoading(true);
      if (!organization?.id) {
        throw new Error("No organization context found");
      }
      const success = await controlMeasuresRepository.updateControlMeasure(
        controlId,
        { implemented },
        organization.id
      );
      if (!success) {
        throw new Error("Failed to update control measure");
      }
      toast({
        title: implemented ? "Control marked as implemented" : "Control marked as not implemented",
        description: "The control measure has been updated successfully.",
      });
      if (onControlsUpdated) {
        onControlsUpdated();
      }
      return true;
    } catch (error: unknown) {
      toast({
        title: "Error updating control",
        description: error.message ?? "Failed to update the control measure",
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };
  const addControlMeasure = async (description: string, effectiveness?: string) => {
    try {
      setLoading(true);
      if (!organization?.id) {
        throw new Error("No organization context found");
      }
      // Use our type validation utility
      const validatedEffectiveness = validateEffectiveness(effectiveness);
      const newControl = await controlMeasuresRepository.createControlMeasure({
        riskId,
        description,
        effectiveness: validatedEffectiveness,
        implemented: true,
        organizationId: organization.id,
      });
      if (!newControl) {
        throw new Error("Failed to add control measure");
      }
      toast({
        title: "Control added",
        description: "A new control measure has been added.",
      });
      if (onControlsUpdated) {
        onControlsUpdated();
      }
      return newControl;
    } catch (error: unknown) {
      toast({
        title: "Error adding control",
        description: error.message ?? "Failed to add the control measure",
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  };
  const updateControlMeasure = async (controlId: string, data: Partial<ControlMeasure>) => {
    try {
      setLoading(true);
      if (!organization?.id) {
        throw new Error("No organization context found");
      }
      const success = await controlMeasuresRepository.updateControlMeasure(
        controlId,
        data,
        organization.id
      );
      if (!success) {
        throw new Error("Failed to update control measure");
      }
      toast({
        title: "Control updated",
        description: "The control measure has been updated successfully.",
      });
      if (onControlsUpdated) {
        onControlsUpdated();
      }
      return true;
    } catch (error: unknown) {
      toast({
        title: "Error updating control",
        description: error.message ?? "Failed to update the control measure",
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };
  const deleteControlMeasure = async (controlId: string) => {
    try {
      setLoading(true);
      if (!organization?.id) {
        throw new Error("No organization context found");
      }
      const success = await controlMeasuresRepository.deleteControlMeasure(
        controlId,
        organization.id
      );
      if (!success) {
        throw new Error("Failed to delete control measure");
      }
      toast({
        title: "Control deleted",
        description: "The control measure has been removed.",
      });
      if (onControlsUpdated) {
        onControlsUpdated();
      }
      return true;
    } catch (error: unknown) {
      toast({
        title: "Error deleting control",
        description: error.message ?? "Failed to delete the control measure",
        variant: "destructive",
      });
      return false;
    } finally {
      setLoading(false);
    }
  };
  return {
    loading,
    toggleControlImplementation,
    addControlMeasure,
    updateControlMeasure,
    deleteControlMeasure,
  };
};
