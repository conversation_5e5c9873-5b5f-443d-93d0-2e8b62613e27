import * as React from "react";

export interface TouchPoint {
  x: number;
  y: number;
  timestamp: number;
}

export interface SwipeGesture {
  direction: "left" | "right" | "up" | "down";
  distance: number;
  velocity: number;
  duration: number;
}

export interface PinchGesture {
  scale: number;
  center: TouchPoint;
}

export interface TouchGestureOptions {
  onSwipe?: (gesture: SwipeGesture) => void;
  onPinch?: (gesture: PinchGesture) => void;
  onTap?: (point: TouchPoint) => void;
  onDoubleTap?: (point: TouchPoint) => void;
  onLongPress?: (point: TouchPoint) => void;
  swipeThreshold?: number;
  longPressDelay?: number;
  doubleTapDelay?: number;
  preventScroll?: boolean;
}

export function useTouchGestures(options: TouchGestureOptions = {}) {
  const {
    onSwipe,
    onPinch,
    onTap,
    onDoubleTap,
    onLongPress,
    swipeThreshold = 50,
    longPressDelay = 500,
    doubleTapDelay = 300,
    preventScroll = false,
  } = options;

  const touchStartRef = React.useRef<TouchPoint | null>(null);
  const touchEndRef = React.useRef<TouchPoint | null>(null);
  const lastTapRef = React.useRef<TouchPoint | null>(null);
  const longPressTimerRef = React.useRef<NodeJS.Timeout | null>(null);
  const initialDistanceRef = React.useRef<number>(0);
  const isSwipingRef = React.useRef(false);

  const getTouchPoint = (touch: Touch): TouchPoint => ({
    x: touch.clientX,
    y: touch.clientY,
    timestamp: Date.now(),
  });

  const getDistance = (touch1: Touch, touch2: Touch): number => {
    const dx = touch1.clientX - touch2.clientX;
    const dy = touch1.clientY - touch2.clientY;
    return Math.sqrt(dx * dx + dy * dy);
  };

  const handleTouchStart = React.useCallback(
    (e: TouchEvent | React.TouchEvent) => {
      if (preventScroll) {
        e.preventDefault();
      }

      const touch = e.touches[0];
      const touchPoint = touch ? getTouchPoint(touch) : null;
      if (!touchPoint) return;
      touchStartRef.current = touchPoint;
      isSwipingRef.current = false;

      // Handle multi-touch for pinch gestures
      if (e.touches.length === 2) {
        const firstTouch = e.touches[0];
        const secondTouch = e.touches[1];
        if (firstTouch && secondTouch) {
          initialDistanceRef.current = getDistance(firstTouch, secondTouch);
        }
        return;
      }

      // Start long press timer
      if (onLongPress) {
        longPressTimerRef.current = setTimeout(() => {
          if (touchStartRef.current && !isSwipingRef.current) {
            onLongPress(touchStartRef.current);
          }
        }, longPressDelay);
      }
    },
    [onLongPress, longPressDelay, preventScroll]
  );

  const handleTouchMove = React.useCallback(
    (e: TouchEvent | React.TouchEvent) => {
      if (preventScroll) {
        e.preventDefault();
      }

      if (!touchStartRef.current) return;

      const touch = e.touches[0];
      const currentPoint = touch ? getTouchPoint(touch) : null;
      if (!currentPoint) return;

      // Handle pinch gestures
      if (e.touches.length === 2 && onPinch) {
        const firstTouch = e.touches[0];
        const secondTouch = e.touches[1];
        if (firstTouch && secondTouch) {
          const currentDistance = getDistance(firstTouch, secondTouch);
          const scale = currentDistance / initialDistanceRef.current;
          const center = {
            x: (firstTouch.clientX + secondTouch.clientX) / 2,
            y: (firstTouch.clientY + secondTouch.clientY) / 2,
            timestamp: Date.now(),
          };
          onPinch({ scale, center });
        }
        return;
      }

      // Check if movement exceeds swipe threshold
      const deltaX = Math.abs(currentPoint.x - touchStartRef.current.x);
      const deltaY = Math.abs(currentPoint.y - touchStartRef.current.y);

      if (deltaX > swipeThreshold || deltaY > swipeThreshold) {
        isSwipingRef.current = true;
        // Clear long press timer since user is swiping
        if (longPressTimerRef.current) {
          clearTimeout(longPressTimerRef.current);
          longPressTimerRef.current = null;
        }
      }
    },
    [onPinch, swipeThreshold, preventScroll]
  );

  const handleTouchEnd = React.useCallback(
    (e: TouchEvent | React.TouchEvent) => {
      if (preventScroll) {
        e.preventDefault();
      }

      // Clear long press timer
      if (longPressTimerRef.current) {
        clearTimeout(longPressTimerRef.current);
        longPressTimerRef.current = null;
      }

      if (!touchStartRef.current) return;

      const touch = e.changedTouches[0];
      const touchPoint = touch ? getTouchPoint(touch) : null;
      if (!touchPoint) return;
      touchEndRef.current = touchPoint;

      const deltaX = touchPoint.x - touchStartRef.current.x;
      const deltaY = touchPoint.y - touchStartRef.current.y;
      const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
      const duration = touchPoint.timestamp - touchStartRef.current.timestamp;

      // Handle swipe gestures
      if (distance > swipeThreshold && onSwipe) {
        const velocity = distance / duration;
        let direction: SwipeGesture["direction"];

        if (Math.abs(deltaX) > Math.abs(deltaY)) {
          direction = deltaX > 0 ? "right" : "left";
        } else {
          direction = deltaY > 0 ? "down" : "up";
        }

        onSwipe({ direction, distance, velocity, duration });
      }
      // Handle tap gestures
      else if (distance < swipeThreshold && !isSwipingRef.current) {
        // Check for double tap
        if (lastTapRef.current && onDoubleTap) {
          const timeSinceLastTap = touchPoint.timestamp - lastTapRef.current.timestamp;
          const distanceFromLastTap = Math.sqrt(
            Math.pow(touchPoint.x - lastTapRef.current.x, 2) +
              Math.pow(touchPoint.y - lastTapRef.current.y, 2)
          );

          if (timeSinceLastTap < doubleTapDelay && distanceFromLastTap < 50) {
            onDoubleTap(touchPoint);
            lastTapRef.current = null;
            return;
          }
        }

        // Single tap
        if (onTap) {
          onTap(touchPoint);
        }
        lastTapRef.current = touchPoint;
      }

      touchStartRef.current = null;
      touchEndRef.current = null;
      isSwipingRef.current = false;
    },
    [onSwipe, onTap, onDoubleTap, swipeThreshold, doubleTapDelay, preventScroll]
  );

  const gestureHandlers = React.useMemo(
    () => ({
      onTouchStart: handleTouchStart,
      onTouchMove: handleTouchMove,
      onTouchEnd: handleTouchEnd,
    }),
    [handleTouchStart, handleTouchMove, handleTouchEnd]
  );

  return gestureHandlers;
}

// Hook for swipe-to-dismiss functionality
export function useSwipeToDismiss(onDismiss: () => void, threshold = 100) {
  const [offset, setOffset] = React.useState(0);
  const [isDismissing, setIsDismissing] = React.useState(false);

  const gestureHandlers = useTouchGestures({
    onSwipe: gesture => {
      if (gesture.direction === "right" && gesture.distance > threshold) {
        setIsDismissing(true);
        setTimeout(onDismiss, 200); // Allow animation to complete
      }
    },
    preventScroll: true,
  });

  return {
    gestureHandlers,
    offset,
    isDismissing,
    style: {
      transform: `translateX(${offset}px)`,
      transition: isDismissing ? "transform 0.2s ease-out" : "none",
    },
  };
}

// Hook for pull-to-refresh functionality
export function usePullToRefresh(onRefresh: () => Promise<void>, threshold = 80) {
  const [pullDistance, setPullDistance] = React.useState(0);
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  const [canPull, setCanPull] = React.useState(false);

  const gestureHandlers = useTouchGestures({
    onSwipe: async gesture => {
      if (gesture.direction === "down" && gesture.distance > threshold && canPull) {
        setIsRefreshing(true);
        try {
          await onRefresh();
        } finally {
          setIsRefreshing(false);
          setPullDistance(0);
        }
      }
    },
  });

  React.useEffect(() => {
    const checkScrollPosition = () => {
      setCanPull(window.scrollY === 0);
    };

    window.addEventListener("scroll", checkScrollPosition);
    checkScrollPosition();

    return () => window.removeEventListener("scroll", checkScrollPosition);
  }, []);

  return {
    gestureHandlers,
    pullDistance,
    isRefreshing,
    canPull,
  };
}
