import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { cleanupAuthState } from "@/integrations/supabase/client";
import { SignupFormData } from "@/components/auth/signup/SignupForm";
export function useSignupForm() {
  const [searchParams] = useSearchParams();
  const inviteParam = searchParams.get("invite");
  // UI state
  const [error, setError] = useState("");
  const [loading, setLoading] = useState(false);
  const [signupSuccess, setSignupSuccess] = useState(false);
  const [loginLoading, setLoginLoading] = useState(false);
  const { signup, login, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  // Clean up any corrupted auth state on component mount
  useEffect(() => {
    cleanupAuthState();
  }, []);
  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      navigate("/dashboard");
    }
  }, [isAuthenticated, navigate]);
  const handleSignup = async (formData: SignupFormData) => {
    setError("");
    setLoading(true);
    // Basic validation
    if (!formData.name.trim()) {
      setError("Name is required");
      setLoading(false);
      return;
    }
    if (!formData.email.trim()) {
      setError("Email is required");
      setLoading(false);
      return;
    }
    if (formData.password.length < 6) {
      setError("Password must be at least 6 characters");
      setLoading(false);
      return;
    }
    try {
      const response = await signup(
        formData.email,
        formData.password,
        formData.name,
        formData.inviteCode,
        formData.inviteCode ? "use-invite" : "create-organization"
      );
      if (response.success) {
        setSignupSuccess(true);
        toast({
          title: "Account created successfully!",
          description: "Please check your email to verify your account before signing in.",
          variant: "default",
        });
      } else {
        setError(response.message ?? "Failed to create account");
      }
    } catch (err: unknown) {
      setError(err.message ?? "An unexpected error occurred");
    } finally {
      setLoading(false);
    }
  };
  const handleLogin = async (loginEmail: string, loginPassword: string) => {
    setError("");
    if (!loginEmail.trim() || !loginPassword.trim()) {
      setError("Email and password are required");
      return;
    }
    setLoginLoading(true);
    try {
      const result = await login(loginEmail.trim(), loginPassword);
      if (result.success) {
        // Navigation will be handled by the auth state change
      } else {
        setError(
          result.message ??
            "Invalid email or password. Make sure you've confirmed your email address."
        );
      }
    } catch (err: unknown) {
      setError(err?.message ?? "Failed to sign in");
    } finally {
      setLoginLoading(false);
    }
  };
  return {
    inviteParam: inviteParam ?? "",
    error,
    loading,
    signupSuccess,
    loginLoading,
    handleSignup,
    handleLogin,
    setSignupSuccess,
  };
}
