import { useState, useEffect, useCallback } from "react";
import { useParams } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { Risk } from "@/types";
import { riskRepository } from "@/repositories/riskRepository";
import { supabase } from "@/integrations/supabase/client";
import {
  AuthenticationError,
  AuthorizationError,
  BusinessLogicError,
  errorHandler,
  logger,
} from "@/utils/errors";

/**
 * Get the current user's organization ID
 */
const getUserOrganizationId = async (): Promise<string | null> => {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) return null;

  const { data: profile } = await supabase
    .from("profiles")
    .select("organization_id")
    .eq("id", user.id)
    .single();

  return profile?.organization_id ?? null;
};

/**
 * Hook for fetching and managing risk details with organization verification
 * Uses the repository pattern for data access and consistent error handling
 */

export const useRiskDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();
  const [risk, setRisk] = useState<Risk | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchRiskDetails = useCallback(async () => {
    if (!id) {
      const error = new BusinessLogicError(
        "No risk ID provided",
        "missing_risk_id",
        "risk",
        undefined,
        {
          timestamp: new Date(),
          component: "use_risk_details",
          action: "fetch_risk_details",
        }
      );
      setError(error.userMessage);
      setLoading(false);
      return;
    }

    logger.info("Fetching risk details", {
      timestamp: new Date(),
      component: "use_risk_details",
      action: "fetch_risk_details",
      additionalData: { riskId: id },
    });
    setLoading(true);
    setError(null);

    try {
      // First verify organization access
      const organizationId = await getUserOrganizationId();
      if (!organizationId) {
        throw new AuthenticationError("User organization not found", "session", {
          timestamp: new Date(),
          component: "use_risk_details",
          action: "get_organization",
        });
      }

      // Verify the risk belongs to the user's organization
      const { data: riskCheck, error: checkError } = await supabase
        .from("risks")
        .select("organization_id")
        .eq("id", id)
        .single();

      if (checkError) {
        if (checkError.code === "PGRST116") {
          logger.info("Risk not found", {
            timestamp: new Date(),
            component: "use_risk_details",
            action: "risk_access_check",
            additionalData: { riskId: id },
          });
          setRisk(null);
          setError("Risk not found");
          return;
        }
        throw checkError;
      }

      if (!riskCheck || riskCheck.organization_id !== organizationId) {
        logger.warn("Risk access denied - not in user's organization", {
          timestamp: new Date(),
          component: "use_risk_details",
          action: "risk_access_check",
          additionalData: {
            riskId: id,
            userOrgId: organizationId,
            riskOrgId: riskCheck?.organization_id,
          },
        });

        throw new AuthorizationError(
          "Risk not found or access denied",
          "organization_access",
          "user",
          {
            timestamp: new Date(),
            component: "use_risk_details",
            action: "risk_access_check",
            additionalData: { riskId: id },
          }
        );
      }

      // Now fetch the full risk data using repository
      const riskData = await riskRepository.getRiskById(id, organizationId);

      if (!riskData) {
        logger.info("Risk not found after organization check", {
          timestamp: new Date(),
          component: "use_risk_details",
          action: "fetch_risk_data",
          additionalData: { riskId: id, organizationId },
        });
        setRisk(null);
        setError("Risk not found");
        return;
      }

      logger.info("Risk details retrieved successfully", {
        timestamp: new Date(),
        component: "use_risk_details",
        action: "fetch_risk_data",
        additionalData: { riskId: id, riskTitle: riskData.title },
      });
      setRisk(riskData);
    } catch (error: unknown) {
      // Handle through centralized error system
      await errorHandler.handle(error, {
        timestamp: new Date(),
        component: "use_risk_details",
        action: "fetch_risk_details",
        additionalData: { riskId: id },
      });

      const errorMessage =
        error instanceof Error
          ? error.message
          : error && typeof error === "object" && "userMessage" in error
            ? (error as { userMessage: string }).userMessage
            : "Could not load risk details";
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [id, toast]);

  useEffect(() => {
    fetchRiskDetails();
  }, [fetchRiskDetails]);

  return {
    risk,
    loading,
    error,
    fetchRiskDetails,
  };
};
