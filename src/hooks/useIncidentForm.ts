import { useState, useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/auth";
import { useToast } from "@/hooks/use-toast";
import { Risk } from "@/types";
import { errorToast } from "@/components/ui/enhanced-toast";
export const formSchema = z.object({
  title: z.string().min(5, { message: "Title must be at least 5 characters" }),
  description: z.string().min(10, { message: "Description must be at least 10 characters" }),
  severity: z.enum(["Low", "Medium", "High", "Critical"]),
  status: z.enum(["Open", "Investigating", "Resolved", "Closed"]).default("Open"),
  relatedRiskId: z.string().optional(),
});
export type IncidentFormValues = z.infer<typeof formSchema>;
export const useIncidentForm = (initialRiskId?: string) => {
  const { user, organization } = useAuth();
  const { toast } = useToast();
  const [risks, setRisks] = useState<Risk[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingRisks, setLoadingRisks] = useState(false);
  // Form setup with more detailed validation
  const form = useForm<IncidentFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      description: "",
      severity: "Medium",
      status: "Open",
      relatedRiskId: initialRiskId ?? undefined,
    },
    mode: "onChange", // Enable validation on change
  });
  // Fetch risks for the dropdown - filtered by organization
  const fetchRisks = useCallback(async () => {
    if (!user || !organization?.id) return;
    setLoadingRisks(true);
    try {
      const { data, error } = await supabase
        .from("risks")
        .select("id, title")
        .eq("organization_id", organization.id) // Filter by organization
        .order("title", { ascending: true });
      if (error) throw error;
      setRisks(
        data?.map(risk => ({
          id: risk.id,
          title: risk.title,
        })) as Risk[]
      );
    } catch (error: unknown) {
      errorToast({
        title: "Error fetching risks",
        description: error instanceof Error ? error.message : "An unknown error occurred",
      });
    } finally {
      setLoadingRisks(false);
    }
  }, [user, organization?.id]);
  useEffect(() => {
    fetchRisks();
  }, [fetchRisks]);
  return {
    form,
    risks,
    loading,
    setLoading,
    loadingRisks,
    user,
  };
};
