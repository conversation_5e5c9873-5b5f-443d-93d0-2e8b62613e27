import { useState, useEffect } from "react";
import { Incident } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { formatIncidentsData } from "@/utils/riskTransformations";
export function useRelatedIncidents(riskId: string) {
  const [incidents, setIncidents] = useState<Incident[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  useEffect(() => {
    const fetchRelatedIncidents = async () => {
      try {
        setLoading(true);
        setError(null);
        if (!riskId) {
          setIncidents([]);
          return;
        }
        const { data: incidentsData, error: incidentsError } = await supabase
          .from("incidents")
          .select(
            `
            *,
            profiles!incidents_reporter_id_fkey(name),
            risks!incidents_related_risk_id_fkey(title)
          `
          )
          .eq("related_risk_id", riskId)
          .order("created_at", { ascending: false });
        if (incidentsError) {
          throw incidentsError;
        }
        const formattedIncidents = formatIncidentsData(incidentsData ?? []);
        // Add related risk title to each incident
        const incidentsWithRiskTitle = formattedIncidents.map(incident => ({
          ...incident,
          relatedRiskId: riskId,
          relatedRiskTitle: incidentsData?.find(data => data.id === incident.id)?.risks?.title,
        }));
        setIncidents(incidentsWithRiskTitle);
      } catch (err) {
        setError("Failed to load related incidents");
      } finally {
        setLoading(false);
      }
    };
    if (riskId) {
      fetchRelatedIncidents();
    }
  }, [riskId]);
  return { incidents, loading, error };
}
