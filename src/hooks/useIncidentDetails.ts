import { useState, useEffect } from "react";
import { Incident } from "@/types";
import { useParams } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { formatIncidentsData } from "@/utils/riskTransformations";
/**
 * Get the current user's organization ID
 */
const getUserOrganizationId = async (): Promise<string | null> => {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) return null;
  const { data: profile } = await supabase
    .from("profiles")
    .select("organization_id")
    .eq("id", user.id)
    .single();
  return profile?.organization_id ?? null;
};
export function useIncidentDetails() {
  const { id } = useParams();
  const [incident, setIncident] = useState<Incident | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  useEffect(() => {
    const fetchIncident = async () => {
      // Don't fetch if no ID is provided
      if (!id) {
        setError("No incident ID provided");
        setLoading(false);
        return;
      }
      try {
        setLoading(true);
        setError(null);
        // First verify organization access
        const organizationId = await getUserOrganizationId();
        if (!organizationId) {
          throw new Error("User organization not found");
        }
        const { data: incidentData, error: incidentError } = await supabase
          .from("incidents")
          .select(
            `
            *,
            profiles!incidents_reporter_id_fkey(name),
            risks!incidents_related_risk_id_fkey(title)
          `
          )
          .eq("id", id)
          .eq("organization_id", organizationId)
          .single();
        if (incidentError) {
          if (incidentError.code === "PGRST116") {
            setError("Incident not found or access denied");
            return;
          }
          throw incidentError;
        }
        if (incidentData) {
          const formattedIncidents = formatIncidentsData([incidentData]);
          const formattedIncident = formattedIncidents[0];
          // Add related risk title if available
          const incidentWithRiskTitle = {
            ...formattedIncident,
            relatedRiskTitle: incidentData.risks?.title,
          };
          if (incidentWithRiskTitle.id) {
            setIncident(incidentWithRiskTitle);
          }
        } else {
          setError("Incident not found or access denied");
        }
      } catch (err: unknown) {
        setError("Failed to load incident details");
      } finally {
        setLoading(false);
      }
    };
    fetchIncident();
  }, [id]);
  return { incident, loading, error, id };
}
