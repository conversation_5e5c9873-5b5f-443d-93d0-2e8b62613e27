import { describe, it, expect, vi } from 'vitest'
import { renderHook, act } from '@testing-library/react'
import { useRiskCalculations } from '../useRiskCalculations'
import { RiskSeverity } from '@/types'

// Mock the risk calculation service
vi.mock('@/services/risk/riskCalculationService', () => ({
  calculateSeverity: vi.fn((likelihood: number, impact: number) => {
    if (likelihood <= 2 && impact <= 2) return RiskSeverity.LOW
    if (likelihood <= 3 && impact <= 3) return RiskSeverity.MEDIUM
    if (likelihood <= 4 && impact <= 4) return RiskSeverity.HIGH
    return RiskSeverity.CRITICAL
  }),
  getSeverityColor: vi.fn((severity: RiskSeverity) => {
    const colors = {
      [RiskSeverity.LOW]: 'bg-green-100 text-green-800',
      [RiskSeverity.MEDIUM]: 'bg-yellow-100 text-yellow-800',
      [RiskSeverity.HIGH]: 'bg-orange-100 text-orange-800',
      [RiskSeverity.CRITICAL]: 'bg-red-100 text-red-800',
    }
    return colors[severity] || colors[RiskSeverity.LOW]
  }),
  getSeverityPriority: vi.fn((severity: RiskSeverity) => {
    const priorities = {
      [RiskSeverity.LOW]: 2,
      [RiskSeverity.MEDIUM]: 3,
      [RiskSeverity.HIGH]: 4,
      [RiskSeverity.CRITICAL]: 5,
    }
    return priorities[severity] ?? 2
  }),
}))

describe('useRiskCalculations', () => {
  it('should initialize with default values', () => {
    const { result } = renderHook(() => useRiskCalculations())

    expect(result.current.likelihood).toBe(1)
    expect(result.current.impact).toBe(1)
    expect(result.current.severity).toBe(RiskSeverity.LOW)
    expect(result.current.severityColor).toBe('bg-green-100 text-green-800')
    expect(result.current.severityPriority).toBe(2)
  })

  it('should initialize with provided initial values', () => {
    const { result } = renderHook(() => 
      useRiskCalculations({
        initialLikelihood: 3,
        initialImpact: 4,
        autoCalculate: true,
      })
    )

    expect(result.current.likelihood).toBe(3)
    expect(result.current.impact).toBe(4)
    expect(result.current.severity).toBe(RiskSeverity.HIGH)
  })

  it('should update likelihood correctly', () => {
    const { result } = renderHook(() => useRiskCalculations())

    act(() => {
      result.current.setLikelihood(3)
    })

    expect(result.current.likelihood).toBe(3)
    expect(result.current.severity).toBe(RiskSeverity.MEDIUM)
  })

  it('should update impact correctly', () => {
    const { result } = renderHook(() => useRiskCalculations())

    act(() => {
      result.current.setImpact(4)
    })

    expect(result.current.impact).toBe(4)
    expect(result.current.severity).toBe(RiskSeverity.HIGH)
  })

  it('should recalculate severity when both likelihood and impact change', () => {
    const { result } = renderHook(() => useRiskCalculations())

    act(() => {
      result.current.setLikelihood(5)
      result.current.setImpact(5)
    })

    expect(result.current.likelihood).toBe(5)
    expect(result.current.impact).toBe(5)
    expect(result.current.severity).toBe(RiskSeverity.CRITICAL)
    expect(result.current.severityColor).toBe('bg-red-100 text-red-800')
    expect(result.current.severityPriority).toBe(5)
  })

  it('should manually recalculate when recalculate is called', () => {
    const { result } = renderHook(() => 
      useRiskCalculations({
        initialLikelihood: 2,
        initialImpact: 2,
        autoCalculate: false,
      })
    )

    // Initially should be calculated
    expect(result.current.severity).toBe(RiskSeverity.LOW)

    // Change values but don't auto-calculate
    act(() => {
      result.current.setLikelihood(4)
      result.current.setImpact(4)
    })

    // Should still be the old severity since autoCalculate is false
    expect(result.current.likelihood).toBe(4)
    expect(result.current.impact).toBe(4)

    // Manually recalculate
    act(() => {
      result.current.recalculate()
    })

    expect(result.current.severity).toBe(RiskSeverity.HIGH)
  })

  it('should reset to initial values', () => {
    const { result } = renderHook(() => 
      useRiskCalculations({
        initialLikelihood: 2,
        initialImpact: 3,
      })
    )

    // Change values
    act(() => {
      result.current.setLikelihood(5)
      result.current.setImpact(5)
    })

    expect(result.current.likelihood).toBe(5)
    expect(result.current.impact).toBe(5)

    // Reset
    act(() => {
      result.current.reset()
    })

    expect(result.current.likelihood).toBe(2)
    expect(result.current.impact).toBe(3)
    expect(result.current.severity).toBe(RiskSeverity.MEDIUM)
  })

  it('should handle edge cases for likelihood and impact values', () => {
    const { result } = renderHook(() => useRiskCalculations())

    // Test minimum values
    act(() => {
      result.current.setLikelihood(1)
      result.current.setImpact(1)
    })

    expect(result.current.likelihood).toBe(1)
    expect(result.current.impact).toBe(1)
    expect(result.current.severity).toBe(RiskSeverity.LOW)

    // Test maximum values
    act(() => {
      result.current.setLikelihood(5)
      result.current.setImpact(5)
    })

    expect(result.current.likelihood).toBe(5)
    expect(result.current.impact).toBe(5)
    expect(result.current.severity).toBe(RiskSeverity.CRITICAL)
  })

  it('should maintain consistency between severity, color, and priority', () => {
    const { result } = renderHook(() => useRiskCalculations())

    const testCases = [
      { likelihood: 1, impact: 1, expectedSeverity: RiskSeverity.LOW },
      { likelihood: 2, impact: 3, expectedSeverity: RiskSeverity.MEDIUM },
      { likelihood: 4, impact: 3, expectedSeverity: RiskSeverity.HIGH },
      { likelihood: 5, impact: 5, expectedSeverity: RiskSeverity.CRITICAL },
    ]

    testCases.forEach(({ likelihood, impact, expectedSeverity }) => {
      act(() => {
        result.current.setLikelihood(likelihood)
        result.current.setImpact(impact)
      })

      expect(result.current.severity).toBe(expectedSeverity)
      expect(result.current.severityColor).toContain('bg-')
      expect(result.current.severityPriority).toBeGreaterThan(0)
    })
  })

  it('should handle autoCalculate option correctly', () => {
    const { result: autoResult } = renderHook(() => 
      useRiskCalculations({ autoCalculate: true })
    )

    const { result: manualResult } = renderHook(() => 
      useRiskCalculations({ autoCalculate: false })
    )

    // Both should start with the same initial calculation
    expect(autoResult.current.severity).toBe(RiskSeverity.LOW)
    expect(manualResult.current.severity).toBe(RiskSeverity.LOW)

    // Change values
    act(() => {
      autoResult.current.setLikelihood(4)
      manualResult.current.setLikelihood(4)
    })

    // Auto should recalculate immediately
    expect(autoResult.current.severity).toBe(RiskSeverity.HIGH)
    
    // Manual should require explicit recalculation
    expect(manualResult.current.severity).toBe(RiskSeverity.LOW)
    
    act(() => {
      manualResult.current.recalculate()
    })
    
    expect(manualResult.current.severity).toBe(RiskSeverity.HIGH)
  })

  it('should handle rapid successive updates', () => {
    const { result } = renderHook(() => useRiskCalculations())

    act(() => {
      result.current.setLikelihood(2)
      result.current.setImpact(2)
      result.current.setLikelihood(3)
      result.current.setImpact(3)
      result.current.setLikelihood(4)
      result.current.setImpact(4)
    })

    expect(result.current.likelihood).toBe(4)
    expect(result.current.impact).toBe(4)
    expect(result.current.severity).toBe(RiskSeverity.HIGH)
  })
})
