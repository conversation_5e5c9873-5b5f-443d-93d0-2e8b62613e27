import { renderHook } from "@testing-library/react";
import { useIncidents } from "../useIncidents";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider } from "@/contexts/auth/AuthProvider";
import React from "react";
import { vi } from "vitest";

// Mock the optimized incidents hook
vi.mock("../useOptimizedIncidents", () => ({
  useIncidents: vi.fn(() => ({
    incidents: [],
    totalCount: 0,
    hasNextPage: false,
    hasPreviousPage: false,
    isLoading: false,
    isFetching: false,
    isError: false,
    error: null,
    refetch: vi.fn(),
    prefetchNextPage: vi.fn(),
    isStale: false,
    dataUpdatedAt: Date.now(),
    loading: false,
  })),
}));

// Mock auth context
vi.mock("@/contexts/auth", () => ({
  useAuth: () => ({
    user: { id: "test-user" },
    organization: { id: "test-org" },
  }),
}));

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => {
    return React.createElement(
      QueryClientProvider,
      { client: queryClient },
      React.createElement(AuthProvider, null, children)
    );
  };
};

describe("useIncidents", () => {
  it("should initialize with default filters", () => {
    const { result } = renderHook(() => useIncidents(), {
      wrapper: createWrapper(),
    });

    expect(result.current.filters).toEqual({
      search: "",
      status: undefined,
      severity: undefined,
      sortBy: "date",
      sortOrder: "desc",
    });
  });

  it("should provide filter management functions", () => {
    const { result } = renderHook(() => useIncidents(), {
      wrapper: createWrapper(),
    });

    expect(typeof result.current.handleFilterChange).toBe("function");
    expect(typeof result.current.resetFilters).toBe("function");
  });

  it("should not throw when filters are accessed", () => {
    const { result } = renderHook(() => useIncidents(), {
      wrapper: createWrapper(),
    });

    // This should not throw an error
    expect(() => {
      const { search, status, severity } = result.current.filters;
      // Variables are accessed to test they don't throw, but not used further
      void search;
      void status;
      void severity;
    }).not.toThrow();
  });
});
