# Hook Testing Guidelines

## Overview

This directory contains tests for React hooks used throughout the application. The tests are designed to ensure accuracy and prevent false positives by using real implementations instead of simplified mock logic.

## Testing Approach for useRiskCalculations

### Problem Solved

The `useRiskCalculations` hook tests were created to address a specific issue where mock implementations of `riskCalculationService` used simplified logic that didn't match the real service, risking false positives in tests.

### Solution

Instead of using hardcoded mock logic, the tests:

1. **Import actual calculation functions** from the real `riskCalculationService` module
2. **Use real implementations** wrapped with `vi.fn()` for call tracking
3. **Preserve exact behavior** of the production code in tests

### Mock Implementation Pattern

```typescript
vi.mock('@/services/risk/riskCalculationService', async () => {
  // Import the actual module to get the real implementations
  const actual = await vi.importActual('@/services/risk/riskCalculationService');
  
  return {
    // Use the actual calculation functions to ensure test accuracy
    calculateSeverity: vi.fn().mockImplementation(actual.calculateSeverity),
    getSeverityColor: vi.fn().mockImplementation(actual.getSeverityColor),
    getSeverityPriority: vi.fn().mockImplementation(actual.getSeverityPriority),
  };
});
```

### Benefits

1. **Accuracy**: Tests use the exact same logic as production code
2. **Reliability**: No risk of false positives from simplified mock logic
3. **Maintainability**: When the service logic changes, tests automatically reflect the changes
4. **Call Tracking**: Still able to verify function calls and parameters using `vi.fn()`

### Test Coverage

The tests cover:

- ✅ Initialization with default and custom values
- ✅ Auto-calculation behavior
- ✅ Manual recalculation
- ✅ Reset functionality
- ✅ Service function call verification
- ✅ Edge cases and boundary values
- ✅ Complete severity matrix validation

### Running Tests

```bash
# Run all hook tests
npm test src/hooks

# Run specific useRiskCalculations tests
npm test src/hooks/__tests__/useRiskCalculations.test.ts
npm test src/hooks/risk/__tests__/useRiskCalculations.test.ts

# Run tests with UI
npm run test:ui
```

## Best Practices

1. **Use real implementations** when testing business logic
2. **Mock only external dependencies** (APIs, databases, etc.)
3. **Verify both behavior and function calls** in tests
4. **Test edge cases** and boundary conditions
5. **Document the testing approach** for complex scenarios

## File Structure

```
src/hooks/
├── __tests__/
│   ├── README.md                    # This file
│   └── useRiskCalculations.test.ts  # Tests for root-level hook
└── risk/
    └── __tests__/
        └── useRiskCalculations.test.ts  # Tests for risk-specific hook
```
