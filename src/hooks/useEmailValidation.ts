
import { useState } from "react";
import { validateAndParseEmails } from "@/services/user/emailValidationService";

export interface UseEmailValidationResult {
  emailText: string;
  validEmails: string[];
  invalidEmails: string[];
  error: string;
  setEmailText: (text: string) => void;
  validateEmails: () => boolean;
  resetValidation: () => void;
}

export function useEmailValidation(): UseEmailValidationResult {
  const [emailText, setEmailText] = useState("");
  const [validEmails, setValidEmails] = useState<string[]>([]);
  const [invalidEmails, setInvalidEmails] = useState<string[]>([]);
  const [error, setError] = useState("");

  const validateEmails = (): boolean => {
    setError("");
    const { valid, invalid } = validateAndParseEmails(emailText);
    
    if (valid.length === 0) {
      setError("Please enter at least one valid email address");
      return false;
    }
    
    setValidEmails(valid);
    setInvalidEmails(invalid);
    
    if (invalid.length > 0) {
      setError(`Found ${invalid.length} invalid email address(es). Only valid emails will be processed.`);
    }
    
    return true;
  };

  const resetValidation = () => {
    setEmailText("");
    setValidEmails([]);
    setInvalidEmails([]);
    setError("");
  };

  return {
    emailText,
    validEmails,
    invalidEmails,
    error,
    setEmailText,
    validateEmails,
    resetValidation
  };
}
