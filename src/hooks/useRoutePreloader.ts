import { useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";
import { preloadRoute } from "@/utils/lazy-routes";
import { intelligentPreloader } from "@/utils/intelligent-preloader";
/**
 * Fallback preloading strategies for when intelligent preloader has no data
 */
const FALLBACK_PRELOAD_STRATEGIES = {
  // Routes to preload when user lands on index/login
  onLanding: [() => import("@/pages/Dashboard"), () => import("@/pages/Login")],
  // Routes to preload when user is on dashboard
  onDashboard: [
    () => import("@/pages/RiskRegister"),
    () => import("@/pages/Incidents"),
    () => import("@/pages/Reports"),
    () => import("@/pages/Profile"),
  ],
  // Routes to preload when user is in risk management
  onRiskPages: [
    () => import("@/pages/RiskCreate"),
    () => import("@/pages/RiskDetails"),
    () => import("@/pages/RiskTemplates"),
    () => import("@/pages/Dashboard"),
  ],
  // Routes to preload when user is in incident management
  onIncidentPages: [
    () => import("@/pages/IncidentCreate"),
    () => import("@/pages/IncidentDetails"),
    () => import("@/pages/IncidentEdit"),
    () => import("@/pages/Dashboard"),
  ],
  // Routes to preload when user is in administration
  onAdminPages: [
    () => import("@/pages/OrganizationManagement"),
    () => import("@/pages/OrganizationPage"),
    () => import("@/pages/Policies"),
  ],
  // Always preload these critical routes
  critical: [() => import("@/pages/NotFound"), () => import("@/pages/Profile")],
};
/**
 * Determines which routes to preload based on current location (fallback strategy)
 */
function getFallbackPreloadStrategy(pathname: string): (() => Promise<unknown>)[] {
  const routes = [...FALLBACK_PRELOAD_STRATEGIES.critical];
  if (pathname === "/" || pathname === "/login" || pathname === "/signup") {
    routes.push(...FALLBACK_PRELOAD_STRATEGIES.onLanding);
  } else if (pathname === "/dashboard") {
    routes.push(...FALLBACK_PRELOAD_STRATEGIES.onDashboard);
  } else if (pathname.startsWith("/risks")) {
    routes.push(...FALLBACK_PRELOAD_STRATEGIES.onRiskPages);
  } else if (pathname.startsWith("/incidents")) {
    routes.push(...FALLBACK_PRELOAD_STRATEGIES.onIncidentPages);
  } else if (
    pathname.startsWith("/administration") ||
    pathname.startsWith("/policy-admin") ||
    pathname.startsWith("/user-management") ||
    pathname.startsWith("/organization")
  ) {
    routes.push(...FALLBACK_PRELOAD_STRATEGIES.onAdminPages);
  }
  return routes;
}
/**
 * Hook to intelligently preload routes based on user behavior and current location
 * Enhanced with machine learning-like behavior analysis
 */
export function useRoutePreloader() {
  const location = useLocation();
  const previousPathname = useRef<string>("");
  useEffect(() => {
    const currentPathname = location.pathname;
    // Track navigation for intelligent preloading
    intelligentPreloader.trackNavigation(currentPathname);
    // Delay preloading to not interfere with current page load
    const timeoutId = setTimeout(() => {
      // Get analytics to determine if we have enough data for intelligent preloading
      const analytics = intelligentPreloader.getAnalytics();
      if (analytics.totalNavigations >= 5) {
        // Use intelligent preloading if we have enough data
        if (import.meta.env.MODE === "development") {
          // Condition handled
        }
        // Intelligent preloader handles preloading automatically
      } else {
        // Fall back to static preloading strategies
        if (import.meta.env.MODE === "development") {
          // Condition handled
        }
        const routesToPreload = getFallbackPreloadStrategy(currentPathname);
        // Preload routes with staggered timing to avoid overwhelming the network
        routesToPreload.forEach((importFn, index) => {
          setTimeout(() => {
            preloadRoute(importFn);
          }, index * 200); // 200ms between each preload
        });
      }
    }, 1000); // Wait 1 second after route change
    // Update previous pathname for next navigation
    previousPathname.current = currentPathname;
    return () => clearTimeout(timeoutId);
  }, [location.pathname]);
}
/**
 * Hook to preload specific routes on user interaction
 * Enhanced with intelligent preloading integration
 */
export function useInteractionPreloader() {
  const preloadOnHover = (routeImport: () => Promise<unknown>, route?: string) => {
    return {
      onMouseEnter: () => {
        preloadRoute(routeImport);
        if (route) {
          intelligentPreloader.preloadOnInteraction(route);
        }
      },
      onFocus: () => {
        preloadRoute(routeImport);
        if (route) {
          intelligentPreloader.preloadOnInteraction(route);
        }
      },
    };
  };
  const preloadOnClick = (routeImport: () => Promise<unknown>, route?: string) => {
    return {
      onClick: () => {
        preloadRoute(routeImport);
        if (route) {
          intelligentPreloader.preloadOnInteraction(route);
        }
      },
    };
  };
  const preloadOnVisibility = (routeImport: () => Promise<unknown>, route?: string) => {
    return {
      onIntersect: () => {
        preloadRoute(routeImport);
        if (route) {
          intelligentPreloader.preloadOnVisibility(route);
        }
      },
    };
  };
  return {
    preloadOnHover,
    preloadOnClick,
    preloadOnVisibility,
  };
}
/**
 * Preload routes that are commonly accessed together
 */
export function preloadRelatedRoutes(currentRoute: string) {
  const relatedRoutes: Record<string, (() => Promise<unknown>)[]> = {
    "/risks": [() => import("@/pages/RiskCreate"), () => import("@/pages/RiskTemplates")],
    "/risks/create": [() => import("@/pages/RiskRegister"), () => import("@/pages/RiskTemplates")],
    "/incidents": [() => import("@/pages/IncidentCreate")],
    "/incidents/new": [() => import("@/pages/Incidents")],
    "/administration": [
      () => import("@/pages/OrganizationManagement"),
      () => import("@/pages/Policies"),
    ],
  };
  const routes = relatedRoutes[currentRoute] ?? [];
  routes.forEach(importFn => preloadRoute(importFn));
}
export default useRoutePreloader;
