import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { RiskSeverity, RiskStatus } from "@/types";
import { useToast } from "@/components/ui/use-toast";
interface RiskNavigationResult {
  prevRiskId: string | null;
  nextRiskId: string | null;
  loading: boolean;
  error: string | null;
}
/**
 * Hook to get previous and next risk IDs based on current filters and sorting
 */
export const useRiskNavigation = (currentRiskId: string) => {
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  const [navigation, setNavigation] = useState<RiskNavigationResult>({
    prevRiskId: null,
    nextRiskId: null,
    loading: true,
    error: null,
  });
  // Get filter values from URL search params
  const severityFilter = searchParams.get("severity") as RiskSeverity | undefined;
  const statusFilter = searchParams.get("status") as RiskStatus | undefined;
  const categoryFilter = searchParams.get("category");
  const ownerFilter = searchParams.get("owner");
  const sortField = searchParams.get("sortField") ?? "title";
  const sortOrder = (searchParams.get("sortOrder") as "asc" | "desc") || "desc";
  useEffect(() => {
    const fetchRiskNavigation = async () => {
      if (!currentRiskId) return;
      try {
        setNavigation(prev => ({ ...prev, loading: true, error: null }));
        // Map frontend field names to database column names
        const fieldMapping: Record<string, string> = {
          title: "title",
          category: "category_id",
          owner: "owner_id",
          severity: "severity",
          status: "status",
          dueDate: "due_date",
          created_at: "created_at",
        };
        const dbSortField = fieldMapping[sortField] ?? "title";
        const dbSortOrder = sortOrder === "asc" ? "asc" : "desc";
        // First, get the current risk to compare with
        const { data: currentRisk, error: currentError } = await supabase
          .from("risks")
          .select(dbSortField)
          .eq("id", currentRiskId)
          .maybeSingle();
        if (currentError) {
          setNavigation({
            prevRiskId: null,
            nextRiskId: null,
            loading: false,
            error: `Failed to fetch current risk: ${currentError.message}`,
          });
          return;
        }
        if (!currentRisk) {
          setNavigation({
            prevRiskId: null,
            nextRiskId: null,
            loading: false,
            error: "Current risk not found",
          });
          return;
        }
        // Build base query
        const buildFilteredQuery = () => {
          let query = supabase.from("risks").select("id");
          // Apply filters if they exist
          if (severityFilter) {
            query = query.eq("severity", severityFilter);
          }
          if (statusFilter) {
            query = query.eq("status", statusFilter);
          }
          if (categoryFilter) {
            query = query.eq("category_id", categoryFilter);
          }
          if (ownerFilter) {
            query = query.eq("owner_id", ownerFilter);
          }
          return query;
        };
        const currentValue = currentRisk[dbSortField];
        // Get previous risk ID based on sorting
        let prevResults = null;
        let nextResults = null;
        try {
          let prevQuery = buildFilteredQuery();
          if (dbSortOrder === "asc") {
            // If ascending, "previous" means less than current value
            if (currentValue !== null && currentValue !== undefined) {
              prevQuery = prevQuery.lt(dbSortField, currentValue);
            }
            prevQuery = prevQuery.order(dbSortField, { ascending: false });
          } else {
            // If descending, "previous" means greater than current value
            if (currentValue !== null && currentValue !== undefined) {
              prevQuery = prevQuery.gt(dbSortField, currentValue);
            }
            prevQuery = prevQuery.order(dbSortField, { ascending: true });
          }
          const { data: prevRisk, error: prevError } = await prevQuery.limit(1);
          if (prevError) {
            // Condition handled
          } else {
            prevResults = prevRisk;
          }
        } catch (err) {
          // Error caught and handled
        }
        try {
          // Get next risk ID based on sorting
          let nextQuery = buildFilteredQuery();
          if (dbSortOrder === "asc") {
            // If ascending, "next" means greater than current value
            if (currentValue !== null && currentValue !== undefined) {
              nextQuery = nextQuery.gt(dbSortField, currentValue);
            }
            nextQuery = nextQuery.order(dbSortField, { ascending: true });
          } else {
            // If descending, "next" means less than current value
            if (currentValue !== null && currentValue !== undefined) {
              nextQuery = nextQuery.lt(dbSortField, currentValue);
            }
            nextQuery = nextQuery.order(dbSortField, { ascending: false });
          }
          const { data: nextRisk, error: nextError } = await nextQuery.limit(1);
          if (nextError) {
            // Condition handled
          } else {
            nextResults = nextRisk;
          }
        } catch (err) {
          // Error caught and handled
        }
        setNavigation({
          prevRiskId: prevResults?.length > 0 ? prevResults[0].id : null,
          nextRiskId: nextResults?.length > 0 ? nextResults[0].id : null,
          loading: false,
          error: null,
        });
      } catch (error: unknown) {
        setNavigation({
          prevRiskId: null,
          nextRiskId: null,
          loading: false,
          error: error.message ?? "Unknown error occurred",
        });
        toast({
          title: "Navigation Error",
          description: "Failed to load risk navigation. Please try refreshing the page.",
          variant: "destructive",
        });
      }
    };
    fetchRiskNavigation();
  }, [
    currentRiskId,
    sortField,
    sortOrder,
    severityFilter,
    statusFilter,
    categoryFilter,
    ownerFilter,
  ]);
  return navigation;
};
