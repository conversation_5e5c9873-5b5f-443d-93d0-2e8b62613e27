import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { z } from "zod";
import { RiskFormSchema } from "@/components/risk/schema/riskFormSchema";
import { calculateSeverity } from "@/services/risk";
import { RiskTemplate } from "@/types";
import { useAuth } from "@/contexts/auth";
import {
  AuthenticationError,
  BusinessLogicError,
  SystemError,
  errorHandler,
  logger,
} from "@/utils/errors";
type FormValues = z.infer<typeof RiskFormSchema>;
interface UseRiskSubmitProps {
  onSuccess?: () => void;
  categoryMapping: Record<string, string>;
  template: RiskTemplate | null;
}
export const useRiskSubmit = ({ onSuccess, categoryMapping, template }: UseRiskSubmitProps) => {
  const { toast } = useToast();
  const { organization } = useAuth();
  const [submitting, setSubmitting] = useState(false);
  const [uncategorizedId, setUncategorizedId] = useState<string | null>(null);
  // Fetch the Uncategorized category ID on initialization
  useEffect(() => {
    const fetchUncategorizedId = async () => {
      try {
        const { data, error } = await supabase
          .from("risk_categories")
          .select("id")
          .eq("name", "Uncategorized")
          .maybeSingle();
        if (error) {
          logger.error("Error fetching Uncategorized category", error, {
            timestamp: new Date(),
            component: "use_risk_submit",
            action: "fetch_uncategorized_category",
          });
          return;
        }
        if (data) {
          setUncategorizedId(data.id);
          logger.debug("Found Uncategorized category ID", {
            timestamp: new Date(),
            component: "use_risk_submit",
            action: "fetch_uncategorized_category",
            additionalData: { categoryId: data.id },
          });
        }
      } catch (err) {
        logger.error("Failed to fetch Uncategorized category ID", err as Error, {
          timestamp: new Date(),
          component: "use_risk_submit",
          action: "fetch_uncategorized_category",
        });
      }
    };
    fetchUncategorizedId();
  }, []);
  const onSubmit = async (values: FormValues) => {
    try {
      setSubmitting(true);
      logger.info("Submitting risk form", {
        timestamp: new Date(),
        component: "use_risk_submit",
        action: "form_submission",
        additionalData: {
          title: values.title,
          hasTemplate: !!template?.id,
        },
      });
      // Check auth status and organization before proceeding
      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();
      if (sessionError || !session) {
        throw new AuthenticationError("Your session has expired. Please log in again.", "session", {
          timestamp: new Date(),
          component: "use_risk_submit",
          action: "session_check",
        });
      }
      if (!organization?.id) {
        throw new BusinessLogicError(
          "No organization context found. Please ensure you're part of an organization.",
          "organization_required",
          "risk",
          undefined,
          {
            timestamp: new Date(),
            component: "use_risk_submit",
            action: "organization_check",
          }
        );
      }
      const currentUser = session.user;
      logger.debug("User and organization validated", {
        timestamp: new Date(),
        component: "use_risk_submit",
        action: "validation",
        userId: currentUser.id,
        organizationId: organization.id,
      });
      // Calculate severities
      const inherentSeverity = calculateSeverity(values.inherentLikelihood, values.inherentImpact);
      const residualSeverity = calculateSeverity(values.likelihood, values.impact);
      // Get the category ID - use the mapping, the direct categoryId, or the uncategorizedId as fallback
      let categoryId = null;
      if (values.category && categoryMapping[values.category]) {
        categoryId = categoryMapping[values.category];
      } else if (values.categoryId) {
        categoryId = values.categoryId;
      } else if (uncategorizedId) {
        categoryId = uncategorizedId;
      }
      if (!categoryId) {
        logger.warn("No category ID available for risk", {
          timestamp: new Date(),
          component: "use_risk_submit",
          action: "category_validation",
          additionalData: {
            providedCategory: values.category,
            providedCategoryId: values.categoryId,
            uncategorizedId,
          },
        });
      }
      // Check for legacy currentControls to migrate
      let migratedCurrentControls = values.currentControls;
      if (
        (values.controlMeasures && values.controlMeasures.length > 0) ||
        !values.currentControls
      ) {
        migratedCurrentControls = "";
      }
      // Create the risk record with organization_id
      const { data: riskData, error: riskError } = await supabase
        .from("risks")
        .insert({
          title: values.title,
          description: values.description,
          category_id: categoryId ?? null,
          organization_id: organization.id, // Add organization ID
          // Inherent risk properties
          inherent_likelihood: values.inherentLikelihood,
          inherent_impact: values.inherentImpact,
          inherent_severity: inherentSeverity,
          // Residual risk properties
          likelihood: values.likelihood,
          impact: values.impact,
          severity: residualSeverity,
          status: values.status,
          current_controls: migratedCurrentControls ?? null,
          mitigation_approach: values.mitigationApproach ?? null,
          due_date: values.dueDate ? values.dueDate.toISOString() : null,
          created_by: currentUser.id,
          owner_id: values.ownerId || currentUser.id,
          template_id: template?.id ?? null,
        })
        .select();
      if (riskError) {
        throw new SystemError(
          `Error creating risk: ${riskError.message}`,
          "database",
          riskError.code,
          {
            timestamp: new Date(),
            component: "use_risk_submit",
            action: "risk_creation",
            organizationId: organization.id,
          }
        );
      }
      logger.info("Risk created successfully", {
        timestamp: new Date(),
        component: "use_risk_submit",
        action: "risk_creation",
        additionalData: { riskId: riskData?.[0]?.id },
      });
      if (!riskData || riskData.length === 0) {
        throw new SystemError("Failed to create risk record", "database", "NO_DATA_RETURNED", {
          timestamp: new Date(),
          component: "use_risk_submit",
          action: "risk_creation",
        });
      }
      const riskId = riskData[0]?.id;
      if (!riskId) {
        throw new SystemError("Risk ID not returned after creation", "database", "NO_ID_RETURNED", {
          timestamp: new Date(),
          component: "use_risk_submit",
          action: "risk_creation",
        });
      }
      // If we have control measures, add them with organization_id
      if (values.controlMeasures && values.controlMeasures.length > 0) {
        const controls = values.controlMeasures.map(control => ({
          risk_id: riskId,
          organization_id: organization.id,
          description: control.description,
          effectiveness: control.effectiveness ?? "Medium",
          implemented: control.implemented ?? true,
        }));
        const { error: controlsError } = await supabase.from("control_measures").insert(controls);
        if (controlsError) {
          logger.error("Error adding control measures", controlsError, {
            timestamp: new Date(),
            component: "use_risk_submit",
            action: "control_measures_creation",
            additionalData: { riskId, controlCount: controls.length },
          });
          toast({
            title: "Note",
            description: "Risk was created but there was an issue saving some control measures.",
            variant: "default",
          });
        }
      }
      // If we have mitigation actions, add them with organization_id
      if (values.mitigationActions && values.mitigationActions.length > 0) {
        const actions = values.mitigationActions.map(action => ({
          risk_id: riskId,
          organization_id: organization.id,
          description: action.description,
          completed: action.completed ?? false,
        }));
        const { error: actionsError } = await supabase.from("mitigation_actions").insert(actions);
        if (actionsError) {
          logger.error("Error adding mitigation actions", actionsError, {
            timestamp: new Date(),
            component: "use_risk_submit",
            action: "mitigation_actions_creation",
            additionalData: { riskId, actionCount: actions.length },
          });
          toast({
            title: "Note",
            description: "Risk was created but there was an issue saving some mitigation actions.",
            variant: "default",
          });
        }
      }
      toast({
        title: "Risk Created",
        description: "The risk has been successfully added to the register.",
        variant: "default",
      });
      logger.info("Risk form submission completed successfully", {
        timestamp: new Date(),
        component: "use_risk_submit",
        action: "form_submission_complete",
        additionalData: { riskId },
      });
      if (onSuccess) {
        onSuccess();
      }
      return true;
    } catch (error: unknown) {
      // Handle through centralized error system
      const errorContext = {
        timestamp: new Date(),
        component: "use_risk_submit",
        action: "form_submission_error",
        ...(organization?.id && { organizationId: organization.id }),
      };
      await errorHandler.handle(error, errorContext);
      return false;
    } finally {
      setSubmitting(false);
    }
  };
  return {
    submitting,
    onSubmit,
  };
};
