import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
export const useCategoryMapping = () => {
  const [categoryMapping, setCategoryMapping] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [uncategorizedId, setUncategorizedId] = useState<string | null>(null);
  // Fetch categories to get mapping between category names and IDs
  useEffect(() => {
    const fetchCategories = async () => {
      setLoading(true);
      try {
        const { data, error } = await supabase.from("risk_categories").select("id, name");
        if (error) {
          return;
        }
        const mapping: Record<string, string> = {};
        if (data) {
          data.forEach(category => {
            // Map from category name to category ID
            mapping[category.name] = category.id;
            // Store the Uncategorized category ID for fallback
            if (category.name === "Uncategorized") {
              setUncategorizedId(category.id);
            }
          });
          setCategoryMapping(mapping);
        }
      } catch (error) {
        // Error caught and handled
      } finally {
        setLoading(false);
      }
    };
    fetchCategories();
  }, []);
  return {
    categoryMapping,
    uncategorizedId,
    loading,
  };
};
