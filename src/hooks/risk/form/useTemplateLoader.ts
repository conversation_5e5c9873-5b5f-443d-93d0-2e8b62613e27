import { useEffect, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { RiskTemplate } from "@/types";
import { getTemplate } from "@/lib/api/templateService";
import { z } from "zod";
import { RiskFormSchema } from "@/components/risk/schema/riskFormSchema";
type FormValues = z.infer<typeof RiskFormSchema>;
interface UseTemplateLoaderProps {
  form: UseFormReturn<FormValues>;
  templateId?: string;
  updateInherentSeverity: (likelihood: number, impact: number) => void;
  updateResidualSeverity: (likelihood: number, impact: number) => void;
}
export const useTemplateLoader = ({
  form,
  templateId,
  updateInherentSeverity,
  updateResidualSeverity,
}: UseTemplateLoaderProps) => {
  const [template, setTemplate] = useState<RiskTemplate | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  useEffect(() => {
    if (!templateId) return;
    const loadTemplate = async () => {
      try {
        setLoading(true);
        setError(null);
        const templateData = await getTemplate(templateId);
        if (!templateData) {
          throw new Error("Template not found");
        }
        setTemplate(templateData);
        // Apply template values to form
        form.setValue("description", templateData.description);
        form.setValue("category", templateData.category ?? "");
        form.setValue("categoryId", templateData.categoryId ?? "");
        form.setValue("inherentLikelihood", templateData.defaultLikelihood);
        form.setValue("inherentImpact", templateData.defaultImpact);
        // Set residual risk to same as inherent initially
        form.setValue("likelihood", templateData.defaultLikelihood);
        form.setValue("impact", templateData.defaultImpact);
        form.setValue("mitigationApproach", templateData.suggestedMitigationPlan ?? "");
        // Update calculated severities
        updateInherentSeverity(templateData.defaultLikelihood, templateData.defaultImpact);
        updateResidualSeverity(templateData.defaultLikelihood, templateData.defaultImpact);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to load template";
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };
    loadTemplate();
  }, [templateId, form, updateInherentSeverity, updateResidualSeverity]);
  return {
    template,
    loading,
    error,
  };
};
