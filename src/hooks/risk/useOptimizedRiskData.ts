import { useQuery, useQueryClient, keepPreviousData } from "@tanstack/react-query";
import { useAuth } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";
import { Risk, RiskSeverity, RiskStatus } from "@/types";
import { formatRisksData } from "@/utils/riskTransformations";
import { queryKeys } from "@/lib/query-client";
import { useMemo } from "react";

export interface RiskDataParams {
  severities?: RiskSeverity[];
  statuses?: RiskStatus[];
  categoryIds?: string[];
  ownerIds?: string[];
  sortField?: string;
  sortOrder?: "asc" | "desc";
  // Pagination support for large datasets
  page?: number;
  pageSize?: number;
  // Search functionality
  searchTerm?: string;
}

export interface RiskDataResponse {
  risks: Risk[];
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

/**
 * Optimized risk data fetching with TanStack Query
 * Includes caching, background updates, and pagination for large datasets
 */

export const useRiskData = (params: RiskDataParams = {}) => {
  const { organization, user } = useAuth();
  const queryClient = useQueryClient();

  // Memoized query key to prevent unnecessary re-renders
  const queryKey = useMemo(() => {
    if (!organization?.id) return null;

    const baseKey = queryKeys.organization.risks(organization.id);
    const paramsKey = {
      severities: params.severities?.sort(),
      statuses: params.statuses?.sort(),
      categoryIds: params.categoryIds?.sort(),
      ownerIds: params.ownerIds?.sort(),
      sortField: params.sortField,
      sortOrder: params.sortOrder,
      page: params.page ?? 1,
      pageSize: params.pageSize ?? 50,
      searchTerm: params.searchTerm?.toLowerCase().trim(),
    };

    return [...baseKey, paramsKey];
  }, [
    organization?.id,
    params.severities,
    params.statuses,
    params.categoryIds,
    params.ownerIds,
    params.sortField,
    params.sortOrder,
    params.page,
    params.pageSize,
    params.searchTerm,
  ]);

  // Query function with optimized database queries
  const fetchRisks = async (): Promise<RiskDataResponse> => {
    if (!organization?.id) {
      throw new Error("Organization not available");
    }

    const page = params.page ?? 1;
    const pageSize = params.pageSize ?? 50;
    const offset = (page - 1) * pageSize;

    // Build optimized query with pagination
    let query = supabase
      .from("risks")
      .select(
        `
        *,
        profiles!risks_owner_id_fkey(name),
        categories:risk_categories(name)
      `,
        { count: "exact" }
      )
      .eq("organization_id", organization.id);

    // Apply filters efficiently
    if (params.severities?.length) {
      query = query.in("severity", params.severities);
    }

    if (params.statuses?.length) {
      query = query.in("status", params.statuses);
    }

    if (params.categoryIds?.length) {
      query = query.in("category_id", params.categoryIds);
    }

    if (params.ownerIds?.length) {
      query = query.in("owner_id", params.ownerIds);
    }

    // Search functionality
    if (params.searchTerm?.trim()) {
      const searchTerm = params.searchTerm.trim();
      query = query.or(`title.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
    }

    // Apply sorting
    const sortField = params.sortField ?? "created_at";
    const sortOrder = params.sortOrder ?? "desc";
    query = query.order(sortField, { ascending: sortOrder === "asc" });

    // Apply pagination
    query = query.range(offset, offset + pageSize - 1);

    const { data, error, count } = await query;

    if (error) {
      throw new Error(`Failed to fetch risks: ${error.message}`);
    }

    const risks = data ? formatRisksData(data) : [];
    const totalCount = count ?? 0;

    return {
      risks,
      totalCount,
      hasNextPage: offset + pageSize < totalCount,
      hasPreviousPage: page > 1,
    };
  };

  // Use TanStack Query with optimized configuration
  const query = useQuery({
    queryKey: queryKey || [],
    queryFn: fetchRisks,
    enabled: !!organization?.id && !!user?.id && !!queryKey,

    // Cache configuration for large datasets
    staleTime: 3 * 60 * 1000, // 3 minutes - risks don't change frequently
    gcTime: 10 * 60 * 1000, // 10 minutes in cache

    // Keep previous data while fetching new data (great for pagination)
    placeholderData: keepPreviousData,

    // Background refetch for critical data
    refetchOnWindowFocus: true,
    refetchOnReconnect: true,

    // Error retry configuration
    retry: (failureCount, error: Error | unknown) => {
      // Don't retry on authentication errors
      if (error instanceof Error && (error.message?.includes("auth") || (error as any).status === 401)) {
        return false;
      }
      return failureCount < 2;
    },

    // Structural sharing for performance
    structuralSharing: true,
  });

  // Prefetch next page for better UX
  const prefetchNextPage = () => {
    if (!query.data?.hasNextPage || !queryKey || !organization?.id) return;

    const nextPageParams = { ...params, page: (params.page ?? 1) + 1 };
    const baseKey = queryKeys.organization.risks(organization.id);
    const nextPageKey = [...baseKey, nextPageParams];

    queryClient.prefetchQuery({
      queryKey: nextPageKey,
      queryFn: () => fetchRisks(),
      staleTime: 2 * 60 * 1000, // 2 minutes for prefetched data
    });
  };

  return {
    // Data
    risks: query.data?.risks ?? [],
    totalCount: query.data?.totalCount ?? 0,
    hasNextPage: query.data?.hasNextPage ?? false,
    hasPreviousPage: query.data?.hasPreviousPage ?? false,

    // Loading states
    isLoading: query.isLoading,
    isFetching: query.isFetching,
    isError: query.isError,
    error: query.error,

    // Actions
    refetch: query.refetch,
    prefetchNextPage,

    // Query status
    isStale: query.isStale,
    dataUpdatedAt: query.dataUpdatedAt,
  };
};
