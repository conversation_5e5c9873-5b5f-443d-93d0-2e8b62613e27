import { useState, useEffect, useCallback } from "react";
import { useToast } from "@/hooks/use-toast";
import { RiskTemplate } from "@/types";
import {
  fetchRiskTemplates,
  createTemplate as apiCreateTemplate,
  deleteTemplate as apiDeleteTemplate,
} from "@/lib/api/templateService";
export const useRiskTemplates = () => {
  const { toast } = useToast();
  const [templates, setTemplates] = useState<RiskTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const fetchTemplates = useCallback(async () => {
    try {
      setLoading(true);
      const formattedTemplates = await fetchRiskTemplates();
      setTemplates(formattedTemplates ?? []);
    } catch (err: unknown) {
      toast({
        title: "Error loading risk templates",
        description: err.message,
        variant: "destructive",
      });
      setTemplates([]);
    } finally {
      setLoading(false);
    }
  }, [toast]);
  // Create a new template
  const createTemplate = async (template: Omit<RiskTemplate, "id" | "createdAt">) => {
    try {
      const templateId = await apiCreateTemplate(template);
      if (templateId) {
        toast({
          title: "Template created",
          description: "Your risk assessment template has been saved.",
        });
        // Explicitly fetch updated templates
        await fetchTemplates();
        return templateId;
      }
      return null;
    } catch (err: unknown) {
      toast({
        title: "Error creating template",
        description: err.message,
        variant: "destructive",
      });
      return null;
    }
  };
  // Delete a template
  const deleteTemplate = async (templateId: string) => {
    try {
      const success = await apiDeleteTemplate(templateId);
      if (success) {
        toast({
          title: "Template deleted",
          description: "The risk template has been removed",
        });
        // Explicitly fetch updated templates
        await fetchTemplates();
      }
      return success;
    } catch (err: unknown) {
      toast({
        title: "Error deleting template",
        description: err.message,
        variant: "destructive",
      });
      return false;
    }
  };
  // Initial fetch on component mount
  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);
  return {
    templates,
    loading,
    fetchTemplates,
    createTemplate,
    deleteTemplate,
  };
};
