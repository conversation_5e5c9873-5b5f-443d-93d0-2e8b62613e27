import { renderHook, act } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useRiskCalculations } from '../useRiskCalculations';
import { RiskSeverity } from '@/types';
import {
  calculateSeverity,
  getSeverityColor,
  getSeverityPriority
} from '@/services/risk/riskCalculationService';

/**
 * Test suite for useRiskCalculations hook (risk directory version)
 *
 * This test uses the actual calculation functions from riskCalculationService
 * instead of simplified mock logic to ensure test accuracy and prevent false positives.
 *
 * The mock implementation imports the actual functions and wraps them with vi.fn()
 * to enable call tracking while preserving the real calculation behavior.
 */

// Mock the riskCalculationService module but use the actual implementations
vi.mock('@/services/risk/riskCalculationService', async () => {
  // Import the actual module to get the real implementations
  const actual = await vi.importActual('@/services/risk/riskCalculationService') as any;

  return {
    // Use the actual calculation functions to ensure test accuracy
    // This prevents false positives that could occur with simplified mock logic
    calculateSeverity: vi.fn().mockImplementation(actual.calculateSeverity),
    getSeverityColor: vi.fn().mockImplementation(actual.getSeverityColor),
    getSeverityPriority: vi.fn().mockImplementation(actual.getSeverityPriority),
  };
});

describe('useRiskCalculations (risk directory)', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize with default values', () => {
      const { result } = renderHook(() => useRiskCalculations());

      expect(result.current.likelihood).toBe(1);
      expect(result.current.impact).toBe(1);
      expect(result.current.severity).toBe(RiskSeverity.LOW);
      expect(result.current.severityColor).toBe("bg-green-100 text-green-800 border-green-200");
      expect(result.current.severityPriority).toBe(2);
    });

    it('should initialize with custom initial values', () => {
      const { result } = renderHook(() => 
        useRiskCalculations({
          initialLikelihood: 3,
          initialImpact: 4,
          autoCalculate: true
        })
      );

      expect(result.current.likelihood).toBe(3);
      expect(result.current.impact).toBe(4);
      expect(result.current.severity).toBe(RiskSeverity.HIGH);
      expect(result.current.severityColor).toBe("bg-orange-100 text-orange-800 border-orange-200");
      expect(result.current.severityPriority).toBe(4);
    });

    it('should initialize with autoCalculate disabled', () => {
      const { result } = renderHook(() => 
        useRiskCalculations({
          initialLikelihood: 5,
          initialImpact: 5,
          autoCalculate: false
        })
      );

      expect(result.current.likelihood).toBe(5);
      expect(result.current.impact).toBe(5);
      // With autoCalculate false, severity should remain at initial LOW state
      expect(result.current.severity).toBe(RiskSeverity.LOW);
    });
  });

  describe('likelihood and impact updates', () => {
    it('should update likelihood and auto-calculate severity', () => {
      const { result } = renderHook(() => useRiskCalculations());

      act(() => {
        result.current.setLikelihood(4);
      });

      expect(result.current.likelihood).toBe(4);
      expect(result.current.impact).toBe(1);
      expect(result.current.severity).toBe(RiskSeverity.MEDIUM);
      expect(result.current.severityColor).toBe("bg-yellow-100 text-yellow-800 border-yellow-200");
      expect(result.current.severityPriority).toBe(3);
    });

    it('should update impact and auto-calculate severity', () => {
      const { result } = renderHook(() => useRiskCalculations());

      act(() => {
        result.current.setImpact(5);
      });

      expect(result.current.likelihood).toBe(1);
      expect(result.current.impact).toBe(5);
      expect(result.current.severity).toBe(RiskSeverity.MEDIUM);
      expect(result.current.severityColor).toBe("bg-yellow-100 text-yellow-800 border-yellow-200");
      expect(result.current.severityPriority).toBe(3);
    });

    it('should calculate critical severity for high likelihood and impact', () => {
      const { result } = renderHook(() => useRiskCalculations());

      act(() => {
        result.current.setLikelihood(5);
        result.current.setImpact(5);
      });

      expect(result.current.severity).toBe(RiskSeverity.CRITICAL);
      expect(result.current.severityColor).toBe("bg-red-100 text-red-800 border-red-200");
      expect(result.current.severityPriority).toBe(5);
    });
  });

  describe('manual recalculation', () => {
    it('should recalculate when recalculate is called', () => {
      const { result } = renderHook(() => 
        useRiskCalculations({ autoCalculate: false })
      );

      // Update values without auto-calculation
      act(() => {
        result.current.setLikelihood(4);
        result.current.setImpact(4);
      });

      // Severity should still be LOW since autoCalculate is false
      expect(result.current.severity).toBe(RiskSeverity.LOW);

      // Manually trigger recalculation
      act(() => {
        result.current.recalculate();
      });

      // Now severity should be updated
      expect(result.current.severity).toBe(RiskSeverity.HIGH);
      expect(result.current.severityColor).toBe("bg-orange-100 text-orange-800 border-orange-200");
      expect(result.current.severityPriority).toBe(4);
    });
  });

  describe('reset functionality', () => {
    it('should reset to initial values', () => {
      const { result } = renderHook(() => 
        useRiskCalculations({
          initialLikelihood: 2,
          initialImpact: 3,
          autoCalculate: true
        })
      );

      // Change values
      act(() => {
        result.current.setLikelihood(5);
        result.current.setImpact(5);
      });

      expect(result.current.likelihood).toBe(5);
      expect(result.current.impact).toBe(5);
      expect(result.current.severity).toBe(RiskSeverity.CRITICAL);

      // Reset
      act(() => {
        result.current.reset();
      });

      expect(result.current.likelihood).toBe(2);
      expect(result.current.impact).toBe(3);
      expect(result.current.severity).toBe(RiskSeverity.MEDIUM);
    });
  });

  describe('service function calls', () => {
    it('should call calculateSeverity with correct parameters', () => {
      const { result } = renderHook(() => useRiskCalculations());

      act(() => {
        result.current.setLikelihood(3);
        result.current.setImpact(2);
      });

      expect(calculateSeverity).toHaveBeenCalledWith(3, 2);
    });

    it('should call getSeverityColor with calculated severity', () => {
      const { result } = renderHook(() => useRiskCalculations());

      act(() => {
        result.current.setLikelihood(4);
        result.current.setImpact(3);
      });

      expect(getSeverityColor).toHaveBeenCalledWith(RiskSeverity.HIGH);
    });

    it('should call getSeverityPriority with calculated severity', () => {
      const { result } = renderHook(() => useRiskCalculations());

      act(() => {
        result.current.setLikelihood(2);
        result.current.setImpact(2);
      });

      expect(getSeverityPriority).toHaveBeenCalledWith(RiskSeverity.LOW);
    });
  });

  describe('edge cases', () => {
    it('should handle boundary values correctly', () => {
      const { result } = renderHook(() => useRiskCalculations());

      // Test minimum values (1,1) -> LOW
      act(() => {
        result.current.setLikelihood(1);
        result.current.setImpact(1);
      });
      expect(result.current.severity).toBe(RiskSeverity.LOW);

      // Test maximum values (5,5) -> CRITICAL
      act(() => {
        result.current.setLikelihood(5);
        result.current.setImpact(5);
      });
      expect(result.current.severity).toBe(RiskSeverity.CRITICAL);
    });

    it('should handle various severity matrix combinations', () => {
      const { result } = renderHook(() => useRiskCalculations());

      // Test specific matrix combinations based on the actual SEVERITY_MATRIX
      const testCases = [
        { likelihood: 2, impact: 3, expectedSeverity: RiskSeverity.MEDIUM },
        { likelihood: 3, impact: 4, expectedSeverity: RiskSeverity.HIGH },
        { likelihood: 4, impact: 5, expectedSeverity: RiskSeverity.CRITICAL },
        { likelihood: 1, impact: 4, expectedSeverity: RiskSeverity.MEDIUM },
        { likelihood: 5, impact: 2, expectedSeverity: RiskSeverity.HIGH },
      ];

      testCases.forEach(({ likelihood, impact, expectedSeverity }) => {
        act(() => {
          result.current.setLikelihood(likelihood);
          result.current.setImpact(impact);
        });
        expect(result.current.severity).toBe(expectedSeverity);
      });
    });
  });

  describe('comprehensive severity matrix validation', () => {
    it('should validate all severity matrix combinations match the service', () => {
      const { result } = renderHook(() => useRiskCalculations());

      // Test all combinations from 1-5 for both likelihood and impact
      for (let likelihood = 1; likelihood <= 5; likelihood++) {
        for (let impact = 1; impact <= 5; impact++) {
          act(() => {
            result.current.setLikelihood(likelihood);
            result.current.setImpact(impact);
          });

          // The hook should produce the same result as the service
          const expectedSeverity = calculateSeverity(likelihood, impact);
          expect(result.current.severity).toBe(expectedSeverity);
        }
      }
    });
  });
});
