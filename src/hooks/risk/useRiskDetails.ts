import { useState, useEffect, useCallback } from "react";
import { useParams } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { Risk } from "@/types";
import { riskRepository } from "@/repositories/riskRepository";
import { supabase } from "@/integrations/supabase/client";
/**
 * Get the current user's organization ID
 */
const getUserOrganizationId = async (): Promise<string | null> => {
  const {
    data: { user },
  } = await supabase.auth.getUser();
  if (!user) return null;
  const { data: profile } = await supabase
    .from("profiles")
    .select("organization_id")
    .eq("id", user.id)
    .single();
  return profile?.organization_id ?? null;
};
/**
 * Hook for fetching and managing risk details with organization verification
 * Uses the repository pattern for data access and consistent error handling
 */
export const useRiskDetails = () => {
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();
  const [risk, setRisk] = useState<Risk | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const fetchRiskDetails = useCallback(async () => {
    if (!id) {
      setError("No risk ID provided");
      setLoading(false);
      return;
    }
    setLoading(true);
    setError(null);
    try {
      // First verify organization access
      const organizationId = await getUserOrganizationId();
      if (!organizationId) {
        throw new Error("User organization not found");
      }
      // Verify the risk belongs to the user's organization
      const { data: riskCheck, error: checkError } = await supabase
        .from("risks")
        .select("organization_id")
        .eq("id", id)
        .single();
      if (checkError) {
        if (checkError.code === "PGRST116") {
          setRisk(null);
          setError("Risk not found");
          return;
        }
        throw checkError;
      }
      if (!riskCheck || riskCheck.organization_id !== organizationId) {
        setRisk(null);
        setError("Risk not found or access denied");
        return;
      }
      // Now fetch the full risk data using repository
      const riskData = await riskRepository.getRiskById(id, organizationId);
      if (!riskData) {
        setRisk(null);
        setError("Risk not found");
        return;
      }
      setRisk(riskData);
    } catch (error: unknown) {
      setError(error instanceof Error ? error.message : "Could not load risk details");
      toast({
        title: "Error",
        description: "Could not load risk details. Please try again later.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [id, toast]);
  useEffect(() => {
    fetchRiskDetails();
  }, [fetchRiskDetails]);
  return {
    risk,
    loading,
    error,
    fetchRiskDetails,
  };
};
