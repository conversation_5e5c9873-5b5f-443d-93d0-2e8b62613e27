import { useState, useEffect, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
interface FilterNames {
  categoryNames: string[];
  ownerNames: string[];
}
export const useFilterNames = (categoryIds: string[] = [], ownerIds: string[] = []) => {
  const [filterNames, setFilterNames] = useState<FilterNames>({
    categoryNames: [],
    ownerNames: [],
  });
  // Array dependencies are handled directly in useCallback
  const fetchFilterNames = useCallback(async () => {
    // Fetch category names if category ids are provided
    if (categoryIds.length > 0) {
      try {
        const { data: categoryData, error } = await supabase
          .from("risk_categories")
          .select("name")
          .in("id", categoryIds);
        if (error) {
          // Condition handled
        } else if (categoryData) {
          setFilterNames(prev => ({
            ...prev,
            categoryNames: categoryData.map(category => category.name),
          }));
        }
      } catch (error) {
        // Error caught and handled
      }
    } else {
      setFilterNames(prev => ({ ...prev, categoryNames: [] }));
    }
    // Fetch owner names if owner ids are provided
    if (ownerIds.length > 0) {
      try {
        const { data: ownerData, error } = await supabase
          .from("profiles")
          .select("name")
          .in("id", ownerIds);
        if (error) {
          // Condition handled
        } else if (ownerData) {
          setFilterNames(prev => ({
            ...prev,
            ownerNames: ownerData.map(owner => owner.name),
          }));
        }
      } catch (error) {
        // Error caught and handled
      }
    } else {
      setFilterNames(prev => ({ ...prev, ownerNames: [] }));
    }
  }, [categoryIds, ownerIds]);
  useEffect(() => {
    fetchFilterNames();
  }, [fetchFilterNames]);
  return filterNames;
};
