
import { useState, useEffect, useCallback } from "react";
import { RiskSeverity } from "@/types";
import { calculateSeverity, getSeverityColor, getSeverityPriority } from "@/services/risk/riskCalculationService";

export interface UseRiskCalculationsOptions {
  initialLikelihood?: number;
  initialImpact?: number;
  autoCalculate?: boolean;
}

export interface UseRiskCalculationsResult {
  likelihood: number;
  impact: number;
  severity: RiskSeverity;
  severityColor: string;
  severityPriority: number;
  setLikelihood: (value: number) => void;
  setImpact: (value: number) => void;
  recalculate: () => void;
  reset: () => void;
}

export function useRiskCalculations({
  initialLikelihood = 1,
  initialImpact = 1,
  autoCalculate = true
}: UseRiskCalculationsOptions = {}): UseRiskCalculationsResult {
  const [likelihood, setLikelihood] = useState(initialLikelihood);
  const [impact, setImpact] = useState(initialImpact);
  const [severity, setSeverity] = useState<RiskSeverity>(RiskSeverity.LOW);
  const [severityColor, setSeverityColor] = useState("");
  const [severityPriority, setSeverityPriority] = useState(0);

  const recalculate = useCallback(() => {
    const newSeverity = calculateSeverity(likelihood, impact);
    setSeverity(newSeverity);
    setSeverityColor(getSeverityColor(newSeverity));
    setSeverityPriority(getSeverityPriority(newSeverity));
  }, [likelihood, impact]);

  const reset = useCallback(() => {
    setLikelihood(initialLikelihood);
    setImpact(initialImpact);
    recalculate();
  }, [initialLikelihood, initialImpact, recalculate]);

  // Auto-calculate when likelihood or impact changes
  useEffect(() => {
    if (autoCalculate) {
      recalculate();
    }
  }, [autoCalculate, recalculate]);

  return {
    likelihood,
    impact,
    severity,
    severityColor,
    severityPriority,
    setLikelihood,
    setImpact,
    recalculate,
    reset
  };
}
