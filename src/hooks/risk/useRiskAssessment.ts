import { useEffect } from "react";
import { RiskSeverity } from "@/types";
import { useRiskCalculations } from "./useRiskCalculations";

export interface RiskAssessmentData {
  likelihood: number;
  impact: number;
  severity: RiskSeverity;
}

export interface UseRiskAssessmentOptions {
  initialData?: Partial<RiskAssessmentData>;
  onChange?: (data: RiskAssessmentData) => void;
}

export interface UseRiskAssessmentResult {
  likelihood: number;
  impact: number;
  severity: RiskSeverity;
  severityColor: string;
  setLikelihood: (value: number) => void;
  setImpact: (value: number) => void;
  updateAssessment: (data: Partial<RiskAssessmentData>) => void;
  getAssessmentData: () => RiskAssessmentData;
}

export function useRiskAssessment({
  initialData,
  onChange,
}: UseRiskAssessmentOptions = {}): UseRiskAssessmentResult {
  const { likelihood, impact, severity, severityColor, setLikelihood, setImpact } =
    useRiskCalculations({
      initialLikelihood: initialData?.likelihood ?? 1,
      initialImpact: initialData?.impact ?? 1,
      autoCalculate: true,
    });

  const updateAssessment = (data: Partial<RiskAssessmentData>) => {
    if (data.likelihood !== undefined) {
      setLikelihood(data.likelihood);
    }
    if (data.impact !== undefined) {
      setImpact(data.impact);
    }
  };

  const getAssessmentData = (): RiskAssessmentData => ({
    likelihood,
    impact,
    severity,
  });

  // Notify parent of changes
  useEffect(() => {
    if (onChange) {
      onChange(getAssessmentData());
    }
  }, [likelihood, impact, severity, onChange]);

  return {
    likelihood,
    impact,
    severity,
    severityColor,
    setLikelihood,
    setImpact,
    updateAssessment,
    getAssessmentData,
  };
}
