import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuth } from "@/contexts/auth";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Risk, RiskSeverity, RiskStatus } from "@/types";
import { queryKeys, cacheUtils } from "@/lib/query-client";
import { formatRiskData } from "@/utils/riskTransformations";

interface CreateRiskData {
  title: string;
  description: string;
  categoryId: string;
  ownerId: string;
  severity: RiskSeverity;
  status: RiskStatus;
  likelihood: number;
  impact: number;
  currentControls?: string;
  mitigationApproach?: string;
  dueDate?: Date;
}

interface UpdateRiskData extends Partial<CreateRiskData> {
  id: string;
}

/**
 * Optimized risk mutations with cache management and optimistic updates
 */

export const useRiskMutations = () => {
  const { organization, user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Create risk mutation
  const createRisk = useMutation({
    mutationFn: async (data: CreateRiskData): Promise<Risk> => {
      if (!organization?.id || !user?.id) {
        throw new Error("Organization or user not available");
      }

      const riskData = {
        ...data,
        organization_id: organization.id,
        created_by: user.id,
        due_date: data.dueDate?.toISOString(),
        // Calculate inherent risk (before controls)
        inherent_likelihood: data.likelihood,
        inherent_impact: data.impact,
        inherent_severity: data.severity,
      };

      const { data: newRisk, error } = await supabase
        .from("risks")
        .insert(riskData)
        .select(
          `
          *,
          profiles!risks_owner_id_fkey(name),
          categories:risk_categories(name)
        `
        )
        .single();

      if (error) {
        throw new Error(`Failed to create risk: ${error.message}`);
      }

      return formatRiskData(newRisk);
    },
    onMutate: async newRisk => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.organization.risks(organization!.id) });

      // Snapshot previous value
      const previousRisks = queryClient.getQueriesData({
        queryKey: queryKeys.organization.risks(organization!.id),
      });

      // Optimistically update cache
      queryClient.setQueriesData(
        { queryKey: queryKeys.organization.risks(organization!.id) },
        (old: Record<string, unknown>) => {
          if (!old) return old;

          // Create optimistic risk object
          const optimisticRisk: Risk = {
            id: `temp-${Date.now()}`,
            title: newRisk.title,
            description: newRisk.description,
            category: "Loading...",
            categoryId: newRisk.categoryId,
            ownerId: newRisk.ownerId,
            ownerName: "Loading...",
            organizationId: organization!.id,
            severity: newRisk.severity,
            status: newRisk.status,
            likelihood: newRisk.likelihood,
            impact: newRisk.impact,
            inherentLikelihood: newRisk.likelihood,
            inherentImpact: newRisk.impact,
            inherentSeverity: newRisk.severity,
            currentControls: newRisk.currentControls,
            mitigationApproach: newRisk.mitigationApproach,
            dueDate: newRisk.dueDate,
            createdAt: new Date(),
            updatedAt: new Date(),
            controlMeasures: [],
            mitigationActions: [],
          };

          if (old.risks) {
            return {
              ...old,
              risks: [optimisticRisk, ...old.risks],
              totalCount: old.totalCount + 1,
            };
          }

          return old;
        }
      );

      return { previousRisks };
    },
    onError: (err, newRisk, context) => {
      // Rollback optimistic update
      if (context?.previousRisks) {
        context.previousRisks.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }

      toast({
        title: "Error creating risk",
        description: err.message,
        variant: "destructive",
      });
    },
    onSuccess: data => {
      // Invalidate and refetch related queries
      cacheUtils.invalidateOrganization(queryClient, organization!.id);

      toast({
        title: "Risk created successfully",
        description: `"${data.title}" has been added to the risk register.`,
      });
    },
  });

  // Update risk mutation
  const updateRisk = useMutation({
    mutationFn: async (data: UpdateRiskData): Promise<Risk> => {
      if (!organization?.id) {
        throw new Error("Organization not available");
      }

      const updateData = {
        ...data,
        due_date: data.dueDate?.toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Remove id from update data
      const { id, ...updateFields } = updateData;

      const { data: updatedRisk, error } = await supabase
        .from("risks")
        .update(updateFields)
        .eq("id", data.id)
        .eq("organization_id", organization.id)
        .select(
          `
          *,
          profiles!risks_owner_id_fkey(name),
          categories:risk_categories(name)
        `
        )
        .single();

      if (error) {
        throw new Error(`Failed to update risk: ${error.message}`);
      }

      return formatRiskData(updatedRisk);
    },
    onMutate: async updatedRisk => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: queryKeys.risks.detail(updatedRisk.id) });

      // Snapshot previous value
      const previousRisk = queryClient.getQueryData(queryKeys.risks.detail(updatedRisk.id));

      // Optimistically update the specific risk
      queryClient.setQueryData(queryKeys.risks.detail(updatedRisk.id), (old: Risk | undefined) => {
        if (!old) return old;
        return { ...old, ...updatedRisk, updatedAt: new Date() };
      });

      // Update risk in lists
      queryClient.setQueriesData(
        { queryKey: queryKeys.organization.risks(organization!.id) },
        (old: Record<string, unknown>) => {
          if (!old?.risks) return old;

          return {
            ...old,
            risks: old.risks.map((risk: Risk) =>
              risk.id === updatedRisk.id ? { ...risk, ...updatedRisk, updatedAt: new Date() } : risk
            ),
          };
        }
      );

      return { previousRisk };
    },
    onError: (err, updatedRisk, context) => {
      // Rollback optimistic update
      if (context?.previousRisk) {
        queryClient.setQueryData(queryKeys.risks.detail(updatedRisk.id), context.previousRisk);
      }

      toast({
        title: "Error updating risk",
        description: err.message,
        variant: "destructive",
      });
    },
    onSuccess: data => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: queryKeys.risks.detail(data.id) });
      queryClient.invalidateQueries({
        queryKey: queryKeys.organization.dashboard(organization!.id),
      });

      toast({
        title: "Risk updated successfully",
        description: `"${data.title}" has been updated.`,
      });
    },
  });

  // Delete risk mutation
  const deleteRisk = useMutation({
    mutationFn: async (riskId: string): Promise<void> => {
      if (!organization?.id) {
        throw new Error("Organization not available");
      }

      const { error } = await supabase
        .from("risks")
        .delete()
        .eq("id", riskId)
        .eq("organization_id", organization.id);

      if (error) {
        throw new Error(`Failed to delete risk: ${error.message}`);
      }
    },
    onSuccess: (_, riskId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: queryKeys.risks.detail(riskId) });

      // Invalidate lists
      cacheUtils.invalidateOrganization(queryClient, organization!.id);

      toast({
        title: "Risk deleted successfully",
        description: "The risk has been removed from the register.",
      });
    },
    onError: err => {
      toast({
        title: "Error deleting risk",
        description: err.message,
        variant: "destructive",
      });
    },
  });

  return {
    createRisk,
    updateRisk,
    deleteRisk,

    // Loading states
    isCreating: createRisk.isPending,
    isUpdating: updateRisk.isPending,
    isDeleting: deleteRisk.isPending,

    // Any mutation in progress
    isMutating: createRisk.isPending || updateRisk.isPending || deleteRisk.isPending,
  };
};
