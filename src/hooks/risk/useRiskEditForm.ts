import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Risk, RiskSeverity } from "@/types";
import { calculateSeverity } from "@/services/risk";
import { RiskFormSchema } from "@/components/risk/schema/riskFormSchema";
import { z } from "zod";
import { useCategoryMapping } from "./form/useCategoryMapping";
import { useRiskUpdateSubmit } from "./form/useRiskUpdateSubmit";

type FormValues = z.infer<typeof RiskFormSchema>;

interface UseRiskEditFormProps {
  initialRisk: Risk;
  onSuccess?: () => void;
}

export const useRiskEditForm = ({ initialRisk, onSuccess }: UseRiskEditFormProps) => {
  const [inherentSeverity, setInherentSeverity] = useState<RiskSeverity>(
    initialRisk.inherentSeverity
  );
  const [residualSeverity, setResidualSeverity] = useState<RiskSeverity>(initialRisk.severity);

  // Reuse the category mapping hook
  const { categoryMapping, loading: loadingCategories } = useCategoryMapping();

  const form = useForm<FormValues>({
    resolver: zodResolver(RiskFormSchema),
    defaultValues: {
      title: initialRisk.title,
      description: initialRisk.description,
      category: initialRisk.category,
      categoryId: initialRisk.categoryId,
      inherentLikelihood: initialRisk.inherentLikelihood,
      inherentImpact: initialRisk.inherentImpact,
      likelihood: initialRisk.likelihood,
      impact: initialRisk.impact,
      status: initialRisk.status,
      currentControls: initialRisk.currentControls ?? "",
      controlMeasures: initialRisk.controlMeasures ?? [],
      mitigationApproach: initialRisk.mitigationApproach ?? "",
      mitigationActions: initialRisk.mitigationActions ?? [],
      dueDate: initialRisk.dueDate,
      ownerId: initialRisk.ownerId,
    },
  });

  // Update calculated severities and form values for inherent risk
  const updateInherentSeverity = (likelihood: number, impact: number) => {
    const newSeverity = calculateSeverity(likelihood, impact);
    setInherentSeverity(newSeverity);
    // Update the form values so they get submitted
    form.setValue("inherentLikelihood", likelihood);
    form.setValue("inherentImpact", impact);
  };

  // Update calculated severities and form values for residual risk
  const updateResidualSeverity = (likelihood: number, impact: number) => {
    const newSeverity = calculateSeverity(likelihood, impact);
    setResidualSeverity(newSeverity);
    // Update the form values so they get submitted
    form.setValue("likelihood", likelihood);
    form.setValue("impact", impact);
  };

  // Use the submission hook for handling the update
  const { submitting, onSubmit: handleSubmit } = useRiskUpdateSubmit({
    initialRisk,
    onSuccess: onSuccess ?? (() => {}),
    categoryMapping,
  });

  // Wrapper for onSubmit to handle the form values
  const onSubmit = async (values: FormValues) => {
    return await handleSubmit(values);
  };

  return {
    form,
    inherentSeverity,
    residualSeverity,
    submitting,
    loadingCategories,
    updateInherentSeverity,
    updateResidualSeverity,
    onSubmit,
  };
};
