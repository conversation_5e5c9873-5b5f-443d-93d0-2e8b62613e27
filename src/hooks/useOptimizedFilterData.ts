import { useQueries } from "@tanstack/react-query";
import { useAuth } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";
import { User, UserRole, RiskSeverity, RiskStatus } from "@/types";
import { queryKeys } from "@/lib/query-client";
import { useMemo } from "react";

interface FilterData {
  categories: { id: string; name: string }[];
  owners: User[];
  usedSeverities: RiskSeverity[];
  usedStatuses: RiskStatus[];
  loading: boolean;
}

/**
 * Optimized filter data hook using parallel queries
 * Fetches filter options for risk management efficiently
 */

export const useFilterData = () => {
  const { organization } = useAuth();

  // Parallel queries for better performance
  const queries = useQueries({
    queries: [
      // Categories query
      {
        queryKey: queryKeys.organization.all(organization?.id ?? "").concat(["categories"]),
        queryFn: async () => {
          if (!organization?.id) throw new Error("Organization not available");

          const { data, error } = await supabase
            .from("risk_categories")
            .select("id, name")
            .eq("organization_id", organization.id)
            .order("name");

          if (error) {
            throw new Error(`Failed to fetch categories: ${error.message}`);
          }

          return data ?? [];
        },
        enabled: !!organization?.id,
        staleTime: 10 * 60 * 1000, // 10 minutes - categories don't change often
        gcTime: 20 * 60 * 1000,
      },

      // Owners query (users with assigned risks)
      {
        queryKey: queryKeys.organization.all(organization?.id ?? "").concat(["owners"]),
        queryFn: async () => {
          if (!organization?.id) throw new Error("Organization not available");

          const { data, error } = await supabase
            .from("profiles")
            .select(
              `
              id,
              name,
              email,
              role,
              risks!risks_owner_id_fkey(id)
            `
            )
            .eq("organization_id", organization.id)
            .order("name");

          if (error) {
            throw new Error(`Failed to fetch owners: ${error.message}`);
          }

          // Filter out owners with no assigned risks
          const usedOwners = (data ?? [])
            .filter(owner => owner.risks && owner.risks.length > 0)
            .map(({ risks, ...owner }) => ({
              ...owner,
              role: owner.role as UserRole,
            }));

          return usedOwners;
        },
        enabled: !!organization?.id,
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 15 * 60 * 1000,
      },

      // Used severities query
      {
        queryKey: queryKeys.organization.all(organization?.id ?? "").concat(["used-severities"]),
        queryFn: async () => {
          if (!organization?.id) throw new Error("Organization not available");

          const { data, error } = await supabase
            .from("risks")
            .select("severity")
            .eq("organization_id", organization.id)
            .order("severity");

          if (error) {
            throw new Error(`Failed to fetch severities: ${error.message}`);
          }

          const usedSeverities = [...new Set(data?.map(risk => risk.severity))] as RiskSeverity[];
          return usedSeverities;
        },
        enabled: !!organization?.id,
        staleTime: 3 * 60 * 1000, // 3 minutes
        gcTime: 10 * 60 * 1000,
      },

      // Used statuses query
      {
        queryKey: queryKeys.organization.all(organization?.id ?? "").concat(["used-statuses"]),
        queryFn: async () => {
          if (!organization?.id) throw new Error("Organization not available");

          const { data, error } = await supabase
            .from("risks")
            .select("status")
            .eq("organization_id", organization.id)
            .order("status");

          if (error) {
            throw new Error(`Failed to fetch statuses: ${error.message}`);
          }

          const usedStatuses = [...new Set(data?.map(risk => risk.status))] as RiskStatus[];
          return usedStatuses;
        },
        enabled: !!organization?.id,
        staleTime: 3 * 60 * 1000, // 3 minutes
        gcTime: 10 * 60 * 1000,
      },
    ],
  });

  // Extract individual query results
  const [categoriesQuery, ownersQuery, severitiesQuery, statusesQuery] = queries;

  // Memoized computed values
  const filterData = useMemo((): FilterData => {
    const categories = categoriesQuery.data ?? [];
    const owners = ownersQuery.data ?? [];
    const usedSeverities = severitiesQuery.data ?? [];
    const usedStatuses = statusesQuery.data ?? [];
    const loading = queries.some(query => query.isLoading);

    return {
      categories,
      owners,
      usedSeverities,
      usedStatuses,
      loading,
    };
  }, [categoriesQuery.data, ownersQuery.data, severitiesQuery.data, statusesQuery.data, queries]);

  // Combined loading and error states
  const isLoading = queries.some(query => query.isLoading);
  const isFetching = queries.some(query => query.isFetching);
  const isError = queries.some(query => query.isError);
  const errors = queries.filter(query => query.error).map(query => query.error);

  // Refetch all filter data
  const refetchAll = () => {
    queries.forEach(query => query.refetch());
  };

  return {
    // Data (maintaining original interface)
    ...filterData,

    // Additional states
    isLoading,
    isFetching,
    isError,
    errors,

    // Actions
    refetchAll,

    // Individual query states (for granular loading indicators)
    queryStates: {
      categories: {
        isLoading: categoriesQuery.isLoading,
        isFetching: categoriesQuery.isFetching,
        isError: categoriesQuery.isError,
        error: categoriesQuery.error,
      },
      owners: {
        isLoading: ownersQuery.isLoading,
        isFetching: ownersQuery.isFetching,
        isError: ownersQuery.isError,
        error: ownersQuery.error,
      },
      severities: {
        isLoading: severitiesQuery.isLoading,
        isFetching: severitiesQuery.isFetching,
        isError: severitiesQuery.isError,
        error: severitiesQuery.error,
      },
      statuses: {
        isLoading: statusesQuery.isLoading,
        isFetching: statusesQuery.isFetching,
        isError: statusesQuery.isError,
        error: statusesQuery.error,
      },
    },
  };
};
