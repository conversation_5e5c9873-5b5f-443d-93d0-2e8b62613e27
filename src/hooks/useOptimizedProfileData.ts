import { useQueries } from "@tanstack/react-query";
import { useAuth } from "@/contexts/auth";
import { supabase } from "@/integrations/supabase/client";
import { Risk, Comment } from "@/types";
import { formatRisksData } from "@/utils/riskTransformations";
import { fetchUserRelatedComments } from "@/services/profile";
import { queryKeys } from "@/lib/query-client";
import { useMemo } from "react";

interface ProfileData {
  userRisks: Risk[];
  userComments: Comment[];
  activitySummary: {
    totalRisks: number;
    totalComments: number;
    recentActivity: number;
  };
}

/**
 * Optimized profile data hook using parallel queries
 * Fetches user-specific risks and comments efficiently
 */

export const useProfileData = () => {
  const { user } = useAuth();

  // Parallel queries for better performance
  const queries = useQueries({
    queries: [
      // User risks query
      {
        queryKey: queryKeys.users.detail(user?.id ?? "").concat(["risks"]),
        queryFn: async () => {
          if (!user?.id || !organization?.id) throw new Error("User or organization not available");

          const { data, error } = await supabase
            .from("risks")
            .select(
              `
              *,
              profiles!risks_owner_id_fkey(name),
              categories:risk_categories(name)
            `
            )
            .eq("owner_id", user.id)
            .eq("organization_id", organization.id)
            .order("created_at", { ascending: false });

          if (error) {
            throw new Error(`Failed to fetch user risks: ${error.message}`);
          }

          return data ? formatRisksData(data) : [];
        },
        enabled: !!user?.id && !!organization?.id,
        staleTime: 3 * 60 * 1000, // 3 minutes
        gcTime: 10 * 60 * 1000,
      },

      // User comments query
      {
        queryKey: queryKeys.users.detail(user?.id ?? "").concat(["comments"]),
        queryFn: async () => {
          if (!user?.id) throw new Error("User not available");

          const commentsData = await fetchUserRelatedComments(user.id);

          const formattedComments: Comment[] = commentsData.map(comment => ({
            id: comment.id,
            content: comment.content,
            entityType: comment.entityType as "risk" | "incident",
            entityId: comment.entityId,
            userId: comment.userId,
            organizationId: user.organizationId ?? "",
            createdAt: comment.createdAt,
            updatedAt: comment.createdAt,
          }));

          return formattedComments;
        },
        enabled: !!user?.id,
        staleTime: 2 * 60 * 1000, // 2 minutes - comments change more frequently
        gcTime: 8 * 60 * 1000,
      },
    ],
  });

  // Extract individual query results
  const [userRisksQuery, userCommentsQuery] = queries;

  // Memoized computed values
  const profileData = useMemo((): ProfileData => {
    const userRisks = userRisksQuery.data ?? [];
    const userComments = userCommentsQuery.data ?? [];

    return {
      userRisks,
      userComments,
      activitySummary: {
        totalRisks: userRisks.length,
        totalComments: userComments.length,
        recentActivity: userRisks.length + userComments.length,
      },
    };
  }, [userRisksQuery.data, userCommentsQuery.data]);

  // Combined loading and error states
  const isLoading = queries.some(query => query.isLoading);
  const isFetching = queries.some(query => query.isFetching);
  const isError = queries.some(query => query.isError);
  const errors = queries.filter(query => query.error).map(query => query.error);

  // Refetch all profile data
  const refetchAll = () => {
    queries.forEach(query => query.refetch());
  };

  return {
    // Data
    userRisks: profileData.userRisks,
    userComments: profileData.userComments,
    activitySummary: profileData.activitySummary,

    // Loading states
    isLoading,
    isFetching,
    isError,
    errors,

    // Actions
    refetchAll,

    // Individual query states (for granular loading indicators)
    queryStates: {
      userRisks: {
        isLoading: userRisksQuery.isLoading,
        isFetching: userRisksQuery.isFetching,
        isError: userRisksQuery.isError,
        error: userRisksQuery.error,
      },
      userComments: {
        isLoading: userCommentsQuery.isLoading,
        isFetching: userCommentsQuery.isFetching,
        isError: userCommentsQuery.isError,
        error: userCommentsQuery.error,
      },
    },

    // Legacy compatibility
    loading: isLoading,
  };
};
