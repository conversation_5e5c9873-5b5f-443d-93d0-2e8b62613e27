import { useState, useEffect, useCallback } from "react";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { formatRiskData } from "@/utils/riskTransformations";
import { mapDbRiskHistoryToDomain } from "@/utils/typeMappers";
import { Risk, RiskHistoryEntry } from "@/types";
import { DbRiskHistory } from "@/types/db";
import { useAuth } from "@/contexts/auth";
export const useRiskHistory = (riskId: string | undefined) => {
  const { toast } = useToast();
  const { organization } = useAuth();
  const [loading, setLoading] = useState(true);
  const [history, setHistory] = useState<RiskHistoryEntry[]>([]);
  const [currentRisk, setCurrentRisk] = useState<Risk | null>(null);
  const fetchRiskHistory = useCallback(async () => {
    if (!riskId || !organization?.id) return;
    try {
      setLoading(true);
      // First, get the current risk data with proper column selection and organization filtering
      const { data: currentData, error: currentError } = await supabase
        .from("risks")
        .select(
          `
          *,
          profiles!risks_owner_id_fkey(name),
          categories:risk_categories(name)
        `
        )
        .eq("id", riskId)
        .eq("organization_id", organization.id)
        .maybeSingle();
      if (currentError) {
        throw currentError;
      }
      if (currentData) {
        const formattedRisk = formatRiskData(currentData);
        setCurrentRisk(formattedRisk);
        // Fetch historical data from risk_history table - only include existing columns with organization filtering
        const { data: historicalData, error: historyError } = await supabase
          .from("risk_history")
          .select("id, risk_id, recorded_at, likelihood, impact, severity, status, created_by")
          .eq("risk_id", riskId)
          .eq("organization_id", organization.id)
          .order("recorded_at", { ascending: false });
        if (historyError) {
          throw historyError;
        }
        if (historicalData?.length > 0) {
          // Use the mapper to transform the data
          const mappedHistory = historicalData.map(entry =>
            mapDbRiskHistoryToDomain(entry as DbRiskHistory)
          );
          setHistory(mappedHistory);
        } else {
          setHistory([]);
        }
      } else {
        setCurrentRisk(null);
        setHistory([]);
      }
    } catch (err: unknown) {
      toast({
        title: "Error",
        description: "Failed to load risk history data.",
        variant: "destructive",
      });
      setHistory([]);
      setCurrentRisk(null);
    } finally {
      setLoading(false);
    }
  }, [riskId, organization?.id, toast]);
  useEffect(() => {
    fetchRiskHistory();
  }, [fetchRiskHistory]);
  return {
    history,
    loading,
    currentRisk,
  };
};
