import * as React from "react";
import { useIsMobile, useIsTablet, useDeviceType, useScreenOrientation } from "@/hooks/use-mobile";
import { usePerformanceMetrics } from "@/hooks/use-performance";
import { usePWA } from "@/hooks/use-pwa";
import { getDeviceInfo, HapticFeedback, MobilePerformanceMonitor } from "@/utils/mobile-utils";

export interface MobileOptimizationConfig {
  enableHapticFeedback?: boolean;
  enablePerformanceMonitoring?: boolean;
  enableAdaptiveLoading?: boolean;
  enablePWAFeatures?: boolean;
  autoOptimizeImages?: boolean;
  reducedMotionThreshold?: number;
  lowEndDeviceOptimizations?: boolean;
}

export interface MobileOptimizationState {
  // Device info
  deviceInfo: ReturnType<typeof getDeviceInfo>;
  isMobile: boolean;
  isTablet: boolean;
  deviceType: "mobile" | "tablet" | "desktop";
  orientation: "portrait" | "landscape";

  // Performance
  performanceMetrics: ReturnType<typeof usePerformanceMetrics>;
  adaptiveLoading: ReturnType<typeof useAdaptiveLoading>;

  // PWA
  pwaState: ReturnType<typeof usePWA>;

  // Optimization flags
  shouldReduceMotion: boolean;
  shouldLazyLoad: boolean;
  shouldUseVirtualization: boolean;
  shouldEnableHaptics: boolean;

  // Utilities
  hapticFeedback: typeof HapticFeedback;
  performanceMonitor: MobilePerformanceMonitor;
}

const DEFAULT_CONFIG: Required<MobileOptimizationConfig> = {
  enableHapticFeedback: true,
  enablePerformanceMonitoring: true,
  enableAdaptiveLoading: true,
  enablePWAFeatures: true,
  autoOptimizeImages: true,
  reducedMotionThreshold: 16,
  lowEndDeviceOptimizations: true,
};

/**
 * Comprehensive hook for mobile optimization
 * Provides device detection, performance monitoring, and adaptive features
 */
export function useMobileOptimization(
  config: MobileOptimizationConfig = {}
): MobileOptimizationState {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  // Device detection
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const deviceType = useDeviceType();
  const orientation = useScreenOrientation();

  // Device info
  const [deviceInfo, setDeviceInfo] = React.useState(() => getDeviceInfo());

  // Performance monitoring
  const performanceMetrics = usePerformanceMetrics();
  const adaptiveLoading = useAdaptiveLoading();

  // PWA features
  const pwaState = usePWA();

  // Performance monitor instance
  const performanceMonitor = React.useMemo(() => MobilePerformanceMonitor.getInstance(), []);

  // Update device info on resize/orientation change
  React.useEffect(() => {
    const updateDeviceInfo = () => {
      setDeviceInfo(getDeviceInfo());
    };

    window.addEventListener("resize", updateDeviceInfo);
    window.addEventListener("orientationchange", updateDeviceInfo);

    return () => {
      window.removeEventListener("resize", updateDeviceInfo);
      window.removeEventListener("orientationchange", updateDeviceInfo);
    };
  }, []);

  // Optimization flags
  const shouldReduceMotion = React.useMemo(() => {
    if (!finalConfig.lowEndDeviceOptimizations) return false;

    return (
      deviceInfo.isLowEndDevice ||
      window.matchMedia("(prefers-reduced-motion: reduce)").matches ||
      (performanceMetrics.metrics.componentRenderTime ?? 0) > finalConfig.reducedMotionThreshold
    );
  }, [deviceInfo.isLowEndDevice, performanceMetrics.metrics.componentRenderTime, finalConfig]);

  const shouldLazyLoad = React.useMemo(() => {
    if (!finalConfig.enableAdaptiveLoading) return false;

    return (
      deviceInfo.isLowEndDevice ||
      deviceInfo.connectionType === "slow-2g" ||
      deviceInfo.connectionType === "2g" ||
      adaptiveLoading.shouldLazyLoad
    );
  }, [deviceInfo, adaptiveLoading.shouldLazyLoad, finalConfig.enableAdaptiveLoading]);

  const shouldUseVirtualization = React.useMemo(() => {
    return (
      isMobile ||
      deviceInfo.isLowEndDevice ||
      (performanceMetrics.metrics.componentRenderTime ?? 0) > 50
    );
  }, [isMobile, deviceInfo.isLowEndDevice, performanceMetrics.metrics.componentRenderTime]);

  const shouldEnableHaptics = React.useMemo(() => {
    return (
      finalConfig.enableHapticFeedback && HapticFeedback.isSupported() && (isMobile || isTablet)
    );
  }, [finalConfig.enableHapticFeedback, isMobile, isTablet]);

  // Performance monitoring setup
  React.useEffect(() => {
    if (!finalConfig.enablePerformanceMonitoring) return;

    // Log slow operations periodically
    const interval = setInterval(() => {
      performanceMonitor.logSlowOperations(100);
    }, 30000); // Every 30 seconds

    return () => clearInterval(interval);
  }, [finalConfig.enablePerformanceMonitoring, performanceMonitor]);

  // Auto-optimize images based on device capabilities
  React.useEffect(() => {
    if (!finalConfig.autoOptimizeImages) return;

    const images = document.querySelectorAll("img[data-auto-optimize]");
    images.forEach(img => {
      const imgElement = img as HTMLImageElement;
      const originalSrc = imgElement.dataset.originalSrc || imgElement.src;

      if (originalSrc) {
        const url = new URL(originalSrc, window.location.origin);

        // Add optimization parameters
        if (deviceInfo.isLowEndDevice) {
          url.searchParams.set("q", "60");
          url.searchParams.set("w", "800");
        } else if (isMobile) {
          url.searchParams.set("q", "75");
          url.searchParams.set("w", "1200");
        }

        // Add format optimization
        if (deviceInfo.isLowEndDevice) {
          url.searchParams.set("f", "jpeg");
        } else {
          url.searchParams.set("f", "webp");
        }

        imgElement.src = url.toString();
      }
    });
  }, [deviceInfo, isMobile, finalConfig.autoOptimizeImages]);

  return {
    // Device info
    deviceInfo,
    isMobile,
    isTablet,
    deviceType,
    orientation,

    // Performance
    performanceMetrics,
    adaptiveLoading,

    // PWA
    pwaState,

    // Optimization flags
    shouldReduceMotion,
    shouldLazyLoad,
    shouldUseVirtualization,
    shouldEnableHaptics,

    // Utilities
    hapticFeedback: HapticFeedback,
    performanceMonitor,
  };
}

/**
 * Hook for mobile-optimized component rendering
 */
export function useMobileComponent<T extends Record<string, unknown>>(
  mobileComponent: React.ComponentType<T>,
  desktopComponent: React.ComponentType<T>,
  tabletComponent?: React.ComponentType<T>
) {
  const { isMobile, isTablet } = useMobileOptimization();

  return React.useMemo(() => {
    if (isMobile) return mobileComponent;
    if (isTablet && tabletComponent) return tabletComponent;
    return desktopComponent;
  }, [isMobile, isTablet, mobileComponent, desktopComponent, tabletComponent]);
}

/**
 * Hook for mobile-optimized data fetching
 */
export function useMobileQuery<T>(
  queryFn: () => Promise<T>,
  options: {
    enabled?: boolean;
    staleTime?: number;
    cacheTime?: number;
    refetchOnWindowFocus?: boolean;
    mobileOptimized?: boolean;
  } = {
    // Implementation needed
  }
) {
  const { deviceInfo } = useMobileOptimization();
  const [data, setData] = React.useState<T | null>(null);
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState<Error | null>(null);

  const optimizedOptions = React.useMemo(() => {
    if (!options.mobileOptimized) return options;

    return {
      ...options,
      staleTime: deviceInfo.isLowEndDevice ? 10 * 60 * 1000 : (options.staleTime ?? 5 * 60 * 1000),
      cacheTime: deviceInfo.isLowEndDevice ? 30 * 60 * 1000 : (options.cacheTime ?? 15 * 60 * 1000),
      refetchOnWindowFocus: deviceInfo.isLowEndDevice
        ? false
        : options.refetchOnWindowFocus !== false,
    };
  }, [options, deviceInfo.isLowEndDevice]);

  const executeQuery = React.useCallback(async () => {
    if (optimizedOptions.enabled === false) return;

    setIsLoading(true);
    setError(null);

    try {
      const result = await queryFn();
      setData(result);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, [queryFn, optimizedOptions.enabled]);

  React.useEffect(() => {
    executeQuery();
  }, [executeQuery]);

  return {
    data,
    isLoading,
    error,
    refetch: executeQuery,
    isOptimized: options.mobileOptimized && deviceInfo.isLowEndDevice,
  };
}

/**
 * Hook for mobile-optimized animations
 */
export function useMobileAnimation(
  animation: Record<string, unknown>,
  options: {
    reduceMotion?: boolean;
    disableOnLowEnd?: boolean;
  } = {
    // Implementation needed
  }
) {
  const { shouldReduceMotion, deviceInfo } = useMobileOptimization();

  return React.useMemo(() => {
    if (options.reduceMotion && shouldReduceMotion) {
      return { ...animation, transition: { duration: 0 } };
    }

    if (options.disableOnLowEnd && deviceInfo.isLowEndDevice) {
      return { ...animation, transition: { duration: 0 } };
    }

    return animation;
  }, [animation, options, shouldReduceMotion, deviceInfo.isLowEndDevice]);
}
