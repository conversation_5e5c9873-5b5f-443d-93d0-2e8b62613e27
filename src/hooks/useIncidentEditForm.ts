import { useEffect, useState, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate, useParams } from "react-router-dom";
import { useAuth } from "@/contexts/auth";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Risk, RiskSeverity } from "@/types";
import { formSchema, IncidentFormValues } from "./useIncidentForm";
import { updateIncident } from "@/services/incidentService";
export const useIncidentEditForm = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  const [risks, setRisks] = useState<Risk[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingRisks, setLoadingRisks] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  // Form setup
  const form = useForm<IncidentFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      description: "",
      severity: "Medium",
      status: "Open",
      relatedRiskId: undefined,
    },
  });
  // Fetch incident data
  const fetchIncidentData = useCallback(async () => {
    if (!id) return;
    try {
      setInitialLoading(true);
      const { data, error } = await supabase
        .from("incidents")
        .select(
          `
          *,
          profiles:reporter_id(name),
          risks:related_risk_id(title)
        `
        )
        .eq("id", id)
        .maybeSingle();
      if (error) throw error;
      if (data) {
        form.reset({
          title: data.title,
          description: data.description,
          severity: data.severity as RiskSeverity,
          status: data.status as "Open" | "Investigating" | "Resolved" | "Closed",
          relatedRiskId: data.related_risk_id ?? undefined,
        });
      }
    } catch (err: unknown) {
      toast({
        title: "Error",
        description: "Failed to load incident data.",
        variant: "destructive",
      });
    } finally {
      setInitialLoading(false);
    }
  }, [id, form, toast]);
  useEffect(() => {
    fetchIncidentData();
  }, [fetchIncidentData]);
  // Fetch risks for dropdown
  const fetchRisks = useCallback(async () => {
    if (!user) return;
    setLoadingRisks(true);
    try {
      const { data, error } = await supabase
        .from("risks")
        .select("id, title")
        .order("title", { ascending: true });
      if (error) throw error;
      setRisks(
        data?.map(risk => ({
          id: risk.id,
          title: risk.title,
        })) as Risk[]
      );
    } catch (error: unknown) {
      toast({
        title: "Error fetching risks",
        description: error instanceof Error ? error.message : "An unknown error occurred",
        variant: "destructive",
      });
    } finally {
      setLoadingRisks(false);
    }
  }, [user, toast]);
  useEffect(() => {
    fetchRisks();
  }, [fetchRisks]);
  // Handle form submission
  const onSubmit = async (values: IncidentFormValues) => {
    if (!id || !user) return;
    setLoading(true);
    try {
      await updateIncident(id, values);
      toast({
        title: "Success",
        description: "Incident updated successfully.",
      });
      navigate(`/incidents/${id}`);
    } catch (err: unknown) {
      toast({
        title: "Error",
        description: err instanceof Error ? err.message : "Failed to update incident.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };
  return {
    form,
    risks,
    loading,
    loadingRisks,
    initialLoading,
    onSubmit,
    user,
  };
};
