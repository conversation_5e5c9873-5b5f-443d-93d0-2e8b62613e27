import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/contexts/auth";
import { Comment } from "@/types";
import { useToast } from "@/hooks/use-toast";
import {
  fetchComments,
  addComment as addCommentService,
  updateComment as updateCommentService,
  deleteComment as deleteCommentService,
} from "@/services/commentService";
export const useComments = (entityType: "risk" | "incident", entityId: string) => {
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { user } = useAuth();
  const { toast } = useToast();
  const loadComments = useCallback(async () => {
    if (!entityId) return;
    try {
      setLoading(true);
      const commentsData = await fetchComments(entityType, entityId);
      setComments(commentsData as Comment[]);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err : new Error("Failed to fetch comments"));
      toast({
        title: "Error",
        description: "Failed to load comments. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [entityType, entityId, toast]);
  useEffect(() => {
    loadComments();
  }, [loadComments]);
  const addComment = async (content: string) => {
    if (!user) {
      toast({
        title: "Authentication Error",
        description: "You must be logged in to add a comment",
        variant: "destructive",
      });
      throw new Error("You must be logged in to add a comment");
    }
    try {
      const newComment = await addCommentService(entityType, entityId, content, user.id);
      setComments(prev => [...prev, newComment as Comment]);
      toast({
        title: "Comment Added",
        description: "Your comment has been added successfully.",
      });
      return newComment as Comment;
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to add your comment. Please try again.",
        variant: "destructive",
      });
      throw err;
    }
  };
  const updateComment = async (commentId: string, content: string) => {
    try {
      await updateCommentService(commentId, content);
      setComments(prev =>
        prev.map(comment =>
          comment.id === commentId ? { ...comment, content, updatedAt: new Date() } : comment
        )
      );
      toast({
        title: "Comment Updated",
        description: "Your comment has been updated successfully.",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to update your comment. Please try again.",
        variant: "destructive",
      });
      throw err;
    }
  };
  const deleteComment = async (commentId: string) => {
    try {
      await deleteCommentService(commentId);
      setComments(prev => prev.filter(comment => comment.id !== commentId));
      toast({
        title: "Comment Deleted",
        description: "Your comment has been deleted successfully.",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to delete your comment. Please try again.",
        variant: "destructive",
      });
      throw err;
    }
  };
  return {
    comments,
    loading,
    error,
    addComment,
    updateComment,
    deleteComment,
    refresh: loadComments,
  };
};
