import * as React from "react"
import { pwaService } from "@/services/pwa-service"

/**
 * Hook for managing PWA installation
 */
export function usePWAInstall() {
  const [canInstall, setCanInstall] = React.useState(false)
  const [isInstalling, setIsInstalling] = React.useState(false)

  React.useEffect(() => {
    // Check initial install availability
    setCanInstall(pwaService.canInstall())

    // Listen for install prompt availability
    const handleBeforeInstallPrompt = () => {
      setCanInstall(true)
    }

    const handleAppInstalled = () => {
      setCanInstall(false)
      setIsInstalling(false)
    }

    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
    window.addEventListener('appinstalled', handleAppInstalled)

    return () => {
      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.removeEventListener('appinstalled', handleAppInstalled)
    }
  }, [])

  const install = React.useCallback(async () => {
    if (!canInstall || isInstalling) return false

    setIsInstalling(true)
    try {
      const result = await pwaService.install()
      if (result) {
        setCanInstall(false)
      }
      return result
    } finally {
      setIsInstalling(false)
    }
  }, [canInstall, isInstalling])

  return {
    canInstall,
    isInstalling,
    install
  }
}

/**
 * Hook for managing online/offline status
 */
export function useOnlineStatus() {
  const [isOnline, setIsOnline] = React.useState(navigator.onLine)

  React.useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  return isOnline
}

/**
 * Hook for managing offline data queue
 */
export function useOfflineQueue() {
  const [queue, setQueue] = React.useState(pwaService.getOfflineQueue())
  const [isSyncing, setIsSyncing] = React.useState(false)

  const addToQueue = React.useCallback((data: { type: 'risk' | 'incident' | 'comment', data: unknown }) => {
    pwaService.addToOfflineQueue(data)
    setQueue(pwaService.getOfflineQueue())
  }, [])

  const clearQueue = React.useCallback(() => {
    pwaService.clearOfflineQueue()
    setQueue([])
  }, [])

  React.useEffect(() => {
    const handleOnline = () => {
      setIsSyncing(true)
      // Sync will happen automatically via PWA service
      setTimeout(() => {
        setQueue(pwaService.getOfflineQueue())
        setIsSyncing(false)
      }, 2000)
    }

    window.addEventListener('online', handleOnline)
    return () => window.removeEventListener('online', handleOnline)
  }, [])

  return {
    queue,
    queueLength: queue.length,
    addToQueue,
    clearQueue,
    isSyncing
  }
}

/**
 * Hook for managing service worker updates
 */
export function useServiceWorkerUpdate() {
  const [updateAvailable, setUpdateAvailable] = React.useState(false)
  const [isUpdating, setIsUpdating] = React.useState(false)

  React.useEffect(() => {
    const handleUpdateAvailable = () => {
      setUpdateAvailable(true)
    }

    window.addEventListener('pwa-update-available', handleUpdateAvailable)
    return () => window.removeEventListener('pwa-update-available', handleUpdateAvailable)
  }, [])

  const applyUpdate = React.useCallback(async () => {
    setIsUpdating(true)
    try {
      await pwaService.updateServiceWorker()
    } finally {
      setIsUpdating(false)
    }
  }, [])

  const dismissUpdate = React.useCallback(() => {
    setUpdateAvailable(false)
  }, [])

  return {
    updateAvailable,
    isUpdating,
    applyUpdate,
    dismissUpdate
  }
}

/**
 * Hook for managing push notifications
 */
export function usePushNotifications() {
  const [permission, setPermission] = React.useState<NotificationPermission>('default')
  const [isRequesting, setIsRequesting] = React.useState(false)

  React.useEffect(() => {
    if ('Notification' in window) {
      setPermission(Notification.permission)
    }
  }, [])

  const requestPermission = React.useCallback(async () => {
    if (isRequesting || permission === 'granted') return permission

    setIsRequesting(true)
    try {
      const result = await pwaService.requestNotificationPermission()
      setPermission(result)
      return result
    } finally {
      setIsRequesting(false)
    }
  }, [permission, isRequesting])

  const showNotification = React.useCallback(async (title: string, options?: NotificationOptions) => {
    if (permission === 'granted') {
      await pwaService.showNotification(title, options)
    }
  }, [permission])

  return {
    permission,
    isRequesting,
    canShowNotifications: permission === 'granted',
    requestPermission,
    showNotification
  }
}

/**
 * Hook for detecting if app is running as PWA
 */
export function useIsPWA() {
  const [isPWA, setIsPWA] = React.useState(false)

  React.useEffect(() => {
    // Check if running in standalone mode (installed PWA)
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches
    const isIOSStandalone = (window.navigator as any).standalone === true
    
    setIsPWA(isStandalone || isIOSStandalone)
  }, [])

  return isPWA
}

/**
 * Comprehensive PWA hook that combines all PWA functionality
 */
export function usePWA() {
  const install = usePWAInstall()
  const isOnline = useOnlineStatus()
  const offlineQueue = useOfflineQueue()
  const serviceWorkerUpdate = useServiceWorkerUpdate()
  const pushNotifications = usePushNotifications()
  const isPWA = useIsPWA()

  return {
    // Installation
    canInstall: install.canInstall,
    isInstalling: install.isInstalling,
    installApp: install.install,

    // Online status
    isOnline,
    isOffline: !isOnline,

    // Offline queue
    offlineQueue: offlineQueue.queue,
    offlineQueueLength: offlineQueue.queueLength,
    addToOfflineQueue: offlineQueue.addToQueue,
    clearOfflineQueue: offlineQueue.clearQueue,
    isSyncingOfflineData: offlineQueue.isSyncing,

    // Service worker updates
    updateAvailable: serviceWorkerUpdate.updateAvailable,
    isUpdating: serviceWorkerUpdate.isUpdating,
    applyUpdate: serviceWorkerUpdate.applyUpdate,
    dismissUpdate: serviceWorkerUpdate.dismissUpdate,

    // Push notifications
    notificationPermission: pushNotifications.permission,
    canShowNotifications: pushNotifications.canShowNotifications,
    requestNotificationPermission: pushNotifications.requestPermission,
    showNotification: pushNotifications.showNotification,

    // PWA status
    isPWA,
    isWebApp: !isPWA
  }
}
