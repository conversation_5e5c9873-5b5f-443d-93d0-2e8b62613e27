
/**
 * Shared wizard types for multi-step forms and processes
 */

export interface WizardStep {
  id: number;
  title: string;
  description: string;
  isOptional?: boolean;
}

export interface WizardNavigationState {
  currentStep: number;
  totalSteps: number;
  canGoNext: boolean;
  canGoPrevious: boolean;
  isFirstStep: boolean;
  isLastStep: boolean;
}

export interface WizardNavigationActions {
  goToNext: () => void;
  goToPrevious: () => void;
  goToStep: (step: number) => void;
  reset: () => void;
}

export interface WizardValidation {
  validateStep?: (step: number) => boolean;
  getValidationMessage?: (step: number) => string | undefined;
}

export interface WizardCallbacks {
  onComplete?: () => void;
  onCancel?: () => void;
  onStepChange?: (step: number) => void;
}

export interface WizardContainerProps {
  steps: WizardStep[];
  currentStep: number;
  canProceed: boolean;
  isSubmitting?: boolean;
  onPrevious: () => void;
  onNext: () => void;
  onCancel?: () => void;
  nextButtonText?: string;
  validationHintText?: string;
  children: React.ReactNode;
}

// Hook-specific types
export interface UseWizardNavigationOptions {
  totalSteps: number;
  initialStep?: number;
  onComplete?: () => void;
  validateStep?: (step: number) => boolean;
}

export interface UseWizardNavigationResult {
  currentStep: number;
  canGoNext: boolean;
  canGoPrevious: boolean;
  isFirstStep: boolean;
  isLastStep: boolean;
  goToNext: () => void;
  goToPrevious: () => void;
  goToStep: (step: number) => void;
  reset: () => void;
}
