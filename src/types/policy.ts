
export interface Policy {
  id: string;
  title: string;
  description: string;
  category: string;
  version: string;
  status: string;
  effectiveDate?: string;
  documentUrl?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface PolicyRequest {
  id: string;
  title: string;
  description: string;
  reason: string;
  status: string;
  requesterId: string;
  reviewerId?: string;
  referenceDocumentUrl?: string;
  feedback?: string;
  createdAt: string;
  updatedAt: string;
}

import React from 'react';

export type PolicyCategory = {
  id: string;
  name: string;
  icon: React.ComponentType<{ className?: string }> | string;
};

export type PolicyFormValues = {
  title: string;
  description: string;
  category: string;
  effectiveDate?: Date | undefined;
  status: 'draft' | 'published' | 'archived';
  documentUrl?: string; // Added this property to fix the error
};

export type PolicyRequestFormValues = {
  title: string;
  category: string;
  description: string;
  justification: string;
};

// DB response interfaces with snake_case properties to match Supabase
export interface PolicyFromDB {
  id: string;
  title: string;
  description: string;
  category: string;
  version: string;
  status: string;
  effective_date?: string;
  document_url?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface PolicyRequestFromDB {
  id: string;
  title: string;
  description: string;
  reason: string;
  status: string;
  requester_id: string;
  reviewer_id?: string;
  reference_document_url?: string;
  feedback?: string;
  created_at: string;
  updated_at: string;
}
