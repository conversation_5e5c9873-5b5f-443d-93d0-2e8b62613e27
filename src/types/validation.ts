
/**
 * Shared form validation types and interfaces
 */

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings?: string[];
}

export interface FieldValidation<T = unknown> {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  customValidator?: (value: T) => ValidationResult;
}

export interface FormValidationState {
  isValid: boolean;
  errors: Record<string, string[]>;
  warnings: Record<string, string[]>;
  touched: Record<string, boolean>;
  isDirty: boolean;
}

export interface FormField<T = unknown> {
  name: string;
  value: T;
  validation?: FieldValidation<T>;
  isRequired?: boolean;
  isDisabled?: boolean;
}

export interface StepValidation {
  stepNumber: number;
  isValid: boolean;
  requiredFields: string[];
  errors: string[];
  canProceed: boolean;
}

export interface EmailValidationState {
  emailText: string;
  validEmails: string[];
  invalidEmails: string[];
  error: string;
  isValidating: boolean;
}

export interface RiskValidationRules {
  title: FieldValidation;
  description: FieldValidation;
  likelihood: FieldValidation;
  impact: FieldValidation;
  dueDate?: FieldValidation;
}

export interface ValidationMessage {
  type: 'error' | 'warning' | 'info';
  message: string;
  field?: string;
}

export interface ValidationContext {
  validateField: (fieldName: string, value: unknown) => ValidationResult;
  validateForm: () => FormValidationState;
  clearFieldError: (fieldName: string) => void;
  setFieldTouched: (fieldName: string, touched: boolean) => void;
}

// Hook-specific result types
export interface UseEmailValidationResult {
  emailText: string;
  validEmails: string[];
  invalidEmails: string[];
  error: string;
  setEmailText: (text: string) => void;
  validateEmails: () => boolean;
  resetValidation: () => void;
}
