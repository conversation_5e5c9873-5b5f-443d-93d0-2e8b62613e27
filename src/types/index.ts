// src/types/index.ts - Main types export with focused organization

// Re-export all focused type modules
export * from "./wizard";
export * from "./invitation";
export * from "./validation";
export * from "./risk";
export * from "./ui";
export * from "./organization";

// Core entity types
export interface Organization {
  id: string;
  name: string;
  slug: string;
  domain?: string;
  logoUrl?: string;
  subscriptionPlan: "free" | "starter" | "professional" | "enterprise";
  subscriptionStatus: "active" | "inactive" | "cancelled" | "past_due";
  maxUsers: number;
  maxRisks: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface OrganizationUser {
  id: string;
  organizationId: string;
  userId: string;
  role: "owner" | "admin" | "member";
  invitedBy?: string;
  invitedAt?: Date;
  joinedAt: Date;
  createdAt: Date;
}

export type User = {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  department?: string;
  avatar?: string;
  organizationId?: string;
};

export enum UserRole {
  ADMIN = "admin",
  RISK_OWNER = "risk_owner",
  STAFF = "staff",
  BOARD_MEMBER = "board_member",
}

export enum RiskStatus {
  IDENTIFIED = "Identified",
  IN_PROGRESS = "In Progress",
  MITIGATED = "Mitigated",
  ACCEPTED = "Accepted",
  CLOSED = "Closed",
}

export enum RiskSeverity {
  LOW = "Low",
  MEDIUM = "Medium",
  HIGH = "High",
  CRITICAL = "Critical",
}

// Invite code types - keeping this separate to avoid conflicts
export interface InviteCodeData {
  code: string;
  organizationId: string;
  role: UserRole;
  expiresAt?: Date;
  invitedEmail?: string;
}

export type MitigationAction = {
  id: string;
  riskId: string;
  organizationId: string;
  description: string;
  completed: boolean;
  createdAt: Date;
  updatedAt: Date;
};

export type ControlMeasure = {
  id: string;
  riskId: string;
  organizationId: string;
  description: string;
  effectiveness?: "High" | "Medium" | "Low";
  implemented: boolean;
  createdAt: Date;
  updatedAt: Date;
};

// Error handling types
export enum ErrorSeverity {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  CRITICAL = "critical",
}

export enum ErrorCategory {
  VALIDATION = "validation",
  NETWORK = "network",
  AUTHENTICATION = "authentication",
  AUTHORIZATION = "authorization",
  BUSINESS_LOGIC = "business_logic",
  SYSTEM = "system",
  USER_INPUT = "user_input",
  DATA_PROCESSING = "data_processing",
  EXTERNAL_SERVICE = "external_service",
}

export interface ErrorContext {
  userId?: string;
  organizationId?: string;
  component: string;
  action?: string;
  timestamp?: Date;
  userAgent?: string;
  url?: string;
  riskId?: string;
  additionalData?: Record<string, any>;
}

export interface ErrorMetadata {
  id: string;
  category: ErrorCategory;
  severity: ErrorSeverity;
  context: ErrorContext;
  stack?: string;
  recoverable: boolean;
  userMessage: string;
  technicalMessage: string;
  suggestedActions?: string[];
}

export type Risk = {
  id: string;
  title: string;
  description: string;
  category?: string;
  categoryId?: string;
  ownerId: string;
  ownerName?: string;
  organizationId: string;
  createdAt: Date;
  updatedAt: Date;

  // Inherent risk properties (risk without controls)
  inherentLikelihood: number;
  inherentImpact: number;
  inherentSeverity: RiskSeverity;

  // Residual risk properties (risk after controls are applied)
  likelihood: number;
  impact: number;
  severity: RiskSeverity;

  status: RiskStatus;
  currentControls?: string;
  controlMeasures?: ControlMeasure[];
  mitigationApproach?: string;
  mitigationActions?: MitigationAction[];
  dueDate?: Date;
  relatedIncidents?: string[];
};

export type RiskTemplate = {
  id: string;
  name: string;
  description: string;
  category?: string;
  categoryId?: string;
  organizationId: string;
  defaultLikelihood: number;
  defaultImpact: number;
  suggestedMitigationPlan?: string;
  createdAt: Date;
  createdBy: string;
};

export type Incident = {
  id: string;
  title: string;
  description: string;
  reporterId: string;
  reporterName?: string;
  organizationId: string;
  date: Date;
  status: "Open" | "Investigating" | "Resolved" | "Closed";
  severity: RiskSeverity;
  relatedRiskId?: string;
  relatedRiskTitle?: string;
};

export type DashboardMetrics = {
  totalRisks: number;
  risksByCategory: Record<string, number>;
  risksBySeverity: Record<RiskSeverity, number>;
  risksByStatus: Record<RiskStatus, number>;
  recentIncidents: Incident[];
  upcomingMitigations: Risk[];
};

export type Comment = {
  id: string;
  content: string;
  entityType: "risk" | "incident";
  entityId: string;
  entityTitle?: string;
  userId: string;
  organizationId: string;
  userName?: string;
  userAvatar?: string;
  createdAt: Date;
  updatedAt: Date;
  deletedAt?: Date | null;
};

export type RiskHistoryEntry = {
  id: string;
  risk_id: string;
  organizationId: string;
  recorded_at: Date;
  likelihood: number;
  impact: number;
  severity: RiskSeverity;
  status: RiskStatus;
  created_at?: Date;
};
