/**
 * Risk-specific types and interfaces
 */

import { RiskSeverity, RiskStatus, UserRole } from "./index";

export interface RiskAssessmentData {
  likelihood: number;
  impact: number;
  severity: RiskSeverity;
}

export interface InherentRiskData extends RiskAssessmentData {
  // Inherent risk is the same structure as risk assessment
}

export interface ResidualRiskData extends RiskAssessmentData {
  // Residual risk is the same structure as risk assessment
}

export interface RiskFormStep1Data {
  title: string;
  description: string;
  category: string;
  categoryId: string;
  ownerId: string;
}

export interface RiskFormStep2Data {
  inherentLikelihood: number;
  inherentImpact: number;
  inherentSeverity: RiskSeverity;
}

export interface RiskFormStep3Data {
  controlMeasures: ControlMeasureData[];
}

export interface RiskFormStep4Data {
  likelihood: number;
  impact: number;
  severity: RiskSeverity;
}

export interface RiskFormStep5Data {
  status: RiskStatus;
  mitigationApproach: string;
  mitigationActions: MitigationActionData[];
  dueDate: Date | null;
}

export interface ControlMeasureData {
  description: string;
  effectiveness: "High" | "Medium" | "Low";
  implemented: boolean;
}

export interface MitigationActionData {
  description: string;
  completed: boolean;
}

export interface RiskWizardFormData
  extends RiskFormStep1Data,
    RiskFormStep2Data,
    RiskFormStep3Data,
    RiskFormStep4Data,
    RiskFormStep5Data {
  // Implementation needed
}
export interface RiskCalculationOptions {
  initialLikelihood?: number;
  initialImpact?: number;
  autoCalculate?: boolean;
}

export interface RiskCalculationResult {
  likelihood: number;
  impact: number;
  severity: RiskSeverity;
  severityColor: string;
  severityPriority: number;
}

export interface RiskAssessmentOptions {
  initialData?: Partial<RiskAssessmentData>;
  onChange?: (data: RiskAssessmentData) => void;
}

// Hook-specific types
export interface UseRiskCalculationsOptions {
  initialLikelihood?: number;
  initialImpact?: number;
  autoCalculate?: boolean;
}

export interface UseRiskCalculationsResult {
  likelihood: number;
  impact: number;
  severity: RiskSeverity;
  severityColor: string;
  severityPriority: number;
  setLikelihood: (value: number) => void;
  setImpact: (value: number) => void;
  recalculate: () => void;
  reset: () => void;
}

export interface UseRiskAssessmentOptions {
  initialData?: Partial<RiskAssessmentData>;
  onChange?: (data: RiskAssessmentData) => void;
}

export interface UseRiskAssessmentResult {
  likelihood: number;
  impact: number;
  severity: RiskSeverity;
  severityColor: string;
  setLikelihood: (value: number) => void;
  setImpact: (value: number) => void;
  updateAssessment: (data: Partial<RiskAssessmentData>) => void;
  getAssessmentData: () => RiskAssessmentData;
}
