
/**
 * UI component and state types
 */

import React from 'react';

export interface LoadingState {
  isLoading: boolean;
  loadingText?: string;
  progress?: number;
}

export interface ErrorState {
  hasError: boolean;
  error?: Error | string;
  errorCode?: string;
}

export interface AsyncState<T = unknown> extends LoadingState, ErrorState {
  data?: T;
  lastUpdated?: Date;
}

export interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface SortState {
  field: string;
  direction: 'asc' | 'desc';
}

export interface FilterState {
  [key: string]: string | number | boolean | string[] | number[] | null | undefined;
}

export interface TableState {
  pagination: PaginationState;
  sorting: SortState;
  filters: FilterState;
}

export interface ModalState {
  isOpen: boolean;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export interface ToastMessage {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  description?: string;
  duration?: number;
}

export interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon?: React.ComponentType;
  isActive?: boolean;
  isDisabled?: boolean;
  children?: NavigationItem[];
}

export interface StepperProps {
  steps: Array<{
    id: number;
    title: string;
    description?: string;
    isCompleted?: boolean;
    isActive?: boolean;
  }>;
  currentStep: number;
  orientation?: 'horizontal' | 'vertical';
}
