
/**
 * Shared invitation and user management types
 */

import { UserRole } from "./index";

export interface EmailValidation {
  valid: string[];
  invalid: string[];
  duplicates?: string[];
}

export interface EmailWithRole {
  email: string;
  role: UserRole;
}

export interface InvitationResult {
  email: string;
  success: boolean;
  error?: string;
  inviteCode?: string;
}

export interface BulkInviteRequest {
  emails: string[];
  role: UserRole;
  organizationId: string;
  invitedBy: string;
}

export interface UserInvitation {
  id: string;
  email: string;
  role: UserRole;
  status: 'pending' | 'accepted' | 'expired' | 'revoked';
  organizationId: string;
  invitedBy: string;
  inviteCodeId?: string;
  createdAt: Date;
  acceptedAt?: Date;
  emailSentAt?: Date;
  emailOpenedAt?: Date;
}

export interface InvitationManagementState {
  pendingInvitations: UserInvitation[];
  acceptedInvitations: UserInvitation[];
  expiredInvitations: UserInvitation[];
  isLoading: boolean;
  error: string | null;
}

export interface BulkInviteStepData {
  emailText: string;
  validEmails: string[];
  invalidEmails: string[];
  emailsWithRoles: EmailWithRole[];
  results: InvitationResult[];
}

export type BulkInviteStep = 'input' | 'assign-roles' | 'results';
