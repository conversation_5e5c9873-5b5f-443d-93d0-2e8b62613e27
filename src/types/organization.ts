
export interface OrganizationSetupData {
  organizationName: string;
  organizationSize: '1-5' | '6-25' | '26-100' | '101-500' | '500+';
  sectorType: 'health' | 'education' | 'social_services' | 'environment' | 'arts_culture' | 
              'community' | 'advocacy' | 'religious' | 'youth' | 'seniors' | 'housing' | 
              'disaster' | 'international' | 'animal' | 'other';
  sectorDescription?: string;
}

export interface InviteCodeData {
  code: string;
  targetOrganizationId: string;
  userRole: string;
}

export interface SignupChoice {
  type: 'create-organization' | 'join-invite';
  inviteCode?: string;
  organizationData?: OrganizationSetupData;
}
