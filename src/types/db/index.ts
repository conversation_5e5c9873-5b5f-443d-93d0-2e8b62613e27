// src/types/db/index.ts - Complete rewritten file
// Database type definitions that directly map to database tables
// These use snake_case to match database column names

// Organization related database types
export interface DbOrganization {
  id: string;
  name: string;
  slug: string;
  domain?: string | null;
  logo_url?: string | null;
  subscription_plan: string;
  subscription_status: string;
  max_users: number;
  max_risks: number;
  created_at: string;
  updated_at: string;
}

export interface DbOrganizationUser {
  id: string;
  organization_id: string;
  user_id: string;
  role: string;
  invited_by?: string | null;
  invited_at?: string | null;
  joined_at: string;
  created_at: string;
}

// Main entity database types
export interface DbRisk {
  id: string;
  title: string;
  description: string;
  category_id?: string;
  owner_id?: string;
  organization_id: string;
  created_at: string;
  updated_at: string;
  likelihood: number;
  impact: number;
  severity: string;
  status: string;
  current_controls?: string;
  mitigation_approach?: string;
  due_date?: string | null;
  created_by: string;
  template_id?: string | null;
  
  // Inherent risk fields
  inherent_likelihood?: number;
  inherent_impact?: number;
  inherent_severity?: string;
}

export interface DbControlMeasure {
  id: string;
  risk_id: string;
  organization_id: string;
  description: string;
  effectiveness?: string | null;
  implemented: boolean;
  created_at: string;
  updated_at: string;
}

export interface DbMitigationAction {
  id: string;
  risk_id: string;
  organization_id: string;
  description: string;
  completed: boolean;
  created_at: string;
  updated_at: string;
}

export interface DbRiskCategory {
  id: string;
  name: string;
  description?: string;
  organization_id: string;
  created_at: string;
  updated_at: string;
}

export interface DbProfile {
  id: string;
  name: string;
  email: string;
  role: string;
  department?: string | null;
  avatar_url?: string | null;
  organization_id?: string | null;
  created_at: string;
  updated_at: string;
  deleted_at?: string | null;
}

export type DbRiskHistory = {
  id: string;
  risk_id: string;
  organization_id: string;
  recorded_at: string;
  likelihood: number;
  impact: number;
  severity: string;
  status: string;
  created_by: string | null;
};