/**
 * API Response and Error Types
 * Comprehensive type definitions for all API responses
 */

// Supabase-specific response types
export interface SupabaseResponse<T = unknown> {
  data: T | null;
  error: SupabaseError | null;
  count?: number | null;
  status: number;
  statusText: string;
}

export interface SupabaseError {
  message: string;
  code?: string;
  details?: string;
  hint?: string;
}

// Generic API Response wrapper
export interface ApiResponse<T = unknown> {
  data: T;
  error: null;
  status: number;
  message?: string;
  timestamp?: string;
}

export interface ApiError {
  data: null;
  error: {
    message: string;
    code?: string;
    details?: Record<string, unknown>;
    stack?: string;
  };
  status: number;
  timestamp?: string;
}

export type ApiResult<T = unknown> = ApiResponse<T> | ApiError;

// Service operation results with enhanced error information
export interface ServiceResult<T = unknown> {
  success: boolean;
  data?: T;
  error?: ServiceError;
  metadata?: ServiceMetadata;
}

export interface ServiceError {
  message: string;
  code?: string;
  category?:
    | "validation"
    | "network"
    | "authentication"
    | "authorization"
    | "business_logic"
    | "system";
  details?: Record<string, unknown>;
  recoverable?: boolean;
}

export interface ServiceMetadata {
  requestId?: string;
  timestamp?: string;
  duration?: number;
  retryCount?: number;
}

// Database operation results
export interface DatabaseResult<T = unknown> {
  data: T | null;
  error: {
    message: string;
    code?: string;
    hint?: string;
    details?: string;
  } | null;
  status: number;
  count?: number;
}

// Form submission results
export interface FormSubmissionResult {
  success: boolean;
  data?: unknown;
  errors?: {
    field?: string;
    message: string;
  }[];
  validationErrors?: Record<string, string[]>;
}

// Chart/Graph data types
export interface ChartDataPoint {
  name: string;
  value: number;
  color?: string;
  label?: string;
}

export interface TimeSeriesDataPoint {
  date: string | Date;
  value: number;
  category?: string;
}

// Generic component props that accept unknown data
export interface GenericComponentProps {
  data?: unknown;
  options?: Record<string, unknown>;
  className?: string;
  children?: React.ReactNode;
}

// Event handler types
export interface CustomEventHandler<T = HTMLElement> {
  (event: React.SyntheticEvent<T>, data?: unknown): void;
}

// External library integration types
export interface ExternalLibraryConfig {
  [key: string]: unknown;
}

// Filter and search types
export interface FilterValue {
  field: string;
  operator: "eq" | "neq" | "in" | "nin" | "gt" | "gte" | "lt" | "lte" | "like" | "ilike";
  value: string | number | boolean | Array<string | number>;
}

export interface SearchFilters {
  filters: FilterValue[];
  sortBy?: string;
  sortOrder?: "asc" | "desc";
  limit?: number;
  offset?: number;
}

// Type guards for API responses
export function isApiError<T>(result: ApiResult<T>): result is ApiError {
  return result.error !== null;
}

export function isApiSuccess<T>(result: ApiResult<T>): result is ApiResponse<T> {
  return result.error === null;
}

export function isDatabaseError<T>(
  result: DatabaseResult<T>
): result is DatabaseResult<T> & { error: NonNullable<DatabaseResult<T>["error"]> } {
  return result.error !== null;
}

export function isDatabaseSuccess<T>(
  result: DatabaseResult<T>
): result is DatabaseResult<T> & { data: NonNullable<DatabaseResult<T>["data"]> } {
  return result.error === null && result.data !== null;
}
// Specific API response types for different domains

// Risk-related API responses
export interface RiskApiResponse {
  id: string;
  title: string;
  description: string;
  category_id?: string;
  owner_id: string;
  organization_id: string;
  created_at: string;
  updated_at: string;
  inherent_likelihood: number;
  inherent_impact: number;
  inherent_severity: string;
  likelihood: number;
  impact: number;
  severity: string;
  status: string;
  current_controls?: string;
  mitigation_approach?: string;
  due_date?: string;
  created_by: string;
  template_id?: string;
  profiles?: { name: string };
  categories?: { name: string };
}

export interface CreateRiskRequest {
  title: string;
  description: string;
  category_id?: string;
  inherent_likelihood: number;
  inherent_impact: number;
  inherent_severity: string;
  likelihood: number;
  impact: number;
  severity: string;
  status: string;
  mitigation_approach?: string;
  due_date?: string;
  owner_id: string;
  organization_id: string;
  template_id?: string;
}

export interface UpdateRiskRequest extends Partial<CreateRiskRequest> {
  updated_at?: string;
}

// Incident-related API responses
export interface IncidentApiResponse {
  id: string;
  title: string;
  description: string;
  reporter_id: string;
  organization_id: string;
  date: string;
  status: string;
  severity: string;
  related_risk_id?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateIncidentRequest {
  title: string;
  description: string;
  severity: string;
  status: string;
  reporter_id: string;
  organization_id: string;
  related_risk_id?: string;
  date: string;
}

export interface UpdateIncidentRequest extends Partial<CreateIncidentRequest> {
  updated_at?: string;
}

// Policy-related API responses
export interface PolicyApiResponse {
  id: string;
  title: string;
  description: string;
  category: string;
  status: string;
  version: string;
  created_by: string;
  organization_id: string;
  effective_date?: string;
  document_url?: string;
  created_at: string;
  updated_at: string;
}

export interface PolicyRequestApiResponse {
  id: string;
  title: string;
  description: string;
  reason: string;
  status: string;
  requester_id: string;
  reviewer_id?: string;
  organization_id: string;
  reference_document_url?: string;
  feedback?: string;
  created_at: string;
  updated_at: string;
}

export interface CreatePolicyRequest {
  title: string;
  description: string;
  category: string;
  status: string;
  created_by: string;
  organization_id: string;
  effective_date?: string;
  version?: string;
}

export interface CreatePolicyRequestRequest {
  title: string;
  description: string;
  reason: string;
  requester_id: string;
  organization_id: string;
  reference_document_url?: string;
}

// User and organization API responses
export interface UserProfileApiResponse {
  id: string;
  name: string;
  email: string;
  role: string;
  department?: string;
  avatar_url?: string;
  organization_id?: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

export interface OrganizationApiResponse {
  id: string;
  name: string;
  slug: string;
  domain?: string;
  logo_url?: string;
  subscription_plan?: string;
  subscription_status?: string;
  max_users?: number;
  max_risks?: number;
  organization_size?: string;
  sector_type?: string;
  sector_description?: string;
  created_at: string;
  updated_at: string;
}

export interface UserInvitationApiResponse {
  id: string;
  email: string;
  role: string;
  status: string;
  invite_code_id?: string;
  email_sent_at?: string;
  email_opened_at?: string;
  accepted_at?: string;
  created_at: string;
  updated_at: string;
  invited_by?: string;
  organization_id: string;
  user_id?: string;
}

// Control measures and mitigation actions
export interface ControlMeasureApiResponse {
  id: string;
  risk_id: string;
  organization_id: string;
  description: string;
  effectiveness?: string;
  implemented: boolean;
  created_at: string;
  updated_at: string;
}

export interface MitigationActionApiResponse {
  id: string;
  risk_id: string;
  organization_id: string;
  description: string;
  completed: boolean;
  created_at: string;
  updated_at: string;
}

// Comment API responses
export interface CommentApiResponse {
  id: string;
  content: string;
  entity_type: string;
  entity_id: string;
  user_id: string;
  organization_id: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string;
}

// Risk history API responses
export interface RiskHistoryApiResponse {
  id: string;
  risk_id: string;
  organization_id: string;
  recorded_at: string;
  likelihood: number;
  impact: number;
  severity: string;
  status: string;
  created_by?: string;
}

// Type guards for service results
export function isServiceSuccess<T>(
  result: ServiceResult<T>
): result is ServiceResult<T> & { success: true; data: T } {
  return result.success === true;
}

export function isServiceError<T>(
  result: ServiceResult<T>
): result is ServiceResult<T> & { success: false; error: ServiceError } {
  return result.success === false && result.error !== undefined;
}

// Specific type guards for operations that return data
export function isServiceSuccessWithData<T>(
  result: ServiceResult<T>
): result is ServiceResult<T> & { success: true; data: T } {
  return result.success === true && result.data !== undefined;
}

// Type guard for void operations (update, delete)
export function isServiceSuccessVoid(
  result: ServiceResult<void>
): result is ServiceResult<void> & { success: true } {
  return result.success === true;
}

export function isSupabaseError<T>(
  result: SupabaseResponse<T>
): result is SupabaseResponse<T> & { error: SupabaseError } {
  return result.error !== null;
}

export function isSupabaseSuccess<T>(
  result: SupabaseResponse<T>
): result is SupabaseResponse<T> & { data: T } {
  return result.error === null && result.data !== null;
}

// Runtime validation helpers
export function validateApiResponse<T>(
  data: unknown,
  validator: (data: unknown) => data is T
): ServiceResult<T> {
  if (validator(data)) {
    return {
      success: true,
      data,
      metadata: {
        timestamp: new Date().toISOString(),
      },
    };
  }

  return {
    success: false,
    error: {
      message: "Invalid API response format",
      code: "VALIDATION_ERROR",
      category: "validation",
      recoverable: false,
    },
  };
}

// Error transformation utilities
export function transformSupabaseError(error: SupabaseError): ServiceError {
  const category = getErrorCategory(error.code);
  return {
    message: error.message,
    code: error.code ?? "UNKNOWN_ERROR",
    category: category,
    details: {
      hint: error.hint,
      details: error.details,
    },
    recoverable: isRecoverableError(error.code),
  };
}

function getErrorCategory(code?: string): NonNullable<ServiceError["category"]> {
  if (!code) return "system";

  if (code.startsWith("PGRST")) return "network";
  if (code.includes("auth")) return "authentication";
  if (code.includes("permission") || code.includes("access")) return "authorization";
  if (code.includes("validation") || code.includes("constraint")) return "validation";

  return "system";
}

function isRecoverableError(code?: string): boolean {
  if (!code) return false;

  const recoverableCodes = ["PGRST301", "PGRST116", "network_error", "timeout"];
  return recoverableCodes.some(recoverable => code.includes(recoverable));
}
