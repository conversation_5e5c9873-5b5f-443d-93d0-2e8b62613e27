
import { createLazyRoute, ROUTE_GROUPS } from '@/utils/lazy-routes';

// ============================================================================
// CRITICAL ROUTES - Loaded immediately with preloading
// ============================================================================

export const LazyIndex = createLazyRoute(
  () => import('@/pages/Index.tsx'),
  { ...ROUTE_GROUPS.CRITICAL, routeName: 'index' }
);

export const LazyDashboard = createLazyRoute(
  () => import('@/pages/Dashboard'),
  { ...ROUTE_GROUPS.CRITICAL, routeName: 'dashboard' }
);

// ============================================================================
// AUTHENTICATION ROUTES
// ============================================================================

export const LazyLogin = createLazyRoute(
  () => import('@/pages/Login'),
  { ...ROUTE_GROUPS.AUTH, routeName: 'login' }
);

export const LazySignup = createLazyRoute(
  () => import('@/pages/Signup'),
  { ...ROUTE_GROUPS.AUTH, routeName: 'signup' }
);

// ============================================================================
// RISK MANAGEMENT ROUTES
// ============================================================================

export const LazyRiskRegister = createLazyRoute(
  () => import('@/pages/RiskRegister'),
  { ...ROUTE_GROUPS.RISK, routeName: 'risk-register' }
);

export const LazyRiskCreate = createLazyRoute(
  () => import('@/pages/RiskCreate'),
  { ...ROUTE_GROUPS.RISK, routeName: 'risk-create' }
);

export const LazyRiskDetails = createLazyRoute(
  () => import('@/pages/RiskDetails'),
  { ...ROUTE_GROUPS.RISK, routeName: 'risk-details' }
);

export const LazyRiskTemplates = createLazyRoute(
  () => import('@/pages/RiskTemplates'),
  { ...ROUTE_GROUPS.RISK, routeName: 'risk-templates' }
);

// ============================================================================
// INCIDENT MANAGEMENT ROUTES
// ============================================================================

export const LazyIncidents = createLazyRoute(
  () => import('@/pages/Incidents'),
  { ...ROUTE_GROUPS.INCIDENT, routeName: 'incidents' }
);

export const LazyIncidentCreate = createLazyRoute(
  () => import('@/pages/IncidentCreate'),
  { ...ROUTE_GROUPS.INCIDENT, routeName: 'incident-create' }
);

export const LazyIncidentEdit = createLazyRoute(
  () => import('@/pages/IncidentEdit'),
  { ...ROUTE_GROUPS.INCIDENT, routeName: 'incident-edit' }
);

export const LazyIncidentDetails = createLazyRoute(
  () => import('@/pages/IncidentDetails'),
  { ...ROUTE_GROUPS.INCIDENT, routeName: 'incident-details' }
);

// ============================================================================
// POLICY ROUTES
// ============================================================================

export const LazyPolicies = createLazyRoute(
  () => import('@/pages/Policies'),
  { ...ROUTE_GROUPS.POLICY, routeName: 'policies' }
);

// ============================================================================
// ADMINISTRATION ROUTES
// ============================================================================

export const LazyAdministration = createLazyRoute(
  () => import('@/pages/Administration'),
  { ...ROUTE_GROUPS.ADMIN, routeName: 'administration' }
);

export const LazyOrganizationManagement = createLazyRoute(
  () => import('@/pages/OrganizationManagement'),
  { ...ROUTE_GROUPS.ADMIN, routeName: 'organization-management' }
);

export const LazyOrganizationPage = createLazyRoute(
  () => import('@/pages/OrganizationPage'),
  { ...ROUTE_GROUPS.ADMIN, routeName: 'organization' }
);

// ============================================================================
// REPORT ROUTES
// ============================================================================

export const LazyReports = createLazyRoute(
  () => import('@/pages/Reports'),
  { ...ROUTE_GROUPS.REPORTS, routeName: 'reports' }
);

// ============================================================================
// USER ROUTES
// ============================================================================

export const LazyProfile = createLazyRoute(
  () => import('@/pages/Profile'),
  { ...ROUTE_GROUPS.USER, routeName: 'profile' }
);

// ============================================================================
// ERROR ROUTES
// ============================================================================

export const LazyNotFound = createLazyRoute(
  () => import('@/pages/NotFound'),
  { ...ROUTE_GROUPS.MAIN, routeName: 'not-found' }
);
