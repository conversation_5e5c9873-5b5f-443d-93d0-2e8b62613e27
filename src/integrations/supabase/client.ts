// This file is automatically generated. Do not edit it directly.
import { createClient } from "@supabase/supabase-js";
import type { Database } from "./types";
/**
 * SECURITY NOTE: Hybrid Credential Approach
 *
 * - SUPABASE_URL: Configurable via environment variable for flexibility
 * - SUPABASE_PUBLISHABLE_KEY: Hardcoded as it's the "anon" key designed to be public
 *
 * The anon key is safe to expose because:
 * 1. It's designed by Supabase to be client-side
 * 2. All data access is protected by Row Level Security (RLS) policies
 * 3. It has limited permissions (only what <PERSON><PERSON> allows)
 * 4. It cannot access admin functions or bypass security
 *
 * This approach provides environment flexibility while avoiding deployment complications.
 */
// Supabase URL - configurable via environment variable for different environments
const SUPABASE_URL =
  import.meta.env.VITE_SUPABASE_URL ?? "https://alieiaxzqyxjceitkoqx.supabase.co";
// Supabase anon key - safe to hardcode as it's designed to be public
// Protected by Row Level Security (RLS) policies in the database
const SUPABASE_PUBLISHABLE_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFsaWVpYXh6cXl4amNlaXRrb3F4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDYyMjUwNDksImV4cCI6MjA2MTgwMTA0OX0.SZX4qPcWLtzEbHYEmXcpWFShz5eJrrdRoliPEzsFPjU";
// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
/**
 * Enhanced cleanup for Chrome compatibility
 */
export const cleanupAuthState = () => {
  try {
    // Only log in development mode
    if (import.meta.env.MODE === "development") {
      // Condition handled
    }
    // Remove standard auth tokens
    localStorage.removeItem("supabase.auth.token");
    // Get all localStorage keys first to avoid modification during iteration
    const localStorageKeys: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (key.startsWith("supabase.auth.") || key.includes("sb-"))) {
        localStorageKeys.push(key);
      }
    }
    // Remove all Supabase auth keys from localStorage
    localStorageKeys.forEach(key => {
      if (import.meta.env.MODE === "development") {
        // Condition handled
      }
      localStorage.removeItem(key);
    });
    // Remove from sessionStorage if available
    if (typeof sessionStorage !== "undefined") {
      const sessionStorageKeys: string[] = [];
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        if (key && (key.startsWith("supabase.auth.") || key.includes("sb-"))) {
          sessionStorageKeys.push(key);
        }
      }
      sessionStorageKeys.forEach(key => {
        if (import.meta.env.MODE === "development") {
          // Condition handled
        }
        sessionStorage.removeItem(key);
      });
    }
    // Force clear any remaining Supabase client state
    try {
      // Clear any cached auth state in the Supabase client
      supabase.auth.signOut({ scope: "local" });
    } catch (error) {
      if (import.meta.env.MODE === "development") {
        // Condition handled
      }
    }
    if (import.meta.env.MODE === "development") {
      // Condition handled
    }
  } catch (error) {
    if (import.meta.env.MODE === "development") {
      // Condition handled
    }
  }
};
