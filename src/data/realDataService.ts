import { supabase } from "@/integrations/supabase/client";
/**
 * Service for fetching real data from Supabase
 * This replaces the mock data usage throughout the application
 */
export const realDataService = {
  /**
   * Fetch all categories from the database
   */
  async getCategories() {
    const { data, error } = await supabase.from("risk_categories").select("*").order("name");
    if (error) {
      throw error;
    }
    return data ?? [];
  },
  /**
   * Fetch all users/profiles from the database
   */
  async getUsers() {
    const { data, error } = await supabase
      .from("profiles")
      .select("*")
      .is("deleted_at", null)
      .order("name");
    if (error) {
      throw error;
    }
    return (data ?? []).map(profile => ({
      id: profile.id,
      name: profile.name,
      email: profile.email,
      role: profile.role,
      department: profile.department,
      avatar: profile.avatar_url,
      organizationId: profile.organization_id,
    }));
  },
  /**
   * Fetch risk templates from the database
   */
  async getRiskTemplates() {
    const { data, error } = await supabase
      .from("risk_templates")
      .select(
        `
        *,
        categories:risk_categories(name)
      `
      )
      .order("name");
    if (error) {
      throw error;
    }
    return (data ?? []).map(template => ({
      id: template.id,
      name: template.name,
      description: template.description,
      category: template.categories?.name,
      categoryId: template.category_id,
      organizationId: template.organization_id,
      defaultLikelihood: template.default_likelihood,
      defaultImpact: template.default_impact,
      suggestedMitigationPlan: template.suggested_mitigation_plan,
      createdAt: new Date(template.created_at),
      createdBy: template.created_by,
    }));
  },
};
