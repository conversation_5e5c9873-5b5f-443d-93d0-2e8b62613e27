

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/auth";
import { Building, ChevronDown, Users, Crown, Shield } from "lucide-react";

export function OrganizationSwitcher() {
  const { organization, organizationRole, user } = useAuth();

  // Don't render if no organization is loaded
  if (!organization || !user) {
    return null;
  }

  const getRoleIcon = () => {
    switch (organizationRole) {
      case 'owner':
        return <Crown className="h-3 w-3" />;
      case 'admin':
        return <Shield className="h-3 w-3" />;
      case 'member':
        return <Users className="h-3 w-3" />;
      default:
        return <Users className="h-3 w-3" />;
    }
  };

  const getRoleColor = () => {
    switch (organizationRole) {
      case 'owner':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'admin':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'member':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPlanBadgeColor = () => {
    switch (organization.subscriptionPlan) {
      case 'enterprise':
        return 'bg-purple-100 text-purple-800';
      case 'professional':
        return 'bg-blue-100 text-blue-800';
      case 'starter':
        return 'bg-green-100 text-green-800';
      case 'free':
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          className="w-full justify-between min-w-[200px] max-w-[280px]"
          disabled={false}
        >
          <div className="flex items-center gap-2 truncate">
            <Building className="h-4 w-4 flex-shrink-0" />
            <span className="truncate">{organization.name}</span>
          </div>
          <ChevronDown className="h-4 w-4 flex-shrink-0" />
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent align="start" className="w-[320px]">
        <DropdownMenuLabel className="flex items-center justify-between pb-2">
          <span>Current Organization</span>
          <div className="flex items-center gap-1">
            {getRoleIcon()}
            <Badge variant="outline" className={`text-xs ${getRoleColor()}`}>
              {organizationRole}
            </Badge>
          </div>
        </DropdownMenuLabel>
        
        <DropdownMenuItem className="flex items-start gap-3 p-3 cursor-default">
          <Building className="h-5 w-5 mt-0.5 text-muted-foreground" />
          <div className="flex-1 min-w-0">
            <div className="font-medium truncate">{organization.name}</div>
            <div className="text-xs text-muted-foreground mt-1">
              {organization.maxUsers} max users • {organization.maxRisks} max risks
            </div>
            <div className="flex items-center gap-2 mt-2">
              <Badge className={`text-xs ${getPlanBadgeColor()}`}>
                {organization.subscriptionPlan}
              </Badge>
              <Badge variant="outline" className={`text-xs ${
                organization.subscriptionStatus === 'active' 
                  ? 'bg-green-50 text-green-700 border-green-200' 
                  : 'bg-red-50 text-red-700 border-red-200'
              }`}>
                {organization.subscriptionStatus}
              </Badge>
            </div>
          </div>
        </DropdownMenuItem>
        
        <DropdownMenuSeparator />
        
        {/* Future: List other organizations user has access to */}
        <DropdownMenuLabel className="text-xs text-muted-foreground py-1">
          Switch Organizations
        </DropdownMenuLabel>
        
        <DropdownMenuItem disabled className="text-muted-foreground text-sm">
          <Users className="h-4 w-4 mr-2" />
          Join another organization (Coming Soon)
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
