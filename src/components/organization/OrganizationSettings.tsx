import { useState } from "react";
import { useAuth } from "@/contexts/auth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/components/ui/use-toast";
import { useQuery } from "@tanstack/react-query";
import { fetchUsers } from "@/services/user";
import { UserManagementTabs } from "@/components/user/UserManagementTabs";
import { User } from "@/types";
import {
  Settings,
  CreditCard,
  Crown,
  Shield,
  User as UserIcon,
  Save,
  AlertCircle,
} from "lucide-react";
export function OrganizationSettings() {
  const { organization, organizationRole, user } = useAuth();
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  // Form states
  const [orgName, setOrgName] = useState(organization?.name ?? "");
  // Permission check
  const canManageOrg = organizationRole === "owner" || organizationRole === "admin";
  // Fetch users data for members tab
  const {
    data: usersData,
    isLoading: usersLoading,
    refetch: refetchUsers,
  } = useQuery({
    queryKey: ["organization-users"],
    queryFn: async () => {
      const { data, error } = await fetchUsers();
      if (error) {
        throw error;
      }
      return data ?? [];
    },
  });
  if (!organization || !user) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <AlertCircle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h2 className="text-2xl font-bold mb-2">Organization Not Found</h2>
          <p className="text-muted-foreground">You don't seem to be part of an organization yet.</p>
        </div>
      </div>
    );
  }
  if (!canManageOrg) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">
          <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h2 className="text-2xl font-bold mb-2">Access Restricted</h2>
          <p className="text-muted-foreground">
            Only organization owners and administrators can manage organization settings.
          </p>
        </div>
      </div>
    );
  }
  const handleSaveBasicInfo = async () => {
    if (!orgName.trim()) {
      toast({
        title: "Error",
        description: "Organization name cannot be empty",
        variant: "destructive",
      });
      return;
    }
    setIsSaving(true);
    try {
      // TODO: Implement organization update API call
      // For now, just simulate saving
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast({
        title: "Settings Saved",
        description: "Organization settings have been updated successfully.",
      });
    } catch {
      toast({
        title: "Error",
        description: "Failed to save organization settings",
        variant: "destructive",
      });
    } finally {
      setIsSaving(false);
    }
  };
  const handleUserUpdated = () => {
    refetchUsers();
  };
  const getRoleIcon = (role: string) => {
    switch (role) {
      case "owner":
        return <Crown className="h-4 w-4" />;
      case "admin":
        return <Shield className="h-4 w-4" />;
      default:
        return <UserIcon className="h-4 w-4" />;
    }
  };
  const getPlanDetails = () => {
    const plans = {
      free: {
        name: "Free",
        color: "bg-gray-100 text-gray-800",
        features: ["Up to 5 users", "Up to 50 risks", "Basic reporting"],
      },
      starter: {
        name: "Starter",
        color: "bg-green-100 text-green-800",
        features: ["Up to 25 users", "Up to 500 risks", "Advanced reporting"],
      },
      professional: {
        name: "Professional",
        color: "bg-blue-100 text-blue-800",
        features: ["Up to 100 users", "Unlimited risks", "Custom branding"],
      },
      enterprise: {
        name: "Enterprise",
        color: "bg-purple-100 text-purple-800",
        features: ["Unlimited users", "Unlimited risks", "Priority support"],
      },
    };
    return plans[organization.subscriptionPlan as keyof typeof plans] || plans.free;
  };
  const planDetails = getPlanDetails();
  return (
    <div className="container mx-auto py-8 max-w-6xl">
      <Tabs defaultValue="general" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
          <TabsTrigger value="advanced">Advanced</TabsTrigger>
        </TabsList>
        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Organization Information</CardTitle>
              <CardDescription>Basic information about your organization</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="orgName">Organization Name</Label>
                  <Input
                    id="orgName"
                    value={orgName}
                    onChange={e => setOrgName(e.target.value)}
                    placeholder="Enter organization name"
                  />
                </div>
                <div className="space-y-2">
                  <Label>Created</Label>
                  <Input value={new Date(organization.createdAt).toLocaleDateString()} disabled />
                </div>
              </div>
              <div className="space-y-2">
                <Label>Your Role</Label>
                <div className="flex items-center gap-2 px-3 py-2 border rounded-md bg-muted">
                  {getRoleIcon(organizationRole ?? "member")}
                  <span className="capitalize">{organizationRole}</span>
                </div>
              </div>
              <div className="flex justify-end">
                <Button
                  onClick={handleSaveBasicInfo}
                  disabled={isSaving || orgName === organization.name}
                >
                  {isSaving ? (
                    <>
                      <Settings className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="members" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Organization Members</CardTitle>
              <CardDescription>
                Manage team members, roles, and administrative requests
              </CardDescription>
            </CardHeader>
            <CardContent>
              <UserManagementTabs
                users={(usersData as User[]) ?? []}
                loading={usersLoading}
                currentUserId={user?.id ?? ""}
                onUserUpdated={handleUserUpdated}
              />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="billing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Subscription & Billing</CardTitle>
              <CardDescription>
                Manage your subscription plan and billing information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="text-lg font-medium">Current Plan</h3>
                    <Badge className={planDetails.color}>{planDetails.name}</Badge>
                  </div>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    {planDetails.features.map((feature, index) => (
                      <li key={index}>• {feature}</li>
                    ))}
                  </ul>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold">
                    {organization.subscriptionPlan === "free" ? "Free" : "$99"}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {organization.subscriptionPlan === "free" ? "" : "per month"}
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Status</Label>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant={
                        organization.subscriptionStatus === "active" ? "default" : "destructive"
                      }
                    >
                      {organization.subscriptionStatus}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Usage</Label>
                  <div className="text-sm text-muted-foreground">
                    Users: {usersData?.length ?? 0} / {organization.maxUsers} • Risks: 0 /{" "}
                    {organization.maxRisks}
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" disabled>
                  <CreditCard className="mr-2 h-4 w-4" />
                  Manage Billing (Coming Soon)
                </Button>
                {organization.subscriptionPlan === "free" && (
                  <Button disabled>Upgrade Plan (Coming Soon)</Button>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="advanced" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Advanced Settings</CardTitle>
              <CardDescription>Advanced organization configuration options</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Settings className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Advanced Features</h3>
                <p className="text-muted-foreground mb-4">
                  Advanced settings like custom branding, API keys, and data export will be
                  available in future updates.
                </p>
                <div className="space-y-2">
                  <Button variant="outline" disabled>
                    Custom Branding (Coming Soon)
                  </Button>
                  <Button variant="outline" disabled>
                    API Management (Coming Soon)
                  </Button>
                  <Button variant="outline" disabled>
                    Data Export (Coming Soon)
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
