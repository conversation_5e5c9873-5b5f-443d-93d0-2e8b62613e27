
import { Risk } from "@/types";
import { RiskCard } from "@/components/ui/responsive-table/RiskCard";
import { useNavigate } from "react-router-dom";

interface RiskCardWrapperProps {
  item: Risk;
  className?: string;
}

export const RiskCardWrapper = ({ item, className }: RiskCardWrapperProps) => {
  const navigate = useNavigate();
  
  const handleClick = () => {
    navigate(`/risks/${item.id}`);
  };

  return (
    <div onClick={handleClick}>
      <RiskCard item={item} className={className ?? ''} />
    </div>
  );
};
