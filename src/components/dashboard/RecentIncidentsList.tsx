
import { Incident } from "@/types";
import { StatusBadge } from "@/components/ui/status-badge";
import { cn } from "@/lib/utils";
import { Card, CardContent } from "@/components/ui/card";
import { useNavigate } from "react-router-dom";

interface RecentIncidentsListProps {
  incidents: Incident[];
  limit?: number;
}

const RecentIncidentsList = ({ incidents, limit = 5 }: RecentIncidentsListProps) => {
  const navigate = useNavigate();
  
  const getStatusColor = (status: string): string => {
    switch (status) {
      case "Open":
        return "bg-blue-500";
      case "Investigating":
        return "bg-amber-500";
      case "Resolved":
        return "bg-green-500";
      case "Closed":
        return "bg-gray-500";
      default:
        return "bg-gray-500";
    }
  };
  
  const handleIncidentClick = (incidentId: string) => {
    navigate(`/incidents/${incidentId}`);
  };

  return (
    <Card className="card-linear">
      <CardContent className="pt-6 p-0">
        <div className="space-y-4">
          {incidents.slice(0, limit).map((incident) => (
            <div 
              key={incident.id} 
              className="flex items-start space-x-4 p-4 border-b last:border-0 cursor-pointer hover:bg-accent/40"
              onClick={() => handleIncidentClick(incident.id)}
            >
              <div
                className={cn(
                  "w-2 h-2 rounded-full mt-2",
                  getStatusColor(incident.status)
                )}
              />
              <div className="space-y-1 flex-1">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">{incident.title}</p>
                  <StatusBadge status={incident.severity} />
                </div>
                <p className="text-xs text-muted-foreground">
                  Reported by {incident.reporterName} on{" "}
                  {incident.date.toLocaleDateString()}
                </p>
                <p className="text-xs line-clamp-2">{incident.description}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default RecentIncidentsList;
