import { Risk, RiskSeverity, RiskStatus } from "@/types";

// Helper function to count risks by property

export const countRisksBy = <T extends string>(
  risks: Risk[],
  getProperty: (risk: Risk) => T
): Record<string, number> => {
  return risks.reduce(
    (acc, risk) => {
      const key = getProperty(risk);
      acc[key] = (acc[key] ?? 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );
};

// Function to prepare chart data from counts

export const prepareChartData = (counts: Record<string, number>) => {
  return Object.entries(counts).map(([name, value]) => ({
    name,
    value,
  }));
};

// Colors for different charts
export const severityColors = {
  [RiskSeverity.LOW]: "rgb(132, 204, 22)",
  [RiskSeverity.MEDIUM]: "rgb(234, 179, 8)",
  [RiskSeverity.HIGH]: "rgb(249, 115, 22)",
  [RiskSeverity.CRITICAL]: "rgb(239, 68, 68)",
};

export const statusColors = {
  [RiskStatus.IDENTIFIED]: "#94a3b8",
  [RiskStatus.IN_PROGRESS]: "#3b82f6",
  [RiskStatus.MITIGATED]: "#10b981",
  [RiskStatus.ACCEPTED]: "#6366f1",
  [RiskStatus.CLOSED]: "#d1d5db",
};
