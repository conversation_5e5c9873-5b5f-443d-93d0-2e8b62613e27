
import { Risk } from "@/types";
import { StatusBadge } from "@/components/ui/status-badge";
import { useNavigate } from "react-router-dom";
import {
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ResponsiveTable } from "@/components/ui/responsive-table";
import { RiskCardWrapper } from "./RiskCardWrapper";

interface RecentRisksTableProps {
  risks: Risk[];
  limit?: number;
  isLoading?: boolean;
}

const RecentRisksTable = ({ risks, limit = 5, isLoading = false }: RecentRisksTableProps) => {
  const navigate = useNavigate();
  
  const handleRiskClick = (riskId: string) => {
    navigate(`/risks/${riskId}`);
  };

  const limitedRisks = risks.slice(0, limit);

  const tableHeader = (
    <TableHeader>
      <TableRow>
        <TableHead>Title</TableHead>
        <TableHead>Category</TableHead>
        <TableHead>Owner</TableHead>
        <TableHead>Severity</TableHead>
        <TableHead>Status</TableHead>
      </TableRow>
    </TableHeader>
  );

  const tableBody = (
    <TableBody>
      {limitedRisks.map((risk) => (
        <TableRow 
          key={risk.id} 
          className="cursor-pointer hover:bg-accent/40"
          onClick={() => handleRiskClick(risk.id)}
        >
          <TableCell>{risk.title}</TableCell>
          <TableCell>{risk.category}</TableCell>
          <TableCell>{risk.ownerName}</TableCell>
          <TableCell>
            <StatusBadge status={risk.severity} />
          </TableCell>
          <TableCell>
            <StatusBadge status={risk.status} />
          </TableCell>
        </TableRow>
      ))}
    </TableBody>
  );

  return (
    <ResponsiveTable
      data={limitedRisks}
      isLoading={isLoading}
      tableHeader={tableHeader}
      tableBody={tableBody}
      CardComponent={RiskCardWrapper}
      className="shadow-sm"
    />
  );
};

export default RecentRisksTable;
