
import { Risk, Incident } from "@/types";
import RiskMetricsCards from "@/components/dashboard/RiskMetricsCards";
import RecentRisksTable from "@/components/dashboard/RecentRisksTable";
import RecentIncidentsList from "@/components/dashboard/RecentIncidentsList";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { RiskCreateDropdown } from "@/components/dashboard/shared/RiskCreateDropdown";

interface DashboardMainViewProps {
  risks: Risk[];
  incidents: Incident[];
  onRiskCreateSuccess: () => void;
}

export const DashboardMainView = ({ risks, incidents, onRiskCreateSuccess }: DashboardMainViewProps) => {
  const navigate = useNavigate();

  return (
    <>
      <RiskMetricsCards risks={risks} />

      <div className="grid gap-6 lg:grid-cols-2">
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Recent Risks</h3>
            <RiskCreateDropdown onSuccess={onRiskCreateSuccess} size="sm" />
          </div>
          <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
            <RecentRisksTable risks={risks} />
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold">Recent Incidents</h3>
            <Button
              size="sm"
              onClick={() => navigate('/incidents/new')}
              className="flex items-center gap-2"
            >
              <Plus className="h-4 w-4" />
              Report Incident
            </Button>
          </div>
          {incidents.length > 0 ? (
            <RecentIncidentsList incidents={incidents} />
          ) : (
            <div className="rounded-lg border bg-card text-card-foreground shadow-sm p-6 text-center text-muted-foreground">
              <p className="mb-4">No incidents reported yet.</p>
              <Button onClick={() => navigate('/incidents/new')}>
                <Plus className="h-4 w-4 mr-2" />
                Report First Incident
              </Button>
            </div>
          )}
        </div>
      </div>
    </>
  );
};
