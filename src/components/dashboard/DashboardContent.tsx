
import { Risk, Incident } from "@/types";
import { DashboardWelcomeState } from "./welcome/DashboardWelcomeState";
import { DashboardMainView } from "./main/DashboardMainView";

interface DashboardContentProps {
  risks: Risk[];
  incidents: Incident[];
  onDataChange?: () => void;
}

const DashboardContent = ({ risks, incidents, onDataChange }: DashboardContentProps) => {
  const handleRiskCreateSuccess = () => {
    if (onDataChange) {
      onDataChange();
    }
  };

  // Show welcome state when no risks exist
  if (risks.length === 0) {
    return (
      <DashboardWelcomeState 
        incidents={incidents}
        onRiskCreateSuccess={handleRiskCreateSuccess}
      />
    );
  }

  // Show normal dashboard when risks exist
  return (
    <DashboardMainView 
      risks={risks}
      incidents={incidents}
      onRiskCreateSuccess={handleRiskCreateSuccess}
    />
  );
};

export default DashboardContent;
