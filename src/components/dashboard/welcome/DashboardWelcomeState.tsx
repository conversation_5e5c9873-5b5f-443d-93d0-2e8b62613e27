
import { Card, CardContent, CardDescription, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, BarChart3, Shield, AlertTriangle } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Incident } from "@/types";
import RecentIncidentsList from "@/components/dashboard/RecentIncidentsList";
import { RiskCreateDropdown } from "@/components/dashboard/shared/RiskCreateDropdown";

interface DashboardWelcomeStateProps {
  incidents: Incident[];
  onRiskCreateSuccess: () => void;
}

export const DashboardWelcomeState = ({ incidents, onRiskCreateSuccess }: DashboardWelcomeStateProps) => {
  const navigate = useNavigate();

  return (
    <div className="space-y-6">
      <Card className="border-dashed">
        <CardHeader className="text-center pb-4">
          <div className="flex justify-center mb-4">
            <div className="rounded-full bg-primary/10 p-6">
              <Shield className="h-12 w-12 text-primary" />
            </div>
          </div>
          <CardTitle className="text-2xl">Welcome to Risk Management</CardTitle>
          <CardDescription className="text-lg max-w-2xl mx-auto">
            Get started by creating your first risk assessment. Our platform helps you identify, 
            assess, and manage risks effectively across your organization.
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-6">
          <div className="grid gap-4 md:grid-cols-3 max-w-3xl mx-auto">
            <div className="flex flex-col items-center space-y-2 p-4">
              <div className="rounded-full bg-blue-100 p-3">
                <Plus className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-medium">Create Risks</h3>
              <p className="text-sm text-muted-foreground text-center">
                Identify and document potential risks
              </p>
            </div>
            <div className="flex flex-col items-center space-y-2 p-4">
              <div className="rounded-full bg-green-100 p-3">
                <BarChart3 className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-medium">Track Progress</h3>
              <p className="text-sm text-muted-foreground text-center">
                Monitor risk levels and mitigation efforts
              </p>
            </div>
            <div className="flex flex-col items-center space-y-2 p-4">
              <div className="rounded-full bg-orange-100 p-3">
                <AlertTriangle className="h-6 w-6 text-orange-600" />
              </div>
              <h3 className="font-medium">Report Incidents</h3>
              <p className="text-sm text-muted-foreground text-center">
                Document and respond to incidents
              </p>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <RiskCreateDropdown onSuccess={onRiskCreateSuccess} size="lg" />
            <Button 
              variant="outline" 
              onClick={() => navigate('/risks/templates')}
              size="lg"
              className="flex items-center gap-2"
            >
              <BarChart3 className="h-5 w-5" />
              Explore Templates
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Show incidents section even when no risks exist */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Recent Incidents</h3>
          <Button 
            size="sm" 
            onClick={() => navigate('/incidents/create')}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            Report Incident
          </Button>
        </div>
        {incidents.length > 0 ? (
          <RecentIncidentsList incidents={incidents} />
        ) : (
          <Card>
            <CardContent className="p-6 text-center text-muted-foreground">
              <p className="mb-4">No incidents reported yet.</p>
              <Button onClick={() => navigate('/incidents/create')}>
                <Plus className="h-4 w-4 mr-2" />
                Report First Incident
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};
