
import { Risk } from "@/types";
import Severity<PERSON>ieChart from "./charts/SeverityPieChart";
import Status<PERSON>ie<PERSON>hart from "./charts/StatusPieChart";
import UpcomingMitigations from "./charts/UpcomingMitigations";

interface RiskMetricsCardsProps {
  risks: Risk[];
}

const RiskMetricsCards = ({ risks }: RiskMetricsCardsProps) => {
  // Only render metrics cards when there are risks to show
  if (risks.length === 0) {
    return null;
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <SeverityPieChart risks={risks} />
      <StatusPieChart risks={risks} />
      <UpcomingMitigations risks={risks} />
    </div>
  );
};

export default RiskMetricsCards;
