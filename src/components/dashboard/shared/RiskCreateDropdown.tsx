
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Plus, Wand2, FileHeart, Upload } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { RiskCreateSheet } from "@/components/risk/RiskCreateSheet";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { RiskFormWizard } from "@/components/risk/RiskFormWizard";
import { RiskImportDialog } from "@/components/risk/import/RiskImportDialog";

interface RiskCreateDropdownProps {
  onSuccess: () => void;
  size?: "sm" | "lg";
}

export const RiskCreateDropdown = ({ onSuccess, size = "sm" }: RiskCreateDropdownProps) => {
  const navigate = useNavigate();
  const [showCreateRisk, setShowCreateRisk] = useState(false);
  const [showCreateRiskWizard, setShowCreateRiskWizard] = useState(false);
  const [showImportDialog, setShowImportDialog] = useState(false);

  const handleRiskCreateSuccess = () => {
    setShowCreateRisk(false);
    setShowCreateRiskWizard(false);
    setShowImportDialog(false);
    onSuccess();
  };

  const handleAddRiskWizard = () => {
    setShowCreateRiskWizard(true);
  };

  const handleCreateFromTemplate = () => {
    navigate('/risks/templates');
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            size={size}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            {size === "lg" ? "Create Your First Risk" : "Add Risk"}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem onClick={() => setShowCreateRisk(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Quick Create
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleAddRiskWizard}>
            <Wand2 className="mr-2 h-4 w-4" />
            Step-by-Step Wizard
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setShowImportDialog(true)}>
            <Upload className="mr-2 h-4 w-4" />
            Import from CSV
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleCreateFromTemplate}>
            <FileHeart className="mr-2 h-4 w-4" />
            Create from Template
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <RiskCreateSheet 
        isOpen={showCreateRisk}
        onOpenChange={setShowCreateRisk}
        onSuccess={handleRiskCreateSuccess}
      />

      <Dialog open={showCreateRiskWizard} onOpenChange={setShowCreateRiskWizard}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Risk - Step-by-Step Wizard</DialogTitle>
          </DialogHeader>
          <RiskFormWizard
            onSuccess={handleRiskCreateSuccess}
            onCancel={() => setShowCreateRiskWizard(false)}
          />
        </DialogContent>
      </Dialog>

      <RiskImportDialog
        isOpen={showImportDialog}
        onOpenChange={setShowImportDialog}
        onSuccess={handleRiskCreateSuccess}
      />
    </>
  );
};
