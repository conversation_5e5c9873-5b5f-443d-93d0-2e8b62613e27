
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON> } from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Risk, RiskStatus } from "@/types";
import { countRisksBy, prepareChartData } from "../utils/chartDataHelpers";
import { useNavigate } from "react-router-dom";
import { UniversalTooltip } from "@/components/ui/chart";

interface StatusPieChartProps {
  risks: Risk[];
}

// Use the same colors as the risk register
const statusColors = {
  [RiskStatus.IDENTIFIED]: "#3b82f6", // blue-500
  [RiskStatus.IN_PROGRESS]: "#eab308", // yellow-500
  [RiskStatus.MITIGATED]: "#22c55e", // green-500
  [RiskStatus.ACCEPTED]: "#8b5cf6", // purple-500
  [RiskStatus.CLOSED]: "#6b7280", // gray-500
};

const StatusPieChart = ({ risks }: StatusPieChartProps) => {
  const navigate = useNavigate();
  
  // Count risks by status
  const risksByStatus = countRisksBy(risks, (risk) => risk.status);
  
  // Prepare data for chart
  const statusData = prepareChartData(risksByStatus);

  // Handle cell click to navigate to filtered risk register
  const handleCellClick = (status: RiskStatus) => {
    navigate(`/risks?status=${status}`);
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="space-y-1">
          <CardTitle className="text-base font-semibold">Risk Status</CardTitle>
          <CardDescription className="text-sm">Current risk management status</CardDescription>
        </div>
      </CardHeader>
      <CardContent className="pb-4">
        <div className="h-[200px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={statusData}
                cx="50%"
                cy="50%"
                innerRadius={35}
                outerRadius={80}
                paddingAngle={2}
                dataKey="value"
                nameKey="name"
              >
                {statusData.map((entry) => (
                  <Cell
                    key={entry.name}
                    fill={statusColors[entry.name as RiskStatus] || "#6b7280"}
                    style={{ cursor: 'pointer' }}
                    onClick={() => handleCellClick(entry.name as RiskStatus)}
                  />
                ))}
              </Pie>
              <Tooltip
                content={<UniversalTooltip />}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="mt-3 text-xs text-center text-muted-foreground">
          Click on chart segments to view filtered risks
        </div>
      </CardContent>
    </Card>
  );
};

export default StatusPieChart;
