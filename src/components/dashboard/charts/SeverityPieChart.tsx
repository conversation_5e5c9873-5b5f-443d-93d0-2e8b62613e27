
import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, Toolt<PERSON> } from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Risk, RiskSeverity } from "@/types";
import { countRisksBy, prepareChartData } from "../utils/chartDataHelpers";
import { useNavigate } from "react-router-dom";
import { UniversalTooltip } from "@/components/ui/chart";

interface SeverityPieChartProps {
  risks: Risk[];
}

// Use the same colors as the risk register
const severityColors = {
  [RiskSeverity.CRITICAL]: "#ef4444", // red-500
  [RiskSeverity.HIGH]: "#f97316", // orange-500
  [RiskSeverity.MEDIUM]: "#eab308", // yellow-500
  [RiskSeverity.LOW]: "#22c55e", // green-500
};

const SeverityPieChart = ({ risks }: SeverityPieChartProps) => {
  const navigate = useNavigate();
  
  // Count risks by severity
  const risksBySeverity = countRisksBy(risks, (risk) => risk.severity);
  
  // Prepare data for chart
  const severityData = prepareChartData(risksBySeverity);

  // Handle cell click to navigate to filtered risk register
  const handleCellClick = (status: RiskSeverity) => {
    navigate(`/risks?severity=${status}`);
  };

  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="space-y-1">
          <CardTitle className="text-base font-semibold">Risk Severity</CardTitle>
          <CardDescription className="text-sm">Distribution by risk level</CardDescription>
        </div>
      </CardHeader>
      <CardContent className="pb-4">
        <div className="h-[200px]">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={severityData}
                cx="50%"
                cy="50%"
                innerRadius={35}
                outerRadius={80}
                paddingAngle={2}
                dataKey="value"
                nameKey="name"
              >
                {severityData.map((entry) => (
                  <Cell
                    key={entry.name}
                    fill={severityColors[entry.name as RiskSeverity] || "#6b7280"}
                    style={{ cursor: 'pointer' }}
                    onClick={() => handleCellClick(entry.name as RiskSeverity)}
                  />
                ))}
              </Pie>
              <Tooltip
                content={<UniversalTooltip />}
              />
            </PieChart>
          </ResponsiveContainer>
        </div>
        <div className="mt-3 text-xs text-center text-muted-foreground">
          Click on chart segments to view filtered risks
        </div>
      </CardContent>
    </Card>
  );
};

export default SeverityPieChart;
