
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Risk, RiskStatus, RiskSeverity } from "@/types";
import { useNavigate } from "react-router-dom";

interface UpcomingMitigationsProps {
  risks: Risk[];
}

const UpcomingMitigations = ({ risks }: UpcomingMitigationsProps) => {
  const navigate = useNavigate();
  
  const upcomingRisks = risks
    .filter((risk) => risk.dueDate && risk.status !== RiskStatus.CLOSED)
    .sort(
      (a, b) => 
        new Date(a.dueDate!).getTime() - new Date(b.dueDate!).getTime()
    )
    .slice(0, 3);

  const handleRiskClick = (riskId: string) => {
    navigate(`/risks/${riskId}`);
  };

  const getSeverityTextColor = (severity: RiskSeverity): string => {
    switch (severity) {
      case RiskSeverity.LOW:
        return "text-green-600";
      case RiskSeverity.MEDIUM:
        return "text-yellow-600";
      case RiskSeverity.HIGH:
        return "text-orange-600";
      case RiskSeverity.CRITICAL:
        return "text-red-600";
      default:
        return "text-gray-600";
    }
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <div className="space-y-1">
          <CardTitle className="text-sm font-medium">Due Dates</CardTitle>
          <CardDescription>Upcoming risk mitigations</CardDescription>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {upcomingRisks.map((risk) => (
            <div 
              key={risk.id} 
              className="flex items-center cursor-pointer hover:bg-accent/40 p-1.5 rounded-md transition-colors"
              onClick={() => handleRiskClick(risk.id)}
            >
              <div className="space-y-0.5 flex-1">
                <p className={`text-sm font-medium ${getSeverityTextColor(risk.severity)}`}>
                  {risk.title}
                </p>
                <p className="text-xs text-muted-foreground">
                  Due {risk.dueDate?.toLocaleDateString()}
                </p>
              </div>
            </div>
          ))}
          {upcomingRisks.length === 0 && (
            <p className="text-sm text-muted-foreground py-5 text-center">
              No upcoming mitigations
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default UpcomingMitigations;
