
import { Skeleton } from "@/components/ui/skeleton";

export const DashboardLoading = () => {
  return (
    <div className="space-y-6">
      {/* Risk Metrics Cards Loading */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Skeleton className="h-[160px] w-full rounded-lg" />
        <Skeleton className="h-[160px] w-full rounded-lg" />
        <Skeleton className="h-[160px] w-full rounded-lg" />
      </div>
      
      {/* Recent Risks Table Loading */}
      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2 space-y-4">
          <Skeleton className="h-7 w-48" />
          <div className="space-y-3">
            <Skeleton className="h-10 w-full rounded-md" />
            <Skeleton className="h-10 w-full rounded-md" />
            <Skeleton className="h-10 w-full rounded-md" />
            <Skeleton className="h-10 w-full rounded-md" />
            <Skeleton className="h-10 w-full rounded-md" />
          </div>
        </div>
        
        {/* Recent Incidents List Loading */}
        <div className="space-y-4">
          <Skeleton className="h-7 w-36" />
          <div className="space-y-3">
            <Skeleton className="h-24 w-full rounded-md" />
            <Skeleton className="h-24 w-full rounded-md" />
            <Skeleton className="h-24 w-full rounded-md" />
          </div>
        </div>
      </div>
    </div>
  );
};
