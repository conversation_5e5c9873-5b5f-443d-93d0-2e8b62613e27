import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Risk } from "@/types";
import { Calendar, FileText, FileSpreadsheet, Download } from "lucide-react";
import { exportToPdf, exportToExcelLegacy } from "@/utils/exportUtils";
import { useIncidents } from "@/hooks/useIncidents";
import { Incident } from "@/types";
interface ExportsTabProps {
  risks: Risk[];
  loading: boolean;
}
const ExportsTab = ({ risks, loading }: ExportsTabProps) => {
  const incidentsQuery = useIncidents();
  const incidents: Incident[] = (incidentsQuery.incidents as Incident[]) ?? [];
  const [exportType, setExportType] = useState<"pdf" | "excel">("pdf");
  const [reportType, setReportType] = useState<string>("risk-summary");
  const [isExporting, setIsExporting] = useState<boolean>(false);
  const handleExport = async () => {
    try {
      setIsExporting(true);
      const exportData = {
        risks,
        incidents,
        reportType,
      };
      if (exportType === "pdf") {
        await exportToPdf(reportType, exportData);
      } else {
        await exportToExcelLegacy(reportType, exportData);
      }
    } catch (error) {
      // Error caught and handled
    } finally {
      setIsExporting(false);
    }
  };
  return (
    <Card>
      <CardHeader>
        <div>
          <CardTitle>Export Reports</CardTitle>
          <CardDescription>Generate reports for meetings and documentation</CardDescription>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <div className="space-y-2 flex-1">
            <label className="text-sm font-medium">Report Type</label>
            <Select value={reportType} onValueChange={setReportType}>
              <SelectTrigger>
                <SelectValue placeholder="Select report type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="risk-summary">Risk Summary Report</SelectItem>
                <SelectItem value="risk-detailed">Detailed Risk Report</SelectItem>
                <SelectItem value="incidents">Incidents Report</SelectItem>
                <SelectItem value="matrix">Risk Matrix Report</SelectItem>
                <SelectItem value="categories">Categories Analysis</SelectItem>
                <SelectItem value="board">Board Executive Summary</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2 flex-1">
            <label className="text-sm font-medium">Export Format</label>
            <Tabs
              value={exportType}
              onValueChange={v => setExportType(v as "pdf" | "excel")}
              className="w-full"
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="pdf" className="flex gap-2">
                  <FileText className="h-4 w-4" />
                  PDF
                </TabsTrigger>
                <TabsTrigger value="excel" className="flex gap-2">
                  <FileSpreadsheet className="h-4 w-4" />
                  Excel
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          <div className="space-y-2 flex-1">
            <label className="text-sm font-medium">Date Range</label>
            <Button variant="outline" className="w-full justify-start text-left font-normal">
              <Calendar className="mr-2 h-4 w-4" />
              Last 30 days
            </Button>
          </div>
        </div>
        <TabsContent value="pdf" className="mt-4 space-y-4">
          <div className="bg-muted/40 rounded-lg p-4 space-y-2">
            <h3 className="font-medium">PDF Export Options</h3>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <input type="checkbox" id="include-logo" defaultChecked />
                <label htmlFor="include-logo">Include Company Logo</label>
              </div>
              <div className="flex items-center space-x-2">
                <input type="checkbox" id="include-charts" defaultChecked />
                <label htmlFor="include-charts">Include Charts</label>
              </div>
              <div className="flex items-center space-x-2">
                <input type="checkbox" id="include-metadata" defaultChecked />
                <label htmlFor="include-metadata">Include Metadata</label>
              </div>
              <div className="flex items-center space-x-2">
                <input type="checkbox" id="compress" />
                <label htmlFor="compress">Compress PDF</label>
              </div>
            </div>
          </div>
        </TabsContent>
        <TabsContent value="excel" className="mt-4 space-y-4">
          <div className="bg-muted/40 rounded-lg p-4 space-y-2">
            <h3 className="font-medium">Excel Export Options</h3>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <input type="checkbox" id="include-formulas" />
                <label htmlFor="include-formulas">Include Formulas</label>
              </div>
              <div className="flex items-center space-x-2">
                <input type="checkbox" id="include-sheet-summary" defaultChecked />
                <label htmlFor="include-sheet-summary">Include Summary Sheet</label>
              </div>
              <div className="flex items-center space-x-2">
                <input type="checkbox" id="include-filters" defaultChecked />
                <label htmlFor="include-filters">Include Filters</label>
              </div>
              <div className="flex items-center space-x-2">
                <input type="checkbox" id="include-pivot-tables" />
                <label htmlFor="include-pivot-tables">Include Pivot Tables</label>
              </div>
            </div>
          </div>
        </TabsContent>
        <div className="flex justify-end">
          <Button onClick={handleExport} disabled={isExporting || loading}>
            <Download className="mr-2 h-4 w-4" />
            {isExporting ? "Generating..." : "Generate Report"}
          </Button>
        </div>
        <div className="mt-4">
          <h3 className="text-sm font-medium mb-2">Recent Exports</h3>
          <div className="border rounded-md">
            <div className="p-4 border-b">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Risk Summary Report</p>
                  <p className="text-xs text-muted-foreground">PDF • Generated 2 days ago</p>
                </div>
                <Button variant="ghost" size="sm">
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="p-4 border-b">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Board Executive Summary</p>
                  <p className="text-xs text-muted-foreground">Excel • Generated 1 week ago</p>
                </div>
                <Button variant="ghost" size="sm">
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <p className="font-medium">Incidents Report</p>
                  <p className="text-xs text-muted-foreground">PDF • Generated 2 weeks ago</p>
                </div>
                <Button variant="ghost" size="sm">
                  <Download className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
export default ExportsTab;
