
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Risk, RiskSeverity } from "@/types";

interface RiskSummaryCardProps {
  risks: Risk[];
}

const RiskSummaryCard = ({ risks }: RiskSummaryCardProps) => {
  // More robust risk metrics calculation with error handling
  const criticalRisksCount = Array.isArray(risks) ? 
    risks.filter(risk => risk?.severity === RiskSeverity.CRITICAL).length : 0;
    
  const mitigatedRisksCount = Array.isArray(risks) ? 
    risks.filter(risk => risk?.status && risk.status.toLowerCase() === "mitigated").length : 0;
    
  const openRisksCount = Array.isArray(risks) ? 
    risks.filter(risk => risk?.status && 
      ["identified", "in progress"].includes(risk.status.toLowerCase())).length : 0;

  const totalRisks = Array.isArray(risks) ? risks.length : 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Risk Summary</CardTitle>
        <CardDescription>Overview of risk metrics</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <div className="flex flex-col space-y-1.5 rounded-lg border p-4">
            <span className="text-xs font-medium text-muted-foreground">Total Risks</span>
            <span className="text-2xl font-bold">{totalRisks}</span>
          </div>
          <div className="flex flex-col space-y-1.5 rounded-lg border p-4">
            <span className="text-xs font-medium text-muted-foreground">Critical Risks</span>
            <span className="text-2xl font-bold text-red-600">
              {criticalRisksCount}
            </span>
          </div>
          <div className="flex flex-col space-y-1.5 rounded-lg border p-4">
            <span className="text-xs font-medium text-muted-foreground">Mitigated Risks</span>
            <span className="text-2xl font-bold text-green-600">
              {mitigatedRisksCount}
            </span>
          </div>
          <div className="flex flex-col space-y-1.5 rounded-lg border p-4">
            <span className="text-xs font-medium text-muted-foreground">Open Risks</span>
            <span className="text-2xl font-bold text-blue-600">
              {openRisksCount}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RiskSummaryCard;
