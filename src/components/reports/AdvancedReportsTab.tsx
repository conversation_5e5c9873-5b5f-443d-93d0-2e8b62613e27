
import { Risk } from "@/types";
import RiskHeatMapComparison from "./RiskHeatMapComparison";
import ControlEffectivenessReport from "./ControlEffectivenessReport";
import RiskAppetiteDashboard from "./RiskAppetiteDashboard";

interface AdvancedReportsTabProps {
  risks: Risk[];
  loading: boolean;
}

const AdvancedReportsTab = ({ risks, loading }: AdvancedReportsTabProps) => {
  return (
    <div className="space-y-6">
      <RiskAppetiteDashboard risks={risks} loading={loading} />
      <RiskHeatMapComparison risks={risks} loading={loading} />
      <ControlEffectivenessReport risks={risks} loading={loading} />
    </div>
  );
};

export default AdvancedReportsTab;
