
import { Risk } from "@/types";
import SeverityDistributionChart from "./charts/SeverityDistributionChart";
import CategoryDistributionChart from "./charts/CategoryDistributionChart";
import StatusDistributionChart from "./charts/StatusDistributionChart";
import RiskSummaryCard from "@/components/reports/RiskSummaryCard";

interface OverviewTabProps {
  risks: Risk[];
  loading: boolean;
}

const OverviewTab = ({ risks, loading }: OverviewTabProps) => {
  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <SeverityDistributionChart risks={risks} loading={loading} />
        <CategoryDistributionChart risks={risks} loading={loading} />
        <StatusDistributionChart risks={risks} loading={loading} />
      </div>

      <RiskSummaryCard risks={risks} />
    </div>
  );
};

export default OverviewTab;
