
import { useState, useMemo } from "react";
import { format } from "date-fns";
import { Risk, RiskSeverity } from "@/types";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { getSeverityColorClass } from "@/components/risk/utils/riskCalculations";

interface HistoricalRiskTableProps {
  risks: Risk[];
  loading: boolean;
}

const HistoricalRiskTable = ({ risks, loading }: HistoricalRiskTableProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filterBy, setFilterBy] = useState<string>("all");
  
  const filteredRisks = useMemo(() => {
    if (!risks) return [];
    
    return risks.filter(risk => {
      // Search filter
      const matchesSearch = 
        risk.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        risk.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (risk.ownerName?.toLowerCase().includes(searchTerm.toLowerCase()));
      
      // Status filter
      const matchesFilter = 
        filterBy === "all" ||
        (filterBy === "critical" && risk.severity === RiskSeverity.CRITICAL) ||
        (filterBy === "high" && risk.severity === RiskSeverity.HIGH) ||
        (filterBy === "open" && ["Identified", "In Progress"].includes(risk.status)) ||
        (filterBy === "mitigated" && risk.status === "Mitigated");
      
      return matchesSearch && matchesFilter;
    });
  }, [risks, searchTerm, filterBy]);
  
  const sortedRisks = useMemo(() => {
    return [...filteredRisks].sort((a, b) => {
      // Sort by updated date (most recent first)
      return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
    });
  }, [filteredRisks]);
  
  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex gap-4">
          <Skeleton className="h-10 flex-1" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Skeleton className="h-[400px] w-full" />
      </div>
    );
  }
  
  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4">
        <Input 
          placeholder="Search risks..."
          value={searchTerm}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
          className="flex-1"
        />
        
        <Select value={filterBy} onValueChange={setFilterBy}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <SelectValue placeholder="Filter by..." />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Risks</SelectItem>
            <SelectItem value="critical">Critical Risks</SelectItem>
            <SelectItem value="high">High Risks</SelectItem>
            <SelectItem value="open">Open Risks</SelectItem>
            <SelectItem value="mitigated">Mitigated Risks</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {sortedRisks.length > 0 ? (
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Severity</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Owner</TableHead>
                <TableHead>Created</TableHead>
                <TableHead>Last Updated</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedRisks.map(risk => (
                <TableRow key={risk.id}>
                  <TableCell className="font-medium">{risk.title}</TableCell>
                  <TableCell>
                    <Badge variant="outline" className={getSeverityColorClass(risk.severity)}>
                      {risk.severity}
                    </Badge>
                  </TableCell>
                  <TableCell>{risk.status}</TableCell>
                  <TableCell>{risk.ownerName ?? "Unassigned"}</TableCell>
                  <TableCell>{format(new Date(risk.createdAt), "MMM d, yyyy")}</TableCell>
                  <TableCell>
                    {format(new Date(risk.updatedAt), "MMM d, yyyy")}
                    <div className="text-xs text-muted-foreground">
                      {format(new Date(risk.updatedAt), "h:mm a")}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ) : (
        <div className="text-center py-8 border rounded-md">
          <p className="text-muted-foreground">No risks found matching your criteria</p>
        </div>
      )}
      
      <div className="text-xs text-muted-foreground pt-2">
        Showing {sortedRisks.length} of {risks.length} risks
      </div>
    </div>
  );
};

export default HistoricalRiskTable;
