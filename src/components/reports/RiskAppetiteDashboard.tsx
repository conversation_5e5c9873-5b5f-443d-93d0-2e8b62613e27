
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Risk, RiskSeverity } from "@/types";
import { Skeleton } from "@/components/ui/skeleton";
import { CheckCircle, XCircle, AlertCircle } from "lucide-react";
import { Pie<PERSON>hart, Pie, Cell, ResponsiveContainer, Tooltip } from "recharts";
import { UniversalTooltip } from "@/components/ui/chart";

interface RiskAppetiteDashboardProps {
  risks: Risk[];
  loading: boolean;
}

// Define risk appetite thresholds (these could be configurable)
const RISK_APPETITE_THRESHOLDS = {
  critical: 0, // No critical risks acceptable
  high: 2,     // Max 2 high risks acceptable
  medium: 8,   // Max 8 medium risks acceptable
  low: 20      // Max 20 low risks acceptable
};

// Use the same colors as the risk register
const pieColors = {
  Critical: "#ef4444", // red-500
  High: "#f97316", // orange-500
  Medium: "#eab308", // yellow-500
  Low: "#22c55e", // green-500
};

const RiskAppetiteDashboard = ({ risks, loading }: RiskAppetiteDashboardProps) => {
  if (loading) {
    return <Skeleton className="h-[600px] w-full" />;
  }

  // Count risks by severity (residual risk)
  const riskCounts = {
    critical: risks.filter(r => r.severity === RiskSeverity.CRITICAL).length,
    high: risks.filter(r => r.severity === RiskSeverity.HIGH).length,
    medium: risks.filter(r => r.severity === RiskSeverity.MEDIUM).length,
    low: risks.filter(r => r.severity === RiskSeverity.LOW).length,
  };

  // Calculate appetite status
  const appetiteStatus = {
    critical: {
      current: riskCounts.critical,
      threshold: RISK_APPETITE_THRESHOLDS.critical,
      withinAppetite: riskCounts.critical <= RISK_APPETITE_THRESHOLDS.critical,
      percentage: RISK_APPETITE_THRESHOLDS.critical === 0 ? 
        (riskCounts.critical === 0 ? 0 : 100) : 
        (riskCounts.critical / RISK_APPETITE_THRESHOLDS.critical) * 100
    },
    high: {
      current: riskCounts.high,
      threshold: RISK_APPETITE_THRESHOLDS.high,
      withinAppetite: riskCounts.high <= RISK_APPETITE_THRESHOLDS.high,
      percentage: (riskCounts.high / RISK_APPETITE_THRESHOLDS.high) * 100
    },
    medium: {
      current: riskCounts.medium,
      threshold: RISK_APPETITE_THRESHOLDS.medium,
      withinAppetite: riskCounts.medium <= RISK_APPETITE_THRESHOLDS.medium,
      percentage: (riskCounts.medium / RISK_APPETITE_THRESHOLDS.medium) * 100
    },
    low: {
      current: riskCounts.low,
      threshold: RISK_APPETITE_THRESHOLDS.low,
      withinAppetite: riskCounts.low <= RISK_APPETITE_THRESHOLDS.low,
      percentage: (riskCounts.low / RISK_APPETITE_THRESHOLDS.low) * 100
    }
  };

  // Calculate overall appetite status
  const totalWithinAppetite = Object.values(appetiteStatus).filter(s => s.withinAppetite).length;
  const overallAppetiteScore = (totalWithinAppetite / 4) * 100;

  // Prepare data for pie chart with consistent colors
  const pieData = [
    { name: 'Critical', value: riskCounts.critical, color: pieColors.Critical },
    { name: 'High', value: riskCounts.high, color: pieColors.High },
    { name: 'Medium', value: riskCounts.medium, color: pieColors.Medium },
    { name: 'Low', value: riskCounts.low, color: pieColors.Low },
  ].filter(item => item.value > 0);

  const getStatusIcon = (withinAppetite: boolean, severity: string) => {
    if (severity === 'critical' && riskCounts.critical === 0) {
      return <CheckCircle className="h-5 w-5 text-green-600" />;
    }
    if (withinAppetite) {
      return <CheckCircle className="h-5 w-5 text-green-600" />;
    }
    return <XCircle className="h-5 w-5 text-red-600" />;
  };


  return (
    <Card className="border-primary/10">
      <CardHeader>
        <CardTitle>Risk Appetite Dashboard</CardTitle>
        <p className="text-sm text-muted-foreground">
          Monitor current risk levels against organizational risk appetite
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall Status */}
        <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/50 dark:to-purple-950/50 rounded-lg border dark:border-border">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-semibold text-foreground">Overall Risk Appetite Status</h3>
            <Badge
              variant={overallAppetiteScore >= 75 ? "default" : "destructive"}
              className="text-sm"
            >
              {overallAppetiteScore >= 75 ? "Within Appetite" : "Exceeds Appetite"}
            </Badge>
          </div>
          <Progress
            value={overallAppetiteScore}
            className="h-3"
          />
          <p className="text-sm text-muted-foreground mt-2">
            {totalWithinAppetite} of 4 risk categories within acceptable levels
          </p>
        </div>

        <div className="grid gap-6 lg:grid-cols-2">
          {/* Risk Distribution Chart */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Current Risk Distribution</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip
                    content={<UniversalTooltip />}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Appetite Thresholds */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Risk Appetite Thresholds</h3>
            <div className="space-y-4">
              {Object.entries(appetiteStatus).map(([severity, status]) => (
                <div key={severity} className="border rounded-lg p-3">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(status.withinAppetite, severity)}
                      <span className="font-medium capitalize">{severity}</span>
                    </div>
                    <span className="text-sm text-muted-foreground">
                      {status.current} / {status.threshold}
                    </span>
                  </div>
                  <Progress 
                    value={Math.min(100, status.percentage)} 
                    className="h-2"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>Current: {status.current}</span>
                    <span>Threshold: {status.threshold}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Risks Exceeding Appetite */}
        {!appetiteStatus.critical.withinAppetite || 
         !appetiteStatus.high.withinAppetite || 
         !appetiteStatus.medium.withinAppetite || 
         !appetiteStatus.low.withinAppetite ? (
          <div>
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-red-600" />
              Action Required
            </h3>
            <div className="space-y-2">
              {!appetiteStatus.critical.withinAppetite && (
                <div className="p-3 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800/30 rounded-lg">
                  <p className="text-sm text-red-800 dark:text-red-200">
                    <strong>Critical Risk Alert:</strong> {riskCounts.critical} critical risk(s) exceed appetite of {RISK_APPETITE_THRESHOLDS.critical}. Immediate action required.
                  </p>
                </div>
              )}
              {!appetiteStatus.high.withinAppetite && (
                <div className="p-3 bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800/30 rounded-lg">
                  <p className="text-sm text-orange-800 dark:text-orange-200">
                    <strong>High Risk Alert:</strong> {riskCounts.high} high risk(s) exceed appetite of {RISK_APPETITE_THRESHOLDS.high}. Priority attention needed.
                  </p>
                </div>
              )}
              {!appetiteStatus.medium.withinAppetite && (
                <div className="p-3 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800/30 rounded-lg">
                  <p className="text-sm text-yellow-800 dark:text-yellow-200">
                    <strong>Medium Risk Alert:</strong> {riskCounts.medium} medium risk(s) exceed appetite of {RISK_APPETITE_THRESHOLDS.medium}. Review recommended.
                  </p>
                </div>
              )}
            </div>
          </div>
        ) : (
          <div className="p-4 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800/30 rounded-lg">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
              <p className="text-green-800 dark:text-green-200 font-medium">
                All risk categories are within acceptable appetite levels
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default RiskAppetiteDashboard;
