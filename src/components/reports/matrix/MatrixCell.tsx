import { RiskSeverity } from "@/types";
import { calculateSeverity } from "@/components/risk/utils/riskCalculations";

interface MatrixCellProps {
  impact: number;
  likelihood: number;
  count: number;
  isSelected: boolean;
  onClick: () => void;
}

export const MatrixCell = ({ impact, likelihood, count, isSelected, onClick }: MatrixCellProps) => {
  const severity = getCellSeverity(impact, likelihood);
  const cellColor = getCellColor(impact, likelihood);

  return (
    <div
      className={`border relative p-2 cursor-pointer transition-all duration-200 
        ${cellColor} 
        ${isSelected ? "ring-2 ring-primary shadow-md scale-105 z-10" : ""}
        ${count > 0 ? "hover:scale-105" : "hover:bg-opacity-80"}`}
      onClick={onClick}
      aria-label={`Risk cell: Impact ${impact}, Likelihood ${likelihood}, Severity ${severity}`}
    >
      {count > 0 ? (
        <div className="absolute inset-0 flex items-center justify-center">
          <div
            className={`
            rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium
            ${getSeverityColor(severity)} shadow-sm
          `}
          >
            {count}
          </div>
        </div>
      ) : null}
      <div className="text-xs text-gray-500 absolute bottom-1 right-1">{impact * likelihood}</div>
    </div>
  );
};

// Function to get cell color based on severity level - updated to match Risk Heat Map Comparison

export const getCellColor = (impact: number, likelihood: number) => {
  const severity = calculateSeverity(likelihood, impact);
  switch (severity) {
    case RiskSeverity.CRITICAL:
      return "bg-gradient-to-br from-red-50 to-red-100 hover:from-red-100 hover:to-red-200 border-red-300";
    case RiskSeverity.HIGH:
      return "bg-gradient-to-br from-orange-50 to-orange-100 hover:from-orange-100 hover:to-orange-200 border-orange-300";
    case RiskSeverity.MEDIUM:
      return "bg-gradient-to-br from-yellow-50 to-yellow-100 hover:from-yellow-100 hover:to-yellow-200 border-yellow-300";
    case RiskSeverity.LOW:
      return "bg-gradient-to-br from-green-50 to-green-100 hover:from-green-100 hover:to-green-200 border-green-300";
    default:
      return "";
  }
};

// Function to get cell severity based on impact and likelihood

export const getCellSeverity = (impact: number, likelihood: number) => {
  return calculateSeverity(likelihood, impact);
};

// Function to get badge color for severity - updated to match Risk Heat Map Comparison

export const getSeverityColor = (severity: RiskSeverity) => {
  switch (severity) {
    case RiskSeverity.CRITICAL:
      return "bg-red-600 text-white";
    case RiskSeverity.HIGH:
      return "bg-orange-500 text-white";
    case RiskSeverity.MEDIUM:
      return "bg-yellow-500 text-white";
    case RiskSeverity.LOW:
      return "bg-green-500 text-white";
    default:
      return "";
  }
};
