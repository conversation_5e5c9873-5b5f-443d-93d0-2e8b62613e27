
import { useState } from "react";
import { Risk, RiskSeverity } from "@/types";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { ChevronDown, ChevronUp } from "lucide-react";
import { getSeverityColor, getCellSeverity } from "./MatrixCell";
import { useNavigate } from "react-router-dom";

interface SelectedCellDetailsProps {
  selectedCell: string;
  selectedRisks: Risk[];
}

export const SelectedCellDetails = ({ selectedCell, selectedRisks }: SelectedCellDetailsProps) => {
  const [isDetailExpanded, setIsDetailExpanded] = useState(true);
  const navigate = useNavigate();
  
  const toggleDetails = () => {
    setIsDetailExpanded(!isDetailExpanded);
  };
  
  const handleRiskClick = (riskId: string) => {
    navigate(`/risks/${riskId}`);
  };
  
  if (!selectedCell) return null;
  
  const [impact, likelihood] = selectedCell.split('-').map(Number);
  const severity = getCellSeverity(impact ?? 0, likelihood ?? 0);
  
  return (
    <Card className="mt-6 border-t-4 animate-in slide-in-from-top duration-300 overflow-hidden" 
      style={{ 
        borderTopColor: (() => {
          switch (severity) {
            case RiskSeverity.CRITICAL: return '#ef4444';
            case RiskSeverity.HIGH: return '#f97316';
            case RiskSeverity.MEDIUM: return '#eab308';
            case RiskSeverity.LOW: return '#84cc16';
            default: return '';
          }
        })()
      }}
    >
      <div 
        className="p-4 flex items-center justify-between cursor-pointer border-b"
        onClick={toggleDetails}
      >
        <h3 className="font-medium">
          <div className="flex items-center gap-2">
            <span>Impact {impact}, Likelihood {likelihood}</span>
            <Badge className={`${getSeverityColor(severity)} shadow-sm`}>{severity}</Badge>
            <span className="text-sm text-muted-foreground ml-2">
              {selectedRisks.length} risk{selectedRisks.length !== 1 ? 's' : ''} found
            </span>
          </div>
        </h3>
        <div>
          {isDetailExpanded ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
        </div>
      </div>
      
      {isDetailExpanded && (
        <CardContent className={`pt-4 transition-all duration-300 ${isDetailExpanded ? 'max-h-96 overflow-y-auto' : 'max-h-0'}`}>
          {selectedRisks.length > 0 ? (
            <div className="space-y-2">
              <div className="border rounded-md divide-y overflow-hidden">
                {selectedRisks.map(risk => (
                  <div 
                    key={risk.id} 
                    className="p-4 hover:bg-muted/50 transition-colors cursor-pointer"
                    onClick={() => handleRiskClick(risk.id)}
                  >
                    <div className="font-medium text-primary">{risk.title}</div>
                    <div className="text-sm text-muted-foreground mt-1 flex flex-wrap gap-x-4 gap-y-1">
                      <span>{risk.category}</span>
                      <span>•</span>
                      <span className={`${
                        risk.status === 'Mitigated' ? 'text-green-600' :
                        risk.status === 'In Progress' ? 'text-blue-600' :
                        'text-muted-foreground'
                      }`}>{risk.status}</span>
                      <span>•</span>
                      <span>Owner: {risk.ownerName ?? 'Unassigned'}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <div className="text-center py-6 text-muted-foreground">
              No risks found for this combination of impact and likelihood
            </div>
          )}
        </CardContent>
      )}
    </Card>
  );
};
