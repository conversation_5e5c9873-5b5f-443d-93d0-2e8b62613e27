
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import HistoricalRiskTable from "@/components/reports/HistoricalRiskTable";
import { Risk } from "@/types";

interface HistoricalTabProps {
  risks: Risk[];
  loading: boolean;
}

const HistoricalTab = ({ risks, loading }: HistoricalTabProps) => {
  return (
    <Card>
      <CardHeader>
        <div>
          <CardTitle>Risk History</CardTitle>
          <CardDescription>Historical risk tracking and analysis</CardDescription>
        </div>
      </CardHeader>
      <CardContent>
        <HistoricalRiskTable risks={risks} loading={loading} />
      </CardContent>
    </Card>
  );
};

export default HistoricalTab;
