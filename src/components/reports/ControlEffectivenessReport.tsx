
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Risk } from "@/types";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowDown, ArrowUp, Minus } from "lucide-react";

interface ControlEffectivenessReportProps {
  risks: Risk[];
  loading: boolean;
}

const ControlEffectivenessReport = ({ risks, loading }: ControlEffectivenessReportProps) => {
  if (loading) {
    return <Skeleton className="h-[600px] w-full" />;
  }

  // Calculate risk reduction for each risk
  const riskReductions = risks.map(risk => {
    const inherentScore = risk.inherentLikelihood * risk.inherentImpact;
    const residualScore = risk.likelihood * risk.impact;
    const reduction = inherentScore - residualScore;
    const reductionPercentage = inherentScore > 0 ? (reduction / inherentScore) * 100 : 0;
    
    return {
      ...risk,
      inherentScore,
      residualScore,
      reduction,
      reductionPercentage
    };
  }).sort((a, b) => b.reduction - a.reduction);

  // Calculate summary statistics
  const totalRisks = risks.length;
  const risksWithReduction = riskReductions.filter(r => r.reduction > 0).length;
  const averageReduction = riskReductions.reduce((sum, r) => sum + r.reductionPercentage, 0) / totalRisks;
  const highEffectivenessRisks = riskReductions.filter(r => r.reductionPercentage >= 50).length;

  const getReductionIcon = (reduction: number) => {
    if (reduction > 0) return <ArrowDown className="h-4 w-4 text-green-600" />;
    if (reduction < 0) return <ArrowUp className="h-4 w-4 text-red-600" />;
    return <Minus className="h-4 w-4 text-gray-500" />;
  };

  const getEffectivenessColor = (percentage: number) => {
    if (percentage >= 50) return "text-green-600";
    if (percentage >= 25) return "text-yellow-600";
    if (percentage > 0) return "text-orange-600";
    return "text-red-600";
  };

  const getEffectivenessLabel = (percentage: number) => {
    if (percentage >= 50) return "High";
    if (percentage >= 25) return "Medium";
    if (percentage > 0) return "Low";
    return "None";
  };

  return (
    <Card className="border-primary/10">
      <CardHeader>
        <CardTitle>Control Effectiveness Report</CardTitle>
        <p className="text-sm text-muted-foreground">
          Analyze how effectively controls are reducing risk exposure
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Summary Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{totalRisks}</div>
            <div className="text-sm text-muted-foreground">Total Risks</div>
          </div>
          <div className="p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{risksWithReduction}</div>
            <div className="text-sm text-muted-foreground">Risks Reduced</div>
          </div>
          <div className="p-4 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">{averageReduction.toFixed(1)}%</div>
            <div className="text-sm text-muted-foreground">Avg Reduction</div>
          </div>
          <div className="p-4 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">{highEffectivenessRisks}</div>
            <div className="text-sm text-muted-foreground">High Effectiveness</div>
          </div>
        </div>

        {/* Risk Reduction Table */}
        <div>
          <h3 className="text-lg font-semibold mb-4">Risk Reduction Analysis</h3>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {riskReductions.map((risk) => (
              <div key={risk.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-medium text-sm">{risk.title}</h4>
                    <p className="text-xs text-muted-foreground mt-1">
                      {risk.category}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    {getReductionIcon(risk.reduction)}
                    <Badge 
                      variant="outline" 
                      className={getEffectivenessColor(risk.reductionPercentage)}
                    >
                      {getEffectivenessLabel(risk.reductionPercentage)}
                    </Badge>
                  </div>
                </div>
                
                <div className="grid gap-2 md:grid-cols-3 text-sm">
                  <div>
                    <span className="text-muted-foreground">Inherent: </span>
                    <span className="font-medium">{risk.inherentScore}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Residual: </span>
                    <span className="font-medium">{risk.residualScore}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Reduction: </span>
                    <span className={`font-medium ${getEffectivenessColor(risk.reductionPercentage)}`}>
                      {risk.reductionPercentage.toFixed(1)}%
                    </span>
                  </div>
                </div>
                
                <Progress 
                  value={Math.max(0, risk.reductionPercentage)} 
                  className="h-2"
                />
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ControlEffectivenessReport;
