
import { useState } from "react";
import { Risk } from "@/types";
import { Skeleton } from "@/components/ui/skeleton";
import { MatrixCell } from "./matrix/MatrixCell";
import { SelectedCellDetails } from "./matrix/SelectedCellDetails";
import { generateMatrixData } from "./matrix/matrixUtils";

interface RiskMatrixHeatmapProps {
  risks: Risk[];
  loading: boolean;
}

const RiskMatrixHeatmap = ({ risks, loading }: RiskMatrixHeatmapProps) => {
  const [selectedCell, setSelectedCell] = useState<string | null>(null);
  
  // Generate the 5x5 matrix data
  const matrixData = generateMatrixData(risks);
  
  // Get risks for the selected cell
  const selectedRisks = selectedCell 
    ? risks.filter(risk => {
        const [impact, likelihood] = selectedCell.split('-').map(Number);
        return risk.impact === impact && risk.likelihood === likelihood;
      })
    : [];
  
  if (loading) {
    return <Skeleton className="h-[500px] w-full" />;
  }

  // Descriptions for likelihood levels
  const likelihoodDescriptions = [
    "Almost Certain", // 5
    "Likely",         // 4
    "Possible",       // 3
    "Unlikely",       // 2
    "Rare"            // 1
  ];

  return (
    <div className="space-y-6">
      <div className="flex">
        {/* Y-axis with three columns: Label, Descriptions, Numbers */}
        <div className="grid grid-cols-[15px,32px,13px] mr-0.5 gap-x-0 min-w-[60px]">
          {/* Column 1: Centered "Likelihood" label */}
          <div className="flex items-center justify-center">
            <span 
              className="text-xs font-bold whitespace-nowrap"
              style={{ 
                transformOrigin: 'center',
                transform: 'rotate(-90deg)',
              }}
            >
              Likelihood
            </span>
          </div>
          
          {/* Column 2: Likelihood descriptions (vertical) */}
          <div className="grid grid-rows-5 h-[500px]">
            {likelihoodDescriptions.map((description, index) => (
              <div key={index} className="flex items-center justify-center h-full relative">
                <span 
                  className="text-xs text-muted-foreground absolute"
                  style={{ 
                    transformOrigin: 'center',
                    transform: 'rotate(-90deg)',
                  }}
                >
                  {description}
                </span>
              </div>
            ))}
          </div>
          
          {/* Column 3: Numerical values right next to matrix */}
          <div className="grid grid-rows-5 h-[500px]">
            {[5, 4, 3, 2, 1].map((value) => (
              <div key={value} className="flex items-center justify-end pr-0.5">
                <span className="text-xs font-medium">{value}</span>
              </div>
            ))}
          </div>
        </div>
        
        {/* Matrix with improved styling and visual cues */}
        <div className="flex-1 flex flex-col">
          <div className="grid grid-rows-5 h-[500px]">
            {[5, 4, 3, 2, 1].map(likelihood => (
              <div key={likelihood} className="grid grid-cols-5">
                {[1, 2, 3, 4, 5].map(impact => {
                  const cellId = `${impact}-${likelihood}`;
                  const count = matrixData[cellId] ?? 0;
                  const isSelected = selectedCell === cellId;
                  
                  return (
                    <MatrixCell
                      key={cellId}
                      impact={impact}
                      likelihood={likelihood}
                      count={count}
                      isSelected={isSelected}
                      onClick={() => setSelectedCell(isSelected ? null : cellId)}
                    />
                  );
                })}
              </div>
            ))}
          </div>
          
          {/* X-axis improved alignment */}
          <div className="flex flex-col mt-2">
            {/* Numerical values */}
            <div className="grid grid-cols-5 text-center">
              {[1, 2, 3, 4, 5].map(value => (
                <div key={value} className="text-xs font-medium">
                  {value}
                </div>
              ))}
            </div>
            
            {/* Impact descriptions - centered under numbers */}
            <div className="grid grid-cols-5 text-center mt-1">
              <div className="text-xs text-muted-foreground">Minimal</div>
              <div className="text-xs text-muted-foreground">Minor</div>
              <div className="text-xs text-muted-foreground">Moderate</div>
              <div className="text-xs text-muted-foreground">Major</div>
              <div className="text-xs text-muted-foreground">Critical</div>
            </div>
            
            {/* Impact axis label - centered below descriptions */}
            <div className="text-center mt-2">
              <span className="text-xs font-bold">Impact</span>
            </div>
          </div>
        </div>
      </div>

      {/* Selected cell details with improved UI */}
      {selectedCell && (
        <SelectedCellDetails 
          selectedCell={selectedCell} 
          selectedRisks={selectedRisks} 
        />
      )}
    </div>
  );
};

export default RiskMatrixHeatmap;
