
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Risk, RiskStatus } from "@/types";
import { TrendingUp, TrendingDown, AlertTriangle, Clock, Users } from "lucide-react";

interface ReportSummaryCardsProps {
  risks: Risk[];
  loading: boolean;
  filters: Record<string, unknown>;
}

const ReportSummaryCards = ({ risks, loading, filters }: ReportSummaryCardsProps) => {
  // Calculate summary metrics
  const totalRisks = risks.length;
  const highSeverityRisks = risks.filter(r => r.severity === "High" || r.severity === "Critical").length;
  const openRisks = risks.filter(r => r.status === RiskStatus.IDENTIFIED || r.status === RiskStatus.IN_PROGRESS).length;
  const overdueRisks = risks.filter(r => {
    if (!r.dueDate) return false;
    return new Date(r.dueDate) < new Date();
  }).length;
  
  const avgImpactScore = risks.length > 0 
    ? risks.reduce((sum, r) => sum + (r.impact ?? 0), 0) / risks.length 
    : 0;
  
  const avgLikelihoodScore = risks.length > 0 
    ? risks.reduce((sum, r) => sum + (r.likelihood ?? 0), 0) / risks.length 
    : 0;

  // Calculate trends (mock data - in real implementation would compare with previous period)
  const trends = {
    total: Math.random() > 0.5 ? "up" : "down",
    highSeverity: Math.random() > 0.5 ? "up" : "down",
    open: Math.random() > 0.5 ? "up" : "down",
    overdue: Math.random() > 0.5 ? "up" : "down",
  };

  const summaryCards = [
    {
      title: "Total Risks",
      value: totalRisks.toString(),
      description: "All risks in scope",
      icon: Users,
      trend: trends.total,
      trendValue: "12%",
      color: "default" as const,
    },
    {
      title: "High Severity",
      value: highSeverityRisks.toString(),
      description: "Critical & High risks",
      icon: AlertTriangle,
      trend: trends.highSeverity,
      trendValue: "8%",
      color: "destructive" as const,
    },
    {
      title: "Open Issues",
      value: openRisks.toString(),
      description: "Active risks requiring attention",
      icon: Clock,
      trend: trends.open,
      trendValue: "5%",
      color: "warning" as const,
    },
    {
      title: "Overdue",
      value: overdueRisks.toString(),
      description: "Past mitigation due date",
      icon: TrendingDown,
      trend: trends.overdue,
      trendValue: "3%",
      color: "destructive" as const,
    },
  ];

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-3">
                <div className="h-4 bg-muted rounded w-1/2"></div>
                <div className="h-8 bg-muted rounded w-1/3"></div>
                <div className="h-3 bg-muted rounded w-3/4"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {summaryCards.map((card) => {
          const Icon = card.icon;
          const TrendIcon = card.trend === "up" ? TrendingUp : TrendingDown;
          
          return (
            <Card key={card.title}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-muted-foreground">{card.title}</p>
                    <div className="flex items-center space-x-2">
                      <p className="text-2xl font-bold">{card.value}</p>
                      <div className={`flex items-center text-xs ${
                        card.trend === "up" ? "text-red-600" : "text-green-600"
                      }`}>
                        <TrendIcon className="h-3 w-3 mr-1" />
                        {card.trendValue}
                      </div>
                    </div>
                    <p className="text-xs text-muted-foreground">{card.description}</p>
                  </div>
                  <div className={`p-2 rounded-full ${
                    card.color === "destructive" ? "bg-red-100 text-red-600" :
                    card.color === "warning" ? "bg-yellow-100 text-yellow-600" :
                    "bg-blue-100 text-blue-600"
                  }`}>
                    <Icon className="h-4 w-4" />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Additional Insights */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Risk Scores</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Avg Impact Score</span>
                <Badge variant="outline">{avgImpactScore.toFixed(1)}/5</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Avg Likelihood Score</span>
                <Badge variant="outline">{avgLikelihoodScore.toFixed(1)}/5</Badge>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-muted-foreground">Risk Coverage</span>
                <Badge variant="outline">{totalRisks > 0 ? ((openRisks / totalRisks) * 100).toFixed(0) : 0}%</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Applied Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              {Object.keys(filters).length === 0 ? "No filters applied" : `${Object.keys(filters).length} filter(s) applied`}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ReportSummaryCards;
