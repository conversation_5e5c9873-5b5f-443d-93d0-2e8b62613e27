import { Risk } from "@/types";

// Local type definition for filters since CustomReportsTab was removed
interface ReportFilters {
  severity?: string[];
  status?: string[];
  category?: string[];
  startDate?: string;
  endDate?: string;
}

export const filterRisks = (risks: Risk[], filters?: ReportFilters) => {
  if (!filters || Object.keys(filters).length === 0) {
    return risks;
  }

  return risks.filter(risk => {
    // Filter by severity
    if (filters.severity && filters.severity.length > 0) {
      if (!filters.severity.includes(risk.severity)) {
        return false;
      }
    }

    // Filter by status
    if (filters.status && filters.status.length > 0) {
      if (!filters.status.includes(risk.status)) {
        return false;
      }
    }

    // Filter by category
    if (filters.category && filters.category.length > 0) {
      if (!filters.category.includes(risk.category ?? "Uncategorized")) {
        return false;
      }
    }

    // Filter by date range
    if (filters.startDate && risk.createdAt) {
      const startDate = new Date(filters.startDate);
      const riskDate = new Date(risk.createdAt);
      if (riskDate < startDate) {
        return false;
      }
    }

    if (filters.endDate && risk.createdAt) {
      const endDate = new Date(filters.endDate);
      const riskDate = new Date(risk.createdAt);
      if (riskDate > endDate) {
        return false;
      }
    }

    return true;
  });
};
