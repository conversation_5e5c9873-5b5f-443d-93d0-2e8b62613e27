import { TableHeader, TableHead, TableBody, TableRow, TableCell } from "@/components/ui/table";
import { Risk } from "@/types";
import { StatusBadge } from "@/components/ui/status-badge";
import { ResponsiveTable } from "@/components/ui/responsive-table";
import { RiskCardWrapper } from "@/components/dashboard/RiskCardWrapper";

interface CustomReportTableProps {
  risks: Risk[];
  dataType: string;
  isLoading?: boolean;
}

const CustomReportTable = ({ risks, dataType, isLoading = false }: CustomReportTableProps) => {
  // Filter which columns to display based on dataType
  const showAllColumns = dataType === 'all';
  const showBySeverity = dataType === 'severity';
  const showByStatus = dataType === 'status';
  const showByCategory = dataType === 'category';
  const showByOwner = dataType === 'owner';
  
  const tableHeader = (
    <TableHeader>
      <TableRow>
        {showAllColumns && <TableHead>Title</TableHead>}
        {(showAllColumns || showByCategory) && <TableHead>Category</TableHead>}
        {(showAllColumns || showByOwner) && <TableHead>Owner</TableHead>}
        {(showAllColumns || showBySeverity) && <TableHead>Severity</TableHead>}
        {(showAllColumns || showByStatus) && <TableHead>Status</TableHead>}
        {showAllColumns && <TableHead>Due Date</TableHead>}
      </TableRow>
    </TableHeader>
  );

  const tableBody = (
    <TableBody>
      {risks.map((risk) => (
        <TableRow key={risk.id}>
          {showAllColumns && (
            <TableCell className="font-medium">
              {risk.title}
            </TableCell>
          )}
          
          {(showAllColumns || showByCategory) && (
            <TableCell>
              {risk.category ?? "Uncategorized"}
            </TableCell>
          )}
          
          {(showAllColumns || showByOwner) && (
            <TableCell>
              {risk.ownerName ?? "Unassigned"}
            </TableCell>
          )}
          
          {(showAllColumns || showBySeverity) && (
            <TableCell>
              <StatusBadge status={risk.severity} />
            </TableCell>
          )}
          
          {(showAllColumns || showByStatus) && (
            <TableCell>
              <StatusBadge status={risk.status} />
            </TableCell>
          )}
          
          {showAllColumns && (
            <TableCell>
              {risk.dueDate ? new Date(risk.dueDate).toLocaleDateString() : "—"}
            </TableCell>
          )}
        </TableRow>
      ))}
    </TableBody>
  );

  const emptyState = (
    <div className="text-center py-4">
      <p className="text-muted-foreground">No data available for this report</p>
    </div>
  );

  return (
    <ResponsiveTable
      data={risks}
      isLoading={isLoading}
      tableHeader={tableHeader}
      tableBody={tableBody}
      CardComponent={RiskCardWrapper}
      emptyState={emptyState}
      className="shadow-sm"
    />
  );
};

export default CustomReportTable;
