
import { But<PERSON> } from "@/components/ui/button";
import { Download, FileText, Share2 } from "lucide-react";

interface ReportExportActionsProps {
  onExportPDF: () => void;
  onExportCSV: () => void;
  onShare?: () => void;
}

const ReportExportActions = ({ onExportPDF, onExportCSV, onShare }: ReportExportActionsProps) => {
  return (
    <div className="flex justify-end gap-2">
      <Button variant="outline" size="sm" onClick={onExportPDF}>
        <FileText className="h-4 w-4 mr-2" />
        Export PDF
      </Button>
      <Button variant="outline" size="sm" onClick={onExportCSV}>
        <Download className="h-4 w-4 mr-2" />
        Export CSV
      </Button>
      {onShare && (
        <Button variant="outline" size="sm" onClick={onShare}>
          <Share2 className="h-4 w-4 mr-2" />
          Share
        </Button>
      )}
    </div>
  );
};

export default ReportExportActions;
