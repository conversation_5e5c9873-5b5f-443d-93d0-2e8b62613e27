
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RiskSeverity, RiskStatus } from "@/types";
import { Button } from "@/components/ui/button";

interface ReportFiltersProps {
  filters: {
    severity?: string[];
    status?: string[];
    category?: string;
    startDate?: string;
    endDate?: string;
  };
  dataSource: "risks" | "incidents";
  onChange: (filters: Record<string, unknown>) => void;
}

const ReportFilters = ({ filters, dataSource, onChange }: ReportFiltersProps) => {
  const [startDate, setStartDate] = useState(filters.startDate ?? "");
  const [endDate, setEndDate] = useState(filters.endDate ?? "");
  
  const handleDateChange = () => {
    onChange({
      ...filters,
      startDate,
      endDate
    });
  };
  
  const handleSeverityChange = (severity: string) => {
    const severityArray = filters.severity ?? [];
    const newArray = severityArray.includes(severity)
      ? severityArray.filter(s => s !== severity)
      : [...severityArray, severity];
    
    onChange({
      ...filters,
      severity: newArray
    });
  };

  const handleStatusChange = (status: string) => {
    const statusArray = filters.status ?? [];
    const newArray = statusArray.includes(status)
      ? statusArray.filter(s => s !== status)
      : [...statusArray, status];
    
    onChange({
      ...filters,
      status: newArray
    });
  };
  
  const handleReset = () => {
    onChange({});
    setStartDate("");
    setEndDate("");
  };
  
  const severityOptions = Object.values(RiskSeverity);
  const statusOptions = Object.values(RiskStatus);
  
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium">Report Filters</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label>Data Source</Label>
          <Select 
            value={dataSource} 
            onValueChange={(value) => onChange({ ...filters, dataSource: value })}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="risks">Risks</SelectItem>
              <SelectItem value="incidents">Incidents</SelectItem>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label>Date Range</Label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <input
                type="date"
                value={startDate}
                onChange={(e) => setStartDate(e.target.value)}
                className="w-full px-3 py-2 border rounded-md text-sm"
              />
            </div>
            <div>
              <input
                type="date"
                value={endDate}
                onChange={(e) => setEndDate(e.target.value)}
                className="w-full px-3 py-2 border rounded-md text-sm"
              />
            </div>
          </div>
          <Button 
            variant="outline" 
            className="w-full mt-1" 
            size="sm" 
            onClick={handleDateChange}
          >
            Apply Dates
          </Button>
        </div>
        
        <div className="space-y-2">
          <Label>Severity</Label>
          <div className="flex flex-wrap gap-1">
            {severityOptions.map((severity) => (
              <button
                key={severity}
                onClick={() => handleSeverityChange(severity)}
                className={`px-2 py-1 text-xs rounded-full ${
                  filters.severity?.includes(severity)
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted hover:bg-muted/80"
                }`}
              >
                {severity}
              </button>
            ))}
          </div>
        </div>
        
        <div className="space-y-2">
          <Label>Status</Label>
          <div className="flex flex-wrap gap-1">
            {statusOptions.map((status) => (
              <button
                key={status}
                onClick={() => handleStatusChange(status)}
                className={`px-2 py-1 text-xs rounded-full ${
                  filters.status?.includes(status)
                    ? "bg-primary text-primary-foreground"
                    : "bg-muted hover:bg-muted/80"
                }`}
              >
                {status}
              </button>
            ))}
          </div>
        </div>
        
        <div className="pt-2">
          <Button 
            variant="ghost" 
            className="w-full text-xs" 
            onClick={handleReset}
          >
            Reset Filters
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ReportFilters;
