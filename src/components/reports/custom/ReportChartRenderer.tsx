
import { 
  <PERSON><PERSON>hart, 
  Pie, 
  Cell, 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  LineChart, 
  Line,
  AreaChart,
  Area,
  Legend
} from "recharts";
import { severityColors } from "@/components/dashboard/utils/chartDataHelpers";

interface ChartDataEntry {
  name: string;
  value: number;
  [key: string]: unknown;
}

interface ReportChartRendererProps {
  section: {
    type: "table" | "pie" | "bar" | "line" | "area";
    title: string;
    data: string;
  };
  chartData: ChartDataEntry[];
}

const ReportChartRenderer = ({ section, chartData }: ReportChartRendererProps) => {
  if (section.type === "pie") {
    return (
      <div className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={chartData}
              cx="50%"
              cy="50%"
              innerRadius={60}
              outerRadius={120}
              paddingAngle={2}
              dataKey="value"
              nameKey="name"
              label={({ name, value }) => `${name}: ${value}`}
            >
              {chartData.map((entry, idx) => (
                <Cell 
                  key={`cell-${idx}`}
                  fill={(typeof severityColors[entry.name as keyof typeof severityColors] === 'string' 
                    ? severityColors[entry.name as keyof typeof severityColors] 
                    : `hsl(${idx * 45 % 360}, 70%, 60%)`)}
                />
              ))}
            </Pie>
            <Tooltip />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
    );
  }

  if (section.type === "bar") {
    return (
      <div className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip formatter={(value) => [`${value} risks`, 'Count']} />
            <Legend />
            <Bar dataKey="value" fill="#8884d8" />
          </BarChart>
        </ResponsiveContainer>
      </div>
    );
  }

  if (section.type === "line") {
    return (
      <div className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Line type="monotone" dataKey="high" stroke="#ef4444" name="High Severity" />
            <Line type="monotone" dataKey="medium" stroke="#f59e0b" name="Medium Severity" />
            <Line type="monotone" dataKey="low" stroke="#22c55e" name="Low Severity" />
          </LineChart>
        </ResponsiveContainer>
      </div>
    );
  }

  if (section.type === "area") {
    return (
      <div className="h-[300px]">
        <ResponsiveContainer width="100%" height="100%">
          <AreaChart data={chartData}>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis dataKey="month" />
            <YAxis />
            <Tooltip />
            <Legend />
            <Area type="monotone" dataKey="high" stackId="1" stroke="#ef4444" fill="#ef4444" />
            <Area type="monotone" dataKey="medium" stackId="1" stroke="#f59e0b" fill="#f59e0b" />
            <Area type="monotone" dataKey="low" stackId="1" stroke="#22c55e" fill="#22c55e" />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    );
  }

  return null;
};

export default ReportChartRenderer;
