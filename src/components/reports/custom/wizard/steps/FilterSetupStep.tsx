
// import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { RiskSeverity, RiskStatus } from "@/types";
import { X, Calendar, Filter } from "lucide-react";

// Local type definitions since CustomReportsTab was removed
interface ReportConfig {
  filters: {
    severity?: string[];
    status?: string[];
    category?: string;
    dateRange?: string;
    startDate?: string;
    endDate?: string;
    search?: string;
  };
}

interface FilterSetupStepProps {
  report: ReportConfig;
  onChange: (report: ReportConfig) => void;
  stepData?: unknown;
  onStepDataChange: (data: unknown) => void;
}

const FilterSetupStep = ({ report, on<PERSON>hang<PERSON>, step<PERSON><PERSON>, onStepDataChange }: FilterSetupStepProps) => {
  
  const quickPresets = [
    { name: "High Risk Items", filters: { severity: ["High", "Critical"] } },
    { name: "Open Issues", filters: { status: ["Open", "In Progress"] } },
    { name: "Overdue Items", filters: { overdue: true } },
    { name: "Recent Activity", filters: { dateRange: "last30days" } },
    { name: "All Active", filters: { status: ["Open", "In Progress", "Under Review"] } },
  ];

  const smartDateRanges = [
    { value: "today", label: "Today" },
    { value: "yesterday", label: "Yesterday" },
    { value: "last7days", label: "Last 7 days" },
    { value: "last30days", label: "Last 30 days" },
    { value: "thismonth", label: "This month" },
    { value: "lastmonth", label: "Last month" },
    { value: "thisquarter", label: "This quarter" },
    { value: "thisyear", label: "This year" },
    { value: "custom", label: "Custom range" },
  ];

  const updateFilters = (newFilters: Record<string, unknown>) => {
    const updatedReport = { ...report, filters: { ...report.filters, ...newFilters } };
    onChange(updatedReport);
    onStepDataChange({ 
      ...(typeof stepData === 'object' && stepData !== null ? stepData : {}), 
      filters: updatedReport.filters 
    });
  };

  const addSeverityFilter = (severity: string) => {
    const currentSeverities = report.filters.severity ?? [];
    if (!currentSeverities.includes(severity)) {
      updateFilters({ severity: [...currentSeverities, severity] });
    }
  };

  const removeSeverityFilter = (severity: string) => {
    const currentSeverities = report.filters.severity ?? [];
    updateFilters({ severity: currentSeverities.filter(s => s !== severity) });
  };

  const addStatusFilter = (status: string) => {
    const currentStatuses = report.filters.status ?? [];
    if (!currentStatuses.includes(status)) {
      updateFilters({ status: [...currentStatuses, status] });
    }
  };

  const removeStatusFilter = (status: string) => {
    const currentStatuses = report.filters.status ?? [];
    updateFilters({ status: currentStatuses.filter(s => s !== status) });
  };

  const applyPreset = (preset: { name: string; filters: Record<string, unknown> }) => {
    updateFilters(preset['filters']);
  };

  const clearAllFilters = () => {
    updateFilters({});
  };

  return (
    <div className="space-y-6">
      {/* Quick Filter Presets */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Quick Filter Presets
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {quickPresets.map((preset) => (
              <Button
                key={preset.name}
                variant="outline"
                size="sm"
                onClick={() => applyPreset(preset)}
              >
                {preset.name}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Advanced Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Advanced Filters</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Date Range Filter */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Date Range
            </Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Select onValueChange={(value) => updateFilters({ dateRange: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select date range" />
                </SelectTrigger>
                <SelectContent>
                  {smartDateRanges.map((range) => (
                    <SelectItem key={range.value} value={range.value}>
                      {range.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {report.filters.dateRange === "custom" && (
                <div className="grid grid-cols-2 gap-2">
                  <Input
                    type="date"
                    value={report.filters.startDate ?? ""}
                    onChange={(e) => updateFilters({ startDate: e.target.value })}
                  />
                  <Input
                    type="date"
                    value={report.filters.endDate ?? ""}
                    onChange={(e) => updateFilters({ endDate: e.target.value })}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Severity Filter */}
          <div className="space-y-3">
            <Label>Risk Severity</Label>
            <div className="flex flex-wrap gap-2">
              {Object.values(RiskSeverity).map((severity) => {
                const isSelected = report.filters.severity?.includes(severity);
                return (
                  <Badge
                    key={severity}
                    variant={isSelected ? "default" : "secondary"}
                    className="cursor-pointer"
                    onClick={() => isSelected ? removeSeverityFilter(severity) : addSeverityFilter(severity)}
                  >
                    {severity}
                    {isSelected && <X className="h-3 w-3 ml-1" />}
                  </Badge>
                );
              })}
            </div>
          </div>

          {/* Status Filter */}
          <div className="space-y-3">
            <Label>Status</Label>
            <div className="flex flex-wrap gap-2">
              {Object.values(RiskStatus).map((status) => {
                const isSelected = report.filters.status?.includes(status);
                return (
                  <Badge
                    key={status}
                    variant={isSelected ? "default" : "secondary"}
                    className="cursor-pointer"
                    onClick={() => isSelected ? removeStatusFilter(status) : addStatusFilter(status)}
                  >
                    {status}
                    {isSelected && <X className="h-3 w-3 ml-1" />}
                  </Badge>
                );
              })}
            </div>
          </div>

          {/* Category Filter */}
          <div className="space-y-3">
            <Label>Category</Label>
            <Select onValueChange={(value) => updateFilters({ category: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Select category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="operational">Operational</SelectItem>
                <SelectItem value="financial">Financial</SelectItem>
                <SelectItem value="strategic">Strategic</SelectItem>
                <SelectItem value="compliance">Compliance</SelectItem>
                <SelectItem value="technology">Technology</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Global Search */}
          <div className="space-y-3">
            <Label>Search</Label>
            <Input
              placeholder="Search across all fields..."
              value={report.filters.search ?? ""}
              onChange={(e) => updateFilters({ search: e.target.value })}
            />
          </div>
        </CardContent>
      </Card>

      {/* Active Filters Summary */}
      {Object.keys(report.filters).length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>Active Filters</CardTitle>
              <Button variant="ghost" size="sm" onClick={clearAllFilters}>
                Clear All
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {report.filters.severity?.map((severity) => (
                <Badge key={severity} variant="outline">
                  Severity: {severity}
                  <X className="h-3 w-3 ml-1 cursor-pointer" onClick={() => removeSeverityFilter(severity)} />
                </Badge>
              ))}
              {report.filters.status?.map((status) => (
                <Badge key={status} variant="outline">
                  Status: {status}
                  <X className="h-3 w-3 ml-1 cursor-pointer" onClick={() => removeStatusFilter(status)} />
                </Badge>
              ))}
              {report.filters.category && (
                <Badge variant="outline">
                  Category: {report.filters.category}
                  <X className="h-3 w-3 ml-1 cursor-pointer" onClick={() => updateFilters({ category: undefined })} />
                </Badge>
              )}
              {report.filters.search && (
                <Badge variant="outline">
                  Search: "{report.filters.search}"
                  <X className="h-3 w-3 ml-1 cursor-pointer" onClick={() => updateFilters({ search: undefined })} />
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default FilterSetupStep;
