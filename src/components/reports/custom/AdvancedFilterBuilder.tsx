
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Plus, Trash2, Save, Clock, Search } from "lucide-react";

interface FilterRule {
  id: string;
  field: string;
  operator: string;
  value: string;
  logic?: "AND" | "OR";
}

interface FilterGroup {
  id: string;
  name: string;
  rules: FilterRule[];
  savedAt: Date;
}

interface AdvancedFilterBuilderProps {
  onFiltersChange: (filters: Record<string, unknown>) => void;
}

const AdvancedFilterBuilder = ({ onFiltersChange }: AdvancedFilterBuilderProps) => {
  const [filterRules, setFilterRules] = useState<FilterRule[]>([]);
  const [savedFilters, setSavedFilters] = useState<FilterGroup[]>([]);
  const [filterName, setFilterName] = useState("");
  const [showSaveDialog, setShowSaveDialog] = useState(false);

  const fieldOptions = [
    { value: "title", label: "Title" },
    { value: "description", label: "Description" },
    { value: "severity", label: "Severity" },
    { value: "status", label: "Status" },
    { value: "category", label: "Category" },
    { value: "owner", label: "Owner" },
    { value: "created_at", label: "Created Date" },
    { value: "due_date", label: "Due Date" },
    { value: "impact_score", label: "Impact Score" },
    { value: "likelihood_score", label: "Likelihood Score" },
  ];

  const operatorOptions = [
    { value: "equals", label: "Equals" },
    { value: "not_equals", label: "Not Equals" },
    { value: "contains", label: "Contains" },
    { value: "not_contains", label: "Does Not Contain" },
    { value: "starts_with", label: "Starts With" },
    { value: "ends_with", label: "Ends With" },
    { value: "greater_than", label: "Greater Than" },
    { value: "less_than", label: "Less Than" },
    { value: "between", label: "Between" },
    { value: "is_empty", label: "Is Empty" },
    { value: "is_not_empty", label: "Is Not Empty" },
  ];

  const quickFilters = [
    { name: "High Priority", description: "High/Critical severity items", rules: [] },
    { name: "Overdue", description: "Past due date items", rules: [] },
    { name: "Open Issues", description: "Open or In Progress status", rules: [] },
    { name: "Recent", description: "Created in last 30 days", rules: [] },
  ];

  const addFilterRule = () => {
    const newRule: FilterRule = {
      id: Date.now().toString(),
      field: "",
      operator: "equals",
      value: "",
      logic: filterRules.length > 0 ? "AND" : "AND",
    };
    setFilterRules([...filterRules, newRule]);
  };

  const updateFilterRule = (id: string, updates: Partial<FilterRule>) => {
    setFilterRules(rules => rules.map(rule => 
      rule.id === id ? { ...rule, ...updates } : rule
    ));
  };

  const removeFilterRule = (id: string) => {
    setFilterRules(rules => rules.filter(rule => rule.id !== id));
  };

  const saveFilterGroup = () => {
    if (!filterName.trim()) return;
    
    const newGroup: FilterGroup = {
      id: Date.now().toString(),
      name: filterName,
      rules: filterRules,
      savedAt: new Date(),
    };
    
    setSavedFilters([...savedFilters, newGroup]);
    setFilterName("");
    setShowSaveDialog(false);
  };

  const loadFilterGroup = (group: FilterGroup) => {
    setFilterRules(group.rules);
    applyFilters(group.rules);
  };

  const applyFilters = (rules: FilterRule[] = filterRules) => {
    // Convert filter rules to the format expected by the report
    const filters: Record<string, unknown> = {};
    
    rules.forEach(rule => {
      if (rule.field && rule.value) {
        if (rule.field === "severity" || rule.field === "status") {
          if (!filters[rule.field]) filters[rule.field] = [];
          if (rule.operator === "equals") {
            (filters[rule.field] as unknown[]).push(rule.value);
          }
        } else {
          filters[rule.field] = rule.value;
        }
      }
    });
    
    onFiltersChange(filters);
  };

  const clearAllFilters = () => {
    setFilterRules([]);
    onFiltersChange({});
  };

  return (
    <div className="space-y-6">
      {/* Quick Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Quick Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {quickFilters.map((filter) => (
              <div
                key={filter.name}
                className="p-3 border rounded-lg cursor-pointer hover:bg-muted/50 transition-colors"
                onClick={() => {/* Apply quick filter */}}
              >
                <h4 className="font-medium text-sm">{filter.name}</h4>
                <p className="text-xs text-muted-foreground">{filter.description}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Advanced Filter Builder */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle>Advanced Filter Builder</CardTitle>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => setShowSaveDialog(true)}>
                <Save className="h-4 w-4 mr-1" />
                Save Filters
              </Button>
              <Button variant="outline" size="sm" onClick={addFilterRule}>
                <Plus className="h-4 w-4 mr-1" />
                Add Rule
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {filterRules.map((rule, index) => (
            <div key={rule.id} className="space-y-3 p-4 border rounded-lg">
              {index > 0 && (
                <div className="flex items-center gap-2">
                  <Select 
                    value={rule.logic || "AND"} 
                    onValueChange={(value: "AND" | "OR") => updateFilterRule(rule.id, { logic: value })}
                  >
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="AND">AND</SelectItem>
                      <SelectItem value="OR">OR</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-3 items-end">
                <div className="space-y-1">
                  <Label className="text-xs">Field</Label>
                  <Select 
                    value={rule.field} 
                    onValueChange={(value) => updateFilterRule(rule.id, { field: value })}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select field" />
                    </SelectTrigger>
                    <SelectContent>
                      {fieldOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-1">
                  <Label className="text-xs">Operator</Label>
                  <Select 
                    value={rule.operator} 
                    onValueChange={(value) => updateFilterRule(rule.id, { operator: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {operatorOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-1">
                  <Label className="text-xs">Value</Label>
                  <Input
                    value={rule.value}
                    onChange={(e) => updateFilterRule(rule.id, { value: e.target.value })}
                    placeholder="Enter value"
                  />
                </div>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeFilterRule(rule.id)}
                  className="h-10 w-10 p-0"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ))}

          {filterRules.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <Search className="h-8 w-8 mx-auto mb-2" />
              <p>No filter rules defined</p>
              <p className="text-sm">Click "Add Rule" to start building your filter</p>
            </div>
          )}

          {filterRules.length > 0 && (
            <div className="flex gap-2 pt-4 border-t">
              <Button onClick={() => applyFilters()}>Apply Filters</Button>
              <Button variant="outline" onClick={clearAllFilters}>Clear All</Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Saved Filters */}
      {savedFilters.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Saved Filter Groups</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {savedFilters.map((group) => (
                <div key={group.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <h4 className="font-medium">{group.name}</h4>
                    <p className="text-sm text-muted-foreground">
                      {group.rules.length} rules • Saved {group.savedAt.toLocaleDateString()}
                    </p>
                  </div>
                  <Button variant="outline" size="sm" onClick={() => loadFilterGroup(group)}>
                    Load
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Save Dialog */}
      {showSaveDialog && (
        <Card>
          <CardHeader>
            <CardTitle>Save Filter Group</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Filter Group Name</Label>
              <Input
                value={filterName}
                onChange={(e) => setFilterName(e.target.value)}
                placeholder="Enter a name for this filter group"
              />
            </div>
            <div className="flex gap-2">
              <Button onClick={saveFilterGroup}>Save</Button>
              <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default AdvancedFilterBuilder;
