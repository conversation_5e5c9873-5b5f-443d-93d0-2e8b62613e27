
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import RiskMatrixHeatmap from "@/components/reports/RiskMatrixHeatmap";
import { Risk } from "@/types";

interface MatrixTabProps {
  risks: Risk[];
  loading: boolean;
}

const MatrixTab = ({ risks, loading }: MatrixTabProps) => {
  return (
    <Card className="border-primary/10">
      <CardHeader className="pb-3">
        <div>
          <CardTitle>Risk Matrix (5x5)</CardTitle>
          <CardDescription>Likelihood vs Impact visualization</CardDescription>
        </div>
      </CardHeader>
      <CardContent>
        <RiskMatrixHeatmap risks={risks} loading={loading} />
      </CardContent>
    </Card>
  );
};

export default MatrixTab;
