import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>A<PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
} from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Risk, RiskStatus } from "@/types";
import { countRisksBy, prepareChartData } from "@/components/dashboard/utils/chartDataHelpers";
import { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { UniversalTooltip } from "@/components/ui/chart";

interface StatusDistributionChartProps {
  risks: Risk[];
  loading: boolean;
}

// Use the same colors as the risk register
const statusColors = {
  [RiskStatus.IDENTIFIED]: "#3b82f6", // blue-500
  [RiskStatus.IN_PROGRESS]: "#eab308", // yellow-500
  [RiskStatus.MITIGATED]: "#22c55e", // green-500
  [RiskStatus.ACCEPTED]: "#8b5cf6", // purple-500
  [RiskStatus.CLOSED]: "#6b7280", // gray-500
};

const StatusDistributionChart = ({ risks, loading }: StatusDistributionChartProps) => {
  const navigate = useNavigate();

  // Prepare status data for chart
  const statusData = useMemo(() => {
    if (!risks.length) return [];
    const risksByStatus = countRisksBy(risks, risk => risk.status);
    return prepareChartData(risksByStatus).map(item => ({
      ...item,
      fill: statusColors[item.name as RiskStatus] || "#6b7280",
    }));
  }, [risks]);

  // Handle bar click to navigate to filtered risk register
  const handleBarClick = (data: unknown) => {
    const statusData = data as { name?: string };
    if (statusData?.name) {
      navigate(`/risks?status=${statusData.name}`);
    }
  };

  const renderPlaceholder = () => (
    <div className="flex h-full items-center justify-center">
      <p className="text-sm text-muted-foreground">
        {loading ? "Loading data..." : "No risk data available"}
      </p>
    </div>
  );

  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <div className="space-y-1">
          <CardTitle className="text-lg font-semibold">Risk by Status</CardTitle>
          <CardDescription className="text-sm">Current status distribution</CardDescription>
        </div>
      </CardHeader>
      <CardContent className="pb-3">
        <div className="h-[240px]">
          {!loading && risks.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={statusData} margin={{ top: 15, right: 20, left: 20, bottom: 35 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" opacity={0.3} />
                <XAxis
                  dataKey="name"
                  tick={false}
                  axisLine={{ stroke: "hsl(var(--border))" }}
                  tickLine={false}
                />
                <YAxis
                  tick={{
                    fontSize: 11,
                    fill: "hsl(var(--muted-foreground))",
                    fontFamily: "Inter, sans-serif",
                  }}
                  axisLine={{ stroke: "hsl(var(--border))" }}
                  tickLine={{ stroke: "hsl(var(--border))" }}
                />
                <Tooltip content={<UniversalTooltip />} cursor={false} />
                <Bar
                  dataKey="value"
                  cursor="pointer"
                  onClick={handleBarClick}
                  radius={[4, 4, 0, 0]}
                >
                  {statusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.fill} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          ) : (
            renderPlaceholder()
          )}
        </div>
        <div className="mt-2 text-xs text-center text-muted-foreground">
          Click on bars to view filtered risks
        </div>
      </CardContent>
    </Card>
  );
};

export default StatusDistributionChart;
