import { <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer, Toolt<PERSON> } from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Risk, RiskSeverity } from "@/types";
import { countRisksBy, prepareChartData } from "@/components/dashboard/utils/chartDataHelpers";
import { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { UniversalTooltip } from "@/components/ui/chart";

interface SeverityDistributionChartProps {
  risks: Risk[];
  loading: boolean;
}

// Use the same colors as the risk register
const severityColors = {
  [RiskSeverity.CRITICAL]: "#ef4444", // red-500
  [RiskSeverity.HIGH]: "#f97316", // orange-500
  [RiskSeverity.MEDIUM]: "#eab308", // yellow-500
  [RiskSeverity.LOW]: "#22c55e", // green-500
};

const SeverityDistributionChart = ({ risks, loading }: SeverityDistributionChartProps) => {
  const navigate = useNavigate();

  // Prepare severity data for chart
  const severityData = useMemo(() => {
    if (!risks.length) return [];
    const risksBySeverity = countRisksBy(risks, risk => risk.severity);
    return prepareChartData(risksBySeverity);
  }, [risks]);

  // Handle cell click to navigate to filtered risk register
  const handleCellClick = (severity: RiskSeverity) => {
    navigate(`/risks?severity=${severity}`);
  };

  const renderPlaceholder = () => (
    <div className="flex h-full items-center justify-center">
      <p className="text-sm text-muted-foreground">
        {loading ? "Loading data..." : "No risk data available"}
      </p>
    </div>
  );

  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <div className="space-y-1">
          <CardTitle className="text-lg font-semibold">Risk by Severity</CardTitle>
          <CardDescription className="text-sm">
            Distribution of risks by severity level
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="pb-3">
        <div className="h-[240px]">
          {!loading && risks.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart margin={{ top: 10, right: 10, bottom: 10, left: 10 }}>
                <Pie
                  data={severityData}
                  cx="50%"
                  cy="50%"
                  innerRadius={45}
                  outerRadius={90}
                  paddingAngle={3}
                  dataKey="value"
                  nameKey="name"
                >
                  {severityData.map(entry => (
                    <Cell
                      key={`cell-${entry.name}`}
                      fill={severityColors[entry.name as RiskSeverity] || "#6b7280"}
                      style={{ cursor: "pointer" }}
                      onClick={() => handleCellClick(entry.name as RiskSeverity)}
                    />
                  ))}
                </Pie>
                <Tooltip content={<UniversalTooltip />} cursor={false} />
              </PieChart>
            </ResponsiveContainer>
          ) : (
            renderPlaceholder()
          )}
        </div>
        <div className="mt-2 text-xs text-center text-muted-foreground">
          Click on chart segments to view filtered risks
        </div>
      </CardContent>
    </Card>
  );
};

export default SeverityDistributionChart;
