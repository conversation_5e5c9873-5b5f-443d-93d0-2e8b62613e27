import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>Axis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Cell,
  LabelList,
} from "recharts";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Risk } from "@/types";
import { countRisksBy, prepareChartData } from "@/components/dashboard/utils/chartDataHelpers";
import { useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { UniversalTooltip } from "@/components/ui/chart";
interface CategoryDistributionChartProps {
  risks: Risk[];
  loading: boolean;
}
// Color palette for categories - using a varied set of colors for visual distinction
const categoryColors = [
  "#3b82f6", // blue-500
  "#10b981", // emerald-500
  "#f59e0b", // amber-500
  "#ef4444", // red-500
  "#8b5cf6", // violet-500
  "#06b6d4", // cyan-500
  "#84cc16", // lime-500
  "#f97316", // orange-500
  "#ec4899", // pink-500
  "#6366f1", // indigo-500
];
// Custom label component to render category names on bars
const CustomLabel = (props: Record<string, unknown>) => {
  const { x, y, width, height, payload } = props;
  // Safety check to ensure payload and payload.name exist, and bar is wide enough for text
  if (
    !payload ||
    typeof payload !== "object" ||
    !("name" in payload) ||
    !payload.name ||
    (width as number) < 80
  ) {
    return null;
  }
  const labelX = (x as number) + (width as number) / 2;
  const labelY = (y as number) + (height as number) / 2;
  return (
    <text
      x={labelX}
      y={labelY}
      fill="white"
      fontSize="12"
      fontWeight="500"
      textAnchor="middle"
      dominantBaseline="middle"
      fontFamily="Inter, sans-serif"
    >
      {(payload as { name: string }).name}
    </text>
  );
};
const CategoryDistributionChart = ({ risks, loading }: CategoryDistributionChartProps) => {
  const navigate = useNavigate();
  // Prepare category data for chart
  const categoryData = useMemo(() => {
    if (!risks.length) return [];
    // Count risks by category name
    const risksByCategory = countRisksBy(risks, risk => risk.category || "Uncategorized");
    const chartData = prepareChartData(risksByCategory);
    // Add ID and color to each category
    return chartData.map((item, index) => {
      // Find a risk with this category name to get its ID
      const riskWithThisCategory = risks.find(risk => risk.category === item.name);
      return {
        ...item,
        id: riskWithThisCategory?.categoryId ?? null, // Add ID if available
        fill: categoryColors[index % categoryColors.length], // Cycle through colors
      };
    });
  }, [risks]);
  // Handle bar click to navigate to filtered risk register
  const handleBarClick = (data: unknown) => {
    const categoryData = data as { id?: string; name?: string };
    if (categoryData?.id) {
      // Navigate with category ID
      navigate(`/risks?category=${categoryData.id}`);
    } else if (categoryData?.name) {
      // Fallback to category name if no ID
      navigate(`/risks?category=${encodeURIComponent(categoryData.name)}`);
    }
  };
  const renderPlaceholder = () => (
    <div className="flex h-full items-center justify-center">
      <p className="text-sm text-muted-foreground">
        {loading ? "Loading data..." : "No risk data available"}
      </p>
    </div>
  );
  return (
    <Card className="h-full">
      <CardHeader className="pb-2">
        <div className="space-y-1">
          <CardTitle className="text-lg font-semibold">Risk by Category</CardTitle>
          <CardDescription className="text-sm">Distribution across categories</CardDescription>
        </div>
      </CardHeader>
      <CardContent className="pb-3">
        <div className="h-[240px]">
          {!loading && risks.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={categoryData}
                layout="vertical"
                margin={{ top: 10, right: 20, left: 20, bottom: 10 }}
              >
                <CartesianGrid
                  strokeDasharray="3 3"
                  horizontal={false}
                  stroke="hsl(var(--border))"
                  opacity={0.3}
                />
                <XAxis
                  type="number"
                  tick={{
                    fontSize: 11,
                    fill: "hsl(var(--muted-foreground))",
                    fontFamily: "Inter, sans-serif",
                  }}
                  axisLine={{ stroke: "hsl(var(--border))" }}
                  tickLine={{ stroke: "hsl(var(--border))" }}
                />
                <YAxis
                  type="category"
                  dataKey="name"
                  width={0}
                  tick={false}
                  axisLine={false}
                  tickLine={false}
                />
                <Tooltip content={<UniversalTooltip />} cursor={false} />
                <Bar
                  dataKey="value"
                  cursor="pointer"
                  onClick={handleBarClick}
                  radius={[0, 4, 4, 0]}
                >
                  {categoryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.fill} />
                  ))}
                  <LabelList content={<CustomLabel />} />
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          ) : (
            renderPlaceholder()
          )}
        </div>
        <div className="mt-2 text-xs text-center text-muted-foreground">
          Click on bars to view filtered risks
        </div>
      </CardContent>
    </Card>
  );
};
export default CategoryDistributionChart;
