
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Risk } from "@/types";
import { Skeleton } from "@/components/ui/skeleton";
import { getCellColor, getCellSeverity } from "./matrix/MatrixCell";
import { generateMatrixData } from "./matrix/matrixUtils";

interface RiskHeatMapComparisonProps {
  risks: Risk[];
  loading: boolean;
}

const RiskHeatMapComparison = ({ risks, loading }: RiskHeatMapComparisonProps) => {
  if (loading) {
    return <Skeleton className="h-[600px] w-full" />;
  }

  // Generate matrix data for inherent risk
  const inherentMatrixData: { [key: string]: number } = {};
  risks.forEach(risk => {
    const cellId = `${risk.inherentImpact}-${risk.inherentLikelihood}`;
    inherentMatrixData[cellId] = (inherentMatrixData[cellId] ?? 0) + 1;
  });

  // Generate matrix data for residual risk
  const residualMatrixData = generateMatrixData(risks);

  const renderMatrix = (matrixData: { [key: string]: number }, title: string, titleColor: string) => (
    <div className="flex-1">
      <h3 className={`text-lg font-semibold mb-4 ${titleColor}`}>{title}</h3>
      
      <div className="flex">
        {/* Y-axis labels */}
        <div className="grid grid-cols-[15px,32px,13px] mr-0.5 gap-x-0 min-w-[60px]">
          <div className="flex items-center justify-center">
            <span 
              className="text-xs font-bold whitespace-nowrap"
              style={{ 
                transformOrigin: 'center',
                transform: 'rotate(-90deg)',
              }}
            >
              Likelihood
            </span>
          </div>
          
          <div className="grid grid-rows-5 h-[300px]">
            {["Almost Certain", "Likely", "Possible", "Unlikely", "Rare"].map((description, index) => (
              <div key={index} className="flex items-center justify-center h-full relative">
                <span 
                  className="text-xs text-muted-foreground absolute"
                  style={{ 
                    transformOrigin: 'center',
                    transform: 'rotate(-90deg)',
                  }}
                >
                  {description}
                </span>
              </div>
            ))}
          </div>
          
          <div className="grid grid-rows-5 h-[300px]">
            {[5, 4, 3, 2, 1].map((value) => (
              <div key={value} className="flex items-center justify-end pr-0.5">
                <span className="text-xs font-medium">{value}</span>
              </div>
            ))}
          </div>
        </div>
        
        {/* Matrix */}
        <div className="flex-1 flex flex-col">
          <div className="grid grid-rows-5 h-[300px]">
            {[5, 4, 3, 2, 1].map(likelihood => (
              <div key={likelihood} className="grid grid-cols-5">
                {[1, 2, 3, 4, 5].map(impact => {
                  const cellId = `${impact}-${likelihood}`;
                  const count = matrixData[cellId] ?? 0;
                  
                  return (
                    <div
                      key={cellId}
                      className={`border relative p-2 ${getCellColor(impact, likelihood)}`}
                    >
                      {count > 0 && (
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className={`
                            rounded-full w-6 h-6 flex items-center justify-center text-xs font-medium
                            ${getCellSeverity(impact, likelihood) === 'Critical' ? 'bg-red-600 text-white' :
                              getCellSeverity(impact, likelihood) === 'High' ? 'bg-orange-500 text-white' :
                              getCellSeverity(impact, likelihood) === 'Medium' ? 'bg-yellow-500 text-white' :
                              'bg-green-500 text-white'} shadow-sm
                          `}>
                            {count}
                          </div>
                        </div>
                      )}
                      <div className="text-xs text-gray-500 absolute bottom-1 right-1">
                        {impact * likelihood}
                      </div>
                    </div>
                  );
                })}
              </div>
            ))}
          </div>
          
          {/* X-axis labels */}
          <div className="flex flex-col mt-2">
            <div className="grid grid-cols-5 text-center">
              {[1, 2, 3, 4, 5].map(value => (
                <div key={value} className="text-xs font-medium">
                  {value}
                </div>
              ))}
            </div>
            
            <div className="grid grid-cols-5 text-center mt-1">
              <div className="text-xs text-muted-foreground">Minimal</div>
              <div className="text-xs text-muted-foreground">Minor</div>
              <div className="text-xs text-muted-foreground">Moderate</div>
              <div className="text-xs text-muted-foreground">Major</div>
              <div className="text-xs text-muted-foreground">Critical</div>
            </div>
            
            <div className="text-center mt-2">
              <span className="text-xs font-bold">Impact</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <Card className="border-primary/10">
      <CardHeader>
        <CardTitle>Risk Heat Map Comparison</CardTitle>
        <p className="text-sm text-muted-foreground">
          Compare inherent risk (before controls) vs residual risk (after controls)
        </p>
      </CardHeader>
      <CardContent>
        <div className="grid gap-8 lg:grid-cols-2">
          {renderMatrix(inherentMatrixData, "Inherent Risk", "text-red-600")}
          {renderMatrix(residualMatrixData, "Residual Risk", "text-blue-600")}
        </div>
      </CardContent>
    </Card>
  );
};

export default RiskHeatMapComparison;
