
import React, { ComponentType, ReactNode } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { ErrorContext, ErrorSeverity } from '@/types';
import { errorHandler, logger, SystemError } from '@/utils/errors';
import { errorMonitoringService } from '@/services/errorMonitoringService';
import { loggingService } from '@/services/loggingService';

export interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
  context?: Partial<ErrorContext>;
  resetKeys?: Array<string | number>;
}

export interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
  context: Partial<ErrorContext>;
}

/**
 * Base error boundary component that integrates with our centralized error handling
 */
export const BaseErrorBoundary: React.FC<ErrorBoundaryProps> = ({
  children,
  fallback: FallbackComponent,
  onError,
  context,
  resetKeys = []
}) => {
  const handleError = (error: Error, errorInfo: React.ErrorInfo) => {
    // Create system error with React-specific context
    const errorContext: ErrorContext = {
      timestamp: new Date(),
      component: errorInfo.componentStack?.split('\n')[1]?.trim() ?? 'unknown',
      action: 'react_error_boundary',
      userId: context?.userId ?? 'anonymous',
      organizationId: context?.organizationId ?? 'unknown',
      additionalData: {
        componentStack: errorInfo.componentStack,
        errorBoundary: true,
        ...context?.additionalData
      }
    };

    const systemError = new SystemError(
      error.message,
      'react_component',
      'REACT_ERROR_BOUNDARY',
      errorContext,
      ErrorSeverity.HIGH // React errors are typically high severity
    );

    // Track error in monitoring system
    errorMonitoringService.trackError(systemError, errorContext);

    // Handle through centralized error handler
    errorHandler.handle(systemError, errorContext);

    // Log structured error
    loggingService.error(
      `React Error Boundary caught error: ${error.message}`,
      error,
      errorContext
    );

    // Call custom error handler if provided
    if (onError) {
      onError(error, errorInfo);
    }
  };

  const ErrorFallback = ({ error, resetErrorBoundary }: { error: Error; resetErrorBoundary: () => void }) => {
    const safeContext = context ?? {};
    if (FallbackComponent) {
      return <FallbackComponent error={error} resetErrorBoundary={resetErrorBoundary} context={safeContext} />;
    }

    return <DefaultErrorFallback error={error} resetErrorBoundary={resetErrorBoundary} context={safeContext} />;
  };

  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={handleError}
      resetKeys={resetKeys}
    >
      {children}
    </ErrorBoundary>
  );
};

/**
 * Sanitizes error messages to prevent exposing internal system details to users
 */
const sanitizeErrorMessage = (error: Error): string => {
  const message = error.message.toLowerCase();
  
  // Common patterns that should be hidden from users
  const sensitivePatterns = [
    /chunk.*failed/i,
    /loading.*chunk/i,
    /network.*error/i,
    /fetch.*failed/i,
    /cors.*error/i,
    /unauthorized/i,
    /forbidden/i,
    /internal.*server.*error/i,
    /database.*error/i,
    /sql.*error/i,
    /connection.*refused/i,
    /timeout/i
  ];

  // Check for sensitive patterns and provide user-friendly alternatives
  if (sensitivePatterns.some(pattern => pattern.test(message))) {
    if (message.includes('chunk') || message.includes('loading')) {
      return 'Unable to load page content. Please check your connection and try again.';
    }
    if (message.includes('network') || message.includes('fetch') || message.includes('cors')) {
      return 'Connection issue detected. Please check your internet connection.';
    }
    if (message.includes('unauthorized') || message.includes('forbidden')) {
      return 'Access denied. Please check your permissions or sign in again.';
    }
    if (message.includes('timeout')) {
      return 'Request timed out. Please try again.';
    }
    return 'A temporary issue occurred. Please try again in a moment.';
  }

  // For other errors, provide a generic but helpful message
  if (error.name === 'TypeError' || error.name === 'ReferenceError') {
    return 'A technical issue occurred. Please refresh the page and try again.';
  }

  // Return original message if it seems safe and user-friendly
  if (message.length < 100 && !message.includes('stack') && !message.includes('trace')) {
    return error.message;
  }

  return 'An unexpected error occurred. Please try again.';
};

/**
 * Enhanced recovery mechanisms for error boundaries
 */
interface RecoveryOptions {
  canRetry: boolean;
  canReload: boolean;
  canGoBack: boolean;
  retryDelay?: number;
  maxRetries?: number;
}

const useErrorRecovery = (_context: Partial<ErrorContext>) => {
  const [retryCount, setRetryCount] = React.useState(0);
  const [isRetrying, setIsRetrying] = React.useState(false);

  const getRecoveryOptions = (error: Error): RecoveryOptions => {
    const message = error.message.toLowerCase();
    
    // Network-related errors - allow retry with delay
    if (message.includes('chunk') || message.includes('network') || message.includes('fetch')) {
      return {
        canRetry: retryCount < 3,
        canReload: true,
        canGoBack: false,
        retryDelay: Math.min(1000 * Math.pow(2, retryCount), 5000), // Exponential backoff
        maxRetries: 3
      };
    }

    // Component errors - allow immediate retry
    if (error.name === 'TypeError' || error.name === 'ReferenceError') {
      return {
        canRetry: retryCount < 2,
        canReload: true,
        canGoBack: true,
        maxRetries: 2
      };
    }

    // Default recovery options
    return {
      canRetry: retryCount < 1,
      canReload: true,
      canGoBack: true,
      maxRetries: 1
    };
  };

  const handleRetryWithDelay = async (resetErrorBoundary: () => void, delay?: number) => {
    if (delay && delay > 0) {
      setIsRetrying(true);
      await new Promise(resolve => setTimeout(resolve, delay));
      setIsRetrying(false);
    }
    
    setRetryCount(prev => prev + 1);
    resetErrorBoundary();
  };

  return {
    retryCount,
    isRetrying,
    getRecoveryOptions,
    handleRetryWithDelay
  };
};

/**
 * Default error fallback component with enhanced user experience
 */
const DefaultErrorFallback: React.FC<ErrorFallbackProps> = ({ 
  error, 
  resetErrorBoundary, 
  context 
}) => {
  const { retryCount, isRetrying, getRecoveryOptions, handleRetryWithDelay } = useErrorRecovery(context);
  const recoveryOptions = getRecoveryOptions(error);
  const userFriendlyMessage = sanitizeErrorMessage(error);

  const handleRetry = () => {
    const logContext: ErrorContext = {
      timestamp: new Date(),
      component: 'error_boundary_fallback',
      action: 'user_retry',
      userId: (context?.userId as string) ?? 'anonymous',
      organizationId: (context?.organizationId as string) ?? 'unknown',
      additionalData: { 
        ...(context?.additionalData ?? {}),
        retryCount,
        errorType: error.name
      }
    };
    logger.info('User initiated error boundary reset', logContext);
    handleRetryWithDelay(resetErrorBoundary, recoveryOptions.retryDelay);
  };

  const handleReload = () => {
    const logContext: ErrorContext = {
      timestamp: new Date(),
      component: 'error_boundary_fallback', 
      action: 'page_reload',
      userId: (context?.userId as string) ?? 'anonymous',
      organizationId: (context?.organizationId as string) ?? 'unknown',
      additionalData: { 
        ...(context?.additionalData ?? {}),
        retryCount,
        errorType: error.name
      }
    };
    logger.info('User initiated page reload from error boundary', logContext);
    window.location.reload();
  };

  const handleGoBack = () => {
    const logContext: ErrorContext = {
      timestamp: new Date(),
      component: 'error_boundary_fallback',
      action: 'navigate_back',
      userId: (context?.userId as string) ?? 'anonymous',
      organizationId: (context?.organizationId as string) ?? 'unknown',
      additionalData: { 
        ...(context?.additionalData ?? {}),
        retryCount,
        errorType: error.name
      }
    };
    logger.info('User navigated back from error boundary', logContext);
    window.history.back();
  };

  return (
    <div className="min-h-[400px] flex items-center justify-center p-6">
      <div className="max-w-md w-full">
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-destructive/20 rounded-full flex items-center justify-center">
              <svg
                className="w-5 h-5 text-destructive"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <div>
              <h3 className="font-semibold text-destructive">Something went wrong</h3>
              <p className="text-sm text-muted-foreground">
                We're sorry for the inconvenience
              </p>
            </div>
          </div>

          <div className="space-y-3">
            <div className="bg-muted/50 rounded p-3">
              <p className="text-sm text-muted-foreground">
                {userFriendlyMessage}
              </p>
            </div>

            {retryCount > 0 && (
              <div className="text-xs text-muted-foreground bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded p-2">
                {retryCount === 1 ? 'Tried once.' : `Tried ${retryCount} times.`}
                {retryCount >= (recoveryOptions.maxRetries ?? 1) && ' Consider reloading the page.'}
              </div>
            )}

            <div className="flex gap-2">
              {recoveryOptions.canRetry && (
                <button
                  onClick={handleRetry}
                  disabled={isRetrying}
                  className="flex-1 px-3 py-2 bg-primary text-primary-foreground rounded text-sm font-medium hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isRetrying ? 'Retrying...' : 'Try Again'}
                </button>
              )}
              
              {recoveryOptions.canReload && (
                <button
                  onClick={handleReload}
                  className="px-3 py-2 bg-muted text-muted-foreground rounded text-sm font-medium hover:bg-muted/80 transition-colors"
                >
                  Reload Page
                </button>
              )}

              {recoveryOptions.canGoBack && (
                <button
                  onClick={handleGoBack}
                  className="px-3 py-2 bg-muted text-muted-foreground rounded text-sm font-medium hover:bg-muted/80 transition-colors"
                >
                  Go Back
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BaseErrorBoundary;
