import React from 'react';
import { BaseErrorBoundary, ErrorFallbackProps } from './BaseErrorBoundary';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, Home, ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

/**
 * Analyzes page errors and provides appropriate user messaging and recovery options
 */
const analyzePageError = (error: Error) => {
  const message = error.message.toLowerCase();
  
  if (message.includes('chunk') || message.includes('loading')) {
    return {
      type: 'resource_loading',
      title: 'Page Loading Issue',
      description: 'Some page resources failed to load properly.',
      userMessage: 'We\'re having trouble loading this page. This is usually a temporary issue.',
      suggestions: [
        'Refresh the page to reload all resources',
        'Check your internet connection',
        'Try again in a few moments',
        'Clear your browser cache if the problem continues'
      ],
      canRetry: true,
      shouldReload: true
    };
  }
  
  if (message.includes('network') || message.includes('fetch') || message.includes('cors')) {
    return {
      type: 'network_error',
      title: 'Connection Problem',
      description: 'Unable to connect to our servers.',
      userMessage: 'We\'re having trouble connecting to our servers. Please check your connection.',
      suggestions: [
        'Check your internet connection',
        'Try refreshing the page',
        'Wait a moment and try again',
        'Contact support if the issue persists'
      ],
      canRetry: true,
      shouldReload: true
    };
  }
  
  if (message.includes('unauthorized') || message.includes('forbidden')) {
    return {
      type: 'access_error',
      title: 'Access Issue',
      description: 'You may not have permission to view this page.',
      userMessage: 'It looks like you don\'t have permission to access this page.',
      suggestions: [
        'Check that you\'re signed in to your account',
        'Verify you have the necessary permissions',
        'Try signing out and back in',
        'Contact your administrator for access'
      ],
      canRetry: false,
      shouldReload: false
    };
  }
  
  if (message.includes('not found') || message.includes('404')) {
    return {
      type: 'not_found',
      title: 'Page Not Found',
      description: 'The page you\'re looking for doesn\'t exist.',
      userMessage: 'We couldn\'t find the page you\'re looking for.',
      suggestions: [
        'Check the URL for any typos',
        'Go back to the previous page',
        'Visit the dashboard',
        'Use the navigation menu to find what you need'
      ],
      canRetry: false,
      shouldReload: false
    };
  }
  
  return {
    type: 'general_error',
    title: 'Unexpected Error',
    description: 'An unexpected error occurred on this page.',
    userMessage: 'We encountered an unexpected issue. This is usually temporary.',
    suggestions: [
      'Refresh the page to try again',
      'Go back and try a different approach',
      'Check your internet connection',
      'Contact support if the problem continues'
    ],
    canRetry: true,
    shouldReload: true
  };
};

/**
 * Enhanced page-level error fallback component
 */
const PageErrorFallback: React.FC<ErrorFallbackProps> = ({ 
  error, 
  resetErrorBoundary,
  context: _context 
}) => {
  const navigate = useNavigate();
  const [retryCount, setRetryCount] = React.useState(0);
  const [isRetrying, setIsRetrying] = React.useState(false);
  
  const errorAnalysis = analyzePageError(error);
  const maxRetries = 2;

  const handleRetry = async () => {
    if (!errorAnalysis.canRetry || retryCount >= maxRetries) return;
    
    setIsRetrying(true);
    setRetryCount(prev => prev + 1);
    
    // Add a delay for better UX
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    setIsRetrying(false);
    resetErrorBoundary();
  };

  const handleGoHome = () => {
    navigate('/dashboard');
  };

  const handleGoBack = () => {
    if (window.history.length > 1) {
      window.history.back();
    } else {
      navigate('/dashboard');
    }
  };

  const handleReload = () => {
    window.location.reload();
  };

  const handleContactSupport = () => {
    // This could open a support modal or navigate to a support page
    const subject = encodeURIComponent(`Page Error: ${errorAnalysis.title}`);
    const body = encodeURIComponent(
      `I encountered an error on the page.\n\nError Type: ${errorAnalysis.type}\nPage: ${window.location.pathname}\nTime: ${new Date().toISOString()}\n\nPlease help resolve this issue.`
    );
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="max-w-2xl w-full">
        <div className="text-center mb-8">
          <div className="w-20 h-20 bg-destructive/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertTriangle className="w-10 h-10 text-destructive" />
          </div>
          <h1 className="text-3xl font-bold mb-2">{errorAnalysis.title}</h1>
          <p className="text-muted-foreground text-lg">
            {errorAnalysis.userMessage}
          </p>
        </div>

        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>{errorAnalysis.title}</AlertTitle>
          <AlertDescription>
            {errorAnalysis.description}
          </AlertDescription>
        </Alert>

        {retryCount > 0 && (
          <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              {retryCount === 1 ? 'Tried once.' : `Tried ${retryCount} times.`}
              {retryCount >= maxRetries && ' Maximum retry attempts reached.'}
            </p>
          </div>
        )}

        <div className="bg-card border rounded-lg p-6 mb-6">
          <h3 className="font-semibold mb-4">What you can try:</h3>
          <ul className="space-y-3 text-sm text-muted-foreground">
            {errorAnalysis.suggestions.map((suggestion, index) => (
              <li key={index} className="flex items-start gap-3">
                <span className="w-1.5 h-1.5 bg-current rounded-full mt-2 flex-shrink-0"></span>
                <span>{suggestion}</span>
              </li>
            ))}
          </ul>
        </div>

        <div className="flex flex-col sm:flex-row gap-3 mb-6">
          {errorAnalysis.canRetry && retryCount < maxRetries && (
            <Button 
              onClick={handleRetry} 
              className="flex items-center justify-center gap-2"
              disabled={isRetrying}
            >
              <RefreshCw className={`h-4 w-4 ${isRetrying ? 'animate-spin' : ''}`} />
              {isRetrying ? 'Retrying...' : 'Try Again'}
            </Button>
          )}
          
          {errorAnalysis.shouldReload && (
            <Button 
              variant="outline" 
              onClick={handleReload} 
              className="flex items-center justify-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Reload Page
            </Button>
          )}
          
          <Button 
            variant="outline" 
            onClick={handleGoBack} 
            className="flex items-center justify-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Go Back
          </Button>
          
          <Button 
            variant="outline" 
            onClick={handleGoHome} 
            className="flex items-center justify-center gap-2"
          >
            <Home className="h-4 w-4" />
            Dashboard
          </Button>
        </div>

        <div className="text-center">
          <p className="text-sm text-muted-foreground mb-3">
            Still having trouble? We're here to help.
          </p>
          <Button 
            variant="link" 
            onClick={handleContactSupport}
            className="text-sm"
          >
            Contact Support
          </Button>
        </div>
      </div>
    </div>
  );
};

/**
 * Error boundary for entire pages
 */
export const PageErrorBoundary: React.FC<{ 
  children: React.ReactNode;
  pageName?: string;
}> = ({ children, pageName }) => {
  return (
    <BaseErrorBoundary
      fallback={PageErrorFallback}
      context={{
        component: 'page',
        action: 'page_load',
        additionalData: { pageName }
      }}
      resetKeys={pageName ? [pageName] : []} // Reset when page changes
    >
      {children}
    </BaseErrorBoundary>
  );
};

export default PageErrorBoundary;
