// Export all error boundary components
export { BaseErrorBoundary } from './BaseErrorBoundary';
export { RiskErrorBoundary } from './RiskErrorBoundary';
export { FormErrorBoundary } from './FormErrorBoundary';
export { PageErrorBoundary } from './PageErrorBoundary';
export { LazyLoadErrorBoundary } from './LazyLoadErrorBoundary';

// Export types
export type { ErrorBoundaryProps, ErrorFallbackProps } from './BaseErrorBoundary';

// Re-export error handling utilities for convenience
export {
  errorHandler,
  logger,
  AppError,
  ValidationError,
  NetworkError,
  AuthenticationError,
  AuthorizationError,
  BusinessLogicError,
  SystemError,
  DataProcessingError,
  ExternalServiceError
} from '@/utils/errors';
