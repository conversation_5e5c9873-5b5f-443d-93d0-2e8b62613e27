import React from "react";
import { BaseErrorBoundary, ErrorFallbackProps } from "./BaseErrorBoundary";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>riangle, RefreshCw, Home } from "lucide-react";
import { useNavigate } from "react-router-dom";
/**
 * Analyzes risk management errors and provides contextual recovery options
 */
const analyzeRiskError = (error: Error) => {
  const message = error.message.toLowerCase();
  if (
    message.includes("validation") ||
    message.includes("required") ||
    message.includes("invalid")
  ) {
    return {
      type: "validation_error",
      title: "Risk Data Validation Error",
      description: "There's an issue with the risk data format or required fields.",
      userMessage: "Some risk information needs to be corrected before proceeding.",
      suggestions: [
        "Check that all required risk fields are completed",
        "Verify risk scores are within valid ranges (1-5)",
        "Ensure dates are in the correct format",
        "Review risk categories and impact descriptions",
      ],
      canRetry: true,
      showDataRecovery: true,
    };
  }
  if (
    message.includes("permission") ||
    message.includes("unauthorized") ||
    message.includes("forbidden")
  ) {
    return {
      type: "permission_error",
      title: "Risk Access Restricted",
      description: "You don't have permission to access this risk information.",
      userMessage: "Access to this risk data is restricted based on your current permissions.",
      suggestions: [
        "Check that you're signed in to the correct account",
        "Verify your role includes risk management permissions",
        "Contact your administrator to request access",
        "Try accessing risks you have permission to view",
      ],
      canRetry: false,
      showDataRecovery: false,
    };
  }
  if (message.includes("network") || message.includes("fetch") || message.includes("timeout")) {
    return {
      type: "network_error",
      title: "Risk Data Connection Issue",
      description: "Unable to load risk data due to connection problems.",
      userMessage: "We're having trouble loading your risk data. This is usually temporary.",
      suggestions: [
        "Check your internet connection",
        "Try refreshing the risk data",
        "Wait a moment and try again",
        "Check if other parts of the application are working",
      ],
      canRetry: true,
      showDataRecovery: false,
    };
  }
  if (message.includes("not found") || message.includes("404")) {
    return {
      type: "not_found_error",
      title: "Risk Not Found",
      description: "The risk you're looking for doesn't exist or has been removed.",
      userMessage: "We couldn't find the risk you're trying to access.",
      suggestions: [
        "Check that the risk ID is correct",
        "Verify the risk hasn't been deleted",
        "Try searching for the risk in the register",
        "Contact the risk owner if you need access",
      ],
      canRetry: false,
      showDataRecovery: false,
    };
  }
  if (message.includes("calculation") || message.includes("score") || message.includes("matrix")) {
    return {
      type: "calculation_error",
      title: "Risk Calculation Error",
      description: "There's an issue with risk score calculations or matrix operations.",
      userMessage:
        "We encountered a problem calculating risk scores or generating the risk matrix.",
      suggestions: [
        "Check that probability and impact values are valid",
        "Verify risk matrix configuration is correct",
        "Try recalculating the risk scores",
        "Contact support if calculations seem incorrect",
      ],
      canRetry: true,
      showDataRecovery: true,
    };
  }
  return {
    type: "general_error",
    title: "Risk Management Error",
    description: "An unexpected error occurred in the risk management system.",
    userMessage: "We encountered an unexpected issue with the risk management system.",
    suggestions: [
      "Try refreshing the risk data",
      "Check your internet connection",
      "Navigate back to the risk register",
      "Contact support if the problem continues",
    ],
    canRetry: true,
    showDataRecovery: true,
  };
};
/**
 * Enhanced risk-specific error fallback component
 */
const RiskErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetErrorBoundary,
  context: _context,
}) => {
  const navigate = useNavigate();
  const [retryCount, setRetryCount] = React.useState(0);
  const [isRetrying, setIsRetrying] = React.useState(false);
  const [showTechnicalDetails, setShowTechnicalDetails] = React.useState(false);
  const errorAnalysis = analyzeRiskError(error);
  const maxRetries = 3;
  const handleRetry = async () => {
    if (retryCount >= maxRetries) return;
    setIsRetrying(true);
    setRetryCount(prev => prev + 1);
    // Add delay for better UX
    await new Promise(resolve => setTimeout(resolve, 800));
    setIsRetrying(false);
    resetErrorBoundary();
  };
  const handleGoHome = () => {
    navigate("/dashboard");
  };
  const handleGoToRiskRegister = () => {
    navigate("/risks");
  };
  const handleCreateNewRisk = () => {
    navigate("/risks/create");
  };
  const handleExportData = () => {
    // This would trigger a data export if possible
    try {
      const riskData = localStorage.getItem("riskData") ?? "{}";
      const blob = new Blob([riskData], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `risk-data-backup-${new Date().toISOString().split("T")[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (err) {
      // Error caught and handled
    }
  };
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <div className="text-center mb-6">
          <div className="w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertTriangle className="w-8 h-8 text-destructive" />
          </div>
          <h2 className="text-2xl font-bold mb-2">{errorAnalysis.title}</h2>
          <p className="text-muted-foreground">{errorAnalysis.userMessage}</p>
        </div>
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>{errorAnalysis.title}</AlertTitle>
          <AlertDescription>{errorAnalysis.description}</AlertDescription>
        </Alert>
        {retryCount > 0 && (
          <div className="mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              {retryCount === 1 ? "Tried once." : `Tried ${retryCount} times.`}
              {retryCount >= maxRetries && " Maximum retry attempts reached."}
            </p>
          </div>
        )}
        <div className="bg-card border rounded-lg p-6 mb-6">
          <h3 className="font-semibold mb-4">Recommended actions:</h3>
          <ul className="space-y-3 text-sm text-muted-foreground">
            {errorAnalysis.suggestions.map((suggestion, index) => (
              <li key={index} className="flex items-start gap-3">
                <span className="w-1.5 h-1.5 bg-current rounded-full mt-2 flex-shrink-0"></span>
                <span>{suggestion}</span>
              </li>
            ))}
          </ul>
          {showTechnicalDetails && (
            <div className="mt-4 pt-4 border-t">
              <h4 className="font-medium mb-2 text-sm">Technical Details:</h4>
              <div className="bg-muted rounded p-3">
                <code className="text-xs break-all font-mono">{error.message}</code>
              </div>
            </div>
          )}
        </div>
        <div className="flex flex-col sm:flex-row gap-3 mb-6">
          {errorAnalysis.canRetry && retryCount < maxRetries && (
            <Button
              onClick={handleRetry}
              className="flex items-center justify-center gap-2"
              disabled={isRetrying}
            >
              <RefreshCw className={`h-4 w-4 ${isRetrying ? "animate-spin" : ""}`} />
              {isRetrying ? "Retrying..." : "Try Again"}
            </Button>
          )}
          <Button
            variant="outline"
            onClick={handleGoToRiskRegister}
            className="flex items-center justify-center gap-2"
          >
            Risk Register
          </Button>
          {errorAnalysis.type !== "permission_error" && (
            <Button
              variant="outline"
              onClick={handleCreateNewRisk}
              className="flex items-center justify-center gap-2"
            >
              Create New Risk
            </Button>
          )}
          <Button
            variant="outline"
            onClick={handleGoHome}
            className="flex items-center justify-center gap-2"
          >
            <Home className="h-4 w-4" />
            Dashboard
          </Button>
        </div>
        {errorAnalysis.showDataRecovery && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
              Data Recovery Options
            </h4>
            <p className="text-sm text-blue-800 dark:text-blue-200 mb-3">
              If you have unsaved risk data, you can try to export it for backup.
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={handleExportData}
              className="border-blue-300 text-blue-700 hover:bg-blue-100 dark:border-blue-600 dark:text-blue-300 dark:hover:bg-blue-900/40"
            >
              Export Risk Data
            </Button>
          </div>
        )}
        <div className="text-center">
          <Button
            variant="link"
            onClick={() => setShowTechnicalDetails(!showTechnicalDetails)}
            className="text-sm text-muted-foreground"
          >
            {showTechnicalDetails ? "Hide" : "Show"} Technical Details
          </Button>
        </div>
      </div>
    </div>
  );
};
/**
 * Error boundary specifically for risk management components
 */
export const RiskErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <BaseErrorBoundary
      fallback={RiskErrorFallback}
      context={{
        component: "risk_management",
        action: "risk_operation",
      }}
      resetKeys={["risk"]} // Reset when risk-related props change
    >
      {children}
    </BaseErrorBoundary>
  );
};
export default RiskErrorBoundary;
