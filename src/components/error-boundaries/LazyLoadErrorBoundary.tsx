import React from "react";
import { BaseErrorBoundary, ErrorFallbackProps } from "./BaseErrorBoundary";
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>fresh<PERSON><PERSON>, Wifi, WifiOff } from "lucide-react";
/**
 * Determines the type and appropriate recovery strategy for lazy loading errors
 */
const analyzeLazyLoadError = (error: Error) => {
  const message = error.message.toLowerCase();
  if (message.includes("loading chunk") || message.includes("chunkloaderror")) {
    return {
      type: "chunk_load_failure",
      title: "Page Loading Issue",
      description: "Unable to load page content due to a network issue.",
      icon: "network",
      canRetry: true,
      shouldClearCache: true,
      retryDelay: 1000,
    };
  }
  if (message.includes("loading css chunk") || message.includes("css")) {
    return {
      type: "css_load_failure",
      title: "Style Loading Issue",
      description: "Page styles failed to load. The page may look different than expected.",
      icon: "style",
      canRetry: true,
      shouldClearCache: true,
      retryDelay: 500,
    };
  }
  if (message.includes("network") || message.includes("fetch")) {
    return {
      type: "network_error",
      title: "Connection Problem",
      description: "Unable to connect to the server. Please check your internet connection.",
      icon: "network",
      canRetry: true,
      shouldClearCache: false,
      retryDelay: 2000,
    };
  }
  if (message.includes("timeout")) {
    return {
      type: "timeout_error",
      title: "Loading Timeout",
      description: "The page is taking too long to load.",
      icon: "timeout",
      canRetry: true,
      shouldClearCache: false,
      retryDelay: 1500,
    };
  }
  return {
    type: "unknown_error",
    title: "Loading Error",
    description: "There was an unexpected issue loading this page.",
    icon: "error",
    canRetry: true,
    shouldClearCache: false,
    retryDelay: 1000,
  };
};
/**
 * Enhanced lazy loading error fallback with intelligent recovery
 */
const LazyLoadErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetErrorBoundary,
  context: _context,
}) => {
  const [retryCount, setRetryCount] = React.useState(0);
  const [isRetrying, setIsRetrying] = React.useState(false);
  const [connectionStatus, setConnectionStatus] = React.useState(navigator.onLine);
  const errorAnalysis = analyzeLazyLoadError(error);
  const maxRetries = 3;
  // Monitor connection status
  React.useEffect(() => {
    const handleOnline = () => setConnectionStatus(true);
    const handleOffline = () => setConnectionStatus(false);
    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);
    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, []);
  const handleRetry = async () => {
    if (retryCount >= maxRetries) return;
    setIsRetrying(true);
    setRetryCount(prev => prev + 1);
    try {
      // Clear service worker cache if needed
      if (errorAnalysis.shouldClearCache && "serviceWorker" in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        await Promise.all(registrations.map(registration => registration.update()));
      }
      // Clear browser cache for chunk loading errors
      if (errorAnalysis.type === "chunk_load_failure" && "caches" in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName =>
            cacheName.includes("chunk") || cacheName.includes("static")
              ? caches.delete(cacheName)
              : Promise.resolve()
          )
        );
      }
      // Wait for the specified delay
      await new Promise(resolve => setTimeout(resolve, errorAnalysis.retryDelay));
    } catch (cacheError) {
      // Error caught and handled
    }
    setIsRetrying(false);
    resetErrorBoundary();
  };
  const handleReload = () => {
    // Force a hard reload to bypass cache
    window.location.reload();
  };
  const getIcon = () => {
    switch (errorAnalysis.icon) {
      case "network":
        return connectionStatus ? (
          <Wifi className="w-8 h-8 text-destructive" />
        ) : (
          <WifiOff className="w-8 h-8 text-destructive" />
        );
      case "style":
        return <AlertTriangle className="w-8 h-8 text-orange-500" />;
      case "timeout":
        return <AlertTriangle className="w-8 h-8 text-yellow-500" />;
      default:
        return <AlertTriangle className="w-8 h-8 text-destructive" />;
    }
  };
  const getRetryButtonText = () => {
    if (isRetrying) return "Retrying...";
    if (retryCount === 0) return "Try Again";
    return `Try Again (${retryCount}/${maxRetries})`;
  };
  return (
    <div className="min-h-[400px] flex items-center justify-center p-4">
      <div className="max-w-md w-full text-center">
        <div className="w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mx-auto mb-4">
          {getIcon()}
        </div>
        <h2 className="text-xl font-semibold mb-2">{errorAnalysis.title}</h2>
        <p className="text-muted-foreground mb-4">{errorAnalysis.description}</p>
        {!connectionStatus && (
          <Alert variant="destructive" className="mb-4 text-left">
            <WifiOff className="h-4 w-4" />
            <AlertTitle>No Internet Connection</AlertTitle>
            <AlertDescription>
              You appear to be offline. Please check your connection and try again.
            </AlertDescription>
          </Alert>
        )}
        {retryCount > 0 && (
          <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded text-left">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              {retryCount === 1 ? "Tried once." : `Tried ${retryCount} times.`}
              {retryCount >= maxRetries && " Consider reloading the page for a fresh start."}
            </p>
          </div>
        )}
        <div className="space-y-3 text-sm text-muted-foreground mb-6 text-left">
          <p className="font-medium text-foreground">Troubleshooting steps:</p>
          <ul className="space-y-2">
            <li className="flex items-start gap-2">
              <span className="w-1.5 h-1.5 bg-current rounded-full mt-2 flex-shrink-0"></span>
              <span>Check your internet connection</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="w-1.5 h-1.5 bg-current rounded-full mt-2 flex-shrink-0"></span>
              <span>Try refreshing the page</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="w-1.5 h-1.5 bg-current rounded-full mt-2 flex-shrink-0"></span>
              <span>Clear your browser cache if the problem persists</span>
            </li>
            <li className="flex items-start gap-2">
              <span className="w-1.5 h-1.5 bg-current rounded-full mt-2 flex-shrink-0"></span>
              <span>Contact support if you continue to experience issues</span>
            </li>
          </ul>
        </div>
        <div className="flex flex-col gap-3">
          {retryCount < maxRetries && (
            <Button
              onClick={handleRetry}
              className="flex items-center justify-center gap-2"
              disabled={isRetrying || !connectionStatus}
            >
              <RefreshCw className={`h-4 w-4 ${isRetrying ? "animate-spin" : ""}`} />
              {getRetryButtonText()}
            </Button>
          )}
          <Button
            variant="outline"
            onClick={handleReload}
            className="flex items-center justify-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Reload Page
          </Button>
        </div>
        <p className="text-xs text-muted-foreground mt-4">
          {connectionStatus
            ? "If this problem persists, try clearing your browser cache or contact support."
            : "Waiting for internet connection to be restored..."}
        </p>
      </div>
    </div>
  );
};
/**
 * Error boundary specifically for lazy-loaded components
 */
export const LazyLoadErrorBoundary: React.FC<{
  children: React.ReactNode;
  routeName?: string;
}> = ({ children, routeName }) => {
  return (
    <BaseErrorBoundary
      fallback={LazyLoadErrorFallback}
      context={{
        component: "lazy-route",
        action: "lazy_load",
        additionalData: { routeName },
      }}
      resetKeys={routeName ? [routeName] : []} // Reset when route changes
    >
      {children}
    </BaseErrorBoundary>
  );
};
export default LazyLoadErrorBoundary;
