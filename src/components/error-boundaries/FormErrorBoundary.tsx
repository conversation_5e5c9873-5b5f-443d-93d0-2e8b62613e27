import React from "react";
import { BaseErrorBoundary, ErrorFallbackProps } from "./BaseErrorBoundary";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { <PERSON>ertCircle, RefreshCw, X } from "lucide-react";
/**
 * Determines the user-friendly message for form errors
 */
const getFormErrorMessage = (
  error: Error
): { title: string; description: string; suggestions: string[] } => {
  const message = error.message.toLowerCase();
  if (
    message.includes("validation") ||
    message.includes("required") ||
    message.includes("invalid")
  ) {
    return {
      title: "Form Validation Error",
      description: "Some information in the form needs to be corrected.",
      suggestions: [
        "Check that all required fields are filled out",
        "Verify that email addresses and phone numbers are in the correct format",
        "Ensure dates are valid and in the expected format",
        "Review any highlighted fields for specific requirements",
      ],
    };
  }
  if (message.includes("network") || message.includes("fetch") || message.includes("timeout")) {
    return {
      title: "Connection Issue",
      description: "Unable to save your form data due to a connection problem.",
      suggestions: [
        "Check your internet connection",
        "Try submitting the form again",
        "Save your work locally if possible",
        "Contact support if the problem continues",
      ],
    };
  }
  if (
    message.includes("unauthorized") ||
    message.includes("forbidden") ||
    message.includes("permission")
  ) {
    return {
      title: "Access Issue",
      description: "You may not have permission to perform this action.",
      suggestions: [
        "Check that you're signed in to your account",
        "Verify you have the necessary permissions",
        "Try refreshing the page and signing in again",
        "Contact your administrator if you need access",
      ],
    };
  }
  return {
    title: "Form Error",
    description: "There was an unexpected issue with the form.",
    suggestions: [
      "Try submitting the form again",
      "Refresh the page and re-enter your information",
      "Check your internet connection",
      "Contact support if the issue persists",
    ],
  };
};
/**
 * Enhanced form-specific error fallback component
 */
const FormErrorFallback: React.FC<ErrorFallbackProps> = ({
  error,
  resetErrorBoundary,
  context: _context,
}) => {
  const [retryCount, setRetryCount] = React.useState(0);
  const [isRetrying, setIsRetrying] = React.useState(false);
  const errorInfo = getFormErrorMessage(error);
  const maxRetries = 3;
  const handleRetry = async () => {
    if (retryCount >= maxRetries) return;
    setIsRetrying(true);
    setRetryCount(prev => prev + 1);
    // Add a small delay for better UX
    await new Promise(resolve => setTimeout(resolve, 500));
    setIsRetrying(false);
    resetErrorBoundary();
  };
  const handleClose = () => {
    // This would typically close a modal or navigate away
    window.history.back();
  };
  const handleSaveLocally = () => {
    // Attempt to save form data to localStorage
    try {
      const formData = document.querySelectorAll("input, textarea, select");
      const savedData: Record<string, string> = {};
      formData.forEach(element => {
        const input = element as HTMLInputElement;
        if (input.name && input.value) {
          savedData[input.name] = input.value;
        }
      });
      if (Object.keys(savedData).length > 0) {
        const timestamp = new Date().toISOString();
        localStorage.setItem(`form_backup_${timestamp}`, JSON.stringify(savedData));
        alert("Form data has been saved locally. You can restore it when you return to this form.");
      }
    } catch (err) {
      // Error caught and handled
    }
  };
  return (
    <div className="p-6">
      <Alert variant="destructive" className="mb-4">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>{errorInfo.title}</AlertTitle>
        <AlertDescription>{errorInfo.description}</AlertDescription>
      </Alert>
      <div className="bg-card border rounded-lg p-4">
        {retryCount > 0 && (
          <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              {retryCount === 1 ? "Tried once." : `Tried ${retryCount} times.`}
              {retryCount >= maxRetries && " Maximum retry attempts reached."}
            </p>
          </div>
        )}
        <div className="space-y-3 text-sm text-muted-foreground mb-4">
          <p className="font-medium text-foreground">What you can try:</p>
          <ul className="space-y-2">
            {errorInfo.suggestions.map((suggestion, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="w-1.5 h-1.5 bg-current rounded-full mt-2 flex-shrink-0"></span>
                <span>{suggestion}</span>
              </li>
            ))}
          </ul>
        </div>
        <div className="flex flex-wrap gap-2">
          {retryCount < maxRetries && (
            <Button
              onClick={handleRetry}
              size="sm"
              className="flex items-center gap-2"
              disabled={isRetrying}
            >
              <RefreshCw className={`h-3 w-3 ${isRetrying ? "animate-spin" : ""}`} />
              {isRetrying ? "Retrying..." : "Try Again"}
            </Button>
          )}
          <Button
            variant="outline"
            onClick={handleSaveLocally}
            size="sm"
            className="flex items-center gap-2"
          >
            Save Locally
          </Button>
          <Button
            variant="outline"
            onClick={handleClose}
            size="sm"
            className="flex items-center gap-2"
          >
            <X className="h-3 w-3" />
            Close
          </Button>
        </div>
      </div>
    </div>
  );
};
/**
 * Error boundary specifically for form components
 */
export const FormErrorBoundary: React.FC<{
  children: React.ReactNode;
  formName?: string;
}> = ({ children, formName }) => {
  return (
    <BaseErrorBoundary
      fallback={FormErrorFallback}
      context={{
        component: "form",
        action: "form_interaction",
        additionalData: { formName },
      }}
      resetKeys={formName ? [formName] : []} // Reset when form changes
    >
      {children}
    </BaseErrorBoundary>
  );
};
export default FormErrorBoundary;
