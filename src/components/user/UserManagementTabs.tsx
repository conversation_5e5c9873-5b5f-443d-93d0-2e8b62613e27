
import { useState } from "react";
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { UsersTabContent } from "./tabs/UsersTabContent";
import { SetupTabContent } from "./tabs/SetupTabContent";
import AdminRequestsList from "./AdminRequestsList";
import { RiskTransfersList } from "./tabs/RiskTransfersList";
import { User } from "@/types";

interface UserManagementTabsProps {
  users: User[];
  loading: boolean;
  currentUserId: string;
  onUserUpdated: () => void;
}

export function UserManagementTabs({ 
  users, 
  loading, 
  currentUserId,
  onUserUpdated 
}: UserManagementTabsProps) {
  const [currentTab, setCurrentTab] = useState("users");

  return (
    <Tabs 
      value={currentTab} 
      onValueChange={setCurrentTab} 
      className="space-y-4"
    >
      <div className="flex justify-between items-center">
        <TabsList className="grid w-full grid-cols-4 max-w-md">
          <TabsTrigger value="users" className="text-xs sm:text-sm">Users</TabsTrigger>
          <TabsTrigger value="setup" className="text-xs sm:text-sm">Setup</TabsTrigger>
          <TabsTrigger value="admin" className="text-xs sm:text-sm">Admin</TabsTrigger>
          <TabsTrigger value="transfers" className="text-xs sm:text-sm">Transfers</TabsTrigger>
        </TabsList>
      </div>
      
      <TabsContent value="users" className="space-y-4">
        <UsersTabContent 
          users={users} 
          loading={loading} 
          currentUserId={currentUserId}
          onUserUpdated={onUserUpdated}
        />
      </TabsContent>
      
      <TabsContent value="setup" className="space-y-6">
        <SetupTabContent onUserUpdated={onUserUpdated} />
      </TabsContent>
      
      <TabsContent value="admin" className="space-y-6">
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">Administrative Requests</h3>
            <AdminRequestsList />
          </div>
        </div>
      </TabsContent>
      
      <TabsContent value="transfers" className="space-y-4">
        <RiskTransfersList onTransferProcessed={onUserUpdated} />
      </TabsContent>
    </Tabs>
  );
}
