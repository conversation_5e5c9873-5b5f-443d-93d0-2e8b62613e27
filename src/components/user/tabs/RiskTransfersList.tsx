import { useState, useEffect, useCallback } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { AlertCircle, CheckCircle, Loader2 } from "lucide-react";
import { getPendingRiskTransfers, completeRiskTransfer } from "@/services/user";
import { User } from "@/types";
interface RiskTransfer {
  id: string;
  risk_id: string;
  risk_title: string;
  old_owner_id: string;
  old_owner_name: string;
  new_owner_id: string | null;
  transferred_at: string;
}
interface RiskTransfersListProps {
  onTransferProcessed?: () => void;
}
export function RiskTransfersList({ onTransferProcessed }: RiskTransfersListProps) {
  const [transfers, setTransfers] = useState<RiskTransfer[]>([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState<Record<string, boolean>>({});
  const [owners, setOwners] = useState<User[]>([]);
  const { toast } = useToast();
  const loadRiskTransfers = useCallback(async () => {
    try {
      setLoading(true);
      const { data, error, owners: ownersData } = await getPendingRiskTransfers();
      if (error) throw error;

      // Type the data properly
      const typedTransfers: RiskTransfer[] = (data as RiskTransfer[]) ?? [];
      setTransfers(typedTransfers);
      setOwners(ownersData ?? []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Could not load risk transfer requests",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);
  useEffect(() => {
    loadRiskTransfers();
  }, [loadRiskTransfers]);
  const handleAssignRisk = async (transferId: string, riskId: string, newOwnerId: string) => {
    try {
      setProcessing(prev => ({ ...prev, [transferId]: true }));
      const { success, error } = await completeRiskTransfer(transferId, riskId, newOwnerId);
      if (!success) throw error;
      // Update local state
      setTransfers(prev => prev.filter(t => t.id !== transferId));
      toast({
        title: "Risk Assigned",
        description: "Risk has been successfully assigned to new owner",
      });
      if (onTransferProcessed) onTransferProcessed();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to assign risk to new owner",
        variant: "destructive",
      });
    } finally {
      setProcessing(prev => ({ ...prev, [transferId]: false }));
    }
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl">Risk Ownership Transfers</CardTitle>
        <CardDescription>
          Manage risks that need to be reassigned after user deletion
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {loading ? (
            // Skeleton loaders for loading state
            Array(3)
              .fill(0)
              .map((_, i) => (
                <Card key={i} className="p-4">
                  <div className="space-y-2">
                    <Skeleton className="h-5 w-40" />
                    <Skeleton className="h-4 w-full" />
                    <div className="flex justify-end gap-2">
                      <Skeleton className="h-9 w-32" />
                      <Skeleton className="h-9 w-32" />
                    </div>
                  </div>
                </Card>
              ))
          ) : transfers.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground flex flex-col items-center">
              <CheckCircle className="h-12 w-12 text-green-500 mb-2" />
              <p>No pending risk transfers</p>
              <p className="text-sm">All risks have been assigned to active users</p>
            </div>
          ) : (
            transfers.map(transfer => (
              <Card key={transfer.id} className="p-4">
                <div className="space-y-3">
                  <div>
                    <div className="font-medium">{transfer.risk_title}</div>
                    <div className="text-sm text-muted-foreground">
                      Previously owned by: {transfer.old_owner_name ?? "Unknown"}
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center text-amber-600">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      <span className="text-sm">Requires assignment</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <select
                        className="border rounded px-2 py-1 text-sm focus:outline-none focus:ring-2"
                        disabled={!!processing[transfer.id]}
                        onChange={e => {
                          if (e.target.value) {
                            handleAssignRisk(transfer.id, transfer.risk_id, e.target.value);
                          }
                        }}
                        defaultValue=""
                      >
                        <option value="" disabled>
                          Assign to...
                        </option>
                        {owners.map(owner => (
                          <option key={owner.id} value={owner.id}>
                            {owner.name} ({owner.role})
                          </option>
                        ))}
                      </select>
                      {processing[transfer.id] && (
                        <Loader2 className="h-4 w-4 animate-spin text-primary" />
                      )}
                    </div>
                  </div>
                </div>
              </Card>
            ))
          )}
          {!loading && (
            <div className="flex justify-end">
              <Button variant="outline" onClick={loadRiskTransfers} disabled={loading}>
                Refresh
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
