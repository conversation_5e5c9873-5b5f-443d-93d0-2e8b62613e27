
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { BulkInviteUsers } from "../BulkInviteUsers";
import { InviteCodeGenerator } from "../InviteCodeGenerator";
import { InvitationsList } from "../InvitationsList";

interface SetupTabContentProps {
  onUserUpdated: () => void;
}

export function SetupTabContent({ onUserUpdated }: SetupTabContentProps) {
  const [activeSubTab, setActiveSubTab] = useState<"bulk" | "single" | "status">("bulk");

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium mb-4">Invite New Users</h3>
        
        {/* Setup sub-navigation using buttons */}
        <div className="flex flex-wrap gap-2 mb-4">
          <Button
            variant={activeSubTab === "bulk" ? "default" : "outline"}
            size="sm"
            onClick={() => setActiveSubTab("bulk")}
          >
            Bulk Invite
          </Button>
          <Button
            variant={activeSubTab === "single" ? "default" : "outline"}
            size="sm"
            onClick={() => setActiveSubTab("single")}
          >
            Single Invite
          </Button>
          <Button
            variant={activeSubTab === "status" ? "default" : "outline"}
            size="sm"
            onClick={() => setActiveSubTab("status")}
          >
            Invitation Status
          </Button>
        </div>
        
        {/* Content based on selected sub-tab */}
        {activeSubTab === "bulk" && (
          <BulkInviteUsers onInvitesSent={onUserUpdated} />
        )}
        
        {activeSubTab === "single" && (
          <InviteCodeGenerator onGenerated={onUserUpdated} />
        )}
        
        {activeSubTab === "status" && (
          <InvitationsList onRefresh={onUserUpdated} />
        )}
      </div>
    </div>
  );
}
