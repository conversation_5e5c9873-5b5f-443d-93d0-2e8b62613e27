import { useState } from "react";
import { User, UserRole } from "@/types";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { UserRoleSelect } from "../UserRoleSelect";
import { updateUserRole } from "@/services/user";
import { useToast } from "@/components/ui/use-toast";
import { Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { UserDeleteDialog } from "../UserDeleteDialog";
interface UsersTabContentProps {
  users: User[];
  loading: boolean;
  currentUserId: string;
  onUserUpdated: () => void;
}
export function UsersTabContent({
  users,
  loading,
  currentUserId,
  onUserUpdated,
}: UsersTabContentProps) {
  const [updatingUsers, setUpdatingUsers] = useState<Record<string, boolean>>({});
  const [userToDelete, setUserToDelete] = useState<User | null>(null);
  const { toast } = useToast();
  const handleRoleChange = async (userId: string, newRole: UserRole): Promise<boolean> => {
    try {
      setUpdatingUsers(prev => ({ ...prev, [userId]: true }));
      const { success, error } = await updateUserRole(userId, newRole);
      if (!success) {
        throw error || new Error("Failed to update user role");
      }
      toast({
        title: "Role Updated",
        description: "User role has been successfully updated",
        variant: "default",
      });
      onUserUpdated();
      return true;
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update user role",
        variant: "destructive",
      });
      return false;
    } finally {
      setUpdatingUsers(prev => ({ ...prev, [userId]: false }));
    }
  };
  const handleDeleteComplete = () => {
    setUserToDelete(null);
    onUserUpdated();
  };
  return (
    <div className="grid gap-4">
      {loading
        ? // Skeleton loaders
          Array(5)
            .fill(0)
            .map((_, i) => (
              <Card key={i} className="p-4">
                <div className="flex justify-between items-center">
                  <div className="space-y-2">
                    <Skeleton className="h-5 w-40" />
                    <Skeleton className="h-4 w-60" />
                  </div>
                  <Skeleton className="h-9 w-32" />
                </div>
              </Card>
            ))
        : users.map(user => (
            <Card key={user.id} className="p-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="font-medium">{user.name}</h3>
                  <p className="text-sm text-muted-foreground">{user.email}</p>
                </div>
                <div className="flex items-center gap-3">
                  <UserRoleSelect
                    userId={user.id}
                    currentRole={user.role}
                    onRoleChange={handleRoleChange}
                    disabled={user.id === currentUserId || !!updatingUsers[user.id]}
                    isLoading={!!updatingUsers[user.id]}
                  />
                  {user.id !== currentUserId && (
                    <Button
                      variant="ghost"
                      size="icon"
                      className="text-destructive hover:bg-destructive/10"
                      onClick={() => setUserToDelete(user)}
                    >
                      <Trash2 className="h-5 w-5" />
                    </Button>
                  )}
                </div>
              </div>
            </Card>
          ))}
      {/* User Delete Dialog */}
      {userToDelete && (
        <UserDeleteDialog
          user={userToDelete}
          onCancel={() => setUserToDelete(null)}
          onComplete={handleDeleteComplete}
        />
      )}
    </div>
  );
}
