import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Users } from "lucide-react";
import { EmailInputStep } from "./bulk-invite/EmailInputStep";
import { RoleAssignmentStep } from "./bulk-invite/RoleAssignmentStep";
import { InvitationResultsStep } from "./bulk-invite/InvitationResultsStep";
import { useBulkInviteLogic } from "./bulk-invite/useBulkInviteLogic";

interface BulkInviteUsersProps {
  onInvitesSent?: () => void;
}

export function BulkInviteUsers({ onInvitesSent }: BulkInviteUsersProps) {
  const {
    emailText,
    validEmails,
    invalidEmails,
    emailsWithRoles,
    currentStep,
    isProcessing,
    results,
    error,
    setEmailText,
    handleAssignRoles,
    updateEmailRole,
    handleSendInvites,
    handleReset,
    goBackToInput,
  } = useBulkInviteLogic(onInvitesSent);

  const renderCurrentStep = () => {
    switch (currentStep) {
      case "input":
        return (
          <EmailInputStep
            emailText={emailText}
            onEmailTextChange={setEmailText}
            onNext={handleAssignRoles}
            error={error}
          />
        );
      case "assign-roles":
        return (
          <RoleAssignmentStep
            validEmails={validEmails}
            invalidEmails={invalidEmails}
            emailsWithRoles={emailsWithRoles}
            onUpdateEmailRole={updateEmailRole}
            onBack={goBackToInput}
            onSendInvites={handleSendInvites}
            isProcessing={isProcessing}
          />
        );
      case "results":
        return <InvitationResultsStep results={results} onReset={handleReset} />;
      default:
        return null;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl flex items-center gap-2">
          <Users className="h-5 w-5" />
          Bulk Invite Users
        </CardTitle>
        <CardDescription>
          Invite multiple users at once by entering their email addresses
        </CardDescription>
      </CardHeader>

      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {renderCurrentStep()}
      </CardContent>
    </Card>
  );
}
