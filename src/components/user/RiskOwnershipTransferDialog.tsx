import { useState, useEffect } from "react";
import { User } from "@/types";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { getUserRisks, getRiskOwnerCandidates, transferRiskOwnership } from "@/services/user";
import { useToast } from "@/components/ui/use-toast";
import { ScrollArea } from "@/components/ui/scroll-area";
interface TransferRisk {
  id: string;
  title: string;
  owner_id: string | null;
}
interface RiskOwnershipTransferDialogProps {
  user: User;
  onCancel: () => void;
  onComplete: () => void;
}
export function RiskOwnershipTransferDialog({
  user,
  onCancel,
  onComplete,
}: RiskOwnershipTransferDialogProps) {
  const [risks, setRisks] = useState<TransferRisk[]>([]);
  const [candidates, setCandidates] = useState<User[]>([]);
  const [selectedOwner, setSelectedOwner] = useState<string>("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  // Fetch risks and candidates on mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        // Fetch risks owned by the user
        const { data: risksData, error: risksError } = await getUserRisks(user.id);
        if (risksError) throw risksError;
        // Fetch potential new owners (risk owners and admins)
        const { data: candidatesData, error: candidatesError } = await getRiskOwnerCandidates(
          user.id
        );
        if (candidatesError) throw candidatesError;
        // Map the database response to our expected format
        const mappedRisks: TransferRisk[] =
          risksData?.map(item => ({
            id: item.id,
            title: item.title,
            owner_id: item.owner_id,
          })) || [];

        setRisks(mappedRisks);
        setCandidates(candidatesData ?? []);
        // Pre-select first candidate if available
        if (candidatesData && candidatesData.length > 0) {
          setSelectedOwner(candidatesData[0]!.id);
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to load risk ownership data",
          variant: "destructive",
        });
        onCancel();
      } finally {
        setIsLoading(false);
      }
    };
    fetchData();
  }, [user.id, toast, onCancel]);
  const handleTransfer = async () => {
    if (!selectedOwner) {
      toast({
        title: "Error",
        description: "Please select a new owner for the risks",
        variant: "destructive",
      });
      return;
    }
    try {
      setIsSubmitting(true);
      // Transfer all risks to the selected owner
      const { success, error } = await transferRiskOwnership(
        user.id,
        selectedOwner,
        risks.map(risk => risk.id)
      );
      if (!success) {
        throw error || new Error("Failed to transfer risk ownership");
      }
      toast({
        title: "Risks Transferred",
        description: `Successfully transferred ${risks.length} risks to new owner`,
      });
      // Continue with user deletion
      onComplete();
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to transfer risk ownership";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  const handleSkip = () => {
    toast({
      title: "No Owner Assigned",
      description: "Risks will be marked for later assignment by an administrator",
    });
    onComplete();
  };
  return (
    <Dialog open={true} onOpenChange={isOpen => !isOpen && onCancel()}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Transfer Risk Ownership</DialogTitle>
          <DialogDescription>
            {user.name} owns {risks.length} risks that need to be assigned to a new owner.
          </DialogDescription>
        </DialogHeader>
        {isLoading ? (
          <div className="flex justify-center py-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : (
          <>
            <div className="space-y-4 py-2">
              <div>
                <h3 className="mb-2 font-medium">Risks to transfer:</h3>
                <ScrollArea className="max-h-40 overflow-auto">
                  <ul className="space-y-1 text-sm">
                    {risks.map(risk => (
                      <li key={risk.id} className="border-b border-muted pb-1">
                        {risk.title}
                      </li>
                    ))}
                  </ul>
                </ScrollArea>
              </div>
              <div className="space-y-1">
                <label htmlFor="new-owner" className="text-sm font-medium">
                  New Owner
                </label>
                <Select
                  disabled={isSubmitting}
                  value={selectedOwner}
                  onValueChange={setSelectedOwner}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a new owner" />
                  </SelectTrigger>
                  <SelectContent>
                    {candidates.length === 0 ? (
                      <SelectItem value="no-candidates" disabled>
                        No eligible users available
                      </SelectItem>
                    ) : (
                      candidates.map(candidate => (
                        <SelectItem key={candidate.id} value={candidate.id}>
                          {candidate.name} ({candidate.role})
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter className="flex-col sm:flex-row sm:justify-between">
              <Button variant="ghost" onClick={handleSkip} disabled={isSubmitting}>
                Skip Transfer
              </Button>
              <div className="flex gap-2">
                <Button variant="outline" onClick={onCancel} disabled={isSubmitting}>
                  Cancel
                </Button>
                <Button
                  onClick={handleTransfer}
                  disabled={!selectedOwner || isSubmitting || candidates.length === 0}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Transferring...
                    </>
                  ) : (
                    <>Transfer & Delete</>
                  )}
                </Button>
              </div>
            </DialogFooter>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
