import { useState } from "react";
import {
  Select,
  SelectContent,
  SelectI<PERSON>,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UserRole } from "@/types";
import { Button } from "@/components/ui/button";
import { Check, Loader2 } from "lucide-react";
interface UserRoleSelectProps {
  userId: string;
  currentRole: UserRole;
  onRoleChange: (userId: string, role: UserRole) => Promise<boolean>;
  disabled?: boolean;
  isLoading?: boolean;
}
export function UserRoleSelect({
  userId,
  currentRole,
  onRoleChange,
  disabled = false,
  isLoading = false,
}: UserRoleSelectProps) {
  const [selectedRole, setSelectedRole] = useState<UserRole>(currentRole);
  const [isSaving, setIsSaving] = useState(false);
  const hasChanges = selectedRole !== currentRole;
  const handleRoleSelect = (value: string) => {
    setSelectedRole(value as UserRole);
  };
  const handleSave = async () => {
    if (!hasChanges) return;
    setIsSaving(true);
    try {
      const success = await onRoleChange(userId, selectedRole);
      if (!success) {
        // Reset to current role if save failed
        setSelectedRole(currentRole);
      }
    } catch (error) {
      // Reset to current role on error
      setSelectedRole(currentRole);
    } finally {
      setIsSaving(false);
    }
  };
  return (
    <div className="flex items-center gap-2">
      <Select
        value={selectedRole}
        onValueChange={handleRoleSelect}
        disabled={disabled || isSaving || isLoading}
      >
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Select role" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value={UserRole.ADMIN}>Administrator</SelectItem>
          <SelectItem value={UserRole.RISK_OWNER}>Risk Owner</SelectItem>
          <SelectItem value={UserRole.STAFF}>Staff</SelectItem>
          <SelectItem value={UserRole.BOARD_MEMBER}>Board Member</SelectItem>
        </SelectContent>
      </Select>
      {hasChanges && (
        <Button size="sm" onClick={handleSave} disabled={isSaving || isLoading} className="ml-2">
          {isSaving ? (
            <>
              <Loader2 className="mr-1 h-4 w-4 animate-spin" />
              <span>Saving</span>
            </>
          ) : (
            <>
              <Check className="w-4 h-4 mr-1" />
              Save
            </>
          )}
        </Button>
      )}
    </div>
  );
}
