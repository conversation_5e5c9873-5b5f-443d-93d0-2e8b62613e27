
import { Card } from "@/components/ui/card";
import { StatusBadge } from "@/components/ui/status-badge";
import { Button } from "@/components/ui/button";
import { Check, X } from "lucide-react";

interface AdminRequest {
  id: string;
  user_id: string;
  justification: string;
  status: 'pending' | 'approved' | 'rejected';
  created_at: string;
  reviewer_id?: string;
  feedback?: string;
  user_name?: string;
  user_email?: string;
}

interface AdminRequestCardProps {
  request: AdminRequest;
  onApprove: (requestId: string, userId: string) => void;
  onReject: (requestId: string, userId: string) => void;
  isProcessing: boolean;
}

export function AdminRequestCard({ 
  request, 
  onApprove, 
  onReject, 
  isProcessing 
}: AdminRequestCardProps) {
  return (
    <Card className="p-4">
      <div className="space-y-3">
        <div className="flex justify-between items-start">
          <div>
            <div className="font-medium">{request.user_name ?? 'Unknown User'}</div>
            <div className="text-sm text-muted-foreground">{request.user_email}</div>
          </div>
          <div className="flex items-center gap-2">
            <StatusBadge status={request.status} />
            <div className="text-xs text-muted-foreground">
              {new Date(request.created_at).toLocaleDateString()}
            </div>
          </div>
        </div>
        
        <div className="text-sm bg-muted p-3 rounded-sm">
          <div className="font-medium mb-1">Justification:</div>
          <div>{request.justification}</div>
        </div>
        
        {request.status === 'pending' && (
          <div className="flex justify-end gap-2">
            <Button
              onClick={() => onReject(request.id, request.user_id)}
              variant="outline"
              size="sm"
              disabled={isProcessing}
              className="border-red-200 text-red-600 hover:bg-red-50"
            >
              <X className="h-4 w-4 mr-1" /> Reject
            </Button>
            <Button
              onClick={() => onApprove(request.id, request.user_id)}
              variant="outline"
              size="sm"
              disabled={isProcessing}
              className="border-green-200 text-green-600 hover:bg-green-50"
            >
              <Check className="h-4 w-4 mr-1" /> Approve
            </Button>
          </div>
        )}
      </div>
    </Card>
  );
}
