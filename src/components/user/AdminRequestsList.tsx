import { useState, useEffect, useCallback } from "react";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Check, X } from "lucide-react";
import { log } from "@/services/loggingService";

interface AdminRequest {
  id: string;
  user_id: string;
  organization_id: string | null;
  justification: string;
  status: string;
  created_at: string;
  feedback: string | null;
  reviewer_id: string | null;
}

const AdminRequestsList = () => {
  const [requests, setRequests] = useState<AdminRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const loadAdminRequests = useCallback(async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("admin_requests")
        .select(
          `
          id,
          user_id,
          organization_id,
          justification,
          status,
          created_at,
          feedback,
          reviewer_id
        `
        )
        .order("created_at", { ascending: false });

      if (error) throw error;

      // Map the database response to our expected format
      const mappedRequests: AdminRequest[] =
        data?.map(item => ({
          id: item.id,
          user_id: item.user_id,
          organization_id: item.organization_id,
          justification: item.justification,
          status: item.status,
          created_at: item.created_at,
          feedback: item.feedback,
          reviewer_id: item.reviewer_id,
        })) || [];

      setRequests(mappedRequests);
    } catch (error) {
      log.error("Error loading admin requests", error, {
        component: "AdminRequestsList",
        action: "loadAdminRequests",
      });
      toast({
        title: "Error",
        description: "Failed to load admin requests",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    loadAdminRequests();
  }, [loadAdminRequests]);

  const handleRequestAction = async (requestId: string, action: "approve" | "reject") => {
    try {
      const { error } = await supabase
        .from("admin_requests")
        .update({ status: action === "approve" ? "approved" : "rejected" })
        .eq("id", requestId);

      if (error) throw error;

      toast({
        title: "Success",
        description: `Request ${action}d successfully`,
      });

      // Reload requests
      loadAdminRequests();
    } catch (error) {
      log.error(`Error ${action}ing request`, error, {
        component: "AdminRequestsList",
        action: `${action}Request`,
      });
      toast({
        title: "Error",
        description: `Failed to ${action} request`,
        variant: "destructive",
      });
    }
  };

  if (loading) {
    return <div>Loading admin requests...</div>;
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Admin Access Requests</h3>

      {requests.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <p className="text-center text-muted-foreground">No admin requests found</p>
          </CardContent>
        </Card>
      ) : (
        requests.map(request => (
          <Card key={request.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-base">Request #{request.id.slice(-8)}</CardTitle>
                  <CardDescription>
                    Requested on {new Date(request.created_at).toLocaleDateString()}
                    {request.justification && ` - ${request.justification}`}
                  </CardDescription>
                </div>
                <Badge
                  variant={
                    request.status === "pending"
                      ? "secondary"
                      : request.status === "approved"
                        ? "default"
                        : "destructive"
                  }
                >
                  {request.status}
                </Badge>
              </div>
            </CardHeader>

            {request.status === "pending" && (
              <CardContent>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    onClick={() => handleRequestAction(request.id, "approve")}
                    className="flex items-center gap-1"
                  >
                    <Check className="h-4 w-4" />
                    Approve
                  </Button>
                  <Button
                    size="sm"
                    variant="destructive"
                    onClick={() => handleRequestAction(request.id, "reject")}
                    className="flex items-center gap-1"
                  >
                    <X className="h-4 w-4" />
                    Reject
                  </Button>
                </div>
              </CardContent>
            )}
          </Card>
        ))
      )}
    </div>
  );
};

export default AdminRequestsList;
