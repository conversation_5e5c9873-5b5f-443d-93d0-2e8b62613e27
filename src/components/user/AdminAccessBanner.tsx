
import { useState } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { AdminRequestDialog } from "./AdminRequestDialog";

export function AdminAccessBanner() {
  const [adminDialogOpen, setAdminDialogOpen] = useState(false);

  return (
    <>
      <div className="mb-6">
        <Card className="p-4 bg-amber-50 border-amber-200">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div>
              <h3 className="text-lg font-medium">Need administrator privileges?</h3>
              <p className="text-muted-foreground">
                Submit a request to become an administrator
              </p>
            </div>
            <Button onClick={() => setAdminDialogOpen(true)} variant="default">
              Request admin access
            </Button>
          </div>
        </Card>
      </div>
      
      <AdminRequestDialog 
        isOpen={adminDialogOpen}
        onClose={() => setAdminDialogOpen(false)}
      />
    </>
  );
}
