import { useState } from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { AlertCircle, CheckCircle, Copy, RefreshCw } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useToast } from "@/components/ui/use-toast";
import { generateInviteCode } from "@/services/user/inviteCodeService";
import { useAuth } from "@/contexts/auth";
import { UserRole } from "@/types";
interface InviteCodeGeneratorProps {
  onGenerated?: () => void;
}
export function InviteCodeGenerator({ onGenerated }: InviteCodeGeneratorProps) {
  const [role, setRole] = useState<UserRole>(UserRole.STAFF);
  const [isGenerating, setIsGenerating] = useState(false);
  const [generatedCode, setGeneratedCode] = useState<string | null>(null);
  const [error, setError] = useState("");
  const [copied, setCopied] = useState(false);
  const { toast } = useToast();
  const { user } = useAuth();
  const handleGenerateCode = async () => {
    if (!user?.id) {
      setError("You must be logged in to generate invite codes");
      return;
    }
    setIsGenerating(true);
    setError("");
    setGeneratedCode(null);
    setCopied(false);
    try {
      const result = await generateInviteCode(role, user.id);
      if (result.error || !result.code) {
        throw new Error("Failed to generate invite code");
      }
      setGeneratedCode(result.code);
      toast({
        title: "Code Generated",
        description: "Invite code has been successfully created",
        variant: "default",
      });
      if (onGenerated) onGenerated();
    } catch (err) {
      setError(err instanceof Error ? err.message : "An unexpected error occurred");
      toast({
        title: "Error",
        description: "Failed to generate invite code",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };
  const handleCopy = async () => {
    if (!generatedCode) return;
    try {
      await navigator.clipboard.writeText(generatedCode);
      setCopied(true);
      setTimeout(() => setCopied(false), 3000);
      toast({
        title: "Copied",
        description: "Invite code copied to clipboard",
        variant: "default",
      });
    } catch (err) {
      toast({
        title: "Error",
        description: "Failed to copy to clipboard",
        variant: "destructive",
      });
    }
  };
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl">Generate Invite Codes</CardTitle>
        <CardDescription>
          Create invite codes for new users to register with specific roles
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="role">Role for New User</Label>
            <Select value={role} onValueChange={(value: UserRole) => setRole(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={UserRole.ADMIN}>Administrator</SelectItem>
                <SelectItem value={UserRole.RISK_OWNER}>Risk Owner</SelectItem>
                <SelectItem value={UserRole.STAFF}>Staff</SelectItem>
                <SelectItem value={UserRole.BOARD_MEMBER}>Board Member</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {generatedCode && (
            <div className="space-y-2 mt-6">
              <Label htmlFor="inviteCode">Invite Code</Label>
              <div className="flex items-center gap-2">
                <Input id="inviteCode" value={generatedCode} readOnly className="font-mono" />
                <Button
                  type="button"
                  size="icon"
                  variant="outline"
                  onClick={handleCopy}
                  className="flex-shrink-0"
                >
                  {copied ? <CheckCircle className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button
          type="button"
          variant="default"
          onClick={handleGenerateCode}
          disabled={isGenerating}
        >
          {isGenerating ? (
            <>
              <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
              Generating...
            </>
          ) : (
            "Generate Invite Code"
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
