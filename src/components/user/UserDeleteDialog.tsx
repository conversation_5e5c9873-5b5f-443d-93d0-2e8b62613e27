import { useState } from "react";
import { User } from "@/types";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { deleteUser, getUserRisks } from "@/services/user";
import { useToast } from "@/components/ui/use-toast";
import { Button } from "@/components/ui/button";
import { Loader2, AlertCircle } from "lucide-react";
import { RiskOwnershipTransferDialog } from "./RiskOwnershipTransferDialog";
interface UserDeleteDialogProps {
  user: User;
  onCancel: () => void;
  onComplete: () => void;
}
export function UserDeleteDialog({ user, onCancel, onComplete }: UserDeleteDialogProps) {
  const [isDeleting, setIsDeleting] = useState(false);
  const [showTransferDialog, setShowTransferDialog] = useState(false);
  const [, setHasRisks] = useState<boolean | null>(null);
  const [isCheckingRisks, setIsCheckingRisks] = useState(false);
  const { toast } = useToast();
  const checkForRisks = async () => {
    try {
      setIsCheckingRisks(true);
      const { data, error } = await getUserRisks(user.id);
      if (error) throw error;
      const hasAssignedRisks = (data?.length ?? 0) > 0;
      setHasRisks(hasAssignedRisks);
      if (hasAssignedRisks) {
        setShowTransferDialog(true);
      } else {
        // No risks to transfer, proceed with deletion
        handleDeleteUser();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to check if user has assigned risks",
        variant: "destructive",
      });
    } finally {
      setIsCheckingRisks(false);
    }
  };
  const handleDeleteUser = async () => {
    try {
      setIsDeleting(true);
      const { success, error } = await deleteUser(user.id);
      if (!success) {
        throw error || new Error("Failed to delete user");
      }
      toast({
        title: "User Deleted",
        description: `${user.name} has been successfully removed from the system`,
      });
      onComplete();
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "Failed to delete user";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      onCancel();
    } finally {
      setIsDeleting(false);
    }
  };
  // First step - confirm dialog for deletion
  if (!showTransferDialog) {
    return (
      <AlertDialog open={true} onOpenChange={isOpen => !isOpen && onCancel()}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-destructive" />
              Delete User
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete <strong>{user.name}</strong>? This action cannot be
              undone and will remove the user from the system.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isCheckingRisks || isDeleting}>Cancel</AlertDialogCancel>
            <Button
              variant="destructive"
              onClick={checkForRisks}
              disabled={isCheckingRisks || isDeleting}
            >
              {isCheckingRisks ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Checking risks...
                </>
              ) : (
                <>Delete User</>
              )}
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    );
  }
  // Second step - transfer ownership dialog if user has risks
  return (
    <RiskOwnershipTransferDialog user={user} onCancel={onCancel} onComplete={handleDeleteUser} />
  );
}
