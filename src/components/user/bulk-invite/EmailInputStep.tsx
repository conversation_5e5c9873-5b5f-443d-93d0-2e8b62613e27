import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { UserCheck } from "lucide-react";

interface EmailInputStepProps {
  emailText: string;
  onEmailTextChange: (text: string) => void;
  onNext: () => void;
  error?: string;
}

export function EmailInputStep({ emailText, onEmailTextChange, onNext }: EmailInputStepProps) {
  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="emailText">Email Addresses</Label>
        <Textarea
          id="emailText"
          placeholder="Enter email addresses separated by commas, semicolons, or new lines&#10;&#10;Example:&#10;<EMAIL>&#10;<EMAIL>, <EMAIL>&#10;<EMAIL>; <EMAIL>"
          value={emailText}
          onChange={e => onEmailTextChange(e.target.value)}
          rows={8}
          className="resize-none"
        />
        <p className="text-xs text-muted-foreground">
          Separate email addresses with commas, semicolons, or new lines
        </p>
      </div>

      <div className="flex justify-end">
        <Button type="button" onClick={onNext} disabled={!emailText.trim()}>
          <UserCheck className="mr-2 h-4 w-4" />
          Assign Roles
        </Button>
      </div>
    </div>
  );
}
