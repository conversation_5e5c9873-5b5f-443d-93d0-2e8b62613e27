
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { CheckCircle, AlertCircle, Mail } from "lucide-react";
import { InviteResult } from "@/services/user/bulkInviteService";

interface InvitationResultsStepProps {
  results: InviteResult[];
  onReset: () => void;
}

export function InvitationResultsStep({ results, onReset }: InvitationResultsStepProps) {
  const successCount = results.filter(r => r.success).length;
  
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-lg font-medium">Invitation Results</h4>
        <Button variant="outline" onClick={onReset}>
          Create More Invitations
        </Button>
      </div>
      
      <div className="space-y-2">
        {results.map((result, index) => (
          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
            <div className="flex items-center gap-2">
              {result.success ? (
                <CheckCircle className="h-4 w-4 text-green-600" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600" />
              )}
              <span className="font-medium">{result.email}</span>
            </div>
            <div className="flex items-center gap-2">
              {result.success ? (
                <Badge variant="default" className="bg-green-100 text-green-800">
                  Invitation Created
                </Badge>
              ) : (
                <Badge variant="destructive">
                  Failed
                </Badge>
              )}
            </div>
          </div>
        ))}
      </div>
      
      <Alert>
        <Mail className="h-4 w-4" />
        <AlertDescription>
          Invite codes have been generated. In the next phase, we'll add email sending functionality 
          to automatically send invitation emails to users.
        </AlertDescription>
      </Alert>
      
      <div className="flex justify-between w-full">
        <div className="text-sm text-muted-foreground">
          {successCount} of {results.length} invitations created successfully
        </div>
      </div>
    </div>
  );
}
