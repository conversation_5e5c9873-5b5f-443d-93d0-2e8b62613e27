import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Send, UserCheck } from "lucide-react";
import { UserRole } from "@/types";

interface EmailWithRole {
  email: string;
  role: UserRole;
}

interface RoleAssignmentStepProps {
  validEmails: string[];
  invalidEmails: string[];
  emailsWithRoles: EmailWithRole[];
  onUpdateEmailRole: (email: string, role: UserRole) => void;
  onBack: () => void;
  onSendInvites: () => void;
  isProcessing: boolean;
}

export function RoleAssignmentStep({
  validEmails,
  invalidEmails,
  emailsWithRoles,
  onUpdateEmailRole,
  onBack,
  onSendInvites,
  isProcessing,
}: RoleAssignmentStepProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="text-lg font-medium">Assign Roles</h4>
        <Button variant="outline" onClick={onBack}>
          Back to Email Input
        </Button>
      </div>

      {invalidEmails.length > 0 && (
        <div className="space-y-2">
          <h5 className="text-sm font-medium text-destructive">
            Invalid Emails (will be skipped):
          </h5>
          <div className="flex gap-1 flex-wrap">
            {invalidEmails.map((email, index) => (
              <Badge key={index} variant="outline" className="text-destructive border-destructive">
                {email}
              </Badge>
            ))}
          </div>
          <Separator />
        </div>
      )}

      <div className="space-y-3">
        <div className="flex items-center gap-2 mb-2">
          <UserCheck className="h-4 w-4 text-green-600" />
          <span className="text-sm font-medium">Valid Emails ({validEmails.length})</span>
        </div>

        {emailsWithRoles.map((item, index) => (
          <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
            <span className="font-medium">{item.email}</span>
            <Select
              value={item.role}
              onValueChange={(value: UserRole) => onUpdateEmailRole(item.email, value)}
            >
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={UserRole.ADMIN}>Administrator</SelectItem>
                <SelectItem value={UserRole.RISK_OWNER}>Risk Owner</SelectItem>
                <SelectItem value={UserRole.STAFF}>Staff</SelectItem>
                <SelectItem value={UserRole.BOARD_MEMBER}>Board Member</SelectItem>
              </SelectContent>
            </Select>
          </div>
        ))}
      </div>

      <div className="flex justify-between">
        <div className="text-sm text-muted-foreground">
          {emailsWithRoles.length} valid email{emailsWithRoles.length !== 1 ? "s" : ""} ready for
          invitation
        </div>
        <Button
          type="button"
          onClick={onSendInvites}
          disabled={emailsWithRoles.length === 0 || isProcessing}
        >
          {isProcessing ? (
            <>
              <Send className="mr-2 h-4 w-4 animate-pulse" />
              Creating Invitations...
            </>
          ) : (
            <>
              <Send className="mr-2 h-4 w-4" />
              Create {emailsWithRoles.length} Invitation{emailsWithRoles.length !== 1 ? "s" : ""}
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
