import { useState } from "react";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/auth";
import { UserRole } from "@/types";
import { useEmailValidation } from "@/hooks/useEmailValidation";
import { createBulkInvitations, InviteResult } from "@/services/user/bulkOperationsService";
interface EmailWithRole {
  email: string;
  role: UserRole;
}
type StepType = "input" | "assign-roles" | "results";
export function useBulkInviteLogic(onInvitesSent?: () => void) {
  const {
    emailText,
    validEmails,
    invalidEmails,
    error: validationError,
    setEmailText,
    validateEmails,
    resetValidation,
  } = useEmailValidation();
  const [emailsWithRoles, setEmailsWithRoles] = useState<EmailWithRole[]>([]);
  const [currentStep, setCurrentStep] = useState<StepType>("input");
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState<InviteResult[]>([]);
  const [error, setError] = useState("");
  const { toast } = useToast();
  const { user, organization } = useAuth();
  const handleAssignRoles = () => {
    setError("");
    if (!validateEmails()) {
      setError(validationError);
      return;
    }
    const emailsWithDefaultRoles: EmailWithRole[] = validEmails.map(email => ({
      email,
      role: UserRole.STAFF,
    }));
    setEmailsWithRoles(emailsWithDefaultRoles);
    if (invalidEmails.length > 0) {
      setError(
        `Found ${invalidEmails.length} invalid email address(es). Only valid emails will be processed.`
      );
    }
    setCurrentStep("assign-roles");
  };
  const updateEmailRole = (email: string, role: UserRole) => {
    setEmailsWithRoles(prev => prev.map(item => (item.email === email ? { ...item, role } : item)));
  };
  const handleSendInvites = async () => {
    if (!user?.id || !organization?.id) {
      setError("You must be logged in to send invitations");
      return;
    }
    if (emailsWithRoles.length === 0) {
      setError("No valid email addresses to send invitations to");
      return;
    }
    setIsProcessing(true);
    setError("");
    try {
      const roleGroups = emailsWithRoles.reduce(
        (acc, { email, role }) => {
          if (!acc[role]) acc[role] = [];
          acc[role].push(email);
          return acc;
        },
        {} as Record<UserRole, string[]>
      );
      const allResults: InviteResult[] = [];
      for (const [role, emails] of Object.entries(roleGroups)) {
        const { results: roleResults, error: bulkError } = await createBulkInvitations(
          emails,
          role as UserRole,
          organization.id,
          user.id
        );
        if (bulkError) {
          const errorResults: InviteResult[] = emails.map(email => ({
            email,
            success: false,
            error: bulkError,
          }));
          allResults.push(...errorResults);
        } else {
          allResults.push(...roleResults);
        }
      }
      setResults(allResults);
      setCurrentStep("results");
      const successCount = allResults.filter(r => r.success).length;
      const failureCount = allResults.filter(r => !r.success).length;
      if (successCount > 0) {
        toast({
          title: "Invitations Created",
          description: `Successfully created ${successCount} invitation(s)${failureCount > 0 ? `, ${failureCount} failed` : ""}`,
          variant: successCount === allResults.length ? "default" : "destructive",
        });
        if (onInvitesSent) onInvitesSent();
      } else {
        toast({
          title: "All Invitations Failed",
          description: "No invitations were created successfully",
          variant: "destructive",
        });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to send invitations");
      toast({
        title: "Error",
        description: "Failed to send invitations",
        variant: "destructive",
      });
    } finally {
      setIsProcessing(false);
    }
  };
  const handleReset = () => {
    resetValidation();
    setEmailsWithRoles([]);
    setResults([]);
    setCurrentStep("input");
    setError("");
  };
  const goBackToInput = () => {
    setCurrentStep("input");
  };
  return {
    // State
    emailText,
    validEmails,
    invalidEmails,
    emailsWithRoles,
    currentStep,
    isProcessing,
    results,
    error: error || validationError,
    // Actions
    setEmailText,
    handleAssignRoles,
    updateEmailRole,
    handleSendInvites,
    handleReset,
    goBackToInput,
  };
}
