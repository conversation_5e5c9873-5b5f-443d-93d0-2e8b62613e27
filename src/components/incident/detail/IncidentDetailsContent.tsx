
import { format } from "date-fns";
import { Link } from "react-router-dom";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Incident } from "@/types";
import StatusIndicator from "@/components/incident/StatusIndicator";

interface IncidentDetailsContentProps {
  incident: Incident;
}

const IncidentDetailsContent = ({ incident }: IncidentDetailsContentProps) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>Description</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="whitespace-pre-wrap">{incident.description}</p>
        </CardContent>
      </Card>

      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-base font-medium">Reported By</span>
              <span>{incident.reporterName}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-base font-medium">Date Reported</span>
              <span>{format(incident.date, 'MMM d, yyyy, h:mm a')}</span>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-base font-medium">Status</span>
              <StatusIndicator status={incident.status} />
            </div>
            
            {incident.relatedRiskId && (
              <div className="flex justify-between items-center">
                <span className="text-base font-medium">Related Risk</span>
                <Link 
                  to={`/risks/${incident.relatedRiskId}`}
                  className="text-blue-600 hover:underline"
                >
                  {incident.relatedRiskTitle ?? 'View Risk'}
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default IncidentDetailsContent;
