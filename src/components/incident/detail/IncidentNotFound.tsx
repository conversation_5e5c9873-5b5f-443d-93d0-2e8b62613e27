
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const IncidentNotFound = () => {
  const navigate = useNavigate();
  
  return (
    <div className="flex flex-col items-center justify-center py-12">
      <h2 className="text-2xl font-bold mb-2">Incident Not Found</h2>
      <p className="text-muted-foreground mb-6">
        The incident you're looking for doesn't exist or you don't have permission to view it.
      </p>
      <Button onClick={() => navigate('/incidents')}>
        Return to Incidents
      </Button>
    </div>
  );
};

export default IncidentNotFound;
