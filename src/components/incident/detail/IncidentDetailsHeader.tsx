
import { Incident } from "@/types";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Edit, MoreHorizontal, Copy, FileDown, Trash2, ArrowLeft } from "lucide-react";
import { useAuth } from "@/contexts/auth";
import { useNavigate } from "react-router-dom";
import { getSeverityColor } from "@/services/risk/riskCalculationService";

interface IncidentDetailsHeaderProps {
  incident: Incident;
  canEdit?: boolean;
  onEdit?: () => void;
  onDelete?: () => void;
}

const IncidentDetailsHeader = ({
  incident,
  canEdit = true,
  onEdit,
  onDelete
}: IncidentDetailsHeaderProps) => {
  const { user } = useAuth();
  const navigate = useNavigate();

  const handleBackToRisk = () => {
    if (incident.relatedRiskId) {
      navigate(`/risks/${incident.relatedRiskId}`);
    }
  };

  const canEditIncident = user?.id === incident.reporterId || user?.role === 'admin';
  const canDeleteIncident = user?.role === 'admin';

  return (
    <div className="space-y-4">
      {/* Back to Risk button when incident is related to a risk */}
      {incident.relatedRiskId && (
        <Button 
          variant="outline" 
          onClick={handleBackToRisk}
          className="mb-4"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Related Risk
        </Button>
      )}

      {/* Compact breadcrumb and actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>Incidents</span>
          <span className="text-foreground font-medium">{incident.title}</span>
        </div>
        <div className="flex items-center gap-2">
          {/* Primary Edit Button */}
          {canEdit && canEditIncident && onEdit && (
            <Button size="sm" onClick={onEdit}>
              <Edit className="h-4 w-4 mr-1" />
              <span className="hidden sm:inline">Edit</span>
            </Button>
          )}
          
          {/* More Actions Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">More actions</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate Incident
              </DropdownMenuItem>
              
              <DropdownMenuItem>
                <FileDown className="mr-2 h-4 w-4" />
                Export Details
              </DropdownMenuItem>
              
              {canDeleteIncident && onDelete && (
                <DropdownMenuItem onClick={onDelete} className="text-destructive">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Incident
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Prominent title with key status */}
      <div className="flex items-start justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">{incident.title}</h1>
          <p className="text-muted-foreground mt-1">
            {incident.description.length > 100 
              ? `${incident.description.substring(0, 100)}...` 
              : incident.description}
          </p>
        </div>
        <div className="flex items-center gap-3 ml-4">
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            {incident.status}
          </Badge>
          <Badge className={getSeverityColor(incident.severity)}>
            {incident.severity} Severity
          </Badge>
        </div>
      </div>
    </div>
  );
};

export default IncidentDetailsHeader;
