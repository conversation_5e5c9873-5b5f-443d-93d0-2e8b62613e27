
import { useState, useEffect, useMemo } from "react";
import { X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export type IncidentFilters = {
  search?: string;
  status?: string | undefined;
  severity?: string | undefined;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
};

type IncidentFiltersProps = {
  filters?: IncidentFilters;
  onFilterChange: (filters: IncidentFilters) => void;
  onResetFilters: () => void;
};

const IncidentFilters = ({
  filters,
  onFilterChange,
  onResetFilters,
}: IncidentFiltersProps) => {
  // Add defensive check for filters with useMemo to stabilize the reference
  const safeFilters = useMemo(() => filters ?? {}, [filters]);
  const [search, setSearch] = useState(safeFilters.search ?? "");
  const [activeFilterCount, setActiveFilterCount] = useState(0);
  
  // Count active filters for badge
  useEffect(() => {
    let count = 0;
    if (safeFilters.search) count++;
    if (safeFilters.status) count++;
    if (safeFilters.severity) count++;
    setActiveFilterCount(count);
  }, [safeFilters]);

  // Handle search with debounce
  useEffect(() => {
    const handler = setTimeout(() => {
      onFilterChange({ ...safeFilters, search });
    }, 300);

    return () => clearTimeout(handler);
  }, [search, onFilterChange, safeFilters]);

  return (
    <div className="border rounded-md p-4 mb-4 bg-card">
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-medium">Filters</h3>
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {activeFilterCount}
              </Badge>
            )}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={onResetFilters}
            className="h-8"
          >
            <X className="h-4 w-4 mr-2" />
            Reset
          </Button>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {/* Search input */}
          <div className="col-span-1 md:col-span-2">
            <Input
              placeholder="Search incidents..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-full"
            />
          </div>
          
          {/* Status filter */}
          <div>
            <Select
              value={safeFilters.status ?? "all-statuses"}
              onValueChange={(value) =>
                onFilterChange({ ...safeFilters, status: value === "all-statuses" ? undefined : value })
              }
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-statuses">All Statuses</SelectItem>
                <SelectItem value="Open">Open</SelectItem>
                <SelectItem value="Investigating">Investigating</SelectItem>
                <SelectItem value="Resolved">Resolved</SelectItem>
                <SelectItem value="Closed">Closed</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* Severity filter */}
          <div>
            <Select
              value={safeFilters.severity ?? "all-severities"}
              onValueChange={(value) =>
                onFilterChange({ ...safeFilters, severity: value === "all-severities" ? undefined : value })
              }
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Severity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-severities">All Severities</SelectItem>
                <SelectItem value="Low">Low</SelectItem>
                <SelectItem value="Medium">Medium</SelectItem>
                <SelectItem value="High">High</SelectItem>
                <SelectItem value="Critical">Critical</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex-1">
            <Select
              value={safeFilters.sortBy ?? "date"}
              onValueChange={(value) =>
                onFilterChange({ ...safeFilters, sortBy: value })
              }
            >
              <SelectTrigger className="max-w-[200px]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Date Reported</SelectItem>
                <SelectItem value="title">Title</SelectItem>
                <SelectItem value="severity">Severity</SelectItem>
                <SelectItem value="status">Status</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div>
            <Select
              value={safeFilters.sortOrder ?? "desc"}
              onValueChange={(value) =>
                onFilterChange({
                  ...safeFilters,
                  sortOrder: value as "asc" | "desc"
                })
              }
            >
              <SelectTrigger className="max-w-[120px]">
                <SelectValue placeholder="Order" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="asc">Ascending</SelectItem>
                <SelectItem value="desc">Descending</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IncidentFilters;
