import { RiskSeverity } from "@/types";

interface SeverityBadgeProps {
  severity: RiskSeverity;
}

export const getSeverityColor = (severity: RiskSeverity): string => {
  switch (severity) {
    case RiskSeverity.LOW:
      return "status-badge low";
    case RiskSeverity.MEDIUM:
      return "status-badge medium";
    case RiskSeverity.HIGH:
      return "status-badge high";
    case RiskSeverity.CRITICAL:
      return "status-badge critical";
    default:
      return "status-badge";
  }
};

const SeverityBadge = ({ severity }: SeverityBadgeProps) => {
  // Handle undefined or empty severity
  if (!severity) {
    return <span className="status-badge">Unknown</span>;
  }

  return <span className={getSeverityColor(severity)}>{severity}</span>;
};

export default SeverityBadge;
