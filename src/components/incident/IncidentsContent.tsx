
import { Incident } from "@/types";
import IncidentsTable from "./IncidentsTable";

interface IncidentsContentProps {
  incidents: Incident[];
  loading: boolean;
  error: string | null;
}

const IncidentsContent = ({ incidents, loading, error }: IncidentsContentProps) => {
  if (error) {
    return (
      <div className="p-6 text-center">
        <p className="text-destructive">Error loading incidents: {error}</p>
      </div>
    );
  }

  return <IncidentsTable incidents={incidents} isLoading={loading} />;
};

export default IncidentsContent;
