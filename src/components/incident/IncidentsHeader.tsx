
import { useIsMobile } from "@/hooks/use-mobile";
import { But<PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { Plus } from "lucide-react";

const IncidentsHeader = () => {
  const isMobile = useIsMobile();

  return (
    <div className="flex justify-end">
      <Button asChild className={isMobile ? "w-full" : ""}>
        <Link to="/incidents/new">
          <Plus className="mr-2 h-4 w-4" /> Report Incident
        </Link>
      </Button>
    </div>
  );
};

export default IncidentsHeader;
