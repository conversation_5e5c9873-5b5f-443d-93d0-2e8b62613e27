
import { Incident } from "@/types";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";
import IncidentTableHeader from "./IncidentTableHeader";
import IncidentTableRow from "./IncidentTableRow";
import { ResponsiveTable, IncidentCard } from "@/components/ui/responsive-table";

interface IncidentsTableProps {
  incidents: Incident[];
  isLoading?: boolean;
}

const IncidentsTable = ({ incidents, isLoading = false }: IncidentsTableProps) => {
  const tableHeader = <IncidentTableHeader />;

  const tableBody = (
    <TableBody>
      {incidents.map((incident) => (
        <IncidentTableRow key={incident.id} incident={incident} />
      ))}
    </TableBody>
  );

  const emptyState = (
    <Table>
      {tableHeader}
      <TableBody>
        <TableRow>
          <TableCell colSpan={7} className="text-center h-24">
            No incidents reported
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>
  );

  return (
    <ResponsiveTable
      data={incidents}
      isLoading={isLoading}
      tableHeader={tableHeader}
      tableBody={tableBody}
      CardComponent={IncidentCard}
      emptyState={emptyState}
      className="shadow-sm"
    />
  );
};

export default IncidentsTable;
