
import { Incident } from "@/types";
import { TableCell, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { ArrowUpRight } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import SeverityBadge from "./SeverityBadge";
import StatusIndicator from "./StatusIndicator";
import { cn } from "@/lib/utils";

interface IncidentTableRowProps {
  incident: Incident;
}

const IncidentTableRow = ({ incident }: IncidentTableRowProps) => {
  const navigate = useNavigate();
  const isClosed = incident.status === "Closed" || incident.status === "Resolved";

  return (
    <TableRow 
      key={incident.id} 
      className={cn(
        "cursor-pointer hover:bg-accent/40",
        isClosed && "bg-gray-50 opacity-75"
      )}
      onClick={() => navigate(`/incidents/${incident.id}`)}
    >
      <TableCell>
        <Link 
          to={`/incidents/${incident.id}`} 
          className="hover:underline font-medium"
          onClick={(e) => e.stopPropagation()}
        >
          {incident.title}
        </Link>
      </TableCell>
      <TableCell>{incident.reporterName}</TableCell>
      <TableCell>
        {incident.date.toLocaleDateString()}
      </TableCell>
      <TableCell>
        <SeverityBadge severity={incident.severity} />
      </TableCell>
      <TableCell>
        <StatusIndicator status={incident.status} />
      </TableCell>
      <TableCell>
        {incident.relatedRiskId ? (
          <Link 
            to={`/risks/${incident.relatedRiskId}`} 
            className="text-blue-600 hover:underline" 
            onClick={(e) => e.stopPropagation()}
          >
            {incident.relatedRiskTitle ?? 'View Risk'}
          </Link>
        ) : (
          <span className="text-muted-foreground">None</span>
        )}
      </TableCell>
      <TableCell>
        <Button variant="ghost" size="icon" asChild>
          <Link to={`/incidents/${incident.id}`} onClick={(e) => e.stopPropagation()}>
            <ArrowUpRight className="h-4 w-4" />
          </Link>
        </Button>
      </TableCell>
    </TableRow>
  );
};

export default IncidentTableRow;
