import { cn } from "@/lib/utils";
import { CheckCircle, Lock } from "lucide-react";

interface StatusIndicatorProps {
  status: string;
  showLabel?: boolean;
}

export const getStatusColor = (status: string): string => {
  switch (status) {
    case "Open":
      return "bg-blue-500";
    case "Investigating":
      return "bg-amber-500";
    case "Resolved":
      return "bg-green-500";
    case "Closed":
      return "bg-gray-600";
    default:
      return "bg-gray-500";
  }
};

export const getStatusIcon = (status: string) => {
  switch (status) {
    case "Resolved":
      return <CheckCircle className="h-3 w-3 mr-1" />;
    case "Closed":
      return <Lock className="h-3 w-3 mr-1" />;
    default:
      return null;
  }
};

const StatusIndicator = ({ status, showLabel = true }: StatusIndicatorProps) => {
  const icon = getStatusIcon(status);

  return (
    <div className="flex items-center">
      <div
        className={cn(`w-2 h-2 rounded-full`, showLabel ? "mr-2" : "", getStatusColor(status))}
      />
      {showLabel && (
        <div className="flex items-center">
          {icon}
          <span>{status}</span>
        </div>
      )}
    </div>
  );
};

export default StatusIndicator;
