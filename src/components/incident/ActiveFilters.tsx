
import { X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { IncidentFilters } from "./IncidentFilters";

interface ActiveFiltersProps {
  filters?: IncidentFilters;
  onFilterChange: (filters: IncidentFilters) => void;
}

const ActiveFilters = ({ filters, onFilterChange }: ActiveFiltersProps) => {
  const safeFilters = filters ?? {};
  const hasActiveFilters =
    !!safeFilters.search ||
    !!safeFilters.status ||
    !!safeFilters.severity;
  
  if (!hasActiveFilters) {
    return null;
  }
  
  const removeFilter = (key: keyof IncidentFilters) => {
    const updatedFilters = { ...safeFilters };
    delete updatedFilters[key];
    onFilterChange(updatedFilters);
  };
  
  return (
    <div className="flex flex-wrap gap-2 mb-4">
      <span className="text-sm text-muted-foreground mr-1 pt-0.5">Active filters:</span>
      
      {safeFilters.search && (
        <Badge variant="outline" className="flex items-center gap-1">
          Search: {safeFilters.search}
          <button
            onClick={() => removeFilter('search')}
            className="ml-1"
          >
            <X className="h-3 w-3" />
          </button>
        </Badge>
      )}

      {safeFilters.status && (
        <Badge variant="outline" className="flex items-center gap-1">
          Status: {safeFilters.status}
          <button
            onClick={() => removeFilter('status')}
            className="ml-1"
          >
            <X className="h-3 w-3" />
          </button>
        </Badge>
      )}
      
      {safeFilters.severity && (
        <Badge variant="outline" className="flex items-center gap-1">
          Severity: {safeFilters.severity}
          <button
            onClick={() => removeFilter('severity')}
            className="ml-1"
          >
            <X className="h-3 w-3" />
          </button>
        </Badge>
      )}
    </div>
  );
};

export default ActiveFilters;
