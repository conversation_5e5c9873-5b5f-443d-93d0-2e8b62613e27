
import { Loader2 } from "lucide-react";
import { 
  Form, 
  FormControl, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Risk } from "@/types";
import { UseFormReturn } from "react-hook-form";
import { IncidentFormValues } from "@/hooks/useIncidentForm";
import { FormFieldStatus } from "@/components/ui/form-field-status";
import { useState } from "react";

interface IncidentFormProps {
  form: UseFormReturn<IncidentFormValues>;
  risks: Risk[];
  loadingRisks: boolean;
  loading: boolean;
  onSubmit: (values: IncidentFormValues) => void;
  onCancel: () => void;
  submitButtonText?: string;
}

export const IncidentForm = ({
  form,
  risks,
  loadingRisks,
  loading,
  onSubmit,
  onCancel,
  submitButtonText = "Report Incident"
}: IncidentFormProps) => {
  const [touchedFields, setTouchedFields] = useState<Record<string, boolean>>({});
  
  // Watch form values for validation feedback
  const title = form.watch("title");
  const description = form.watch("description");
  const severity = form.watch("severity");
  const status = form.watch("status");

  // Track touched fields for validation display
  const handleFieldBlur = (fieldName: string) => {
    setTouchedFields(prev => ({
      ...prev,
      [fieldName]: true
    }));
  };

  // Validation state helpers
  const isTitleValid = title?.length >= 5;
  const isTitleInvalid = touchedFields['title'] && !isTitleValid;
  
  const isDescriptionValid = description?.length >= 10;
  const isDescriptionInvalid = touchedFields['description'] && !isDescriptionValid;
  
  // Form completion status
  const isFormValid = isTitleValid && isDescriptionValid && severity && status;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input 
                  placeholder="Brief description of the incident" 
                  {...field} 
                  onBlur={() => handleFieldBlur("title")}
                  className={`${isTitleInvalid ? 'border-red-300 focus-visible:ring-red-400' : ''} 
                             ${isTitleValid && touchedFields['title'] ? 'border-green-300 focus-visible:ring-green-400' : ''}`}
                />
              </FormControl>
              <FormFieldStatus 
                isValid={Boolean(isTitleValid && touchedFields['title'])} 
                isInvalid={Boolean(isTitleInvalid)}
                validMessage="Title looks good!"
                size="sm"
              />
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Description</FormLabel>
              <FormControl>
                <Textarea 
                  placeholder="Detailed description of what happened"
                  className={`min-h-[120px] ${isDescriptionInvalid ? 'border-red-300 focus-visible:ring-red-400' : ''} 
                              ${isDescriptionValid && touchedFields['description'] ? 'border-green-300 focus-visible:ring-green-400' : ''}`}
                  {...field} 
                  onBlur={() => handleFieldBlur("description")}
                />
              </FormControl>
              <FormFieldStatus 
                isValid={Boolean(isDescriptionValid && touchedFields['description'])} 
                isInvalid={Boolean(isDescriptionInvalid)}
                validMessage="Description is detailed enough"
                size="sm"
              />
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="severity"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Severity</FormLabel>
                <Select 
                  onValueChange={(value) => {
                    field.onChange(value);
                    handleFieldBlur("severity");
                  }} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className={touchedFields['severity'] ? 'border-green-300 focus-visible:ring-green-400' : ''}>
                      <SelectValue placeholder="Select severity" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="Low">Low</SelectItem>
                    <SelectItem value="Medium">Medium</SelectItem>
                    <SelectItem value="High">High</SelectItem>
                    <SelectItem value="Critical">Critical</SelectItem>
                  </SelectContent>
                </Select>
                <FormFieldStatus 
                  isValid={Boolean(severity && touchedFields['severity'])}
                  size="sm"
                  validMessage="Severity selected"
                />
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <Select 
                  onValueChange={(value) => {
                    field.onChange(value);
                    handleFieldBlur("status");
                  }}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className={touchedFields['status'] ? 'border-green-300 focus-visible:ring-green-400' : ''}>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="Open">Open</SelectItem>
                    <SelectItem value="Investigating">Investigating</SelectItem>
                    <SelectItem value="Resolved">Resolved</SelectItem>
                    <SelectItem value="Closed">Closed</SelectItem>
                  </SelectContent>
                </Select>
                <FormFieldStatus 
                  isValid={Boolean(status && touchedFields['status'])}
                  size="sm"
                  validMessage="Status selected"
                />
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="relatedRiskId"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Related Risk (Optional)</FormLabel>
               <Select 
                onValueChange={(value) => {
                  field.onChange(value === "none" ? "" : value);
                  handleFieldBlur("relatedRiskId");
                }}
                value={field.value ?? "none"}
               >
                <FormControl>
                  <SelectTrigger className={touchedFields['relatedRiskId'] ? 'border-green-300 focus-visible:ring-green-400' : ''}>
                    <SelectValue placeholder="Select related risk (if applicable)" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="none">None</SelectItem>
                  {loadingRisks ? (
                    <SelectItem value="loading" disabled>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Loading risks...
                    </SelectItem>
                  ) : (
                    risks.map((risk) => (
                      <SelectItem key={risk.id} value={risk.id}>
                        {risk.title}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {touchedFields['relatedRiskId'] && (
                <FormFieldStatus 
                  isValid={true}
                  size="sm"
                  validMessage="Risk selection complete"
                />
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end space-x-4">
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
          >
            Cancel
          </Button>
          <Button 
            type="submit" 
            disabled={loading || !isFormValid}
            className={!isFormValid ? "opacity-50" : ""}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {submitButtonText === "Report Incident" ? "Submitting..." : "Updating..."}
              </>
            ) : (
              submitButtonText
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
};
