
// src/components/route/ProtectedRoute.tsx - Updated to work with nested routes using Outlet

import React from 'react';
import { useAuth } from '@/contexts/auth';
import { Navigate, useLocation, Outlet } from 'react-router-dom';
import { UserRole } from '@/types';
import { OrganizationSetup } from '@/components/onboarding/OrganizationSetup';

interface ProtectedRouteProps {
  requiredRoles?: UserRole[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  requiredRoles = [] 
}) => {
  const { 
    user, 
    isAuthenticated, 
    isLoading, 
    hasPermission, 
    needsOrganizationSetup 
  } = useAuth();
  const location = useLocation();

  // Removed console.log to prevent spam

  // Show loading while checking auth
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ returnTo: location.pathname }} replace />;
  }

  // If user is authenticated but no user object, show loading to prevent redirect loop
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-10 h-10 border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="text-muted-foreground">Loading user profile...</p>
        </div>
      </div>
    );
  }

  // Check if user needs organization setup
  if (needsOrganizationSetup) {
    return <OrganizationSetup onComplete={() => window.location.reload()} />;
  }

  // Check role-based permissions
  if (requiredRoles.length > 0 && !hasPermission(requiredRoles)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background p-4">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-muted-foreground">
            You don't have permission to access this page.
          </p>
        </div>
      </div>
    );
  }

  return <Outlet />;
};

export default ProtectedRoute;
