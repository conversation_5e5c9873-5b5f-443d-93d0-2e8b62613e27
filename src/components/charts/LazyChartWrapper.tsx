/**
 * Lazy-loaded chart wrapper component
 * Dynamically imports Recharts only when charts are actually needed
 */

import React, { Suspense, ComponentType } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { loadChartComponents } from "@/utils/dynamic-imports";

interface LazyChartWrapperProps {
  title?: string | undefined;
  description?: string | undefined;
  children: React.ReactNode;
  fallbackHeight?: number | undefined;
  className?: string;
}

/**
 * Loading skeleton for charts
 */
const ChartSkeleton: React.FC<{ height?: number }> = ({ height = 300 }) => (
  <div className="space-y-3">
    <Skeleton className="h-4 w-1/3" />
    <Skeleton className={`w-full`} style={{ height: `${height}px` }} />
    <div className="flex space-x-2">
      <Skeleton className="h-3 w-16" />
      <Skeleton className="h-3 w-16" />
      <Skeleton className="h-3 w-16" />
    </div>
  </div>
);

/**
 * Error fallback for chart loading failures
 */
const ChartErrorFallback: React.FC<{ error?: Error }> = ({ error }) => (
  <Card className="w-full">
    <CardHeader>
      <CardTitle className="text-destructive">Chart Loading Error</CardTitle>
    </CardHeader>
    <CardContent>
      <p className="text-sm text-muted-foreground">
        Unable to load chart component. Please try refreshing the page.
      </p>
      {import.meta.env.MODE === "development" && error && (
        <details className="mt-2">
          <summary className="text-xs cursor-pointer">Error Details</summary>
          <pre className="text-xs mt-1 p-2 bg-muted rounded">{error.message}</pre>
        </details>
      )}
    </CardContent>
  </Card>
);

/**
 * Lazy chart wrapper component
 */
export const LazyChartWrapper: React.FC<LazyChartWrapperProps> = ({
  title,
  description,
  children,
  fallbackHeight = 300,
  className,
}) => {
  const [error, setError] = React.useState<Error | null>(null);
  const [isLoading, setIsLoading] = React.useState(true);

  React.useEffect(() => {
    // Preload chart components when wrapper mounts
    loadChartComponents()
      .then(() => {
        setIsLoading(false);
      })
      .catch(err => {
        setError(err);
        setIsLoading(false);
      });
  }, []);

  if (error) {
    return <ChartErrorFallback error={error} />;
  }

  if (isLoading) {
    return (
      <Card className={className}>
        {(title ?? description) && (
          <CardHeader>
            {title && <CardTitle>{title}</CardTitle>}
            {description && <p className="text-sm text-muted-foreground">{description}</p>}
          </CardHeader>
        )}
        <CardContent>
          <ChartSkeleton height={fallbackHeight} />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {(title ?? description) && (
        <CardHeader>
          {title && <CardTitle>{title}</CardTitle>}
          {description && <p className="text-sm text-muted-foreground">{description}</p>}
        </CardHeader>
      )}
      <CardContent>
        <Suspense fallback={<ChartSkeleton height={fallbackHeight} />}>{children}</Suspense>
      </CardContent>
    </Card>
  );
};

/**
 * Higher-order component for lazy loading chart components
 */

export const withLazyChart = <P extends object>(WrappedComponent: ComponentType<P>) => {
  const LazyChartComponent = React.lazy(async () => {
    // Ensure chart library is loaded
    await loadChartComponents();
    return { default: WrappedComponent };
  });

  const WithLazyChartComponent: React.FC<
    P & {
      fallbackHeight?: number | undefined;
      title?: string | undefined;
      description?: string | undefined;
    }
  > = ({ fallbackHeight, title, description, ...props }) => (
    <LazyChartWrapper 
      title={title} 
      description={description} 
      fallbackHeight={fallbackHeight}
    >
      <Suspense fallback={<ChartSkeleton height={fallbackHeight || 300} />}>
        <LazyChartComponent {...(props as any)} />
      </Suspense>
    </LazyChartWrapper>
  );

  WithLazyChartComponent.displayName = `withLazyChart(${WrappedComponent.displayName ?? WrappedComponent.name ?? "Component"})`;

  return WithLazyChartComponent;
};

export default LazyChartWrapper;
