
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Users, Plus, ArrowRight } from "lucide-react";

export type OnboardingChoice = 'create-organization' | 'use-invite';

interface OrganizationOnboardingProps {
  onChoice: (choice: OnboardingChoice, data?: Record<string, unknown>) => void;
}

export function OrganizationOnboarding({ onChoice }: OrganizationOnboardingProps) {
  const [selectedOption, setSelectedOption] = useState<OnboardingChoice | null>(null);
  const [organizationName, setOrganizationName] = useState('');
  const [organizationSize, setOrganizationSize] = useState('');
  const [sectorType, setSectorType] = useState('');
  const [sectorDescription, setSectorDescription] = useState('');
  const [inviteCode, setInviteCode] = useState('');
  const [loading, setLoading] = useState(false);

  const handleContinue = async () => {
    if (!selectedOption) return;
    
    setLoading(true);
    try {
      switch (selectedOption) {
        case 'create-organization':
          if (organizationName.trim() && organizationSize && sectorType) {
            await onChoice('create-organization', { 
              name: organizationName.trim(),
              organizationSize,
              sectorType,
              sectorDescription: sectorDescription.trim() ?? undefined
            });
          }
          break;
        case 'use-invite':
          if (inviteCode.trim()) {
            await onChoice('use-invite', { code: inviteCode.trim() });
          }
          break;
      }
    } finally {
      setLoading(false);
    }
  };

  const canContinue = 
    (selectedOption === 'create-organization' && organizationName.trim() && organizationSize && sectorType) ||
    (selectedOption === 'use-invite' && inviteCode.trim());

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold">Join an Organization</CardTitle>
        <CardDescription>
          Choose how you'd like to get started with risk management
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        <RadioGroup 
          value={selectedOption ?? ''} 
          onValueChange={(value: OnboardingChoice) => setSelectedOption(value)}
          className="space-y-4"
        >
          {/* Create New Organization */}
          <div className="flex items-start space-x-3 p-4 border rounded-lg">
            <RadioGroupItem value="create-organization" id="create-organization" className="mt-1" />
            <div className="flex-1">
              <Label htmlFor="create-organization" className="font-medium cursor-pointer">
                <div className="flex items-center gap-2 mb-2">
                  <Plus className="h-5 w-5 text-green-500" />
                  Create New Organization
                </div>
              </Label>
              <p className="text-sm text-muted-foreground mb-3">
                Start your own organization with full administrative control.
              </p>
              {selectedOption === 'create-organization' && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="org-name" className="text-sm">Organization Name</Label>
                    <Input
                      id="org-name"
                      placeholder="Enter your organization name"
                      value={organizationName}
                      onChange={(e) => setOrganizationName(e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="org-size" className="text-sm">Organization Size</Label>
                    <Select value={organizationSize} onValueChange={setOrganizationSize}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select organization size" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1-5">1-5 employees</SelectItem>
                        <SelectItem value="6-25">6-25 employees</SelectItem>
                        <SelectItem value="26-100">26-100 employees</SelectItem>
                        <SelectItem value="101-500">101-500 employees</SelectItem>
                        <SelectItem value="500+">500+ employees</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="sector-type" className="text-sm">Sector Type</Label>
                    <Select value={sectorType} onValueChange={setSectorType}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select your sector" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="health">Health & Medical</SelectItem>
                        <SelectItem value="education">Education</SelectItem>
                        <SelectItem value="social_services">Social Services</SelectItem>
                        <SelectItem value="environment">Environment</SelectItem>
                        <SelectItem value="arts_culture">Arts & Culture</SelectItem>
                        <SelectItem value="community">Community Development</SelectItem>
                        <SelectItem value="advocacy">Advocacy & Human Rights</SelectItem>
                        <SelectItem value="religious">Religious</SelectItem>
                        <SelectItem value="youth">Youth Services</SelectItem>
                        <SelectItem value="seniors">Senior Services</SelectItem>
                        <SelectItem value="housing">Housing</SelectItem>
                        <SelectItem value="disaster">Disaster Relief</SelectItem>
                        <SelectItem value="international">International Aid</SelectItem>
                        <SelectItem value="animal">Animal Welfare</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="sector-description" className="text-sm">Organization Description (Optional)</Label>
                    <Textarea
                      id="sector-description"
                      placeholder="Briefly describe what your organization does"
                      value={sectorDescription}
                      onChange={(e) => setSectorDescription(e.target.value)}
                      rows={3}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Use Invite Code */}
          <div className="flex items-start space-x-3 p-4 border rounded-lg">
            <RadioGroupItem value="use-invite" id="use-invite" className="mt-1" />
            <div className="flex-1">
              <Label htmlFor="use-invite" className="font-medium cursor-pointer">
                <div className="flex items-center gap-2 mb-2">
                  <Users className="h-5 w-5 text-purple-500" />
                  Join with Invite Code
                </div>
              </Label>
              <p className="text-sm text-muted-foreground mb-3">
                Have an invite code? Join a specific organization with assigned role.
              </p>
              {selectedOption === 'use-invite' && (
                <div className="space-y-2">
                  <Label htmlFor="invite-code" className="text-sm">Invite Code</Label>
                  <Input
                    id="invite-code"
                    placeholder="Enter your invite code"
                    value={inviteCode}
                    onChange={(e) => setInviteCode(e.target.value)}
                  />
                </div>
              )}
            </div>
          </div>
        </RadioGroup>

        <Separator />

        <div className="flex justify-end">
          <Button 
            onClick={handleContinue}
            disabled={!canContinue || loading}
            className="flex items-center gap-2"
          >
            {loading ? "Processing..." : "Continue"}
            <ArrowRight className="h-4 w-4" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
