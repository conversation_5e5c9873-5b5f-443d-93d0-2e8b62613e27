import { useState } from "react";
import { useAuth } from "@/contexts/auth";
import { useToast } from "@/components/ui/use-toast";
import { OrganizationOnboarding, OnboardingChoice } from "./OrganizationOnboarding";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, Mail } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

interface OrganizationSetupProps {
  onComplete: () => void;
}

export function OrganizationSetup({ onComplete }: OrganizationSetupProps) {
  const [step, setStep] = useState<"choice" | "email-admin" | "success">("choice");
  const [organizationData, setOrganizationData] = useState<Record<string, unknown>>({});
  const [loading, setLoading] = useState(false);
  const [adminEmailSent, setAdminEmailSent] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();

  const handleChoice = async (choice: OnboardingChoice, data?: Record<string, unknown>) => {
    if (choice === "create-organization") {
      setOrganizationData(data || {});
      await handleCreateOrganization(data || {});
    } else if (choice === "use-invite") {
      // Handle invite code usage
      if (data?.['code'] && typeof data['code'] === "string") {
        await handleInviteCode(data['code'] as string);
      }
    }
  };

  const handleCreateOrganization = async (orgData: Record<string, unknown>) => {
    if (!user?.id || !orgData?.['name'] || typeof orgData['name'] !== "string") {
      toast({
        title: "Error",
        description: "Missing required information",
        variant: "destructive",
      });
      return;
    }

    const organizationName = orgData['name'] as string;
    const slug = organizationName.toLowerCase().replace(/\s+/g, "-");

    setLoading(true);
    try {
      const { data, error } = await supabase.rpc("create_organization_with_owner", {
        p_user_id: user.id,
        p_org_name: organizationName,
        p_org_slug: slug,
        p_org_size: (orgData['organizationSize'] as string) || '',
        p_sector_type: (orgData['sectorType'] as string) || '',
        p_sector_description: (orgData['sectorDescription'] as string) || "New organization",
      });

      if (error) {
        throw error;
      }

      const result = data?.[0];
      if (!result?.success) {
        if (result?.error_message === "Organization already exists") {
          // Organization name already exists, show options
          setStep("email-admin");
          return;
        }
        throw new Error(result?.error_message ?? "Failed to create organization");
      }

      toast({
        title: "Success",
        description: "Organization created successfully!",
        variant: "default",
      });
      onComplete();
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast({
          title: "Error",
          description: error.message ?? "Failed to create organization",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: "An unknown error occurred",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleEmailAdmin = async () => {
    if (!user?.email || !organizationData?.['name'] || typeof organizationData['name'] !== "string") {
      toast({
        title: "Error",
        description: "Missing required information",
        variant: "destructive",
      });
      return;
    }

    const organizationName = organizationData['name'] as string;
    setLoading(true);
    try {
      // Call the edge function to send admin email
      const result = (await supabase.functions.invoke("send-admin-notification", {
        body: {
          type: "organization_request",
          requester_email: user.email,
          requester_name: user.name ?? "User",
          organization_name: organizationName,
          message: `User ${user.email} is requesting access to join the organization "${organizationName}". Please generate an invite code if this request is legitimate.`,
        },
      })) as { data: unknown; error: Error | null };
      
      const { error } = result;
      if (error) {
        throw error;
      }

      setAdminEmailSent(true);
      toast({
        title: "Request Sent",
        description:
          "Your request has been sent to the administrators. You should receive an invite code via email if approved.",
        variant: "default",
      });
    } catch (error: unknown) {
      if (error instanceof Error) {
        toast({
          title: "Error",
          description: "Failed to send request to administrators. Please try again later.",
          variant: "destructive",
        });
      } else {
        toast({
          title: "Error",
          description: "An unknown error occurred",
          variant: "destructive",
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleInviteCode = async (_code: string) => {
    // Handle invite code validation and usage
    // This would be implemented based on your invite code system
    onComplete();
  };

  if (step === "choice") {
    return <OrganizationOnboarding onChoice={handleChoice} />;
  }

  if (step === "email-admin") {
    const orgName = organizationData['name'] as string;
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">Organization Already Exists</CardTitle>
          <CardDescription>
            The organization "{orgName}" already exists. What would you like to do?
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {!adminEmailSent ? (
            <>
              <div className="space-y-4">
                <div className="flex items-start space-x-3 p-4 border rounded-lg">
                  <Mail className="h-5 w-5 text-blue-500 mt-1" />
                  <div className="flex-1">
                    <h3 className="font-medium">Request Access</h3>
                    <p className="text-sm text-muted-foreground">
                      Send a request to the organization administrators to get an invite code.
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex flex-col space-y-3">
                <Button onClick={handleEmailAdmin} disabled={loading} className="w-full">
                  {loading ? "Sending Request..." : "Email Admin for Access"}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setStep("choice")}
                  disabled={loading}
                  className="w-full"
                >
                  Choose Different Option
                </Button>
              </div>
            </>
          ) : (
            <>
              <div className="text-center space-y-4">
                <CheckCircle className="h-12 w-12 mx-auto text-green-500" />
                <div>
                  <h3 className="text-lg font-medium">Request Sent Successfully</h3>
                  <p className="text-muted-foreground">
                    Your request has been sent to the administrators of "{orgName}".
                    You should receive an invite code via email if your request is approved.
                  </p>
                </div>
              </div>
              <div className="flex flex-col space-y-3">
                <Button variant="outline" onClick={() => setStep("choice")} className="w-full">
                  Choose Different Option
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    );
  }

  return null;
}