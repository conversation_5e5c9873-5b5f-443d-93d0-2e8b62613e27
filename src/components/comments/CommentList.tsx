
import { Comment } from "@/types";
import CommentItem from "./CommentItem";
import { Skeleton } from "@/components/ui/skeleton";

interface CommentListProps {
  comments: Comment[];
  loading: boolean;
  onUpdate: (id: string, content: string) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
}

const CommentList = ({ 
  comments, 
  loading, 
  onUpdate, 
  onDelete 
}: CommentListProps) => {
  if (loading) {
    return (
      <div className="space-y-4">
        <CommentSkeleton />
        <CommentSkeleton />
      </div>
    );
  }

  if (comments.length === 0) {
    return (
      <div className="py-6 text-center text-muted-foreground">
        No comments yet. Be the first to comment!
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {comments.map(comment => (
        <CommentItem
          key={comment.id}
          comment={comment}
          onUpdate={onUpdate}
          onDelete={onDelete}
        />
      ))}
    </div>
  );
};

const CommentSkeleton = () => (
  <div className="flex space-x-4 p-4 border rounded-md">
    <Skeleton className="h-10 w-10 rounded-full" />
    <div className="space-y-2 flex-1">
      <div className="flex justify-between">
        <Skeleton className="h-5 w-32" />
        <Skeleton className="h-5 w-16" />
      </div>
      <Skeleton className="h-16 w-full" />
    </div>
  </div>
);

export default CommentList;
