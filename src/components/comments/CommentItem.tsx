import { useState } from "react";
import { useAuth } from "@/contexts/auth";
import { format } from "date-fns";
import { Edit2, Trash2, Save, X } from "lucide-react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Comment } from "@/types";
interface CommentItemProps {
  comment: Comment;
  onUpdate: (id: string, content: string) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
}
const CommentItem = ({ comment, onUpdate, onDelete }: CommentItemProps) => {
  const { user } = useAuth();
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(comment.content);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const isOwner = user?.id === comment.userId;
  const initials = comment.userName
    ? comment.userName
        .split(" ")
        .map(name => name[0])
        .join("")
        .toUpperCase()
        .slice(0, 2)
    : "U";
  const handleUpdate = async () => {
    if (!editContent.trim()) return;
    setIsSubmitting(true);
    try {
      await onUpdate(comment.id, editContent);
      setIsEditing(false);
    } catch (error) {
      // Error caught and handled
    } finally {
      setIsSubmitting(false);
    }
  };
  const handleDelete = async () => {
    const confirmed = window.confirm("Are you sure you want to delete this comment?");
    if (!confirmed) return;
    try {
      setIsSubmitting(true);
      await onDelete(comment.id);
    } catch (error) {
      // Error caught and handled
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <div className="flex space-x-4 p-4 rounded-md border">
      <Avatar className="h-10 w-10">
        <AvatarImage src={comment.userAvatar ?? ""} />
        <AvatarFallback>{initials}</AvatarFallback>
      </Avatar>
      <div className="flex-1 space-y-2">
        <div className="flex justify-between items-center">
          <div>
            <span className="font-medium">{comment.userName ?? "User"}</span>
            <span className="text-sm text-muted-foreground ml-2">
              {format(comment.createdAt, "MMM d, yyyy h:mm a")}
              {comment.updatedAt > comment.createdAt && " (edited)"}
            </span>
          </div>
          {isOwner && !isEditing && (
            <div className="flex space-x-2">
              <Button size="sm" variant="ghost" onClick={() => setIsEditing(true)}>
                <Edit2 className="h-4 w-4" />
              </Button>
              <Button size="sm" variant="ghost" onClick={handleDelete} disabled={isSubmitting}>
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
        {isEditing ? (
          <div className="space-y-2">
            <Textarea
              value={editContent}
              onChange={e => setEditContent(e.target.value)}
              placeholder="Edit your comment..."
              disabled={isSubmitting}
            />
            <div className="flex justify-end space-x-2">
              <Button
                size="sm"
                variant="outline"
                onClick={() => {
                  setIsEditing(false);
                  setEditContent(comment.content);
                }}
                disabled={isSubmitting}
              >
                <X className="h-4 w-4 mr-1" /> Cancel
              </Button>
              <Button
                size="sm"
                onClick={handleUpdate}
                disabled={isSubmitting || !editContent.trim()}
              >
                <Save className="h-4 w-4 mr-1" /> Save
              </Button>
            </div>
          </div>
        ) : (
          <p className="whitespace-pre-wrap">{comment.content}</p>
        )}
      </div>
    </div>
  );
};
export default CommentItem;
