
import { useAuth } from "@/contexts/auth";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useComments } from "@/hooks/useComments";
import CommentList from "./CommentList";
import CommentForm from "./CommentForm";

interface CommentsSectionProps {
  entityType: 'risk' | 'incident';
  entityId: string;
}

const CommentsSection = ({ entityType, entityId }: CommentsSectionProps) => {
  const { user, isAuthenticated } = useAuth();
  const { 
    comments, 
    loading, 
    addComment, 
    updateComment, 
    deleteComment 
  } = useComments(entityType, entityId);

  const handleSubmit = async (content: string) => {
    if (!user) return;
    await addComment(content);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Comments {!loading && `(${comments.length})`}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {isAuthenticated && <CommentForm onSubmit={handleSubmit} />}
        {!isAuthenticated && (
          <div className="py-2 text-center text-muted-foreground">
            Please sign in to leave a comment.
          </div>
        )}
        <CommentList
          comments={comments}
          loading={loading}
          onUpdate={updateComment}
          onDelete={deleteComment}
        />
      </CardContent>
    </Card>
  );
};

export default CommentsSection;
