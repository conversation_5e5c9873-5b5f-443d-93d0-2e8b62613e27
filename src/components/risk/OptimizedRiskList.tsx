import React, { useState, useMemo } from "react";
import { useRiskData } from "@/hooks/useRiskData";
import { useRiskMutations } from "@/hooks/risk/useRiskMutations";
import { RiskSeverity, RiskStatus } from "@/types";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { ChevronLeft, ChevronRight, Search, Filter } from "lucide-react";
interface OptimizedRiskListProps {
  showFilters?: boolean;
  pageSize?: number;
}
/**
 * Example component demonstrating optimized TanStack Query usage
 * Features: pagination, filtering, search, optimistic updates, background refetching
 */
export const OptimizedRiskList: React.FC<OptimizedRiskListProps> = ({
  showFilters = true,
  pageSize = 20,
}) => {
  // Filter and pagination state
  const [filters, setFilters] = useState({
    severities: [] as RiskSeverity[],
    statuses: [] as RiskStatus[],
    searchTerm: "",
    page: 1,
    pageSize,
  });
  // Debounced search term to avoid excessive API calls
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(filters.searchTerm);
    }, 300);
    return () => clearTimeout(timer);
  }, [filters.searchTerm]);
  // Use optimized risk data hook
  const riskDataResult = useRiskData({
    ...filters,
    searchTerm: debouncedSearchTerm,
  });
  const {
    risks,
    totalCount,
    hasNextPage,
    hasPreviousPage,
    isLoading,
    isFetching,
    isError,
    error,
    refetch,
    prefetchNextPage,
    isStale,
    dataUpdatedAt,
  } = riskDataResult;
  // Use risk mutations
  const { updateRisk, deleteRisk, isUpdating, isDeleting } = useRiskMutations();
  // Prefetch next page when user is near the end of current page
  React.useEffect(() => {
    if (hasNextPage && Array.isArray(risks) && risks.length > 0) {
      prefetchNextPage();
    }
  }, [hasNextPage, prefetchNextPage, risks]);
  // Memoized filter options
  const filterOptions = useMemo(
    () => ({
      severities: Object.values(RiskSeverity),
      statuses: Object.values(RiskStatus),
    }),
    []
  );
  // Handle filter changes
  const handleFilterChange = (key: keyof typeof filters, value: unknown) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1, // Reset to first page when filters change
    }));
  };
  // Handle pagination
  const handlePageChange = (newPage: number) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };
  // Handle risk status update with optimistic update
  const handleStatusUpdate = async (riskId: string, newStatus: RiskStatus) => {
    try {
      await updateRisk.mutateAsync({
        id: riskId,
        status: newStatus,
      });
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };
  // Handle risk deletion
  const handleDelete = async (riskId: string) => {
    if (window.confirm("Are you sure you want to delete this risk?")) {
      try {
        await deleteRisk.mutateAsync(riskId);
      } catch (error) {
        // Error caught and handled
      }
    }
  };
  // Loading skeleton
  const LoadingSkeleton = () => (
    <div className="space-y-4">
      {Array.from({ length: 5 }).map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </CardHeader>
          <CardContent>
            <div className="flex space-x-2">
              <Skeleton className="h-6 w-16" />
              <Skeleton className="h-6 w-20" />
              <Skeleton className="h-6 w-24" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
  // Error state
  if (isError) {
    return (
      <div className="text-center py-8">
        <p className="text-red-600 mb-4">
          Error loading risks: {error instanceof Error ? error.message : "Unknown error"}
        </p>
        <Button onClick={() => refetch()}>Try Again</Button>
      </div>
    );
  }
  return (
    <div className="space-y-6">
      {/* Header with data freshness indicator */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Risk Register</h2>
          <p className="text-sm text-gray-600">
            {totalCount} risks total
            {isStale && <span className="ml-2 text-amber-600">• Data may be outdated</span>}
            {dataUpdatedAt && (
              <span className="ml-2 text-gray-500">
                • Last updated: {new Date(dataUpdatedAt).toLocaleTimeString()}
              </span>
            )}
          </p>
        </div>
        {isFetching && (
          <div className="flex items-center space-x-2 text-blue-600">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span className="text-sm">Updating...</span>
          </div>
        )}
      </div>
      {/* Filters */}
      {showFilters && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Filter className="h-5 w-5" />
              <span>Filters</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search risks..."
                  value={filters.searchTerm}
                  onChange={e => handleFilterChange("searchTerm", e.target.value)}
                  className="pl-10"
                />
              </div>
              {/* Severity filter */}
              <Select
                value={filters.severities[0] ?? ""}
                onValueChange={value =>
                  handleFilterChange("severities", value ? [value as RiskSeverity] : [])
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Severities" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Severities</SelectItem>
                  {filterOptions.severities.map(severity => (
                    <SelectItem key={severity} value={severity}>
                      {severity.charAt(0).toUpperCase() + severity.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {/* Status filter */}
              <Select
                value={filters.statuses[0] ?? ""}
                onValueChange={value =>
                  handleFilterChange("statuses", value ? [value as RiskStatus] : [])
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Statuses</SelectItem>
                  {filterOptions.statuses.map(status => (
                    <SelectItem key={status} value={status}>
                      {status.replace("_", " ").charAt(0).toUpperCase() +
                        status.replace("_", " ").slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {/* Clear filters */}
              <Button
                variant="outline"
                onClick={() =>
                  setFilters({
                    severities: [],
                    statuses: [],
                    searchTerm: "",
                    page: 1,
                    pageSize,
                  })
                }
              >
                Clear Filters
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
      {/* Risk list */}
      {isLoading ? (
        <LoadingSkeleton />
      ) : (
        <div className="space-y-4">
          {Array.isArray(risks) &&
            risks.map(risk => (
              <Card key={risk.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-lg">{risk.title}</CardTitle>
                      <p className="text-sm text-gray-600 mt-1">{risk.description}</p>
                    </div>
                    <div className="flex space-x-2">
                      <Badge
                        variant={
                          risk.severity === RiskSeverity.CRITICAL ? "destructive" : "secondary"
                        }
                      >
                        {risk.severity}
                      </Badge>
                      <Badge variant="outline">{risk.status.replace("_", " ")}</Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex justify-between items-center">
                    <div className="text-sm text-gray-600">
                      <p>Owner: {risk.ownerName}</p>
                      <p>Category: {risk.category}</p>
                      {risk.dueDate && <p>Due: {risk.dueDate.toLocaleDateString()}</p>}
                    </div>
                    <div className="flex space-x-2">
                      <Select
                        value={risk.status}
                        onValueChange={value => handleStatusUpdate(risk.id, value as RiskStatus)}
                        disabled={isUpdating}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {filterOptions.statuses.map(status => (
                            <SelectItem key={status} value={status}>
                              {status.replace("_", " ").charAt(0).toUpperCase() +
                                status.replace("_", " ").slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDelete(risk.id)}
                        disabled={isDeleting}
                      >
                        Delete
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
        </div>
      )}
      {/* Pagination */}
      {totalCount > pageSize && (
        <div className="flex justify-between items-center">
          <p className="text-sm text-gray-600">
            Showing {(filters.page - 1) * pageSize + 1} to{" "}
            {Math.min(filters.page * pageSize, totalCount)} of {totalCount} risks
          </p>
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(filters.page - 1)}
              disabled={!hasPreviousPage || isFetching}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handlePageChange(filters.page + 1)}
              disabled={!hasNextPage || isFetching}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
