
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>eader,
  SheetTitle,
  SheetDes<PERSON>
} from "@/components/ui/sheet";
import RiskForm from "@/components/risk/RiskForm";

interface RiskCreateSheetProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export const RiskCreateSheet = ({ isOpen, onOpenChange, onSuccess }: RiskCreateSheetProps) => {
  const handleSuccess = () => {
    // Close the sheet after successfully adding risk
    onSuccess();
  };
  
  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-xl w-full overflow-y-auto">
        <SheetHeader>
          <SheetTitle>Create New Risk</SheetTitle>
          <SheetDescription>
            Add a new risk to the register. Fill out all required fields and click "Save Risk" when complete.
          </SheetDescription>
        </SheetHeader>
        <div className="mt-6 pb-20">
          <RiskForm 
            onSuccess={handleSuccess} 
            onCancel={() => onOpenChange(false)}
          />
        </div>
      </SheetContent>
    </Sheet>
  );
};
