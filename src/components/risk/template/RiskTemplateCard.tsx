import {
  <PERSON>,
  CardContent,
  Card<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Copy, Trash2 } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";
import { RiskTemplate } from "@/types";
interface RiskTemplateCardProps {
  template: RiskTemplate;
  onDelete: () => void;
}
export const RiskTemplateCard = ({ template, onDelete }: RiskTemplateCardProps) => {
  const navigate = useNavigate();
  const handleUseTemplate = () => {
    // Validate template ID
    if (!template.id) {
      return;
    }
    // Ensure we navigate to the correct path that matches the route in App.tsx
    // We're using the fully qualified path to ensure consistency
    navigate("/risks/create", {
      state: {
        templateId: template.id,
      },
    });
    // Add logging to help with debugging
  };
  return (
    <Card className="h-full flex flex-col">
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="mb-1">{template.name}</CardTitle>
            <CardDescription>{template.category}</CardDescription>
          </div>
          <Badge variant="outline" className="ml-2">
            L{template.defaultLikelihood}/I{template.defaultImpact}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="flex-grow">
        <p className="text-sm text-muted-foreground line-clamp-3">{template.description}</p>
        <div className="mt-4 text-xs text-muted-foreground">
          Created on {format(new Date(template.createdAt), "PPP")}
        </div>
      </CardContent>
      <CardFooter className="flex justify-between pt-2 border-t">
        <Button variant="outline" size="sm" onClick={onDelete}>
          <Trash2 className="h-4 w-4 mr-1" />
          Delete
        </Button>
        <Button size="sm" onClick={handleUseTemplate}>
          <Copy className="h-4 w-4 mr-1" />
          Use Template
        </Button>
      </CardFooter>
    </Card>
  );
};
