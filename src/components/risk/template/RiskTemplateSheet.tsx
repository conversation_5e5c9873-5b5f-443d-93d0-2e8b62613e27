import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetDescription,
} from "@/components/ui/sheet";
import RiskTemplateForm from "@/components/risk/template/RiskTemplateForm";
import { useToast } from "@/components/ui/use-toast";
import { useRiskTemplates } from "@/hooks/useRiskTemplates";

type TemplateFormValues = {
  name: string;
  description: string;
  category: string;
  categoryId: string;
  defaultLikelihood: number;
  defaultImpact: number;
  suggestedMitigationPlan?: string | undefined;
};
interface RiskTemplateSheetProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
}
export const RiskTemplateSheet = ({ isOpen, onOpenChange }: RiskTemplateSheetProps) => {
  const { toast } = useToast();
  const { createTemplate, fetchTemplates } = useRiskTemplates();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const handleSuccess = async (formData: TemplateFormValues) => {
    try {
      setIsSubmitting(true);
      const templateId = await createTemplate(formData as any);
      if (templateId) {
        toast({
          title: "Template created",
          description: "Your risk assessment template has been saved.",
        });
        // Explicitly refresh the templates list and ensure we wait for it
        await fetchTemplates();
        // Close the sheet after successful creation and refresh
        onOpenChange(false);
      } else {
        toast({
          title: "Error creating template",
          description: "There was a problem saving your template.",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error creating template",
        description: "There was a problem saving your template.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-xl w-full overflow-y-auto">
        <SheetHeader>
          <SheetTitle>Create Risk Template</SheetTitle>
          <SheetDescription>
            Create a reusable template for risk assessments. Templates help standardize risk
            evaluation across your organization.
          </SheetDescription>
        </SheetHeader>
        <div className="mt-6 pb-20">
          <RiskTemplateForm
            onSubmit={handleSuccess}
            onCancel={() => onOpenChange(false)}
            isSubmitting={isSubmitting}
          />
        </div>
      </SheetContent>
    </Sheet>
  );
};
