import { useForm } from "react-hook-form";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { RiskSeverity } from "@/types";
import { calculateSeverity } from "../utils/riskCalculations";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
// Schema for the risk template form
const templateFormSchema = z.object({
  name: z.string().min(3, { message: "Template name must be at least 3 characters" }),
  description: z.string().min(10, { message: "Please provide a more detailed description" }),
  category: z.string().min(1, { message: "Category is required" }),
  categoryId: z.string().min(1, { message: "Category ID is required" }),
  defaultLikelihood: z.number().min(1).max(5),
  defaultImpact: z.number().min(1).max(5),
  suggestedMitigationPlan: z.string().optional(),
});
type TemplateFormValues = z.infer<typeof templateFormSchema>;
interface RiskTemplateFormProps {
  onSubmit: (data: TemplateFormValues) => void;
  onCancel: () => void;
  isSubmitting: boolean;
}
interface Category {
  id: string;
  name: string;
}
const RiskTemplateForm = ({ onSubmit, onCancel, isSubmitting }: RiskTemplateFormProps) => {
  const [calculatedSeverity, setCalculatedSeverity] = useState<RiskSeverity>(RiskSeverity.LOW);
  const [categories, setCategories] = useState<Category[]>([]);
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const { data, error } = await supabase
          .from("risk_categories")
          .select("id, name")
          .order("name");
        if (error) {
          return;
        }
        setCategories(data ?? []);
      } catch (err) {
        // Error caught and handled
      }
    };
    fetchCategories();
  }, []);
  const form = useForm<TemplateFormValues>({
    resolver: zodResolver(templateFormSchema),
    defaultValues: {
      name: "",
      description: "",
      category: "",
      categoryId: "",
      defaultLikelihood: 1,
      defaultImpact: 1,
      suggestedMitigationPlan: "",
    },
  });
  const updateSeverity = (likelihood: number, impact: number) => {
    const severity = calculateSeverity(likelihood, impact);
    setCalculatedSeverity(severity);
  };
  const handleSubmit = (values: TemplateFormValues) => {
    onSubmit(values);
  };
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Template Name</FormLabel>
              <FormControl>
                <Input placeholder="E.g., IT Security Risk Assessment" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="description"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Template Description</FormLabel>
              <FormControl>
                <Textarea placeholder="Describe what this template is used for" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="category"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Risk Category</FormLabel>
              <Select
                onValueChange={value => {
                  // Find the selected category to get its ID
                  const selectedCategory = categories.find(cat => cat.name === value);
                  // Update both category (name) and categoryId fields
                  field.onChange(value);
                  if (selectedCategory) {
                    form.setValue("categoryId", selectedCategory.id);
                  }
                }}
                value={field.value}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.id} value={category.name}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        {/* Hidden field for categoryId */}
        <FormField
          control={form.control}
          name="categoryId"
          render={({ field }) => <input type="hidden" {...field} />}
        />
        <div className="space-y-4">
          <h3 className="font-medium">Default Risk Assessment Values</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="defaultLikelihood"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Default Likelihood (1-5)</FormLabel>
                  <FormControl>
                    <div className="space-y-2">
                      <Slider
                        min={1}
                        max={5}
                        step={1}
                        defaultValue={[field.value]}
                        onValueChange={values => {
                          field.onChange(values[0]);
                          updateSeverity(values[0] ?? 1, form.getValues("defaultImpact") ?? 1);
                        }}
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Very Low</span>
                        <span>Low</span>
                        <span>Medium</span>
                        <span>High</span>
                        <span>Very High</span>
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="defaultImpact"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Default Impact (1-5)</FormLabel>
                  <FormControl>
                    <div className="space-y-2">
                      <Slider
                        min={1}
                        max={5}
                        step={1}
                        defaultValue={[field.value]}
                        onValueChange={values => {
                          field.onChange(values[0]);
                          updateSeverity(form.getValues("defaultLikelihood") ?? 1, values[0] ?? 1);
                        }}
                      />
                      <div className="flex justify-between text-xs text-muted-foreground">
                        <span>Minimal</span>
                        <span>Minor</span>
                        <span>Moderate</span>
                        <span>Major</span>
                        <span>Critical</span>
                      </div>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          <div className="bg-muted/50 p-4 rounded-md">
            <div className="font-medium">Default Calculated Severity</div>
            <div
              className={`mt-1 text-lg font-semibold ${
                calculatedSeverity === RiskSeverity.LOW
                  ? "text-green-500"
                  : calculatedSeverity === RiskSeverity.MEDIUM
                    ? "text-amber-500"
                    : calculatedSeverity === RiskSeverity.HIGH
                      ? "text-orange-500"
                      : "text-red-500"
              }`}
            >
              {calculatedSeverity}
            </div>
          </div>
        </div>
        <FormField
          control={form.control}
          name="suggestedMitigationPlan"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Suggested Mitigation Plan</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Provide default mitigation strategies for this type of risk"
                  {...field}
                  value={field.value ?? ""}
                />
              </FormControl>
              <FormDescription>
                This will be pre-filled when creating risks from this template
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-end space-x-4 pt-4">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Creating Template..." : "Create Template"}
          </Button>
        </div>
      </form>
    </Form>
  );
};
export default RiskTemplateForm;
