# Risk Component Testing Guidelines

## Overview

This directory contains tests for React components in the risk module. The tests are designed to ensure accuracy by testing against real component implementations rather than mocks.

## RiskCard Component Testing

### Problem Solved

The RiskCard test was created to replace mock component definitions with actual component imports, ensuring tests run against the real implementation rather than simplified mocks.

### Solution

Instead of defining a mock RiskCard component in the test file (lines 6-111), the test now:

1. **Imports the actual component** from `@/components/ui/responsive-table/RiskCard`
2. **Tests real behavior** including actual color mappings, formatting logic, and edge cases
3. **Validates component contract** with proper Risk type interface
4. **Ensures accessibility** and semantic structure

### Key Implementation Details

```typescript
// Import the real component instead of mocking
import { RiskCard } from '@/components/ui/responsive-table/RiskCard';

// Test with real Risk data structure
const mockRisk: Risk = {
  id: '1',
  title: 'Test Risk',
  // ... complete Risk interface
};
```

### Test Coverage

The RiskCard tests cover:

- ✅ **Rendering**: Title, severity, status, category, owner, due date
- ✅ **Severity Colors**: Critical, High, Medium, Low with correct CSS classes
- ✅ **Status Colors**: Proper color mapping based on component logic
- ✅ **Edge Cases**: Missing data, null values, undefined properties
- ✅ **Styling**: Custom className application, hover effects
- ✅ **Date Formatting**: Various date formats and null handling
- ✅ **Status Formatting**: Underscore replacement and capitalization
- ✅ **Accessibility**: Semantic structure and icon rendering

### Important Findings

During test implementation, we discovered and fixed:

1. **Status Mapping Fixed**: The component's status color mapping has been updated to properly handle the RiskStatus enum values (`'Identified'`, `'In Progress'`, `'Mitigated'`, `'Accepted'`, `'Closed'`) instead of the previous lowercase/underscore format.

2. **Component Behavior**: Tests accurately reflect the actual component behavior, including proper color mapping for all enum status values.

### Running Tests

```bash
# Run RiskCard tests specifically
npm test src/components/risk/__tests__/RiskCard.test.tsx

# Run all risk component tests
npm test src/components/risk

# Run with coverage
npm test -- --coverage src/components/risk
```

## Best Practices

1. **Import Real Components**: Always import and test actual component implementations
2. **Use Complete Type Interfaces**: Provide full type-compliant test data
3. **Test Edge Cases**: Include tests for missing, null, and undefined values
4. **Validate Actual Behavior**: Test what the component actually does, not what you expect
5. **Document Discrepancies**: Note any mismatches between types and implementation
6. **Test Accessibility**: Ensure components meet accessibility standards

## File Structure

```
src/components/risk/
├── __tests__/
│   ├── README.md           # This file
│   └── RiskCard.test.tsx   # Tests for RiskCard component
└── [other component files]
```

## Future Improvements

1. **Type Safety**: Ensure component implementations match TypeScript interfaces
2. **Additional Components**: Add tests for other risk-related components following this pattern
3. **Consistency**: Ensure all components use the same status color mapping patterns
