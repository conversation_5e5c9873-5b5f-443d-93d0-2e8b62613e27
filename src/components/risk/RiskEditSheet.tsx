
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  SheetTitle,
  SheetDes<PERSON>
} from "@/components/ui/sheet";
import RiskEditForm from "@/components/risk/RiskEditForm";
import { Risk } from "@/types";

interface RiskEditSheetProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  risk: Risk;
}

export const RiskEditSheet = ({ isOpen, onOpenChange, onSuccess, risk }: RiskEditSheetProps) => {
  const handleSuccess = () => {
    onSuccess();
  };
  
  return (
    <Sheet open={isOpen} onOpenChange={onOpenChange}>
      <SheetContent className="sm:max-w-xl w-full overflow-y-auto">
        <SheetHeader>
          <SheetTitle>Edit Risk</SheetTitle>
          <SheetDescription>
            Update the details for this risk. All fields are required unless marked as optional.
          </SheetDescription>
        </SheetHeader>
        <div className="mt-6">
          <RiskEditForm 
            risk={risk}
            onSuccess={handleSuccess} 
            onCancel={() => onOpenChange(false)}
          />
        </div>
      </SheetContent>
    </Sheet>
  );
};
