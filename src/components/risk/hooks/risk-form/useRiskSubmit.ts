import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { z } from "zod";
import { RiskFormSchema } from "../../schema/riskFormSchema";
import { calculateSeverity } from "../../utils/riskCalculations";
import { RiskTemplate } from "@/types";
import { useAuth } from "@/contexts/auth";
type FormValues = z.infer<typeof RiskFormSchema>;
interface UseRiskSubmitProps {
  onSuccess?: () => void;
  categoryMapping: Record<string, string>;
  template: RiskTemplate | null;
}
export const useRiskSubmit = ({ onSuccess, categoryMapping, template }: UseRiskSubmitProps) => {
  const { toast } = useToast();
  const { organization } = useAuth();
  const [submitting, setSubmitting] = useState(false);
  const [uncategorizedId, setUncategorizedId] = useState<string | null>(null);
  useEffect(() => {
    const fetchUncategorizedId = async () => {
      try {
        const { data, error } = await supabase
          .from("risk_categories")
          .select("id")
          .eq("name", "Uncategorized")
          .maybeSingle();
        if (error) {
          return;
        }
        if (data) {
          setUncategorizedId(data.id);
        }
      } catch (err) {
        // Error caught and handled
      }
    };
    fetchUncategorizedId();
  }, []);
  const onSubmit = async (values: FormValues) => {
    try {
      setSubmitting(true);
      // Check auth status and organization before proceeding
      const {
        data: { session },
        error: sessionError,
      } = await supabase.auth.getSession();
      if (sessionError || !session) {
        toast({
          title: "Authentication Error",
          description: "Your session has expired. Please log in again.",
          variant: "destructive",
        });
        return false;
      }
      if (!organization?.id) {
        toast({
          title: "Organization Error",
          description:
            "No organization context found. Please ensure you're part of an organization.",
          variant: "destructive",
        });
        return false;
      }
      const currentUser = session.user;
      // Calculate severities
      const inherentSeverity = calculateSeverity(values.inherentLikelihood, values.inherentImpact);
      const residualSeverity = calculateSeverity(values.likelihood, values.impact);
      // Get the category ID - use the mapping, the direct categoryId, or the uncategorizedId as fallback
      let categoryId = null;
      if (values.category && categoryMapping[values.category]) {
        categoryId = categoryMapping[values.category];
      } else if (values.categoryId) {
        categoryId = values.categoryId;
      } else if (uncategorizedId) {
        categoryId = uncategorizedId;
      }
      if (!categoryId) {
        toast({
          title: "Warning",
          description: "Risk was created with no category. Please update it later.",
          variant: "default",
        });
      }
      // Check for legacy currentControls to migrate
      let migratedCurrentControls = values.currentControls;
      if (
        (values.controlMeasures && values.controlMeasures.length > 0) ||
        !values.currentControls
      ) {
        migratedCurrentControls = "";
      }
      // Create the risk record with organization_id
      const { data: riskData, error: riskError } = await supabase
        .from("risks")
        .insert({
          title: values.title,
          description: values.description,
          category_id: categoryId || null,
          organization_id: organization.id, // Add organization ID
          // Inherent risk properties
          inherent_likelihood: values.inherentLikelihood,
          inherent_impact: values.inherentImpact,
          inherent_severity: inherentSeverity,
          // Residual risk properties
          likelihood: values.likelihood,
          impact: values.impact,
          severity: residualSeverity,
          status: values.status,
          current_controls: migratedCurrentControls || null,
          mitigation_approach: values.mitigationApproach ?? null,
          due_date: values.dueDate ? values.dueDate.toISOString() : null,
          created_by: currentUser.id,
          owner_id: values.ownerId || currentUser.id,
          template_id: template?.id ?? null,
        })
        .select();
      if (riskError) {
        throw riskError;
      }
      if (!riskData || riskData.length === 0) {
        throw new Error("Failed to create risk record");
      }
      const riskId = riskData[0]?.id;
      // If we have control measures, add them with organization_id
      if (values.controlMeasures && values.controlMeasures.length > 0) {
        const controls = values.controlMeasures.map(control => ({
          risk_id: riskId!,
          organization_id: organization.id,
          description: control.description,
          effectiveness: control.effectiveness ?? "Medium",
          implemented: control.implemented ?? true,
        }));
        const { error: controlsError } = await supabase.from("control_measures").insert(controls);
        if (controlsError) {
          toast({
            title: "Note",
            description: "Risk was created but there was an issue saving some control measures.",
            variant: "default",
          });
        }
      }
      // If we have mitigation actions, add them with organization_id
      if (values.mitigationActions && values.mitigationActions.length > 0) {
        const actions = values.mitigationActions.map(action => ({
          risk_id: riskId!,
          organization_id: organization.id,
          description: action.description,
          completed: action.completed ?? false,
        }));
        const { error: actionsError } = await supabase.from("mitigation_actions").insert(actions);
        if (actionsError) {
          toast({
            title: "Note",
            description: "Risk was created but there was an issue saving some mitigation actions.",
            variant: "default",
          });
        }
      }
      toast({
        title: "Risk Created",
        description: "The risk has been successfully added to the register.",
        variant: "default",
      });
      if (onSuccess) {
        onSuccess();
      }
      return true;
    } catch (error: unknown) {
      toast({
        title: "Error",
        description: (error as Error)?.message ?? "Failed to create risk. Please try again.",
        variant: "destructive",
      });
      return false;
    } finally {
      setSubmitting(false);
    }
  };
  return {
    submitting,
    onSubmit,
  };
};
