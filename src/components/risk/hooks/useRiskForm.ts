import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { RiskStatus, RiskSeverity } from "@/types";
import { calculateSeverity } from "../utils/riskCalculations";
import { RiskFormSchema } from "../schema/riskFormSchema";
import { z } from "zod";
import { useTemplateLoader } from "./risk-form/useTemplateLoader";
import { useCategoryMapping } from "./risk-form/useCategoryMapping";
import { useRiskSubmit } from "./risk-form/useRiskSubmit";
import { useAuth } from "@/contexts/auth";

type FormValues = z.infer<typeof RiskFormSchema>;

interface UseRiskFormProps {
  onSuccess?: () => void;
  templateId?: string;
}

export const useRiskForm = ({ onSuccess, templateId }: UseRiskFormProps = {}) => {
  const [inherentSeverity, setInherentSeverity] = useState<RiskSeverity>(RiskSeverity.LOW);
  const [residualSeverity, setResidualSeverity] = useState<RiskSeverity>(RiskSeverity.LOW);
  const { user: currentUser } = useAuth();

  // Initialize the form with default values
  const form = useForm<FormValues>({
    resolver: zodResolver(RiskFormSchema),
    defaultValues: {
      title: "",
      description: "",
      category: "",
      categoryId: "",
      inherentLikelihood: 1,
      inherentImpact: 1,
      likelihood: 1,
      impact: 1,
      status: RiskStatus.IDENTIFIED,
      currentControls: "",
      controlMeasures: [],
      mitigationApproach: "",
      mitigationActions: [],
      ownerId: currentUser?.id,
    },
  });

  // Update calculated severities
  const updateInherentSeverity = (likelihood: number, impact: number) => {
    setInherentSeverity(calculateSeverity(likelihood, impact));
  };

  const updateResidualSeverity = (likelihood: number, impact: number) => {
    setResidualSeverity(calculateSeverity(likelihood, impact));
  };

  // Load category mappings
  const { categoryMapping } = useCategoryMapping();

  // Load template if provided
  const {
    template,
    loading: loadingTemplate,
    error: templateError,
  } = useTemplateLoader({
    form,
    ...(templateId && { templateId }),
    updateInherentSeverity,
    updateResidualSeverity,
  });

  // Handle form submission
  const { submitting, onSubmit } = useRiskSubmit({
    categoryMapping,
    template,
    ...(onSuccess && { onSuccess }),
  });

  // Wrap the submit function to reset form after successful submission
  const handleSubmit = async (values: FormValues) => {
    const success = await onSubmit(values);
    if (success) {
      form.reset();
    }
    return success;
  };

  return {
    form,
    inherentSeverity,
    residualSeverity,
    submitting,
    loadingTemplate,
    templateError,
    template,
    updateInherentSeverity,
    updateResidualSeverity,
    onSubmit: handleSubmit,
  };
};
