import { useState, useEffect } from "react";
import { UseFormReturn, FieldValues, Path } from "react-hook-form";

export interface FormSectionStatus<T extends FieldValues> {
  id: string;
  label: string;
  fields: Path<T>[];
  isCompleted: boolean;
  isActive: boolean;
}

export const useFormValidationState = <T extends FieldValues>(
  form: UseFormReturn<T>,
  sections: Array<Omit<FormSectionStatus<T>, "isCompleted" | "isActive">>
) => {
  const [sectionStatus, setSectionStatus] = useState<FormSectionStatus<T>[]>(() =>
    sections.map((section, index) => ({
      ...section,
      isCompleted: false,
      isActive: index === 0,
    }))
  );

  const [currentStep, setCurrentStep] = useState(0);
  const [formCompletion, setFormCompletion] = useState(0);

  // Update section completion status when form values change
  useEffect(() => {
    // Remove unused variable and directly use form.getValues() where needed

    const updatedSections = sections.map((section, index) => {
      const isComplete = section.fields.every(field => {
        const value = form.getValues(field);

        // Handle different types of values
        if (Array.isArray(value)) {
          return value.length > 0;
        } else if (typeof value === "string") {
          // Use type assertion to tell TypeScript this is definitely a string
          return (value as string).trim().length > 0;
        } else if (typeof value === "number") {
          return value > 0;
        } else if (value === null || value === undefined) {
          return false;
        }

        return !!value;
      });

      return {
        ...section,
        isCompleted: isComplete,
        isActive: index === currentStep,
      };
    });

    setSectionStatus(updatedSections);

    // Calculate form completion percentage
    const completedCount = updatedSections.filter(s => s.isCompleted).length;
    setFormCompletion(Math.round((completedCount / sections.length) * 100));
  }, [form, sections, currentStep, form.formState]);

  return {
    sectionStatus,
    currentStep,
    setCurrentStep,
    formCompletion,
  };
};
