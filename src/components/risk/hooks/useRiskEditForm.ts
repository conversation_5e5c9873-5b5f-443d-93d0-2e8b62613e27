import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Risk, RiskSeverity } from "@/types";
import { calculateSeverity } from "../utils/riskCalculations";
import { RiskFormSchema } from "../schema/riskFormSchema";
import { z } from "zod";
import { useCategoryMapping } from "./risk-form/useCategoryMapping";
import { useRiskUpdateSubmit } from "../../../hooks/risk/form/useRiskUpdateSubmit";

type FormValues = z.infer<typeof RiskFormSchema>;

interface UseRiskEditFormProps {
  initialRisk: Risk;
  onSuccess?: () => void;
}

export const useRiskEditForm = ({ initialRisk, onSuccess }: UseRiskEditFormProps) => {
  const [inherentSeverity, setInherentSeverity] = useState<RiskSeverity>(
    initialRisk.inherentSeverity
  );
  const [residualSeverity, setResidualSeverity] = useState<RiskSeverity>(initialRisk.severity);

  // Reuse the category mapping hook
  const { categoryMapping, loading: loadingCategories } = useCategoryMapping();

  const form = useForm<FormValues>({
    resolver: zodResolver(RiskFormSchema),
    defaultValues: {
      title: initialRisk.title,
      description: initialRisk.description,
      category: initialRisk.category,
      categoryId: initialRisk.categoryId,
      inherentLikelihood: initialRisk.inherentLikelihood,
      inherentImpact: initialRisk.inherentImpact,
      likelihood: initialRisk.likelihood,
      impact: initialRisk.impact,
      status: initialRisk.status,
      currentControls: initialRisk.currentControls ?? "",
      controlMeasures: initialRisk.controlMeasures ?? [],
      mitigationApproach: initialRisk.mitigationApproach ?? "",
      mitigationActions: initialRisk.mitigationActions ?? [],
      dueDate: initialRisk.dueDate,
      ownerId: initialRisk.ownerId,
    },
  });

  // Update calculated severities
  const updateInherentSeverity = (likelihood: number, impact: number) => {
    setInherentSeverity(calculateSeverity(likelihood, impact));
  };

  const updateResidualSeverity = (likelihood: number, impact: number) => {
    setResidualSeverity(calculateSeverity(likelihood, impact));
  };

  // Use the submission hook for handling the update (from the centralized location)
  const { submitting, onSubmit: handleSubmit } = useRiskUpdateSubmit({
    initialRisk,
    categoryMapping,
    ...(onSuccess && { onSuccess }),
  });

  // Wrapper for onSubmit to handle the form values
  const onSubmit = async (values: FormValues) => {
    return await handleSubmit(values);
  };

  return {
    form,
    inherentSeverity,
    residualSeverity,
    submitting,
    loadingCategories,
    updateInherentSeverity,
    updateResidualSeverity,
    onSubmit,
  };
};
