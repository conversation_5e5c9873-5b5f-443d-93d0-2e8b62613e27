import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Trash2, Plus, Info } from "lucide-react";

interface ControlMeasure {
  description: string;
  effectiveness: "High" | "Medium" | "Low";
  implemented: boolean;
}

interface RiskFormStep3Data {
  controlMeasures?: ControlMeasure[];
  [key: string]: unknown;
}

interface RiskFormStep3Props {
  data: RiskFormStep3Data;
  onUpdate: (data: RiskFormStep3Data) => void;
}

export const RiskFormStep3 = ({ data, onUpdate }: RiskFormStep3Props) => {
  const [newControlDescription, setNewControlDescription] = useState("");
  const [newControlEffectiveness, setNewControlEffectiveness] = useState<"High" | "Medium" | "Low">("Medium");

  const controlMeasures = data.controlMeasures ?? [];

  const addControl = () => {
    if (!newControlDescription.trim()) return;
    
    const newControl: ControlMeasure = {
      description: newControlDescription.trim(),
      effectiveness: newControlEffectiveness,
      implemented: true
    };
    
    const updatedControls = [...controlMeasures, newControl];
    onUpdate({ controlMeasures: updatedControls });
    
    setNewControlDescription("");
    setNewControlEffectiveness("Medium");
  };

  const removeControl = (index: number) => {
    const updatedControls = [...controlMeasures];
    updatedControls.splice(index, 1);
    onUpdate({ controlMeasures: updatedControls });
  };

  const updateControl = (index: number, field: keyof ControlMeasure, value: string | boolean) => {
    const updatedControls = [...controlMeasures];
    const control: ControlMeasure = {
      description: updatedControls[index]?.description ?? "",
      effectiveness: updatedControls[index]?.effectiveness ?? "Medium",
      implemented: updatedControls[index]?.implemented ?? false,
      ...updatedControls[index],
      [field]: value
    };
    updatedControls[index] = control;
    onUpdate({ controlMeasures: updatedControls });
  };

  return (
    <div className="space-y-6">
      <Alert className="max-w-2xl mx-auto">
        <Info className="h-4 w-4" />
        <AlertTitle>What are Current Controls?</AlertTitle>
        <AlertDescription>
          These are the safeguards, procedures, or measures you already have in place to prevent 
          or reduce this risk. Examples: security cameras, backup systems, training programs, 
          policies, or regular inspections.
        </AlertDescription>
      </Alert>

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardContent className="pt-6 space-y-6">
            <div>
              <Label className="text-base font-medium">Current Control Measures</Label>
              <p className="text-sm text-muted-foreground mt-1">
                Add the controls you currently have in place for this risk
              </p>
            </div>

            {/* Existing Controls List */}
            {controlMeasures.length > 0 && (
              <div className="space-y-3">
                {controlMeasures.map((control: ControlMeasure, index: number) => (
                  <div key={index} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <Input
                          value={control.description}
                          onChange={(e) => updateControl(index, "description", e.target.value)}
                          placeholder="Describe this control measure"
                        />
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeControl(index)}
                        className="ml-2 text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <Label className="text-sm">Effectiveness:</Label>
                        <Select
                          value={control.effectiveness}
                          onValueChange={(value) => updateControl(index, "effectiveness", value as "High" | "Medium" | "Low")}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="High">
                              <span className="text-green-600">High</span>
                            </SelectItem>
                            <SelectItem value="Medium">
                              <span className="text-amber-600">Medium</span>
                            </SelectItem>
                            <SelectItem value="Low">
                              <span className="text-red-600">Low</span>
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id={`implemented-${index}`}
                          checked={control.implemented}
                          onChange={(e) => updateControl(index, "implemented", e.target.checked)}
                          className="rounded"
                        />
                        <Label htmlFor={`implemented-${index}`} className="text-sm">
                          Currently implemented
                        </Label>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Add New Control */}
            <div className="border-2 border-dashed border-gray-200 rounded-lg p-4 space-y-4">
              <Label className="text-sm font-medium">Add New Control</Label>
              
              <div className="space-y-3">
                <Input
                  value={newControlDescription}
                  onChange={(e) => setNewControlDescription(e.target.value)}
                  placeholder="Describe a control measure you have in place..."
                />
                
                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    <Label className="text-sm">Effectiveness:</Label>
                    <Select
                      value={newControlEffectiveness}
                      onValueChange={(value) => setNewControlEffectiveness(value as "High" | "Medium" | "Low")}
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="High">
                          <span className="text-green-600">High</span>
                        </SelectItem>
                        <SelectItem value="Medium">
                          <span className="text-amber-600">Medium</span>
                        </SelectItem>
                        <SelectItem value="Low">
                          <span className="text-red-600">Low</span>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <Button
                    onClick={addControl}
                    disabled={!newControlDescription.trim()}
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Add Control
                  </Button>
                </div>
              </div>
            </div>

            {controlMeasures.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <p className="text-sm">No controls added yet.</p>
                <p className="text-xs mt-1">
                  Don't worry - you can always add controls later!
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
