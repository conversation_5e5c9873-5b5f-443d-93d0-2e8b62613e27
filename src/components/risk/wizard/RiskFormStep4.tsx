import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Info, ArrowRight } from "lucide-react";
import { calculateSeverity } from "../utils/riskCalculations";
import { useEffect } from "react";

interface RiskFormStep4Props {
  data: {
    likelihood?: number;
    impact?: number;
    severity?: string;
    inherentLikelihood?: number;
    inherentImpact?: number;
    inherentSeverity?: string;
    [key: string]: unknown;
  };
  onUpdate: (data: Record<string, any>) => void;
}

export const RiskFormStep4 = ({ data, onUpdate }: RiskFormStep4Props) => {
  useEffect(() => {
    const severity = calculateSeverity(data.likelihood ?? 1, data.impact ?? 1);
    onUpdate({ severity });
  }, [data.likelihood, data.impact, onUpdate]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'Critical': return 'text-red-700 bg-red-50 border-red-200';
      case 'High': return 'text-orange-700 bg-orange-50 border-orange-200';
      case 'Medium': return 'text-amber-700 bg-amber-50 border-amber-200';
      case 'Low': return 'text-green-700 bg-green-50 border-green-200';
      default: return 'text-gray-700 bg-gray-50 border-gray-200';
    }
  };

  const inherentScore = (data.inherentLikelihood ?? 1) * (data.inherentImpact ?? 1);
  const residualScore = (data.likelihood ?? 1) * (data.impact ?? 1);
  const riskReduction = inherentScore - residualScore;
  const reductionPercentage = inherentScore > 0 ? (riskReduction / inherentScore) * 100 : 0;

  return (
    <div className="space-y-6">
      <Alert className="max-w-2xl mx-auto">
        <Info className="h-4 w-4" />
        <AlertTitle>What is Residual Risk?</AlertTitle>
        <AlertDescription>
          This is the risk level AFTER considering your current controls. Think: "With all our 
          safeguards in place, what's the realistic likelihood and impact now?"
        </AlertDescription>
      </Alert>

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardContent className="pt-6">
            {/* Show comparison with inherent risk */}
            <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center justify-between">
                <div className="text-center">
                  <div className="text-sm text-muted-foreground">Before Controls</div>
                  <div className="text-lg font-bold text-red-600">{data.inherentSeverity ?? 'Unknown'}</div>
                  <div className="text-xs text-muted-foreground">Score: {inherentScore}</div>
                </div>
                
                <ArrowRight className="h-6 w-6 text-blue-600" />
                
                <div className="text-center">
                  <div className="text-sm text-muted-foreground">After Controls</div>
                  <div className="text-lg font-bold text-blue-600">{data.severity ?? 'Unknown'}</div>
                  <div className="text-xs text-muted-foreground">Score: {residualScore}</div>
                </div>
              </div>
              
              {riskReduction > 0 && (
                <div className="mt-3 text-center">
                  <div className="text-sm text-green-600 font-medium">
                    Risk reduced by {riskReduction} points ({reductionPercentage.toFixed(1)}%)
                  </div>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <Label className="text-base font-medium">
                  With controls in place, how likely is this to happen?
                </Label>
                <div className="mt-4">
                  <Slider 
                    min={1} 
                    max={5} 
                    step={1} 
                    value={[data.likelihood ?? 1]}
                    onValueChange={([value]) => onUpdate({ likelihood: value })}
                    className="mb-4" 
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Rare</span>
                    <span>Unlikely</span>
                    <span>Possible</span>
                    <span>Likely</span>
                    <span>Almost Certain</span>
                  </div>
                </div>
              </div>

              <div>
                <Label className="text-base font-medium">
                  If it happens, how bad would the impact be?
                </Label>
                <div className="mt-4">
                  <Slider 
                    min={1} 
                    max={5} 
                    step={1} 
                    value={[data.impact ?? 1]}
                    onValueChange={([value]) => onUpdate({ impact: value })}
                    className="mb-4" 
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Minimal</span>
                    <span>Minor</span>
                    <span>Moderate</span>
                    <span>Major</span>
                    <span>Critical</span>
                  </div>
                </div>
              </div>
            </div>

            <div className={`mt-8 p-4 rounded-lg border ${getSeverityColor(data.severity ?? 'Low')}`}>
              <div className="text-center">
                <div className="text-sm text-muted-foreground">Current Risk Level (After Controls)</div>
                <div className="text-2xl font-bold mt-1">{(data.severity ?? 'LOW').toUpperCase()}</div>
                <div className="text-sm text-muted-foreground mt-1">
                  Risk Score: {data.likelihood ?? 1} × {data.impact ?? 1} = {residualScore}
                </div>
              </div>
            </div>

            {/* Control effectiveness feedback */}
            <div className="mt-6 p-3 bg-green-50 rounded-md border border-green-200">
              <div className="text-sm">
                <span className="font-medium text-green-800">Control Effectiveness: </span>
                {reductionPercentage >= 50 && (
                  <span className="text-green-700">Excellent! Your controls are highly effective.</span>
                )}
                {reductionPercentage >= 25 && reductionPercentage < 50 && (
                  <span className="text-green-700">Good! Your controls are reasonably effective.</span>
                )}
                {reductionPercentage > 0 && reductionPercentage < 25 && (
                  <span className="text-amber-700">Your controls provide some protection but could be strengthened.</span>
                )}
                {reductionPercentage <= 0 && (
                  <span className="text-red-700">Consider adding more controls to reduce this risk.</span>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
