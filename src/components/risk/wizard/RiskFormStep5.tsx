import { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Trash2, Plus, Info, CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { RiskStatus } from "@/types";

interface MitigationAction {
  description: string;
  completed: boolean;
  dueDate?: Date | undefined;
  assignedTo?: string | undefined;
}

interface RiskFormStep5Data {
  mitigationActions?: MitigationAction[];
  status?: RiskStatus;
  dueDate?: Date | undefined;
  [key: string]: unknown;
}

interface RiskFormStep5Props {
  data: RiskFormStep5Data;
  onUpdate: (data: RiskFormStep5Data) => void;
}

export const RiskFormStep5 = ({ data, onUpdate }: RiskFormStep5Props) => {
  const [newActionDescription, setNewActionDescription] = useState("");

  const mitigationActions = data.mitigationActions ?? [];

  const addAction = () => {
    if (!newActionDescription.trim()) return;
    
    const newAction: MitigationAction = {
      description: newActionDescription.trim(),
      completed: false
    };
    
    const updatedActions = [...mitigationActions, newAction];
    onUpdate({ mitigationActions: updatedActions });
    
    setNewActionDescription("");
  };

  const removeAction = (index: number) => {
    const updatedActions = [...mitigationActions];
    updatedActions.splice(index, 1);
    onUpdate({ mitigationActions: updatedActions });
  };

  const updateAction = (index: number, field: string, value: string | boolean) => {
    const updatedActions = [...mitigationActions];
    const currentAction = updatedActions[index];
    if (!currentAction) return;
    
    updatedActions[index] = {
      description: currentAction.description,
      completed: currentAction.completed,
      dueDate: currentAction.dueDate,
      assignedTo: currentAction.assignedTo,
      [field]: value
    };
    onUpdate({ mitigationActions: updatedActions });
  };

  const getSeverityUrgency = (severity: string) => {
    switch (severity) {
      case 'Critical':
        return {
          message: "Critical risks require immediate action!",
          color: "text-red-600",
          bgColor: "bg-red-50 border-red-200"
        };
      case 'High':
        return {
          message: "High risks should be addressed as a priority.",
          color: "text-orange-600",
          bgColor: "bg-orange-50 border-orange-200"
        };
      case 'Medium':
        return {
          message: "Medium risks should have a clear mitigation plan.",
          color: "text-amber-600",
          bgColor: "bg-amber-50 border-amber-200"
        };
      case 'Low':
        return {
          message: "Low risks can be managed with basic controls.",
          color: "text-green-600",
          bgColor: "bg-green-50 border-green-200"
        };
      default:
        return {
          message: "All risks benefit from good planning.",
          color: "text-gray-600",
          bgColor: "bg-gray-50 border-gray-200"
        };
    }
  };

  const urgency = getSeverityUrgency(data['severity'] as string || 'Low');

  return (
    <div className="space-y-6">
      <Alert className="max-w-2xl mx-auto">
        <Info className="h-4 w-4" />
        <AlertTitle>Mitigation Planning</AlertTitle>
        <AlertDescription>
          Now let's create a plan to manage this risk. What actions will you take to further 
          reduce the likelihood or impact? When do you want to complete these actions?
        </AlertDescription>
      </Alert>

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardContent className="pt-6 space-y-6">
            {/* Risk severity context */}
            <div className={`p-4 rounded-lg border ${urgency.bgColor}`}>
              <div className={`font-medium ${urgency.color}`}>
                Current Risk Level: {data['severity'] as string || 'Low'}
              </div>
              <div className={`text-sm mt-1 ${urgency.color}`}>
                {urgency.message}
              </div>
            </div>

            {/* Status Selection */}
            <div>
              <Label className="text-base font-medium">Risk Status</Label>
              <Select
                value={data.status as string}
                onValueChange={(value) => onUpdate({ status: value as RiskStatus })}
              >
                <SelectTrigger className="mt-2">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(RiskStatus).map((status) => (
                    <SelectItem key={status} value={status}>
                      {status}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground mt-1">
                Most new risks start as "Identified"
              </p>
            </div>

            {/* Mitigation Approach */}
            <div>
              <Label className="text-base font-medium">Overall Mitigation Approach</Label>
              <Textarea
                value={(data['mitigationApproach'] as string) ?? ""}
                onChange={(e) => onUpdate({ mitigationApproach: e.target.value })}
                placeholder="Describe your overall strategy for managing this risk..."
                className="mt-2 min-h-[80px]"
              />
              <p className="text-sm text-muted-foreground mt-1">
                What's your high-level approach? (Optional but recommended)
              </p>
            </div>

            {/* Mitigation Actions */}
            <div>
              <Label className="text-base font-medium">Specific Actions</Label>
              <p className="text-sm text-muted-foreground mt-1 mb-4">
                List specific actions you'll take to manage this risk
              </p>

              {/* Existing Actions List */}
              {mitigationActions.length > 0 && (
                <div className="space-y-3 mb-4">
                  {mitigationActions.map((action: MitigationAction, index: number) => (
                    <div key={index} className="border rounded-lg p-3 space-y-2">
                      <div className="flex items-start justify-between">
                        <Input
                          value={action.description}
                          onChange={(e) => updateAction(index, "description", e.target.value)}
                          placeholder="Describe this action..."
                          className="flex-1"
                        />
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeAction(index)}
                          className="ml-2 text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id={`completed-${index}`}
                          checked={action.completed}
                          onChange={(e) => updateAction(index, "completed", e.target.checked)}
                          className="rounded"
                        />
                        <Label htmlFor={`completed-${index}`} className="text-sm">
                          Completed
                        </Label>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Add New Action */}
              <div className="border-2 border-dashed border-gray-200 rounded-lg p-4 space-y-3">
                <Input
                  value={newActionDescription}
                  onChange={(e) => setNewActionDescription(e.target.value)}
                  placeholder="Add a specific action to take..."
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && newActionDescription.trim()) {
                      e.preventDefault();
                      addAction();
                    }
                  }}
                />
                
                <Button
                  onClick={addAction}
                  disabled={!newActionDescription.trim()}
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  Add Action
                </Button>
              </div>
            </div>

            {/* Due Date */}
            <div>
              <Label className="text-base font-medium">Target Completion Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={`w-full justify-start text-left font-normal mt-2 ${
                      !data.dueDate && "text-muted-foreground"
                    }`}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {data.dueDate ? format(data.dueDate, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={data.dueDate}
                    onSelect={(date) => onUpdate({ dueDate: date || undefined })}
                    disabled={(date) => date < new Date()}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <p className="text-sm text-muted-foreground mt-1">
                When do you want to complete your mitigation actions? (Optional)
              </p>
            </div>

            {/* Summary */}
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="font-medium text-blue-800 mb-2">Risk Summary</div>
              <div className="text-sm text-blue-700 space-y-1">
                <div>• Risk reduced from {data['inherentSeverity'] as string || 'Unknown'} to {data['severity'] as string || 'Unknown'}</div>
                <div>• {mitigationActions.length} specific action{mitigationActions.length !== 1 ? 's' : ''} planned</div>
                <div>• Status: {data.status}</div>
                {data.dueDate && (
                  <div>• Target completion: {format(data.dueDate, "PPP")}</div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
