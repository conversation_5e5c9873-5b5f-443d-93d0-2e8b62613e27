export function useRiskWizardValidation() {
  const validateStep = (step: number, formData: Record<string, unknown>): boolean => {
    switch (step) {
      case 1:
        return (formData['title'] as string)?.trim().length >= 3 && 
               (formData['description'] as string)?.trim().length >= 10;
      case 2:
      case 3:
      case 4:
      case 5:
        return true;
      default:
        return true;
    }
  };

  const getValidationHintText = (step: number, canGoNext: boolean): string | undefined => {
    if (step === 1 && !canGoNext) {
      return "Please fill in the risk title (3+ characters) and description (10+ characters) to continue";
    }
    return undefined;
  };

  return {
    validateStep,
    getValidationHintText
  };
}
