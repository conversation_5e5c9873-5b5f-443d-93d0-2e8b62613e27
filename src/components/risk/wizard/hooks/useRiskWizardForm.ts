import { useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/auth";
import { log } from "@/services/loggingService";

interface ControlMeasure {
  description: string;
  effectiveness: string;
  implemented: boolean;
}

interface MitigationAction {
  description: string;
  completed: boolean;
}

interface RiskWizardFormData {
  // Step 1 - Basic Info
  title: string;
  description: string;
  category: string;
  categoryId: string;
  ownerId: string;
  // Step 2 - Inherent Risk
  inherentLikelihood: number;
  inherentImpact: number;
  inherentSeverity: string;
  // Step 3 - Controls
  controlMeasures: ControlMeasure[];
  // Step 4 - Residual Risk
  likelihood: number;
  impact: number;
  severity: string;
  // Step 5 - Mitigation
  status: string;
  mitigationApproach: string;
  mitigationActions: MitigationAction[];
  dueDate: Date | null;
}

interface UseRiskWizardFormOptions {
  onSuccess?: () => void;
  templateId?: string;
}

export function useRiskWizardForm({ onSuccess, templateId }: UseRiskWizardFormOptions) {
  const { user, organization } = useAuth();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [formData, setFormData] = useState<RiskWizardFormData>({
    // Step 1 - Basic Info
    title: "",
    description: "",
    category: "",
    categoryId: "",
    ownerId: user?.id ?? "",
    // Step 2 - Inherent Risk
    inherentLikelihood: 1,
    inherentImpact: 1,
    inherentSeverity: "Low",
    // Step 3 - Controls
    controlMeasures: [],
    // Step 4 - Residual Risk
    likelihood: 1,
    impact: 1,
    severity: "Low",
    // Step 5 - Mitigation
    status: "Identified",
    mitigationApproach: "",
    mitigationActions: [],
    dueDate: null,
  });

  const updateFormData = (stepData: Record<string, unknown>) => {
    setFormData(prev => ({ ...prev, ...stepData }));
  };

  const handleSubmit = async () => {
    if (!user) {
      toast({
        title: "Authentication Error",
        description: "You must be logged in to create a risk.",
        variant: "destructive",
      });
      return;
    }

    if (!organization?.id) {
      toast({
        title: "Organization Error",
        description: "No organization context found.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      let categoryId = formData.categoryId;

      if (!categoryId && formData.category) {
        const { data: categories } = await supabase
          .from("risk_categories")
          .select("id, name")
          .eq("name", formData.category)
          .single();
        if (categories) {
          categoryId = categories.id;
        }
      }

      if (!categoryId) {
        const { data: uncategorized } = await supabase
          .from("risk_categories")
          .select("id")
          .eq("name", "Uncategorized")
          .single();
        if (uncategorized) {
          categoryId = uncategorized.id;
        }
      }

      const { data: riskData, error: riskError } = await supabase
        .from("risks")
        .insert({
          title: formData.title,
          description: formData.description,
          category_id: categoryId,
          organization_id: organization.id,
          inherent_likelihood: formData.inherentLikelihood,
          inherent_impact: formData.inherentImpact,
          inherent_severity: formData.inherentSeverity,
          likelihood: formData.likelihood,
          impact: formData.impact,
          severity: formData.severity,
          status: formData.status,
          mitigation_approach: formData.mitigationApproach ?? null,
          due_date: formData.dueDate ? formData.dueDate.toISOString() : null,
          created_by: user.id,
          owner_id: formData.ownerId || user.id,
          template_id: templateId ?? null,
        })
        .select()
        .single();

      if (riskError) {
        throw riskError;
      }

      const riskId = riskData.id;

      // Insert control measures
      if (formData.controlMeasures && formData.controlMeasures.length > 0) {
        const controls = formData.controlMeasures.map(control => ({
          risk_id: riskId,
          organization_id: organization.id,
          description: control.description,
          effectiveness: control.effectiveness ?? "Medium",
          implemented: control.implemented ?? true,
        }));

        const { error: controlsError } = await supabase.from("control_measures").insert(controls);

        if (controlsError) {
          log.error("Error inserting control measures", controlsError, {
            component: "useRiskWizardForm",
            action: "insertControlMeasures",
            riskId,
          });
        }
      }

      // Insert mitigation actions
      if (formData.mitigationActions && formData.mitigationActions.length > 0) {
        const actions = formData.mitigationActions.map(action => ({
          risk_id: riskId,
          organization_id: organization.id,
          description: action.description,
          completed: action.completed ?? false,
        }));

        const { error: actionsError } = await supabase.from("mitigation_actions").insert(actions);

        if (actionsError) {
          log.error("Error inserting mitigation actions", actionsError, {
            component: "useRiskWizardForm",
            action: "insertMitigationActions",
            riskId,
          });
        }
      }

      toast({
        title: "Risk Created Successfully!",
        description: "Your risk has been added to the register.",
        variant: "default",
      });

      if (onSuccess) {
        onSuccess();
      }
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create risk. Please try again.";
      toast({
        title: "Error Creating Risk",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return {
    formData,
    updateFormData,
    handleSubmit,
    isSubmitting,
  };
}
