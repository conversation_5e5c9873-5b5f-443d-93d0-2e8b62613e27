import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Info } from "lucide-react";
import { calculateSeverity } from "../utils/riskCalculations";
import { useEffect } from "react";

interface RiskFormStep2Props {
  data: {
    inherentLikelihood?: number;
    inherentImpact?: number;
    inherentSeverity?: string;
  };
  onUpdate: (data: Record<string, any>) => void;
}

export const RiskFormStep2 = ({ data, onUpdate }: RiskFormStep2Props) => {
  useEffect(() => {
    const severity = calculateSeverity(data.inherentLikelihood ?? 1, data.inherentImpact ?? 1);
    onUpdate({ inherentSeverity: severity });
  }, [data.inherentLikelihood, data.inherentImpact, onUpdate]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'Critical': return 'text-red-700 bg-red-50 border-red-200';
      case 'High': return 'text-orange-700 bg-orange-50 border-orange-200';
      case 'Medium': return 'text-amber-700 bg-amber-50 border-amber-200';
      case 'Low': return 'text-green-700 bg-green-50 border-green-200';
      default: return 'text-gray-700 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="space-y-6">
      <Alert className="max-w-2xl mx-auto">
        <Info className="h-4 w-4" />
        <AlertTitle>What is Inherent Risk?</AlertTitle>
        <AlertDescription>
          This is the "raw" risk level if you had no safeguards, controls, or mitigation measures. 
          Think: "What's the worst-case scenario if we did nothing to prevent this?"
        </AlertDescription>
      </Alert>

      <div className="max-w-2xl mx-auto">
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <Label className="text-base font-medium">How likely is this to happen?</Label>
                <div className="mt-4">
                  <Slider 
                    min={1} 
                    max={5} 
                    step={1} 
                    value={[data.inherentLikelihood ?? 1]}
                    onValueChange={([value]) => onUpdate({ inherentLikelihood: value })}
                    className="mb-4" 
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Rare</span>
                    <span>Unlikely</span>
                    <span>Possible</span>
                    <span>Likely</span>
                    <span>Almost Certain</span>
                  </div>
                </div>
              </div>

              <div>
                <Label className="text-base font-medium">How bad would the impact be?</Label>
                <div className="mt-4">
                  <Slider 
                    min={1} 
                    max={5} 
                    step={1} 
                    value={[data.inherentImpact ?? 1]}
                    onValueChange={([value]) => onUpdate({ inherentImpact: value })}
                    className="mb-4" 
                  />
                  <div className="flex justify-between text-xs text-muted-foreground">
                    <span>Minimal</span>
                    <span>Minor</span>
                    <span>Moderate</span>
                    <span>Major</span>
                    <span>Critical</span>
                  </div>
                </div>
              </div>
            </div>

            <div className={`mt-8 p-4 rounded-lg border ${getSeverityColor(data.inherentSeverity ?? 'Low')}`}>
              <div className="text-center">
                <div className="text-sm text-muted-foreground">Inherent Risk Level</div>
                <div className="text-2xl font-bold mt-1">{(data.inherentSeverity ?? 'Low').toUpperCase()}</div>
                <div className="text-sm text-muted-foreground mt-1">
                  Without controls, this would be a {(data.inherentSeverity ?? 'low').toLowerCase()} risk
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
