import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/auth";
interface RiskFormStep1Data {
  title?: string;
  description?: string;
  category?: string;
  categoryId?: string;
  ownerId?: string;
}
interface RiskFormStep1Props {
  data: RiskFormStep1Data;
  onUpdate: (data: Partial<RiskFormStep1Data>) => void;
}
export const RiskFormStep1 = ({ data, onUpdate }: RiskFormStep1Props) => {
  const [categories, setCategories] = useState<Array<{ id: string; name: string }>>([]);
  const [owners, setOwners] = useState<Array<{ id: string; name: string }>>([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch categories
        const { data: categoryData } = await supabase
          .from("risk_categories")
          .select("id, name")
          .order("name");
        if (categoryData) {
          setCategories(categoryData);
        }
        // Fetch potential risk owners
        const { data: profileData } = await supabase
          .from("profiles")
          .select("id, name")
          .in("role", ["admin", "risk_owner"])
          .order("name");
        if (profileData) {
          // Add current user to the list if not already there
          const ownersWithCurrentUser = user
            ? [user, ...profileData.filter(p => p.id !== user.id)]
            : profileData;
          setOwners(
            ownersWithCurrentUser.map(owner => ({
              id: owner.id,
              name:
                owner.name ??
                (owner && typeof owner === "object" && "email" in owner
                  ? (owner as { email: string }).email
                  : "") ??
                "Unknown",
            }))
          );
        }
      } catch (error) {
        // Error caught and handled
      } finally {
        setLoading(false);
      }
    };
    fetchData();
  }, [user]);
  const handleCategoryChange = (categoryName: string) => {
    const selectedCategory = categories.find(cat => cat.name === categoryName);
    onUpdate({
      category: categoryName,
      categoryId: selectedCategory?.id ?? "",
    });
  };
  return (
    <div className="max-w-2xl mx-auto space-y-6">
      <div>
        <Label className="text-base font-medium">What is this risk?</Label>
        <Input
          placeholder="Give this risk a clear, descriptive name"
          className="mt-2"
          value={data.title ?? ""}
          onChange={e => onUpdate({ title: e.target.value })}
        />
        {data.title && data.title.length < 3 && (
          <p className="text-sm text-red-600 mt-1">Title must be at least 3 characters</p>
        )}
      </div>
      <div>
        <Label className="text-base font-medium">Describe the risk in detail</Label>
        <Textarea
          placeholder="What could go wrong? What would cause this risk to occur? Who or what would be affected?"
          className="mt-2 min-h-[100px]"
          value={data.description ?? ""}
          onChange={e => onUpdate({ description: e.target.value })}
        />
        {data.description && data.description.length < 10 && (
          <p className="text-sm text-red-600 mt-1">Description must be at least 10 characters</p>
        )}
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <Label className="text-base font-medium">Category (Optional)</Label>
          {loading ? (
            <div className="mt-2 h-10 bg-gray-100 rounded animate-pulse"></div>
          ) : (
            <Select value={data.category ?? ""} onValueChange={handleCategoryChange}>
              <SelectTrigger className="mt-2">
                <SelectValue placeholder="Choose a category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map(cat => (
                  <SelectItem key={cat.id} value={cat.name}>
                    {cat.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
        <div>
          <Label className="text-base font-medium">Risk Owner</Label>
          {loading ? (
            <div className="mt-2 h-10 bg-gray-100 rounded animate-pulse"></div>
          ) : (
            <Select
              value={data.ownerId ?? ""}
              onValueChange={value => onUpdate({ ownerId: value })}
            >
              <SelectTrigger className="mt-2">
                <SelectValue placeholder="Who's responsible?" />
              </SelectTrigger>
              <SelectContent>
                {owners.map(owner => (
                  <SelectItem key={owner.id} value={owner.id}>
                    {owner.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
      </div>
    </div>
  );
};
