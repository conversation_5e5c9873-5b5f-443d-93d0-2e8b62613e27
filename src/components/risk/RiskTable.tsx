import { Risk, RiskSeverity, RiskStatus } from "@/types";
import { useNavigate } from "react-router-dom";
import { Table, TableBody } from "@/components/ui/table";
import { RiskTableRow } from "./table/RiskTableRow";
import RiskTableHeader from "./table/RiskTableHeader";
import RiskTableEmptyState from "./table/RiskTableEmptyState";
import { ResponsiveTable } from "@/components/ui/responsive-table";
import { RiskCardWrapper } from "@/components/dashboard/RiskCardWrapper";

interface Category {
  id: string;
  name: string;
}

interface User {
  id: string;
  name: string;
}

interface RiskTableProps {
  risks: Risk[];
  isLoading: boolean;
  categories?: Category[];
  owners?: User[];
  activeFilters: {
    severities?: RiskSeverity[];
    statuses?: RiskStatus[];
    categories?: string[];
    ownerIds?: string[];
  };
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
  onFilterChange: (filterType: string, value: string | null) => void;
  onSortChange: (field: string, order: 'asc' | 'desc' | null) => void;
  isLoadingFilters?: boolean;
}

export const RiskTable = ({ 
  risks, 
  isLoading, 
  categories = [], 
  owners = [], 
  activeFilters,
  sortField,
  sortOrder,
  onFilterChange,
  onSortChange,
  isLoadingFilters = false
}: RiskTableProps) => {
  const navigate = useNavigate();
  
  const handleRowClick = (riskId: string) => {
    // Preserve filters and sort in URL when navigating to risk details
    let queryString = '';
    
    const params = new URLSearchParams();
    
    // Add all severities
    if (activeFilters.severities) {
      activeFilters.severities.forEach(severity => params.append('severity', severity));
    }
    
    // Add all statuses
    if (activeFilters.statuses) {
      activeFilters.statuses.forEach(status => params.append('status', status));
    }
    
    // Add all categories
    if (activeFilters.categories) {
      activeFilters.categories.forEach(category => params.append('category', category));
    }
    
    // Add all owners
    if (activeFilters.ownerIds) {
      activeFilters.ownerIds.forEach(ownerId => params.append('owner', ownerId));
    }
    
    if (sortField) params.append('sortField', sortField);
    if (sortOrder) params.append('sortOrder', sortOrder);
    
    if (params.toString()) {
      queryString = `?${params.toString()}`;
    }
    
    navigate(`/risks/${riskId}${queryString}`);
  };

  const tableHeader = (
    <RiskTableHeader 
      categories={categories}
      owners={owners}
      activeFilters={{
        ...(activeFilters.severities && { severities: activeFilters.severities }),
        ...(activeFilters.statuses && { statuses: activeFilters.statuses }),
        ...(activeFilters.categories && { categories: activeFilters.categories }),
        ...(activeFilters.ownerIds && { ownerIds: activeFilters.ownerIds })
      }}
      {...(sortField && { sortField })}
      {...(sortOrder && { sortOrder })}
      onFilterChange={onFilterChange}
      onSortChange={onSortChange}
      isLoadingFilters={isLoadingFilters}
    />
  );

  const tableBody = (
    <TableBody>
      {risks.map((risk) => (
        <RiskTableRow
          key={risk.id}
          risk={risk}
          onClick={handleRowClick}
        />
      ))}
    </TableBody>
  );

  const emptyState = (
    <Table>
      {tableHeader}
      <RiskTableEmptyState />
    </Table>
  );

  return (
    <ResponsiveTable
      data={risks}
      isLoading={isLoading}
      tableHeader={tableHeader}
      tableBody={tableBody}
      CardComponent={RiskCardWrapper}
      emptyState={emptyState}
      className="shadow-sm"
    />
  );
};
