
import { useWizardNavigation } from "@/hooks/useWizardNavigation";
import { WizardContainer } from "@/components/ui/wizard/WizardContainer";
import { RiskFormStep1 } from "./wizard/RiskFormStep1";
import { RiskFormStep2 } from "./wizard/RiskFormStep2";
import { RiskFormStep3 } from "./wizard/RiskFormStep3";
import { RiskFormStep4 } from "./wizard/RiskFormStep4";
import { RiskFormStep5 } from "./wizard/RiskFormStep5";
import { useRiskWizardForm } from "./wizard/hooks/useRiskWizardForm";
import { useRiskWizardValidation } from "./wizard/hooks/useRiskWizardValidation";

interface RiskFormWizardProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  templateId?: string;
}

const steps = [
  { id: 1, title: "Risk Identification", description: "Basic information" },
  { id: 2, title: "Inherent Risk", description: "Risk without controls" },
  { id: 3, title: "Current Controls", description: "Existing safeguards" },
  { id: 4, title: "Residual Risk", description: "Risk with controls" },
  { id: 5, title: "Mitigation Plan", description: "Action planning" }
];

export const RiskFormWizard = ({ onSuccess, onCancel, templateId }: RiskFormWizardProps) => {
  const { formData, updateFormData, handleSubmit, isSubmitting } = useRiskWizardForm({
    onSuccess: onSuccess || (() => {}),
    ...(templateId && { templateId })
  });

  const { validateStep, getValidationHintText } = useRiskWizardValidation();

  const {
    currentStep,
    canGoNext,
    isLastStep,
    goToNext,
    goToPrevious
  } = useWizardNavigation({
    totalSteps: steps.length,
    validateStep: (step: number) => validateStep(step, formData as unknown as Record<string, unknown>),
    onComplete: handleSubmit
  });

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return <RiskFormStep1 data={formData as any} onUpdate={(data) => updateFormData(data as any)} />;
      case 2:
        return <RiskFormStep2 data={formData as any} onUpdate={(data) => updateFormData(data as any)} />;
      case 3:
        return <RiskFormStep3 data={formData as any} onUpdate={(data) => updateFormData(data as any)} />;
      case 4:
        return <RiskFormStep4 data={formData as any} onUpdate={(data) => updateFormData(data as any)} />;
      case 5:
        return <RiskFormStep5 data={formData as any} onUpdate={(data) => updateFormData(data as any)} />;
      default:
        return null;
    }
  };

  const handleNextClick = () => {
    if (isLastStep) {
      handleSubmit();
    } else {
      goToNext();
    }
  };

  const validationHint = getValidationHintText(currentStep, canGoNext);

  return (
    <WizardContainer
      steps={steps}
      currentStep={currentStep}
      canProceed={canGoNext}
      isSubmitting={isSubmitting}
      onPrevious={goToPrevious}
      onNext={handleNextClick}
      onCancel={onCancel || (() => {})}
      nextButtonText={isLastStep ? (isSubmitting ? "Creating Risk..." : "Create Risk") : "Next"}
      {...(validationHint ? { validationHintText: validationHint } : {})}
    >
      {renderCurrentStep()}
    </WizardContainer>
  );
};
