
import { useRiskForm } from "@/hooks/risk";
import BaseRiskForm from "./form-sections/BaseRiskForm";

interface RiskFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
  templateId?: string;
}

const RiskForm = ({ onSuccess, onCancel, templateId }: RiskFormProps) => {
  const { 
    form, 
    inherentSeverity,
    residualSeverity,
    submitting, 
    template, 
    loadingTemplate,
    updateInherentSeverity,
    updateResidualSeverity,
    onSubmit 
  } = useRiskForm({
    onSuccess: onSuccess || (() => {}),
    ...(templateId && { templateId })
  });

  return (
    <BaseRiskForm
      mode="create"
      form={form}
      inherentSeverity={inherentSeverity}
      residualSeverity={residualSeverity}
      submitting={submitting}
      {...(template && { template })}
      loadingTemplate={loadingTemplate}
      updateInherentSeverity={updateInherentSeverity}
      updateResidualSeverity={updateResidualSeverity}
      onSubmit={async (values) => {
        await onSubmit(values);
      }}
      onCancel={onCancel || (() => {})}
    />
  );
};

export default RiskForm;
