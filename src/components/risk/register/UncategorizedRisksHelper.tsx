import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { Risk } from "@/types";
import { AlertTriangle } from "lucide-react";
interface Category {
  id: string;
  name: string;
}
interface UncategorizedRisksHelperProps {
  uncategorizedRisks: Risk[];
  categories: Category[];
  onRisksUpdated: () => void;
}
export const UncategorizedRisksHelper = ({
  uncategorizedRisks,
  categories,
  onRisksUpdated,
}: UncategorizedRisksHelperProps) => {
  const { toast } = useToast();
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [updating, setUpdating] = useState(false);
  if (uncategorizedRisks.length === 0) {
    return null;
  }
  const handleBulkCategorize = async () => {
    if (!selectedCategory) {
      toast({
        title: "Error",
        description: "Please select a category",
        variant: "destructive",
      });
      return;
    }
    setUpdating(true);
    try {
      const riskIds = uncategorizedRisks.map(risk => risk.id);
      const { error } = await supabase
        .from("risks")
        .update({ category_id: selectedCategory })
        .in("id", riskIds);
      if (error) throw error;
      toast({
        title: "Success",
        description: `Updated ${uncategorizedRisks.length} risks with the selected category`,
      });
      onRisksUpdated();
      setSelectedCategory("");
    } catch (error: unknown) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update risk categories",
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  };
  return (
    <Card className="border-orange-200 bg-orange-50">
      <CardHeader className="pb-3">
        <div className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-orange-600" />
          <CardTitle className="text-orange-800">Uncategorized Risks Found</CardTitle>
        </div>
        <CardDescription className="text-orange-700">
          You have{" "}
          <Badge variant="outline" className="text-orange-800">
            {uncategorizedRisks.length}
          </Badge>{" "}
          risks that need to be categorized. Assign them to a category for better organization.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex flex-col sm:flex-row gap-3">
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="flex-1">
              <SelectValue placeholder="Select a category for all uncategorized risks" />
            </SelectTrigger>
            <SelectContent>
              {categories.map(category => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button
            onClick={handleBulkCategorize}
            disabled={!selectedCategory || updating}
            className="sm:w-auto w-full"
          >
            {updating ? "Updating..." : "Categorize All"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
