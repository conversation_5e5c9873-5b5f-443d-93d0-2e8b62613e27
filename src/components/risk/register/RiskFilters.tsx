
import { RiskSeverity, RiskStatus, User } from "@/types";
import { useFilterData } from './filters/useFilterData';

// Import filter components
import SeverityFilter from './filters/SeverityFilter';
import StatusFilter from './filters/StatusFilter';
import CategoryFilter from './filters/CategoryFilter';
import OwnerFilter from './filters/OwnerFilter';
import SortByDueDate from './filters/SortByDueDate';

interface Category {
  id: string;
  name: string;
}

interface RiskFiltersProps {
  categories?: Category[];
  owners?: User[];
  onFilterChange: (filterType: string, value: string | null) => void;
  onSortChange: (sortOrder: 'asc' | 'desc' | null) => void;
  activeFilters: {
    severities?: RiskSeverity[];
    statuses?: RiskStatus[];
    categories?: string[];
    ownerIds?: string[];
  };
  sortOrder?: 'asc' | 'desc' | undefined;
  isLoading?: boolean;
}

const RiskFilters = ({ 
  categories = [],
  owners = [],
  onFilterChange, 
  onSortChange, 
  activeFilters,
  sortOrder,
  isLoading = false
}: RiskFiltersProps) => {
  // Get filter data from custom hook (fallback if props not provided)
  const { 
    categories: hookCategories, 
    owners: hookOwners, 
    usedSeverities, 
    usedStatuses, 
    loading: hookLoading 
  } = useFilterData();

  // Use props if provided, otherwise fall back to hook data
  const finalCategories = categories.length > 0 ? categories : hookCategories;
  const finalOwners = owners.length > 0 ? owners : hookOwners;
  const finalLoading = isLoading || hookLoading;

  return (
    <div className="flex flex-wrap gap-2 mb-4 items-center">
      {/* Category filter */}
      <CategoryFilter 
        onFilterChange={(category) => onFilterChange('category', category)}
        activeCategories={activeFilters.categories ?? []}
        categories={finalCategories}
        loading={finalLoading}
      />

      {/* Owner filter */}
      <OwnerFilter 
        onFilterChange={(ownerId) => onFilterChange('ownerId', ownerId)}
        activeOwners={activeFilters.ownerIds ?? []}
        owners={finalOwners}
        loading={finalLoading}
      />

      {/* Severity filter */}
      <SeverityFilter 
        onFilterChange={(severity) => onFilterChange('severity', severity)} 
        activeSeverities={activeFilters.severities ?? []}
        availableSeverities={usedSeverities}
        loading={finalLoading}
      />

      {/* Status filter */}
      <StatusFilter 
        onFilterChange={(status) => onFilterChange('status', status)}
        activeStatuses={activeFilters.statuses ?? []}
        availableStatuses={usedStatuses}
        loading={finalLoading}
      />

      {/* Sort by due date */}
      <SortByDueDate 
        onSortChange={onSortChange} 
        sortOrder={sortOrder} 
      />
    </div>
  );
};

export default RiskFilters;
