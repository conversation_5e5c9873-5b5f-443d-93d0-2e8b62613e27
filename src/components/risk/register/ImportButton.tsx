
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Upload } from "lucide-react";
import { RiskImportDialog } from "../import/RiskImportDialog";

interface ImportButtonProps {
  onImportSuccess: () => void;
}

export function ImportButton({ onImportSuccess }: ImportButtonProps) {
  const [dialogOpen, setDialogOpen] = useState(false);

  return (
    <>
      <Button 
        variant="outline" 
        onClick={() => setDialogOpen(true)}
        className="gap-2"
      >
        <Upload className="h-4 w-4" />
        Import Risks
      </Button>

      <RiskImportDialog
        isOpen={dialogOpen}
        onOpenChange={setDialogOpen}
        onSuccess={onImportSuccess}
      />
    </>
  );
}
