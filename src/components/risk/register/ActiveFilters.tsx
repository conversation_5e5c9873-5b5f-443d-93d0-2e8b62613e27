
import { RiskSeverity, RiskStatus } from "@/types";
import ActiveFilterBadge from "./filters/ActiveFilterBadge";
import ClearAllFilters from "./filters/ClearAllFilters";
import { useFilterNames } from "./filters/useFilterNames";

interface ActiveFiltersProps {
  severities?: RiskSeverity[];
  statuses?: RiskStatus[];
  categories?: string[];
  ownerIds?: string[];
  onClearFilter: (filterType: 'severity' | 'status' | 'category' | 'ownerId' | 'all') => void;
}

const ActiveFilters = ({ severities = [], statuses = [], categories = [], ownerIds = [], onClearFilter }: ActiveFiltersProps) => {
  const hasActiveFilters = severities.length > 0 || statuses.length > 0 || categories.length > 0 || ownerIds.length > 0;
  const { categoryNames, ownerNames } = useFilterNames(categories, ownerIds);
  
  if (!hasActiveFilters) {
    return null;
  }
  
  return (
    <div className="flex flex-wrap gap-2 mb-4">
      <span className="text-sm text-muted-foreground mr-1 pt-0.5">Active filters:</span>
      
      {categories.length > 0 && categoryNames.length > 0 && (
        <ActiveFilterBadge
          label="Categories"
          value={`${categoryNames.length} selected`}
          onRemove={() => onClearFilter('category')}
        />
      )}
      
      {ownerIds.length > 0 && ownerNames.length > 0 && (
        <ActiveFilterBadge
          label="Owners"
          value={`${ownerNames.length} selected`}
          onRemove={() => onClearFilter('ownerId')}
        />
      )}
      
      {severities.length > 0 && (
        <ActiveFilterBadge
          label="Severities"
          value={`${severities.length} selected`}
          onRemove={() => onClearFilter('severity')}
        />
      )}
      
      {statuses.length > 0 && (
        <ActiveFilterBadge
          label="Statuses"
          value={`${statuses.length} selected`}
          onRemove={() => onClearFilter('status')}
        />
      )}
      
      {hasActiveFilters && (
        <ClearAllFilters onClick={() => onClearFilter('all')} />
      )}
    </div>
  );
};

export default ActiveFilters;
