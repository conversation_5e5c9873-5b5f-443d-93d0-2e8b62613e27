
import { ChevronDown } from "lucide-react";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";

interface Category {
  id: string;
  name: string;
}

interface CategoryFilterProps {
  onFilterChange: (value: string | null) => void;
  activeCategories: string[];
  categories: Category[];
  loading: boolean;
}

const CategoryFilter = ({ onFilterChange, activeCategories = [], categories, loading }: CategoryFilterProps) => {
  const handleToggleCategory = (categoryId: string) => {
    onFilterChange(categoryId);
  };
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          Category
          <ChevronDown className="ml-1 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-48">
        <DropdownMenuLabel>Filter by Category</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          {loading ? (
            <DropdownMenuItem disabled>Loading...</DropdownMenuItem>
          ) : (
            categories.map((category) => (
              <DropdownMenuItem key={category.id} onSelect={(e) => e.preventDefault()}>
                <div className="flex items-center space-x-2 w-full" 
                     onClick={() => handleToggleCategory(category.id)}>
                  <Checkbox 
                    checked={activeCategories.includes(category.id)} 
                    onCheckedChange={() => handleToggleCategory(category.id)} 
                    aria-label={`Filter by ${category.name} category`}
                  />
                  <span>{category.name}</span>
                </div>
              </DropdownMenuItem>
            ))
          )}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default CategoryFilter;
