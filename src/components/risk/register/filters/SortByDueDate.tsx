
import { ArrowDown, ArrowUp } from "lucide-react";
import { Button } from "@/components/ui/button";

interface SortByDueDateProps {
  onSortChange: (sortOrder: 'asc' | 'desc' | null) => void;
  sortOrder?: 'asc' | 'desc' | undefined;
}

const SortByDueDate = ({
  onSortChange,
  sortOrder
}: SortByDueDateProps) => {
  const handleSortToggle = () => {
    if (!sortOrder) {
      onSortChange('desc'); // First click - descending
    } else if (sortOrder === 'desc') {
      onSortChange('asc'); // Second click - ascending
    } else {
      onSortChange(null); // Third click - no sort
    }
  };
  
  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleSortToggle}
      className="flex items-center gap-1"
    >
      <span>Due Date</span>
      {sortOrder === 'desc' && <ArrowDown className="h-4 w-4" />}
      {sortOrder === 'asc' && <ArrowUp className="h-4 w-4" />}
    </Button>
  );
};

export default SortByDueDate;
