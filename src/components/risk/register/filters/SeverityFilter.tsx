
import { ChevronDown } from "lucide-react";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { RiskSeverity } from "@/types";

interface SeverityFilterProps {
  onFilterChange: (value: RiskSeverity | null) => void;
  activeSeverities: RiskSeverity[];
  availableSeverities?: RiskSeverity[]; // Used severities from the database
  loading?: boolean;
}

const SeverityFilter = ({ 
  onFilterChange, 
  activeSeverities = [], 
  availableSeverities,
  loading = false 
}: SeverityFilterProps) => {
  // Fall back to all enum values if no available severities are provided
  const severityOptions = (availableSeverities?.length ?? 0) > 0 
    ? availableSeverities 
    : Object.values(RiskSeverity);
  
  const handleToggleSeverity = (severity: RiskSeverity) => {
    onFilterChange(severity);
  };
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          Severity
          <ChevronDown className="ml-1 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-48">
        <DropdownMenuLabel>Filter by Severity</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          {loading ? (
            <DropdownMenuItem disabled>Loading...</DropdownMenuItem>
          ) : (
            severityOptions?.map((severity) => (
              <DropdownMenuItem key={severity} onSelect={(e) => e.preventDefault()}>
                <div className="flex items-center space-x-2 w-full" 
                     onClick={() => handleToggleSeverity(severity)}>
                  <Checkbox 
                    checked={activeSeverities.includes(severity)} 
                    onCheckedChange={() => handleToggleSeverity(severity)} 
                    aria-label={`Filter by ${severity} severity`}
                  />
                  <span>{severity}</span>
                </div>
              </DropdownMenuItem>
            ))
          )}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default SeverityFilter;
