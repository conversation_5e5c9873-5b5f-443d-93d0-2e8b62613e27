
import { X } from "lucide-react";
import { Badge } from "@/components/ui/badge";

interface ActiveFilterBadgeProps {
  label: string;
  value: string;
  onRemove: () => void;
}

const ActiveFilterBadge = ({ label, value, onRemove }: ActiveFilterBadgeProps) => {
  return (
    <Badge variant="outline" className="flex items-center gap-1">
      {label}: {value}
      <button 
        onClick={onRemove}
        className="ml-1"
        aria-label={`Clear ${label.toLowerCase()} filter`}
      >
        <X className="h-3 w-3" />
      </button>
    </Badge>
  );
};

export default ActiveFilterBadge;
