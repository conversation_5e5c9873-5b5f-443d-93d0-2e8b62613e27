
import { ChevronDown } from "lucide-react";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { User } from "@/types";

interface OwnerFilterProps {
  onFilterChange: (value: string | null) => void;
  activeOwners: string[];
  owners: User[];
  loading: boolean;
}

const OwnerFilter = ({ onFilterChange, activeOwners = [], owners, loading }: OwnerFilterProps) => {
  const handleToggleOwner = (ownerId: string) => {
    onFilterChange(ownerId);
  };
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          Owner
          <ChevronDown className="ml-1 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-48">
        <DropdownMenuLabel>Filter by Owner</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          {loading ? (
            <DropdownMenuItem disabled>Loading...</DropdownMenuItem>
          ) : (
            owners.map((owner) => (
              <DropdownMenuItem key={owner.id} onSelect={(e) => e.preventDefault()}>
                <div className="flex items-center space-x-2 w-full" 
                     onClick={() => handleToggleOwner(owner.id)}>
                  <Checkbox 
                    checked={activeOwners.includes(owner.id)} 
                    onCheckedChange={() => handleToggleOwner(owner.id)} 
                    aria-label={`Filter by owner ${owner.name}`}
                  />
                  <span>{owner.name}</span>
                </div>
              </DropdownMenuItem>
            ))
          )}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default OwnerFilter;
