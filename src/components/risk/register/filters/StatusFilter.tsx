
import { ChevronDown } from "lucide-react";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { RiskStatus } from "@/types";

interface StatusFilterProps {
  onFilterChange: (value: RiskStatus | null) => void;
  activeStatuses: RiskStatus[];
  availableStatuses?: RiskStatus[]; // Used statuses from the database
  loading?: boolean;
}

const StatusFilter = ({ 
  onFilterChange, 
  activeStatuses = [], 
  availableStatuses, 
  loading = false 
}: StatusFilterProps) => {
  // Fall back to all enum values if no available statuses are provided
  const statusOptions = (availableStatuses?.length ?? 0) > 0 
    ? availableStatuses 
    : Object.values(RiskStatus);
  
  const handleToggleStatus = (status: RiskStatus) => {
    onFilterChange(status);
  };
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm">
          Status
          <ChevronDown className="ml-1 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-48">
        <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          {loading ? (
            <DropdownMenuItem disabled>Loading...</DropdownMenuItem>
          ) : (
            statusOptions?.map((status) => (
              <DropdownMenuItem key={status} onSelect={(e) => e.preventDefault()}>
                <div className="flex items-center space-x-2 w-full" 
                     onClick={() => handleToggleStatus(status)}>
                  <Checkbox 
                    checked={activeStatuses.includes(status)} 
                    onCheckedChange={() => handleToggleStatus(status)} 
                    aria-label={`Filter by ${status} status`}
                  />
                  <span>{status}</span>
                </div>
              </DropdownMenuItem>
            ))
          )}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default StatusFilter;
