
import { Button } from "@/components/ui/button";
import { Filter, List } from "lucide-react";
import { RiskCreateDropdown } from "@/components/dashboard/shared/RiskCreateDropdown";

interface RiskRegisterHeaderProps {
  onImportSuccess: () => void;
  onToggleFilters: () => void;
  filtersVisible: boolean;
}

const RiskRegisterHeader = ({ 
  onImportSuccess, 
  onToggleFilters, 
  filtersVisible
}: RiskRegisterHeaderProps) => {
  const handleManageCategories = () => {
    window.location.href = '/risk-categories';
  };

  const handleRiskCreateSuccess = () => {
    // Refresh the risk data after successful creation
    onImportSuccess();
  };
  
  return (
    <div className="flex items-center justify-between">
      <div>
        <Button 
          variant="outline" 
          onClick={onToggleFilters} 
          className="flex items-center gap-2"
        >
          <Filter className="h-4 w-4" />
          {filtersVisible ? "Hide Filters" : "Show Filters"}
        </Button>
      </div>
      
      <div className="flex space-x-2">
        <Button variant="outline" onClick={handleManageCategories}>
          <List className="mr-2 h-4 w-4" />
          Manage Categories
        </Button>
        
        <RiskCreateDropdown onSuccess={handleRiskCreateSuccess} />
      </div>
    </div>
  );
};

export default RiskRegisterHeader;
