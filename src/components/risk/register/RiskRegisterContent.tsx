import { RiskTable } from "@/components/risk/RiskTable";
import RiskFilters from "./RiskFilters";
import { Risk, RiskSeverity, RiskStatus, User } from "@/types";
import { UncategorizedRisksHelper } from "./UncategorizedRisksHelper";

interface Category {
  id: string;
  name: string;
}

interface RiskRegisterContentProps {
  risks: Risk[];
  isLoading: boolean;
  categories?: Category[];
  owners?: User[];
  activeFilters: {
    severities: RiskSeverity[];
    statuses: RiskStatus[];
    categories: string[];
    ownerIds: string[];
  };
  sortOrder?: 'asc' | 'desc' | undefined;
  onFilterChange: (filterType: string, value: string | null) => void;
  onSortChange: (newSortOrder: 'asc' | 'desc' | null) => void;
  isLoadingFilters?: boolean;
}

const RiskRegisterContent = ({
  risks,
  isLoading,
  categories = [],
  owners = [],
  activeFilters,
  sortOrder,
  onFilterChange,
  onSortChange,
  isLoadingFilters = false
}: RiskRegisterContentProps) => {
  // Find uncategorized risks (those with null or undefined category)
  const uncategorizedRisks = risks.filter(risk => !risk.category || risk.category === "Uncategorized");

  const handleRisksUpdated = () => {
    // Trigger a refresh of the risks data
    window.location.reload();
  };

  return (
    <div className="space-y-6">
      <RiskFilters
        categories={categories}
        owners={owners}
        activeFilters={activeFilters}
        sortOrder={sortOrder}
        onFilterChange={onFilterChange}
        onSortChange={onSortChange}
        isLoading={isLoadingFilters}
      />

      {uncategorizedRisks.length > 0 && categories.length > 0 && (
        <UncategorizedRisksHelper
          uncategorizedRisks={uncategorizedRisks}
          categories={categories}
          onRisksUpdated={handleRisksUpdated}
        />
      )}

      <RiskTable
        risks={risks}
        isLoading={isLoading}
        categories={categories}
        owners={owners}
        activeFilters={{
          severities: activeFilters.severities,
          statuses: activeFilters.statuses,
          categories: activeFilters.categories,
          ownerIds: activeFilters.ownerIds
        }}
        onFilterChange={onFilterChange}
        onSortChange={(field, order) => {
          // Map the table sort to the register sort
          if (field === 'dueDate') {
            onSortChange(order);
          }
        }}
        isLoadingFilters={isLoadingFilters}
      />
    </div>
  );
};

export default RiskRegisterContent;
