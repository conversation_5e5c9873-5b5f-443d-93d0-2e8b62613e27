import { Risk, RiskStatus } from "@/types";
import { calculateSeverity } from "../utils/riskCalculations";
import { createCategoryIfNotExists } from "./categoryService";
export interface DefaultValues {
  uncategorizedId: string;
  adminUserId: string;
  adminUserName: string;
  organizationId: string;
  categories: Record<string, string>;
  owners: Record<string, string>;
}
interface CsvRow {
  Title?: string;
  Description?: string;
  Likelihood?: string | number;
  Impact?: string | number;
  Category?: string;
  Owner?: string;
  Status?: string;
  DueDate?: string;
  [key: string]: unknown; // Allow additional fields
}
/**
 * Map a CSV row to a risk object with validation
 */
export async function mapCsvRowToRisk(
  row: CsvRow,
  defaultValues: DefaultValues
): Promise<Partial<Risk>> {
  // Validate and apply defaults for required fields
  const title = row.Title ?? "Untitled";
  const description = row.Description ?? "A detailed description is required";
  // Convert likelihood and impact to numbers between 1-5
  let likelihood = 1;
  if (row.Likelihood !== undefined) {
    const parsedLikelihood = parseInt(String(row.Likelihood), 10);
    likelihood = !isNaN(parsedLikelihood) ? Math.min(5, Math.max(1, parsedLikelihood)) : 1;
  }
  let impact = 1;
  if (row.Impact !== undefined) {
    const parsedImpact = parseInt(String(row.Impact), 10);
    impact = !isNaN(parsedImpact) ? Math.min(5, Math.max(1, parsedImpact)) : 1;
  }
  // Calculate severity
  const severity = calculateSeverity(likelihood, impact);
  // Handle category - create if it doesn't exist
  let categoryId = defaultValues.uncategorizedId;
  if (row.Category && row.Category.trim() !== "") {
    const categoryName = row.Category.trim();
    const categoryNameLower = categoryName.toLowerCase();
    // Check if category exists in our mapping
    if (defaultValues.categories[categoryNameLower]) {
      categoryId = defaultValues.categories[categoryNameLower];
    } else {
      // Create new category
      try {
        categoryId = await createCategoryIfNotExists(categoryName, defaultValues.organizationId);
        // Update the categories mapping for future rows in this import
        defaultValues.categories[categoryNameLower] = categoryId;
      } catch (error) {
        // Fall back to uncategorized if category creation fails
        categoryId = defaultValues.uncategorizedId;
      }
    }
  }
  // Map owner
  let ownerId = defaultValues.adminUserId;
  if (row.Owner) {
    const ownerIdentifier = row.Owner.toLowerCase();
    if (defaultValues.owners[ownerIdentifier]) {
      ownerId = defaultValues.owners[ownerIdentifier];
    }
  }
  // Validate status
  let status = RiskStatus.IDENTIFIED;
  if (row.Status) {
    const inputStatus = row.Status.trim();
    const validStatuses = Object.values(RiskStatus);
    // Check if exact match
    if (validStatuses.includes(inputStatus as RiskStatus)) {
      status = inputStatus as RiskStatus;
    } else {
      // Try case-insensitive match
      const matchedStatus = validStatuses.find(s => s.toLowerCase() === inputStatus.toLowerCase());
      if (matchedStatus) {
        status = matchedStatus;
      }
    }
  }
  // Parse date
  let dueDate: Date | undefined = undefined;
  if (row.DueDate) {
    const date = new Date(row.DueDate);
    if (!isNaN(date.getTime())) {
      dueDate = date;
    }
  }
  return {
    title,
    description,
    categoryId,
    ownerId,
    likelihood,
    impact,
    severity,
    status,
    ...(dueDate && { dueDate }),
  };
}
