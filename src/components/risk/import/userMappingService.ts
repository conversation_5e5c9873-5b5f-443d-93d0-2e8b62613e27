
import { supabase } from "@/integrations/supabase/client";

export interface UserMappingResult {
  adminUserId: string;
  adminUserName: string;
  owners: Record<string, string>;
}

/**
 * Fetch user mappings for an organization
 */
export async function fetchUserMapping(organizationId: string, currentUserId: string): Promise<UserMappingResult> {
  // Get admin user for default owner
  const { data: adminData } = await supabase
    .from('profiles')
    .select('id, name, email')
    .eq('role', 'admin')
    .eq('organization_id', organizationId)
    .order('created_at', { ascending: true })
    .limit(1)
    .single();
    
  // Get all users who can be risk owners in this organization
  const { data: allUsers } = await supabase
    .from('profiles')
    .select('id, name, email')
    .in('role', ['admin', 'risk_owner'])
    .eq('organization_id', organizationId)
    .is('deleted_at', null);

  // Build user name/email to ID mapping
  const owners: Record<string, string> = {};
  if (allUsers) {
    allUsers.forEach(user => {
      owners[user.name.toLowerCase()] = user.id;
      owners[user.email.toLowerCase()] = user.id;
    });
  }

  return {
    adminUserId: adminData?.id ?? currentUserId, // Fallback to current user
    adminUserName: adminData?.name ?? 'System Administrator',
    owners
  };
}
