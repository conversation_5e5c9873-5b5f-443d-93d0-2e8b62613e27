import { supabase } from "@/integrations/supabase/client";
import { Risk, RiskStatus, RiskSeverity } from "@/types";
/**
 * Create a risk in the database
 */
export async function createRisk(
  risk: Partial<Risk>,
  defaultValues: {
    uncategorizedId: string;
    adminUserId: string;
    organizationId: string;
  }
): Promise<string> {
  // Ensure we have valid UUIDs or null values, never empty strings
  const categoryId =
    risk.categoryId && risk.categoryId.trim() !== ""
      ? risk.categoryId
      : defaultValues.uncategorizedId && defaultValues.uncategorizedId.trim() !== ""
        ? defaultValues.uncategorizedId
        : null;
  const ownerId =
    risk.ownerId && risk.ownerId.trim() !== ""
      ? risk.ownerId
      : defaultValues.adminUserId && defaultValues.adminUserId.trim() !== ""
        ? defaultValues.adminUserId
        : null;
  if (!ownerId) {
    throw new Error("No valid owner found for risk");
  }
  if (!defaultValues.organizationId || defaultValues.organizationId.trim() === "") {
    throw new Error("Organization ID is required");
  }
  // Make sure all required fields are present for the database
  const recordToInsert = {
    title: risk.title ?? "Untitled",
    description: risk.description ?? "A detailed description is required",
    likelihood: risk.likelihood ?? 1,
    impact: risk.impact ?? 1,
    severity: risk.severity || RiskSeverity.LOW,
    status: risk.status || RiskStatus.IDENTIFIED,
    inherent_likelihood: risk.likelihood ?? 1,
    inherent_impact: risk.impact ?? 1,
    inherent_severity: risk.severity || RiskSeverity.LOW,
    category_id: categoryId,
    owner_id: ownerId,
    created_by: ownerId,
    organization_id: defaultValues.organizationId,
    due_date: risk.dueDate ? risk.dueDate.toISOString() : null,
    mitigation_approach: null,
    current_controls: null,
    template_id: null,
  };
  // Insert the risk
  const { data, error } = await supabase.from("risks").insert(recordToInsert).select("id").single();
  if (error) {
    throw new Error(`Failed to create risk: ${error.message}`);
  }
  return data.id;
}
