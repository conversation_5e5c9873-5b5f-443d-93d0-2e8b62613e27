import { supabase } from "@/integrations/supabase/client";
export interface CategoryMappingResult {
  uncategorizedId: string;
  categories: Record<string, string>;
}
/**
 * Fetch all categories for an organization and build a mapping
 */
export async function fetchCategoryMapping(organizationId: string): Promise<CategoryMappingResult> {
  // Get uncategorized category ID for this organization
  const { data: categoryData } = await supabase
    .from("risk_categories")
    .select("id")
    .eq("name", "Uncategorized")
    .eq("organization_id", organizationId)
    .single();
  // Get all categories for mapping for this organization
  const { data: allCategories } = await supabase
    .from("risk_categories")
    .select("id, name")
    .eq("organization_id", organizationId)
    .order("name");
  // Build category name to ID mapping
  const categories: Record<string, string> = {};
  if (allCategories) {
    allCategories.forEach(category => {
      categories[category.name.toLowerCase()] = category.id;
    });
  }
  return {
    uncategorizedId: categoryData?.id ?? "",
    categories,
  };
}
/**
 * Create a new category if it doesn't exist
 */
export async function createCategoryIfNotExists(
  categoryName: string,
  organizationId: string
): Promise<string> {
  // First check if category already exists
  const { data: existingCategory } = await supabase
    .from("risk_categories")
    .select("id")
    .eq("name", categoryName)
    .eq("organization_id", organizationId)
    .single();
  if (existingCategory) {
    return existingCategory.id;
  }
  // Create new category
  const { data: newCategory, error } = await supabase
    .from("risk_categories")
    .insert({
      name: categoryName,
      description: `Auto-created during risk import: ${categoryName}`,
      organization_id: organizationId,
    })
    .select("id")
    .single();
  if (error) {
    throw new Error(`Failed to create category "${categoryName}": ${error.message}`);
  }
  return newCategory.id;
}
