
import { CheckCircle, AlertTriangle, XCircle } from "lucide-react";

interface ImportResultsProps {
  result: {
    success: number;
    errors: { row: number; message: string }[];
    total: number;
  };
}

export function ImportResults({ result }: ImportResultsProps) {
  const { success, errors, total } = result;
  
  const getStatusIcon = () => {
    if (errors.length === 0) {
      return <CheckCircle className="h-8 w-8 text-green-500" />;
    } else if (success > 0) {
      return <AlertTriangle className="h-8 w-8 text-yellow-500" />;
    } else {
      return <XCircle className="h-8 w-8 text-red-500" />;
    }
  };

  const getStatusText = () => {
    if (errors.length === 0) {
      return "All risks were imported successfully!";
    } else if (success > 0) {
      return `Imported ${success} of ${total} risks with some errors`;
    } else {
      return "Import failed";
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col items-center justify-center p-4 text-center">
        {getStatusIcon()}
        <h3 className="mt-2 text-lg font-semibold">{getStatusText()}</h3>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {success} risks successfully imported
        </p>
      </div>

      {errors.length > 0 && (
        <div className="rounded-md border border-yellow-200 bg-yellow-50 dark:bg-yellow-950/30 dark:border-yellow-900/50 p-4">
          <h4 className="font-medium text-yellow-800 dark:text-yellow-300 mb-2">
            Issues found ({errors.length})
          </h4>
          <ul className="max-h-48 overflow-y-auto space-y-1 text-sm">
            {errors.map((error, index) => (
              <li key={index} className="text-yellow-700 dark:text-yellow-400">
                {error.row > 0 ? `Row ${error.row}: ` : ""}{error.message}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
