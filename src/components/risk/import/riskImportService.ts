
import { parseCsvFile } from "./csvUtils";
import { supabase } from "@/integrations/supabase/client";
import { fetchCategoryMapping } from "./categoryService";
import { fetchUserMapping } from "./userMappingService";
import { mapCsvRowToRisk, DefaultValues } from "./csvRowMapper";
import { createRisk } from "./riskCreationService";
import { DataProcessingError, AuthenticationError, logger } from "@/utils/errors";

interface ImportResult {
  success: number;
  errors: { row: number; message: string }[];
  total: number;
}

/**
 * Main function to process the CSV import
 */
export async function processRiskCsvImport(file: File): Promise<ImportResult> {
  try {
    const logContext = {
      timestamp: new Date(),
      component: 'risk_import_service',
      action: 'csv_import_start',
      userId: 'unknown',
      organizationId: 'unknown',
      additionalData: { fileName: file.name, fileSize: file.size }
    };
    logger.info('Starting risk CSV import', logContext);

    // Parse the CSV file
    const csvData = await parseCsvFile(file);

    if (!Array.isArray(csvData) || csvData.length === 0) {
      throw new DataProcessingError(
        "No data found in file or invalid format",
        'csv_import',
        'file_parsing',
        0,
        {
          timestamp: new Date(),
          component: 'risk_import_service',
          action: 'csv_parsing',
          additionalData: { fileName: file.name }
        }
      );
    }

    // Fetch default values for fallbacks
    const defaultValues = await fetchDefaultValues();

    // Process each row
    const total = csvData.length;
    const errors: { row: number; message: string }[] = [];
    let successCount = 0;

    const processingContext = {
      timestamp: new Date(),
      component: 'risk_import_service',
      action: 'csv_processing',
      userId: 'unknown',
      organizationId: 'unknown',
      additionalData: { totalRows: total }
    };
    logger.info(`Processing ${total} rows from CSV`, processingContext);

    // Process risks in sequence to handle errors individually
    for (let i = 0; i < csvData.length; i++) {
      const rowNumber = i + 2; // +2 because row 1 is headers
      try {
        const row = csvData[i];
        if (!row) continue;
        const risk = await mapCsvRowToRisk(row, defaultValues);
        await createRisk(risk, defaultValues);
        successCount++;
      } catch (error) {
        const errorContext = {
          timestamp: new Date(),
          component: 'risk_import_service',
          action: 'row_processing',
          userId: 'unknown',
          organizationId: 'unknown',
          additionalData: { rowNumber, totalRows: total }
        };
        logger.error(`Error processing row ${rowNumber}`, error as Error, errorContext);

        errors.push({
          row: rowNumber,
          message: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    const completionContext = {
      timestamp: new Date(),
      component: 'risk_import_service',
      action: 'csv_import_complete',
      userId: 'unknown',
      organizationId: 'unknown',
      additionalData: {
        totalRows: total,
        successCount,
        errorCount: errors.length
      }
    };
    logger.info('Risk CSV import completed', completionContext);

    return {
      success: successCount,
      errors,
      total
    };
  } catch (error) {
    const errorContext = {
      timestamp: new Date(),
      component: 'risk_import_service',
      action: 'csv_import_error',
      userId: 'unknown',
      organizationId: 'unknown'
    };
    logger.error("Risk import failed", error as Error, errorContext);

    if (error instanceof DataProcessingError) {
      throw error;
    }

    throw new DataProcessingError(
      `Import failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'csv_import',
      'import_failure',
      0,
      {
        timestamp: new Date(),
        component: 'risk_import_service',
        action: 'csv_import_error'
      }
    );
  }
}

/**
 * Fetch default values for risk import
 */
async function fetchDefaultValues(): Promise<DefaultValues> {
  // Get current user and organization
  const { data: { user } } = await supabase.auth.getUser();
  if (!user) {
    throw new AuthenticationError(
      "User not authenticated",
      'session',
      {
        timestamp: new Date(),
        component: 'risk_import_service',
        action: 'fetch_default_values'
      }
    );
  }

  // Get user's organization
  const { data: userProfile } = await supabase
    .from('profiles')
    .select('organization_id')
    .eq('id', user.id)
    .single();

  if (!userProfile?.organization_id) {
    throw new DataProcessingError(
      "User organization not found",
      'csv_import',
      'organization_lookup',
      0,
      {
        timestamp: new Date(),
        component: 'risk_import_service',
        action: 'fetch_default_values',
        userId: user.id
      }
    );
  }

  const organizationId = userProfile.organization_id;

  const debugContext = {
    timestamp: new Date(),
    component: 'risk_import_service',
    action: 'fetch_default_values',
    userId: user.id,
    organizationId
  };
  logger.debug('Fetching default values for import', debugContext);

  // Fetch category and user mappings
  const [categoryMapping, userMapping] = await Promise.all([
    fetchCategoryMapping(organizationId),
    fetchUserMapping(organizationId, user.id)
  ]);

  return {
    uncategorizedId: categoryMapping.uncategorizedId,
    adminUserId: userMapping.adminUserId,
    adminUserName: userMapping.adminUserName,
    organizationId,
    categories: categoryMapping.categories,
    owners: userMapping.owners
  };
}
