import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FileUploader } from "./FileUploader";
import { ImportResults } from "./ImportResults";
import { downloadCsvTemplate } from "./csvUtils";
import { processRiskCsvImport } from "./riskImportService";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
interface RiskImportDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}
export function RiskImportDialog({ isOpen, onOpenChange, onSuccess }: RiskImportDialogProps) {
  const [file, setFile] = useState<File | null>(null);
  const [isImporting, setIsImporting] = useState(false);
  const [importResult, setImportResult] = useState<{
    success: number;
    errors: { row: number; message: string }[];
    total: number;
  } | null>(null);
  const handleFileSelected = (selectedFile: File | null) => {
    setFile(selectedFile);
    // Reset results when new file is selected
    setImportResult(null);
  };
  const handleDownloadTemplate = () => {
    downloadCsvTemplate();
  };
  const handleImport = async () => {
    if (!file) return;
    setIsImporting(true);
    try {
      const result = await processRiskCsvImport(file);
      setImportResult(result);
      if (result.success > 0) {
        onSuccess();
      }
    } catch (error) {
      setImportResult({
        success: 0,
        errors: [
          {
            row: 0,
            message: `Import failed: ${error instanceof Error ? error.message : "Unknown error"}`,
          },
        ],
        total: 0,
      });
    } finally {
      setIsImporting(false);
    }
  };
  const handleClose = () => {
    onOpenChange(false);
    // Reset state when dialog is closed
    setFile(null);
    setImportResult(null);
  };
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md md:max-w-lg">
        <DialogHeader>
          <DialogTitle>Import Risks</DialogTitle>
          <DialogDescription>
            Upload a CSV file to import risks. Download our template to ensure your data is
            formatted correctly.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {!importResult ? (
            <>
              <Button variant="outline" onClick={handleDownloadTemplate} className="w-full">
                Download CSV Template
              </Button>
              <FileUploader onFileSelected={handleFileSelected} />
            </>
          ) : (
            <ImportResults result={importResult} />
          )}
        </div>
        <DialogFooter className="flex flex-col sm:flex-row sm:justify-between gap-2">
          <Button variant="outline" onClick={handleClose}>
            {importResult ? "Close" : "Cancel"}
          </Button>
          {!importResult && (
            <Button onClick={handleImport} disabled={!file || isImporting}>
              {isImporting ? (
                <>
                  <LoadingSpinner className="mr-2" />
                  Importing...
                </>
              ) : (
                "Import Risks"
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
