import * as XLSX from "xlsx";
import { saveAs } from "file-saver";
import { RiskStatus } from "@/types";

// CSV column headers that match our risk model
export const CSV_HEADERS = [
  "Title",
  "Description",
  "Category",
  "Owner",
  "Likelihood",
  "Impact",
  "Status",
  "DueDate",
];

// Generic example risk data for the template (not sample data for actual use)
const TEMPLATE_EXAMPLES = [
  {
    Title: "Example Risk 1",
    Description: "Description of the first example risk",
    Category: "Operational",
    Owner: "Risk Owner Name",
    Likelihood: 3,
    Impact: 4,
    Status: RiskStatus.IDENTIFIED,
    DueDate: "2025-06-30",
  },
  {
    Title: "Example Risk 2",
    Description: "Description of the second example risk",
    Category: "Technology",
    Owner: "Another Owner",
    Likelihood: 2,
    Impact: 5,
    Status: RiskStatus.IN_PROGRESS,
    DueDate: "2025-07-15",
  },
];

/**
 * Creates and downloads a CSV template for risk import
 */

export const downloadCsvTemplate = () => {
  // Create worksheet with generic examples
  const ws = XLSX.utils.json_to_sheet(TEMPLATE_EXAMPLES);

  // Set column widths for better readability
  const colWidths = [
    { wch: 20 }, // Title
    { wch: 40 }, // Description
    { wch: 15 }, // Category
    { wch: 15 }, // Owner
    { wch: 10 }, // Likelihood
    { wch: 10 }, // Impact
    { wch: 15 }, // Status
    { wch: 12 }, // DueDate
  ];
  ws["!cols"] = colWidths;

  // Create workbook
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, "Risks");

  // Add an Instructions sheet
  const instructionsData = [
    { A: "Column", B: "Description", C: "Required", D: "Default Value" },
    { A: "Title", B: "Name of the risk", C: "Yes", D: "Untitled" },
    {
      A: "Description",
      B: "Detailed description of the risk",
      C: "Yes",
      D: "A detailed description is required",
    },
    { A: "Category", B: "Risk category from the predefined list", C: "No", D: "Uncategorized" },
    { A: "Owner", B: "Email or name of the risk owner", C: "No", D: "System Administrator" },
    { A: "Likelihood", B: "Value from 1-5 (1=low, 5=high)", C: "No", D: "1" },
    { A: "Impact", B: "Value from 1-5 (1=low, 5=high)", C: "No", D: "1" },
    { A: "Status", B: `One of: ${Object.values(RiskStatus).join(", ")}`, C: "No", D: "Identified" },
    { A: "DueDate", B: "Target date for risk mitigation (YYYY-MM-DD)", C: "No", D: "None" },
  ];

  const wsInstructions = XLSX.utils.json_to_sheet(instructionsData, {
    header: ["A", "B", "C", "D"],
  });
  const instructionsWidths = [
    { wch: 15 }, // Column
    { wch: 40 }, // Description
    { wch: 10 }, // Required
    { wch: 25 }, // Default Value
  ];
  wsInstructions["!cols"] = instructionsWidths;
  XLSX.utils.book_append_sheet(wb, wsInstructions, "Instructions");

  // Generate blob and save file
  const wbout = XLSX.write(wb, { bookType: "xlsx", type: "binary" }) as string;
  const buf = new ArrayBuffer(wbout.length);
  const view = new Uint8Array(buf);
  for (let i = 0; i < wbout.length; i++) {
    view[i] = wbout.charCodeAt(i) & 0xff;
  }

  const blob = new Blob([buf], { type: "application/octet-stream" });
  saveAs(blob, "risk_import_template.xlsx");
};

interface CsvRowData {
  Title?: string;
  Description?: string;
  Category?: string;
  Owner?: string;
  Likelihood?: string | number;
  Impact?: string | number;
  Status?: string;
  DueDate?: string;
  [key: string]: unknown; // Allow additional columns
}

/**
 * Parse CSV/Excel file to extract risk data
 */

export const parseCsvFile = async (file: File): Promise<CsvRowData[]> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e: ProgressEvent<FileReader>) => {
      try {
        const data = e.target?.result;
        if (!data) {
          reject(new Error("Failed to read file"));
          return;
        }

        const workbook = XLSX.read(data, { type: "binary" });
        const firstSheetName = workbook.SheetNames[0];
        if (!firstSheetName) {
          reject(new Error("No sheets found in file"));
          return;
        }
        const firstSheet = workbook.Sheets[firstSheetName];
        if (!firstSheet) {
          reject(new Error("Sheet not found"));
          return;
        }
        const jsonData = XLSX.utils.sheet_to_json(firstSheet) as CsvRowData[];

        resolve(jsonData);
      } catch {
        reject(new Error("Failed to parse file"));
      }
    };

    reader.onerror = () => {
      reject(new Error("Error reading file"));
    };

    reader.readAsBinaryString(file);
  });
};
