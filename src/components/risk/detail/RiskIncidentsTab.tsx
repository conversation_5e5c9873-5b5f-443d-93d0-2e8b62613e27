
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import IncidentsContent from "@/components/incident/IncidentsContent";
import { useRelatedIncidents } from "@/hooks/useRelatedIncidents";
import { useAuth } from "@/contexts/auth";
import { canCreateIncident } from "@/contexts/auth/permissionService";

interface RiskIncidentsTabProps {
  riskId: string;
}

const RiskIncidentsTab = ({ riskId }: RiskIncidentsTabProps) => {
  const { incidents, loading, error } = useRelatedIncidents(riskId);
  const navigate = useNavigate();
  const { user } = useAuth();
  
  // Check if the user can create incidents
  const userCanCreateIncident = canCreateIncident(user);
  
  const handleCreateIncident = () => {
    // Navigate to incident creation page with risk pre-selected
    navigate(`/incidents/new?riskId=${riskId}`);
  };
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">
          Related Incidents 
          {!loading && <span className="ml-2 text-muted-foreground">({incidents.length})</span>}
        </h3>
        {userCanCreateIncident && (
          <Button 
            onClick={handleCreateIncident} 
            className="whitespace-nowrap"
            size="sm"
          >
            <PlusCircle className="mr-2 h-4 w-4" />
            Report Incident
          </Button>
        )}
      </div>
      
      <div className="rounded-lg border bg-card text-card-foreground shadow-sm">
        <IncidentsContent 
          incidents={incidents} 
          loading={loading} 
          error={error} 
        />
      </div>
    </div>
  );
};

export default RiskIncidentsTab;
