import { useState } from "react";
import { Risk } from "@/types";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Edit,
  ChevronRight,
  MoreHorizontal,
  Copy,
  FileDown,
  AlertTriangle,
  Trash2,
} from "lucide-react";
import { useRiskNavigation } from "@/hooks/useRiskNavigation";
import RiskNavigation from "./RiskNavigation";
import { useNavigate } from "react-router-dom";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/auth";
import { getSeverityColor } from "@/services/risk/riskCalculationService";
interface RiskDetailsHeaderProps {
  risk: Risk;
  canEdit?: boolean;
  onEdit?: () => void;
  onDelete?: () => void;
}
const RiskDetailsHeader = ({ risk, canEdit = true, onEdit, onDelete }: RiskDetailsHeaderProps) => {
  const [isExporting, setIsExporting] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const { prevRiskId, nextRiskId, loading } = useRiskNavigation(risk.id);
  const handleEditClick = () => {
    if (onEdit) {
      onEdit();
    }
  };
  const handleDuplicate = () => {
    navigate("/risks/create", {
      state: {
        duplicateFrom: {
          ...risk,
          title: `${risk.title} (Copy)`,
          id: undefined,
        },
      },
    });
  };
  const handleExportPDF = async () => {
    setIsExporting(true);
    try {
      const { jsPDF } = await import("jspdf");
      const doc = new jsPDF();
      doc.setFontSize(20);
      doc.text("Risk Assessment Report", 20, 30);
      doc.setFontSize(12);
      let yPosition = 50;
      doc.text(`Title: ${risk.title}`, 20, yPosition);
      yPosition += 10;
      doc.text(`Description: ${risk.description}`, 20, yPosition);
      yPosition += 10;
      doc.text(`Category: ${risk.category}`, 20, yPosition);
      yPosition += 10;
      doc.text(`Owner: ${risk.ownerName}`, 20, yPosition);
      yPosition += 10;
      doc.text(`Severity: ${risk.severity}`, 20, yPosition);
      yPosition += 10;
      doc.text(`Status: ${risk.status}`, 20, yPosition);
      yPosition += 10;
      doc.text(`Likelihood: ${risk.likelihood}/5`, 20, yPosition);
      yPosition += 10;
      doc.text(`Impact: ${risk.impact}/5`, 20, yPosition);
      yPosition += 10;
      if (risk.currentControls) {
        doc.text(`Current Controls: ${risk.currentControls}`, 20, yPosition);
        yPosition += 10;
      }
      if (risk.mitigationApproach) {
        doc.text(`Mitigation Approach: ${risk.mitigationApproach}`, 20, yPosition);
        yPosition += 10;
      }
      if (risk.dueDate) {
        doc.text(`Due Date: ${risk.dueDate.toLocaleDateString()}`, 20, yPosition);
        yPosition += 10;
      }
      doc.save(`risk-${risk.id}-assessment.pdf`);
      toast({
        title: "Export Successful",
        description: "Risk assessment has been exported to PDF.",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "There was an error exporting the risk assessment.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };
  const handleReportIncident = () => {
    navigate(`/incidents/new?riskId=${risk.id}`);
  };
  const handleDeleteClick = () => {
    if (onDelete) {
      onDelete();
    }
  };
  const canEditRisk = user?.id === risk.ownerId || user?.role === "admin";
  const canDeleteRisk = user?.role === "admin";
  return (
    <div className="space-y-4">
      {/* Compact breadcrumb and actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>Risk Register</span>
          <ChevronRight className="h-4 w-4" />
          <span className="text-foreground font-medium">{risk.title}</span>
        </div>
        <div className="flex items-center gap-2">
          <RiskNavigation
            prevRiskId={prevRiskId}
            nextRiskId={nextRiskId}
            loading={loading}
            preserveFilters={true}
          />
          {/* Primary Edit Button */}
          {canEdit && canEditRisk && onEdit && (
            <Button size="sm" onClick={handleEditClick}>
              <Edit className="h-4 w-4 mr-1" />
              <span className="hidden sm:inline">Edit</span>
            </Button>
          )}
          {/* More Actions Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
                <span className="sr-only">More actions</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={handleDuplicate}>
                <Copy className="mr-2 h-4 w-4" />
                Duplicate Risk
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleExportPDF} disabled={isExporting}>
                <FileDown className="mr-2 h-4 w-4" />
                {isExporting ? "Exporting..." : "Export PDF"}
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleReportIncident}>
                <AlertTriangle className="mr-2 h-4 w-4" />
                Report Incident
              </DropdownMenuItem>
              {canDeleteRisk && onDelete && (
                <DropdownMenuItem onClick={handleDeleteClick} className="text-destructive">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Risk
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      {/* Prominent title with key status */}
      <div className="flex items-start justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">{risk.title}</h1>
          <p className="text-muted-foreground mt-1">
            {risk.description.length > 100
              ? `${risk.description.substring(0, 100)}...`
              : risk.description}
          </p>
        </div>
        <div className="flex items-center gap-3 ml-4">
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            {risk.status}
          </Badge>
          <Badge className={getSeverityColor(risk.severity)}>{risk.severity}</Badge>
        </div>
      </div>
    </div>
  );
};
export default RiskDetailsHeader;
