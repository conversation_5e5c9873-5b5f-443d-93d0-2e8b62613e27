
import { Card, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { CheckedState } from "@radix-ui/react-checkbox";
import { ControlMeasure, MitigationAction } from "@/types";

interface RiskDescriptionProps {
  description: string;
  currentControls: string;
  controlMeasures?: ControlMeasure[];
  mitigationApproach: string;
  mitigationActions?: MitigationAction[];
  onMitigationActionToggle?: (actionId: string, completed: boolean) => void;
  onControlToggle?: (controlId: string, implemented: boolean) => void;
  isEditable?: boolean;
  showDescription?: boolean;
  showMitigationInfo?: boolean;
  showControlInfo?: boolean;
  descriptionOnly?: boolean;
}

const RiskDescription = ({
  description,
  currentControls,
  controlMeasures = [],
  mitigationApproach,
  mitigationActions = [],
  onMitigationActionToggle,
  onControlToggle,
  isEditable = true,
  showDescription = true,
  showMitigationInfo = true,
  showControlInfo = true,
  descriptionOnly = false
}: RiskDescriptionProps) => {
  const handleActionToggle = (actionId: string) => (checked: CheckedState) => {
    if (onMitigationActionToggle && typeof checked === 'boolean') {
      onMitigationActionToggle(actionId, checked);
    }
  };

  const handleControlToggle = (controlId: string) => (checked: CheckedState) => {
    if (onControlToggle && typeof checked === 'boolean') {
      onControlToggle(controlId, checked);
    }
  };

  return (
    <div className="space-y-6">
      {showDescription && (
        <Card>
          <CardHeader>
            <CardTitle>Risk Description</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground whitespace-pre-wrap">
              {description}
            </p>
          </CardContent>
        </Card>
      )}

      {!descriptionOnly && showControlInfo && (
        <Card>
          <CardHeader>
            <CardTitle>Current Controls</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {currentControls && (
              <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                {currentControls}
              </p>
            )}
            
            {controlMeasures.length > 0 && (
              <div className="space-y-3">
                <h4 className="text-sm font-medium">Control Measures</h4>
                {controlMeasures.map((control) => (
                  <div key={control.id} className="flex items-start gap-3 p-3 border rounded-md">
                    <Checkbox
                      id={`control-${control.id}`}
                      checked={control.implemented}
                      onCheckedChange={isEditable ? handleControlToggle(control.id) : () => {}}
                      disabled={!isEditable}
                    />
                    <div className="flex-1 space-y-1">
                      <label 
                        htmlFor={`control-${control.id}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {control.description}
                      </label>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant={control.implemented ? "default" : "secondary"}>
                          {control.implemented ? "Implemented" : "Not Implemented"}
                        </Badge>
                        {control.effectiveness && (
                          <Badge variant="outline">
                            {control.effectiveness} Effectiveness
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {!descriptionOnly && showMitigationInfo && (
        <Card>
          <CardHeader>
            <CardTitle>Mitigation Approach</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {mitigationApproach && (
              <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                {mitigationApproach}
              </p>
            )}
            
            {mitigationActions.length > 0 && (
              <div className="space-y-3">
                <h4 className="text-sm font-medium">Mitigation Actions</h4>
                {mitigationActions.map((action) => (
                  <div key={action.id} className="flex items-start gap-3 p-3 border rounded-md">
                    <Checkbox
                      id={`action-${action.id}`}
                      checked={action.completed}
                      onCheckedChange={isEditable ? handleActionToggle(action.id) : () => {}}
                      disabled={!isEditable}
                    />
                    <div className="flex-1 space-y-1">
                      <label 
                        htmlFor={`action-${action.id}`}
                        className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                      >
                        {action.description}
                      </label>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant={action.completed ? "default" : "secondary"}>
                          {action.completed ? "Completed" : "Pending"}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default RiskDescription;
