
import { useState } from "react";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Risk } from "@/types";
import RiskDescription from "@/components/risk/detail/RiskDescription";
import RiskAssessmentCard from "@/components/risk/detail/RiskAssessmentCard";
import RiskComparisonCard from "@/components/risk/detail/RiskComparisonCard";
import RiskTrendChart from "@/components/risk/detail/RiskTrendChart";
import RiskIncidentsTab from "@/components/risk/detail/RiskIncidentsTab";
import CommentsSection from "@/components/comments/CommentsSection";
import { RiskHistoryEntry } from "@/types";

interface RiskDetailsContentProps {
  risk: Risk;
  history: RiskHistoryEntry[];
  historyLoading: boolean;
  isEditable: boolean;
  onMitigationActionToggle: (actionId: string, completed: boolean) => void;
  onControlToggle: (controlId: string, implemented: boolean) => void;
}

const RiskDetailsContent = ({
  risk,
  history,
  historyLoading,
  isEditable,
  onMitigationActionToggle,
  onControlToggle
}: RiskDetailsContentProps) => {
  const [activeTab, setActiveTab] = useState("details");

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
      <TabsList>
        <TabsTrigger value="details">Details</TabsTrigger>
        <TabsTrigger value="assessment">Risk Assessment</TabsTrigger>
        <TabsTrigger value="trends">Risk Trends</TabsTrigger>
        <TabsTrigger value="incidents">Incidents</TabsTrigger>
        <TabsTrigger value="comments">Comments</TabsTrigger>
      </TabsList>
      
      <TabsContent value="details" className="space-y-6">
        {/* Risk Description above the cards */}
        <RiskDescription 
          description={risk.description} 
          currentControls={risk.currentControls || ""}
          controlMeasures={risk.controlMeasures || []}
          mitigationApproach={risk.mitigationApproach || ""} 
          mitigationActions={risk.mitigationActions || []}
          onMitigationActionToggle={onMitigationActionToggle}
          onControlToggle={onControlToggle}
          isEditable={isEditable}
          showMitigationInfo={false}
          showControlInfo={false}
          descriptionOnly={true}
        />

        <RiskAssessmentCard 
          severity={risk.severity} 
          likelihood={risk.likelihood} 
          impact={risk.impact}
          inherentSeverity={risk.inherentSeverity}
          inherentLikelihood={risk.inherentLikelihood}
          inherentImpact={risk.inherentImpact}
          category={risk.category || 'Uncategorized'}
          ownerName={risk.ownerName || 'Unassigned'}
          status={risk.status}
          {...(risk.dueDate && { dueDate: risk.dueDate })}
          createdAt={risk.createdAt}
          updatedAt={risk.updatedAt}
        />
        
        <RiskDescription 
          description={risk.description} 
          currentControls={risk.currentControls || ""} 
          controlMeasures={risk.controlMeasures || []}
          mitigationApproach={risk.mitigationApproach || ""} 
          mitigationActions={risk.mitigationActions || []}
          onMitigationActionToggle={onMitigationActionToggle}
          onControlToggle={onControlToggle}
          isEditable={isEditable}
          showDescription={false}
        />
      </TabsContent>

      <TabsContent value="assessment" className="space-y-6">
        <RiskComparisonCard risk={risk} />
      </TabsContent>
      
      <TabsContent value="trends" className="space-y-6">
        <RiskTrendChart history={history} loading={historyLoading} />
      </TabsContent>

      <TabsContent value="incidents" className="space-y-6">
        <RiskIncidentsTab riskId={risk.id} />
      </TabsContent>

      <TabsContent value="comments" className="space-y-6">
        <CommentsSection entityType="risk" entityId={risk.id} />
      </TabsContent>
    </Tabs>
  );
};

export default RiskDetailsContent;
