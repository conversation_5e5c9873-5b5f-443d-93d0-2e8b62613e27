
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Risk } from "@/types";

interface RiskComparisonCardProps {
  risk: Risk;
}

const RiskComparisonCard = ({ risk }: RiskComparisonCardProps) => {
  const riskReduction = (risk.inherentLikelihood * risk.inherentImpact) - 
                       (risk.likelihood * risk.impact);
  
  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "Critical": return "bg-red-600 text-white";
      case "High": return "bg-orange-500 text-white";
      case "Medium": return "bg-yellow-500 text-white";
      case "Low": return "bg-green-500 text-white";
      default: return "bg-gray-500 text-white";
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Risk Assessment Comparison</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Inherent Risk Column */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-red-600">Inherent Risk</h3>
            <div className="space-y-2">
              <div className="text-sm">
                <span className="font-medium">Likelihood:</span> {risk.inherentLikelihood}/5
              </div>
              <div className="text-sm">
                <span className="font-medium">Impact:</span> {risk.inherentImpact}/5
              </div>
              <Badge className={`${getSeverityColor(risk.inherentSeverity)} hover:${getSeverityColor(risk.inherentSeverity)}`}>
                {risk.inherentSeverity}
              </Badge>
            </div>
          </div>

          {/* Residual Risk Column */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-blue-600">Residual Risk</h3>
            <div className="space-y-2">
              <div className="text-sm">
                <span className="font-medium">Likelihood:</span> {risk.likelihood}/5
              </div>
              <div className="text-sm">
                <span className="font-medium">Impact:</span> {risk.impact}/5
              </div>
              <Badge className={`${getSeverityColor(risk.severity)} hover:${getSeverityColor(risk.severity)}`}>
                {risk.severity}
              </Badge>
            </div>
          </div>
        </div>

        {/* Control Effectiveness Indicator */}
        <div className="mt-6 p-4 bg-green-50 rounded-md border border-green-200">
          <div className="flex justify-between items-center">
            <span className="font-medium">Risk Reduction Score:</span>
            <span className="text-lg font-bold text-green-600">
              {riskReduction > 0 ? riskReduction : 0} points
            </span>
          </div>
          <div className="text-sm text-muted-foreground mt-1">
            {riskReduction > 0 
              ? "Lower residual risk indicates effective controls" 
              : "Controls may need improvement to reduce risk"}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RiskComparisonCard;
