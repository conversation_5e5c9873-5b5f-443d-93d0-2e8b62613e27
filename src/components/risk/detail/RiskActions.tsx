import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Copy,
  FileDown,
  AlertTriangle,
} from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useAuth } from "@/contexts/auth";
import { Risk } from "@/types";
import { Badge } from "@/components/ui/badge";
import { getSeverityColor } from "@/utils/uiUtils";
interface RiskActionsProps {
  risk: Risk;
  onEdit: () => void;
  onDelete: () => void;
}
const RiskActions = ({ risk, onEdit, onDelete }: RiskActionsProps) => {
  const [isExporting, setIsExporting] = useState(false);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const handleDuplicate = () => {
    // Navigate to create new risk with pre-filled data from this risk
    navigate("/risks/create", {
      state: {
        duplicateFrom: {
          ...risk,
          title: `${risk.title} (Copy)`,
          id: undefined, // Remove ID so it creates a new risk
        },
      },
    });
  };
  const handleExportPDF = async () => {
    setIsExporting(true);
    try {
      // Import jsPDF dynamically to reduce bundle size
      const { jsPDF } = await import("jspdf");
      const doc = new jsPDF();
      // Add title
      doc.setFontSize(20);
      doc.text("Risk Assessment Report", 20, 30);
      // Add risk details
      doc.setFontSize(12);
      let yPosition = 50;
      doc.text(`Title: ${risk.title}`, 20, yPosition);
      yPosition += 10;
      doc.text(`Description: ${risk.description}`, 20, yPosition);
      yPosition += 10;
      doc.text(`Category: ${risk.category}`, 20, yPosition);
      yPosition += 10;
      doc.text(`Owner: ${risk.ownerName}`, 20, yPosition);
      yPosition += 10;
      doc.text(`Severity: ${risk.severity}`, 20, yPosition);
      yPosition += 10;
      doc.text(`Status: ${risk.status}`, 20, yPosition);
      yPosition += 10;
      doc.text(`Likelihood: ${risk.likelihood}/5`, 20, yPosition);
      yPosition += 10;
      doc.text(`Impact: ${risk.impact}/5`, 20, yPosition);
      yPosition += 10;
      if (risk.currentControls) {
        doc.text(`Current Controls: ${risk.currentControls}`, 20, yPosition);
        yPosition += 10;
      }
      if (risk.mitigationApproach) {
        doc.text(`Mitigation Approach: ${risk.mitigationApproach}`, 20, yPosition);
        yPosition += 10;
      }
      if (risk.dueDate) {
        doc.text(`Due Date: ${risk.dueDate.toLocaleDateString()}`, 20, yPosition);
        yPosition += 10;
      }
      // Save the PDF
      doc.save(`risk-${risk.id}-assessment.pdf`);
      toast({
        title: "Export Successful",
        description: "Risk assessment has been exported to PDF.",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "There was an error exporting the risk assessment.",
        variant: "destructive",
      });
    } finally {
      setIsExporting(false);
    }
  };
  const handleReportIncident = () => {
    navigate("/incidents/create", {
      state: {
        relatedRiskId: risk.id,
        relatedRiskTitle: risk.title,
      },
    });
  };
  const canEdit = user?.id === risk.ownerId || user?.role === "admin";
  const canDelete = user?.role === "admin";
  return (
    <div className="flex items-center gap-2">
      <Badge variant="outline" className={`${getSeverityColor(risk.severity)} text-white`}>
        {risk.severity}
      </Badge>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {canEdit && (
            <DropdownMenuItem onClick={onEdit}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Risk
            </DropdownMenuItem>
          )}
          <DropdownMenuItem onClick={handleDuplicate}>
            <Copy className="mr-2 h-4 w-4" />
            Duplicate Risk
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleExportPDF} disabled={isExporting}>
            <FileDown className="mr-2 h-4 w-4" />
            {isExporting ? "Exporting..." : "Export PDF"}
          </DropdownMenuItem>
          <DropdownMenuItem onClick={handleReportIncident}>
            <AlertTriangle className="mr-2 h-4 w-4" />
            Report Incident
          </DropdownMenuItem>
          {canDelete && (
            <DropdownMenuItem onClick={onDelete} className="text-destructive">
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Risk
            </DropdownMenuItem>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
export default RiskActions;
