import { useMemo } from "react";
import { format } from "date-fns";
import { useTheme } from "next-themes";
import { RiskSeverity, RiskHistoryEntry } from "@/types";
import { getSeverityColorClass } from "@/components/risk/utils/riskCalculations";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
interface RiskTrendChartProps {
  history: RiskHistoryEntry[];
  loading: boolean;
}
const RiskTrendChart = ({ history, loading }: RiskTrendChartProps) => {
  const chartData = useMemo(() => {
    if (!history || history.length === 0) {
      return [];
    }
    const processedData = history
      .map(entry => {
        const processedEntry = {
          date: format(new Date(entry.recorded_at), "MMM d, yyyy"),
          recorded_at: entry.recorded_at,
          likelihood: entry.likelihood,
          impact: entry.impact,
          severity: entry.severity,
          riskScore: entry.likelihood * entry.impact,
        };
        return processedEntry;
      })
      .sort((a, b) => new Date(a.recorded_at).getTime() - new Date(b.recorded_at).getTime());
    return processedData;
  }, [history]);
  const getLatestSeverity = (): RiskSeverity => {
    if (!history || history.length === 0) return RiskSeverity.LOW;
    return (history[0]?.severity as RiskSeverity) || RiskSeverity.LOW;
  };
  if (loading) {
    return (
      <Card className="w-full">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold">Risk Assessment Trend</CardTitle>
        </CardHeader>
        <CardContent className="h-64 flex items-center justify-center">
          <div className="text-sm text-muted-foreground">Loading trend data...</div>
        </CardContent>
      </Card>
    );
  }
  if (!history || history.length === 0) {
    return (
      <Card className="w-full">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold">Risk Assessment Trend</CardTitle>
        </CardHeader>
        <CardContent className="h-64 flex items-center justify-center">
          <div className="text-sm text-muted-foreground">
            No historical trend data available. Risk changes will appear here after the risk is
            updated.
          </div>
        </CardContent>
      </Card>
    );
  }
  if (chartData.length === 0) {
    return (
      <Card className="w-full">
        <CardHeader className="pb-3">
          <CardTitle className="text-lg font-semibold">Risk Assessment Trend</CardTitle>
        </CardHeader>
        <CardContent className="h-64 flex items-center justify-center">
          <div className="text-sm text-muted-foreground">
            Unable to process historical data for charting.
          </div>
        </CardContent>
      </Card>
    );
  }
  return (
    <Card className="w-full">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold">Risk Assessment Trend</CardTitle>
      </CardHeader>
      <CardContent className="pb-4">
        <div className="mb-4">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">Current Severity:</span>
            <span className={`font-semibold text-sm ${getSeverityColorClass(getLatestSeverity())}`}>
              {getLatestSeverity()}
            </span>
          </div>
        </div>
        <div className="w-full h-80 overflow-hidden">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
              <XAxis
                dataKey="date"
                tick={{ fontSize: 12, fill: "hsl(var(--muted-foreground))" }}
                axisLine={{ stroke: "hsl(var(--border))" }}
              />
              <YAxis
                domain={[0, 25]}
                tick={{ fontSize: 12, fill: "hsl(var(--muted-foreground))" }}
                axisLine={{ stroke: "hsl(var(--border))" }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line
                type="monotone"
                dataKey="likelihood"
                stroke="hsl(var(--primary))"
                strokeWidth={2}
                dot={{ r: 4, fill: "hsl(var(--primary))" }}
                activeDot={{ r: 6, fill: "hsl(var(--primary))" }}
              />
              <Line
                type="monotone"
                dataKey="impact"
                stroke="hsl(var(--destructive))"
                strokeWidth={2}
                dot={{ r: 4, fill: "hsl(var(--destructive))" }}
                activeDot={{ r: 6, fill: "hsl(var(--destructive))" }}
              />
              <Line
                type="monotone"
                dataKey="riskScore"
                stroke="hsl(var(--secondary))"
                strokeWidth={3}
                dot={{ r: 5, fill: "hsl(var(--secondary))" }}
                activeDot={{ r: 7, fill: "hsl(var(--secondary))" }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};
const CustomTooltip = ({ active, payload, label }: any) => {
  const { resolvedTheme } = useTheme();
  if (active && payload && Array.isArray(payload) && payload.length > 0) {
    const severityValue = (payload[0]?.payload?.severity as RiskSeverity) || RiskSeverity.LOW;
    const tooltipStyle: React.CSSProperties = {
      backgroundColor: resolvedTheme === "dark" ? "#1f2937" : "#ffffff",
      color: resolvedTheme === "dark" ? "#ffffff" : "#000000",
      borderColor: resolvedTheme === "dark" ? "#4b5563" : "#d1d5db",
      borderWidth: "1px",
      borderStyle: "solid",
      padding: "0.75rem",
      borderRadius: "0.375rem",
      boxShadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
    };
    return (
      <div style={tooltipStyle} className="chart-tooltip" data-theme={resolvedTheme}>
        <p className="font-semibold text-sm mb-2">{String(label)}</p>
        <p className="text-sm">
          Likelihood: <span className="font-medium">{payload[0]?.value}</span>
        </p>
        <p className="text-sm">
          Impact: <span className="font-medium">{payload[1]?.value}</span>
        </p>
        <p className="text-sm">
          Risk Score: <span className="font-medium">{payload[2]?.value}</span>
        </p>
        <p className="text-sm">
          Severity:
          <span className={`ml-1 font-semibold ${getSeverityColorClass(severityValue)}`}>
            {severityValue}
          </span>
        </p>
      </div>
    );
  }
  return null;
};
export default RiskTrendChart;
