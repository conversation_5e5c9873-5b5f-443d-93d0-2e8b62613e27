
import { Lock } from "lucide-react";

interface RiskDetailsBannerProps {
  isRiskClosed: boolean;
}

const RiskDetailsBanner = ({ isRiskClosed }: RiskDetailsBannerProps) => {
  if (!isRiskClosed) return null;

  return (
    <div className="bg-gray-100 border border-gray-300 p-4 rounded-lg flex items-center gap-2">
      <Lock className="h-5 w-5 text-gray-600" />
      <div>
        <p className="font-medium text-gray-700">
          This risk is currently closed
        </p>
        <p className="text-sm text-gray-500">
          Use the "Reopen" button to make changes
        </p>
      </div>
    </div>
  );
};

export default RiskDetailsBanner;
