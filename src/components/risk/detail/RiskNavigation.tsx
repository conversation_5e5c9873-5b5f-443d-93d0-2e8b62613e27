import { useNavigate, useSearchParams } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
interface RiskNavigationProps {
  prevRiskId: string | null;
  nextRiskId: string | null;
  loading: boolean;
  preserveFilters?: boolean;
}
const RiskNavigation = ({
  prevRiskId,
  nextRiskId,
  loading,
  preserveFilters = true,
}: RiskNavigationProps) => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const handleNavigate = (riskId: string | null) => {
    if (!riskId) {
      return;
    }
    if (preserveFilters) {
      // Build a query string from the current search params
      const currentParams = Object.fromEntries(searchParams.entries());
      const queryString = new URLSearchParams(currentParams).toString();
      const targetUrl = `/risks/${riskId}${queryString ? `?${queryString}` : ""}`;
      navigate(targetUrl);
    } else {
      navigate(`/risks/${riskId}`);
    }
  };
  return (
    <div className="flex items-center gap-2">
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleNavigate(prevRiskId)}
              disabled={loading || !prevRiskId}
            >
              <ChevronLeft className="mr-1 h-4 w-4" />
              Previous
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{prevRiskId ? "Go to previous risk" : "No previous risk available"}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleNavigate(nextRiskId)}
              disabled={loading || !nextRiskId}
            >
              Next
              <ChevronRight className="ml-1 h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>{nextRiskId ? "Go to next risk" : "No next risk available"}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </div>
  );
};
export default RiskNavigation;
