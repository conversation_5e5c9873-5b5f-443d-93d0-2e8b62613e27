
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Calendar, Lock, CheckCircle } from "lucide-react";
import { format } from "date-fns";
import { RiskStatus } from "@/types";

interface RiskStatusCardProps {
  status: RiskStatus;
  category: string;
  ownerName: string;
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const RiskStatusCard = ({ status, category, ownerName, dueDate, createdAt, updatedAt }: RiskStatusCardProps) => {
  const getStatusColor = (status: RiskStatus) => {
    switch (status) {
      case RiskStatus.IDENTIFIED: return "bg-blue-100 text-blue-800";
      case RiskStatus.IN_PROGRESS: return "bg-yellow-100 text-yellow-800";
      case RiskStatus.MITIGATED: return "bg-green-100 text-green-800";
      case RiskStatus.ACCEPTED: return "bg-purple-100 text-purple-800";
      case RiskStatus.CLOSED: return "bg-gray-600 text-white";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const isClosedOrMitigated = status === RiskStatus.CLOSED || status === RiskStatus.MITIGATED;

  return (
    <Card className={isClosedOrMitigated ? "border-gray-400 bg-gray-50" : ""}>
      <CardHeader>
        <CardTitle>Status & Ownership</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex justify-between items-center">
          <span className="text-base font-medium">Status</span>
          <div className={`px-3 py-1 rounded-full text-sm font-medium flex items-center gap-1 ${getStatusColor(status)}`}>
            {status === RiskStatus.CLOSED && <Lock className="h-3 w-3" />}
            {status === RiskStatus.MITIGATED && <CheckCircle className="h-3 w-3" />}
            {status}
          </div>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-base font-medium">Category</span>
          <span className="text-right">{category}</span>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-base font-medium">Owner</span>
          <span className="text-right">{ownerName}</span>
        </div>
        
        {dueDate && (
          <div className="flex justify-between items-center">
            <span className="text-base font-medium">Due Date</span>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2" />
              <span>{format(dueDate, 'MMMM d, yyyy')}</span>
            </div>
          </div>
        )}
        
        <div className="flex justify-between items-center">
          <span className="text-base font-medium">Date Entered</span>
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-2" />
            <span>{format(createdAt, 'MMM d, yyyy')}</span>
          </div>
        </div>
        
        <div className="flex justify-between items-center">
          <span className="text-base font-medium">Last Updated</span>
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-2" />
            <span>{format(updatedAt, 'MMM d, yyyy, h:mm a')}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RiskStatusCard;
