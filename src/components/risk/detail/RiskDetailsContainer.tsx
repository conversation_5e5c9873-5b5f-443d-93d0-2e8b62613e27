import { useState } from "react";
import { useRiskDetails } from "@/hooks/risk/useRiskDetails";
import { useRiskHistory } from "@/hooks/useRiskHistory";
import { useMitigationActions } from "@/hooks/useMitigationActions";
import { useControlMeasures } from "@/hooks/useControlMeasures";
import { RiskStatus } from "@/types";
import { RiskEditSheet } from "@/components/risk/RiskEditSheet";
import { RiskErrorBoundary } from "@/components/error-boundaries";
import RiskDetailsHeader from "@/components/risk/detail/RiskDetailsHeader";
import RiskDetailsBanner from "@/components/risk/detail/RiskDetailsBanner";
import RiskDetailsContent from "@/components/risk/detail/RiskDetailsContent";
import RiskDetailsError from "@/components/risk/detail/RiskDetailsError";
import RiskDetailsLoading from "@/components/risk/detail/RiskDetailsLoading";
import { log } from "@/services/loggingService";

const RiskDetailsContainer = () => {
  const { risk, loading, error, fetchRiskDetails } = useRiskDetails();
  const { history, loading: historyLoading } = useRiskHistory(risk?.id);
  const [editSheetOpen, setEditSheetOpen] = useState(false);

  // Use the risk actions and controls hooks
  const { toggleActionCompletion } = useMitigationActions(risk?.id ?? "", fetchRiskDetails);

  const { toggleControlImplementation } = useControlMeasures(risk?.id ?? "", fetchRiskDetails);

  const handleRiskUpdated = async () => {
    log.info(
      "Risk updated, refreshing data",
      {},
      {
        component: "RiskDetailsContainer",
        action: "handleRiskUpdated",
      }
    );
    setEditSheetOpen(false);
    // Add a small delay to ensure the database transaction is complete
    await new Promise(resolve => setTimeout(resolve, 100));
    await fetchRiskDetails();
  };

  const handleEditClick = () => {
    log.info(
      "Opening edit sheet for risk",
      {},
      {
        component: "RiskDetailsContainer",
        action: "handleEditClick",
      }
    );
    setEditSheetOpen(true);
  };

  const handleMitigationActionToggle = (actionId: string, completed: boolean) => {
    toggleActionCompletion(actionId, completed);
  };

  const handleControlToggle = (controlId: string, implemented: boolean) => {
    toggleControlImplementation(controlId, implemented);
  };

  const handleDelete = () => {
    // TODO: Implement delete functionality
    log.info(
      "Delete risk requested",
      {},
      {
        component: "RiskDetailsContainer",
        action: "handleDelete",
      }
    );
  };

  if (loading) {
    return <RiskDetailsLoading />;
  }

  if (error) {
    return <RiskDetailsError error={error} />;
  }

  if (!risk) {
    return <RiskDetailsError error="Risk not found" />;
  }

  const isRiskClosed = risk.status === RiskStatus.CLOSED;

  return (
    <RiskErrorBoundary>
      <div className="space-y-6">
        <RiskDetailsBanner isRiskClosed={isRiskClosed} />

        <RiskDetailsHeader
          risk={risk}
          canEdit={!isRiskClosed}
          onEdit={handleEditClick}
          onDelete={handleDelete}
        />

        <RiskDetailsContent
          risk={risk}
          history={history}
          historyLoading={historyLoading}
          isEditable={!isRiskClosed}
          onMitigationActionToggle={handleMitigationActionToggle}
          onControlToggle={handleControlToggle}
        />

        <RiskEditSheet
          isOpen={editSheetOpen}
          onOpenChange={setEditSheetOpen}
          onSuccess={handleRiskUpdated}
          risk={risk}
        />
      </div>
    </RiskErrorBoundary>
  );
};

export default RiskDetailsContainer;
