
import { Progress } from "@/components/ui/progress";
import { MitigationAction } from "@/types";

interface MitigationProgressIndicatorProps {
  actions: MitigationAction[];
}

export const MitigationProgressIndicator = ({ actions }: MitigationProgressIndicatorProps) => {
  // If there are no actions, return null
  if (!actions.length) return null;

  const completedActions = actions.filter(action => action.completed).length;
  const totalActions = actions.length;
  const progressPercentage = Math.round((completedActions / totalActions) * 100);

  return (
    <div className="space-y-2">
      <div className="flex justify-between text-sm">
        <span>Mitigation Progress</span>
        <span className="font-medium">{completedActions} / {totalActions} completed</span>
      </div>
      <Progress value={progressPercentage} className="h-2" />
      {progressPercentage === 100 && (
        <p className="text-xs text-green-600 dark:text-green-400">All actions completed!</p>
      )}
    </div>
  );
};
