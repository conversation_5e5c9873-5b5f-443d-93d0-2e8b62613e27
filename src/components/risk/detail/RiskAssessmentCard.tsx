import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { RiskSeverity } from "@/types";

interface RiskAssessmentCardProps {
  severity: RiskSeverity;
  likelihood: number;
  impact: number;
  inherentSeverity?: RiskSeverity;
  inherentLikelihood?: number;
  inherentImpact?: number;
  category: string;
  ownerName: string;
  status: string;
  dueDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const RiskAssessmentCard = ({ 
  severity, 
  likelihood, 
  impact,
  inherentSeverity,
  category,
  ownerName,
  status,
  dueDate,
  updatedAt
}: RiskAssessmentCardProps) => {
  const getSeverityColor = (sev: string) => {
    switch (sev) {
      case 'Critical': return 'bg-red-500';
      case 'High': return 'bg-orange-500';
      case 'Medium': return 'bg-amber-400';
      case 'Low': return 'bg-green-500';
      default: return 'bg-gray-400';
    }
  };

  return (
    <Card>
      <CardHeader className="pb-4">
        <CardTitle className="text-lg">Risk Assessment & Status</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
          {/* Current Risk Level - most prominent */}
          <div className="md:col-span-1">
            <div className="text-sm font-medium text-muted-foreground mb-2">Current Risk</div>
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${getSeverityColor(severity)}`}></div>
              <span className="text-lg font-semibold">{severity}</span>
            </div>
          </div>

          {/* Likelihood */}
          <div>
            <div className="text-sm font-medium text-muted-foreground mb-2">Likelihood</div>
            <div className="flex items-center gap-1">
              {[1,2,3,4,5].map(i => (
                <div 
                  key={i} 
                  className={`w-2 h-2 rounded-full ${
                    i <= likelihood ? 'bg-blue-500' : 'bg-gray-200'
                  }`} 
                />
              ))}
              <span className="ml-2 text-sm font-medium">{likelihood}/5</span>
            </div>
          </div>

          {/* Impact */}
          <div>
            <div className="text-sm font-medium text-muted-foreground mb-2">Impact</div>
            <div className="flex items-center gap-1">
              {[1,2,3,4,5].map(i => (
                <div 
                  key={i} 
                  className={`w-2 h-2 rounded-full ${
                    i <= impact ? 'bg-red-500' : 'bg-gray-200'
                  }`} 
                />
              ))}
              <span className="ml-2 text-sm font-medium">{impact}/5</span>
            </div>
          </div>

          {/* Status */}
          <div>
            <div className="text-sm font-medium text-muted-foreground mb-2">Status</div>
            <Badge variant="secondary" className="bg-blue-50 text-blue-700 border-blue-200">
              {status}
            </Badge>
          </div>
        </div>

        {/* Show inherent vs residual if available */}
        {inherentSeverity && (
          <div className="mt-6 p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-medium text-green-800">Control Effectiveness</div>
                <div className="text-sm text-green-600">
                  Your controls reduced risk from {inherentSeverity} to {severity}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className={`w-2 h-2 rounded-full ${getSeverityColor(inherentSeverity)}`}></div>
                <span className="text-sm">→</span>
                <div className={`w-2 h-2 rounded-full ${getSeverityColor(severity)}`}></div>
              </div>
            </div>
          </div>
        )}

        {/* Secondary info in a more compact row */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-6 mt-6 border-t">
          <div>
            <div className="text-xs text-muted-foreground">Category</div>
            <div className="text-sm font-medium">{category}</div>
          </div>
          <div>
            <div className="text-xs text-muted-foreground">Owner</div>
            <div className="text-sm font-medium">{ownerName}</div>
          </div>
          <div>
            <div className="text-xs text-muted-foreground">Due Date</div>
            <div className="text-sm font-medium">
              {dueDate ? dueDate.toLocaleDateString() : "Not set"}
            </div>
          </div>
          <div>
            <div className="text-xs text-muted-foreground">Last Updated</div>
            <div className="text-sm font-medium">
              {updatedAt.toLocaleDateString()}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RiskAssessmentCard;