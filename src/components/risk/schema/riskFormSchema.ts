
import { z } from "zod";
import { RiskStatus } from "@/types";

// Define the effectiveness type for control measures
export type EffectivenessType = "High" | "Medium" | "Low";

// Define the schema for control measures
export const ControlMeasureSchema = z.object({
  id: z.string().optional(),
  description: z.string().min(1, "Description is required"),
  effectiveness: z.enum(["High", "Medium", "Low"]).optional(),
  implemented: z.boolean().optional().default(true),
});

// Define the schema for mitigation actions
export const MitigationActionSchema = z.object({
  id: z.string().optional(),
  description: z.string().min(1, "Description is required"),
  completed: z.boolean().optional().default(false),
});

// Define the schema for risk form - including inherent risk fields
export const RiskFormSchema = z.object({
  title: z.string().min(3, "Title must be at least 3 characters"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  category: z.string().optional(),
  
  // Inherent risk fields
  inherentLikelihood: z.number().min(1).max(5),
  inherentImpact: z.number().min(1).max(5),
  
  // Residual risk fields
  likelihood: z.number().min(1).max(5),
  impact: z.number().min(1).max(5),
  
  status: z.nativeEnum(RiskStatus),
  currentControls: z.string().optional(),
  controlMeasures: z.array(ControlMeasureSchema).optional(),
  mitigationApproach: z.string().optional(),
  mitigationActions: z.array(MitigationActionSchema).optional(),
  dueDate: z.date().optional().nullable(),
  ownerId: z.string().optional(),
  categoryId: z.string().optional(),
});

// Define the type for form values based on the schema
export type RiskFormValues = z.infer<typeof RiskFormSchema>;
