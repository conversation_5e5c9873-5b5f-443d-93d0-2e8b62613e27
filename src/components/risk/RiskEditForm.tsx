
import { useRiskEditForm } from "@/hooks/risk";
import BaseRiskForm from "./form-sections/BaseRiskForm";
import { Risk } from "@/types";

interface RiskEditFormProps {
  risk: Risk;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const RiskEditForm = ({ risk, onSuccess, onCancel }: RiskEditFormProps) => {
  const { 
    form, 
    inherentSeverity,
    residualSeverity,
    submitting, 
    updateInherentSeverity,
    updateResidualSeverity,
    onSubmit 
  } = useRiskEditForm({
    onSuccess: onSuccess || (() => {}),
    initialRisk: risk,
  });

  return (
    <BaseRiskForm
      mode="edit"
      form={form}
      inherentSeverity={inherentSeverity}
      residualSeverity={residualSeverity}
      submitting={submitting}
      updateInherentSeverity={updateInherentSeverity}
      updateResidualSeverity={updateResidualSeverity}
      onSubmit={async (values) => {
        await onSubmit(values);
      }}
      onCancel={onCancel || (() => {})}
    />
  );
};

export default RiskEditForm;
