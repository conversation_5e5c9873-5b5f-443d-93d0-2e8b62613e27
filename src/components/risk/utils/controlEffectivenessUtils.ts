import { validateEffectiveness } from "@/utils/typeValidation";

/**
 * Gets the appropriate text color class based on effectiveness level
 */

export const getEffectivenessColor = (effectiveness: string): string => {
  // Use our validation utility to ensure we have a valid effectiveness value
  const validEffectiveness = validateEffectiveness(effectiveness);

  switch (validEffectiveness) {
    case "High":
      return "text-green-600";
    case "Medium":
      return "text-amber-600";
    case "Low":
      return "text-red-600";
    default:
      return "";
  }
};

/**
 * Gets the description text explaining what each effectiveness level means
 */

export const getEffectivenessDescription = (effectiveness: string): string => {
  // Use our validation utility to ensure we have a valid effectiveness value
  const validEffectiveness = validateEffectiveness(effectiveness);

  switch (validEffectiveness) {
    case "High":
      return "Significantly reduces the probability or impact of the risk";
    case "Medium":
      return "Partially reduces the risk but has some limitations";
    case "Low":
      return "Minimal effect on reducing the risk";
    default:
      return "";
  }
};
