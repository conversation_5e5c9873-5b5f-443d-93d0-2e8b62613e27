import * as z from "zod";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UseFormReturn } from "react-hook-form";
import { RiskFormSchema } from "../schema/riskFormSchema";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";
type FormValues = z.infer<typeof RiskFormSchema>;
interface BasicRiskInfoProps {
  form: UseFormReturn<FormValues>;
}
interface Category {
  id: string;
  name: string;
}
const BasicRiskInfo = ({ form }: BasicRiskInfoProps) => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [uncategorizedId, setUncategorizedId] = useState<string | null>(null);
  const [creatingCategories, setCreatingCategories] = useState(false);
  const createDefaultCategories = async () => {
    setCreatingCategories(true);
    try {
      const defaultCategories = [
        {
          name: "Uncategorized",
          description: "Default category for risks without specific classification",
        },
        { name: "Operational", description: "Risks related to day-to-day operations" },
        { name: "Financial", description: "Risks related to financial matters" },
        { name: "Strategic", description: "Risks related to strategic planning and execution" },
        { name: "Compliance", description: "Risks related to regulatory and legal compliance" },
        { name: "Technology", description: "Risks related to technology and IT systems" },
        { name: "Human Resources", description: "Risks related to personnel and staffing" },
        { name: "External", description: "Risks from external factors and environment" },
      ];
      const { data, error } = await supabase
        .from("risk_categories")
        .insert(defaultCategories)
        .select("id, name");
      if (error) {
        return;
      }
      if (data) {
        // Find the Uncategorized category
        const uncategorized = data.find(category => category.name === "Uncategorized");
        let sortedCategories = [...data];
        if (uncategorized) {
          sortedCategories = sortedCategories.filter(category => category.name !== "Uncategorized");
          sortedCategories.unshift(uncategorized);
          setUncategorizedId(uncategorized.id);
        }
        setCategories(sortedCategories);
      }
    } catch (err) {
      // Error caught and handled
    } finally {
      setCreatingCategories(false);
    }
  };
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const { data, error } = await supabase
          .from("risk_categories")
          .select("id, name")
          .order("name");
        if (error) {
          return;
        }
        if (!data || data.length === 0) {
          // No categories exist, we'll show a button to create default ones
          setCategories([]);
          return;
        }
        // Find the Uncategorized category to place it at the top
        const uncategorized = data.find(category => category.name === "Uncategorized");
        let sortedCategories = [...data];
        if (uncategorized) {
          sortedCategories = sortedCategories.filter(category => category.name !== "Uncategorized");
          sortedCategories.unshift(uncategorized);
          setUncategorizedId(uncategorized.id);
        }
        setCategories(sortedCategories);
      } catch (err) {
        // Error caught and handled
      } finally {
        setLoading(false);
      }
    };
    fetchCategories();
  }, []);
  return (
    <>
      <FormField
        control={form.control}
        name="title"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Risk Title</FormLabel>
            <FormControl>
              <Input placeholder="Briefly describe the risk" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Detailed Description</FormLabel>
            <FormControl>
              <Textarea placeholder="Provide a detailed description of the risk" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="category"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Category (Optional)</FormLabel>
            {loading ? (
              <Skeleton className="h-10 w-full" />
            ) : categories.length === 0 ? (
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">
                  No categories available. Create default categories to get started.
                </div>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={createDefaultCategories}
                  disabled={creatingCategories}
                  className="w-full"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  {creatingCategories ? "Creating Categories..." : "Create Default Categories"}
                </Button>
              </div>
            ) : (
              <Select
                onValueChange={value => {
                  const selectedCategory = categories.find(cat => cat.name === value);
                  field.onChange(value);
                  if (selectedCategory) {
                    form.setValue("categoryId", selectedCategory.id);
                  } else if (value === "") {
                    form.setValue("categoryId", uncategorizedId || "");
                  }
                }}
                value={field.value || ""}
              >
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category (optional)" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {categories.map(category => (
                    <SelectItem key={category.id} value={category.name}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            <FormMessage />
          </FormItem>
        )}
      />
      {/* Hidden field for categoryId */}
      <FormField
        control={form.control}
        name="categoryId"
        render={({ field }) => <input type="hidden" {...field} />}
      />
    </>
  );
};
export default BasicRiskInfo;
