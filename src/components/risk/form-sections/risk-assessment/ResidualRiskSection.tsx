import { UseFormReturn } from "react-hook-form";
import { RiskFormSchema } from "../../schema/riskFormSchema";
import { RiskSeverity } from "@/types";
import { z } from "zod";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { getSeverityColor } from "@/services/risk/riskCalculationService";

type FormValues = z.infer<typeof RiskFormSchema>;

interface ResidualRiskSectionProps {
  form: UseFormReturn<FormValues>;
  severity: RiskSeverity;
  onUpdate: (likelihood: number, impact: number) => void;
}

const ResidualRiskSection = ({ form, severity, onUpdate }: ResidualRiskSectionProps) => {
  const rawLikelihood = form.watch("likelihood");
  const rawImpact = form.watch("impact");
  
  // Ensure we always have valid numbers
  const likelihood = typeof rawLikelihood === 'number' ? rawLikelihood : 1;
  const impact = typeof rawImpact === 'number' ? rawImpact : 1;

  const handleLikelihoodChange = (value: number[]) => {
    const newValue = value[0] as number;
    form.setValue("likelihood", newValue);
    // Use the current impact value from local state instead of form
    onUpdate(newValue, impact as number);
  };

  const handleImpactChange = (value: number[]) => {
    const newValue = value[0] as number;
    form.setValue("impact", newValue);
    // Use the current likelihood value from local state instead of form
    onUpdate(likelihood as number, newValue);
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Residual Risk Assessment</CardTitle>
          <Badge className={getSeverityColor(severity)}>
            {severity}
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground">
          Assess the risk after considering existing controls
        </p>
      </CardHeader>
      <CardContent className="space-y-6">
        <FormField
          control={form.control}
          name="likelihood"
          render={() => (
            <FormItem>
              <FormLabel>Likelihood: {likelihood}</FormLabel>
              <FormControl>
                <Slider
                  min={1}
                  max={5}
                  step={1}
                  value={[likelihood as number]}
                  onValueChange={handleLikelihoodChange}
                  className="w-full"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="impact"
          render={() => (
            <FormItem>
              <FormLabel>Impact: {impact}</FormLabel>
              <FormControl>
                <Slider
                  min={1}
                  max={5}
                  step={1}
                  value={[impact as number]}
                  onValueChange={handleImpactChange}
                  className="w-full"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </CardContent>
    </Card>
  );
};

export default ResidualRiskSection;