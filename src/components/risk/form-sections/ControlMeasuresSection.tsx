
import { UseFormReturn } from "react-hook-form";
import { HelpCircle } from "lucide-react";
import { z } from "zod";
import { RiskFormSchema, EffectivenessType } from "../schema/riskFormSchema";
import { FormLabel, FormDescription } from "@/components/ui/form";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import ControlMeasuresManager from "./control-measures/ControlMeasuresManager";
import AddControlForm from "./control-measures/AddControlForm";
import EffectivenessGuide from "./control-measures/EffectivenessGuide";

type FormValues = z.infer<typeof RiskFormSchema>;

interface ControlMeasuresSectionProps {
  form: UseFormReturn<FormValues>;
}

const ControlMeasuresSection = ({ form }: ControlMeasuresSectionProps) => {
  const controlMeasures = form.watch("controlMeasures") || [];
  
  const handleAddControl = (description: string, effectiveness: EffectivenessType) => {
    const newControl = {
      description,
      effectiveness,
      implemented: true
    };
    
    const updatedControls = [...controlMeasures, newControl];
    form.setValue("controlMeasures", updatedControls, {
      shouldDirty: true,
      shouldValidate: true
    });
  };
  
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <FormLabel>Current Control Measures</FormLabel>
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger className="cursor-help">
              <HelpCircle className="h-4 w-4 text-muted-foreground" />
            </TooltipTrigger>
            <TooltipContent className="max-w-xs">
              <p>Add and rate current control measures that are in place to mitigate this risk. Effective controls can help reduce the overall risk severity.</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
      <FormDescription>
        Current control measures help reduce the likelihood or impact of the risk. Rate the effectiveness of each control.
      </FormDescription>

      <ControlMeasuresManager form={form} />
      <AddControlForm onAddControl={handleAddControl} />
      <EffectivenessGuide />
    </div>
  );
};

export default ControlMeasuresSection;
