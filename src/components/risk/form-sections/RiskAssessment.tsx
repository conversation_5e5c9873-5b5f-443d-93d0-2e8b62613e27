
import { UseFormReturn } from "react-hook-form";
import { RiskFormSchema } from "../schema/riskFormSchema";
import { RiskSeverity } from "@/types";
import { z } from "zod";
import InherentRiskSection from "./risk-assessment/InherentRiskSection";
import ResidualRiskSection from "./risk-assessment/ResidualRiskSection";

type FormValues = z.infer<typeof RiskFormSchema>;

interface RiskAssessmentProps {
  form: UseFormReturn<FormValues>;
  inherentSeverity: RiskSeverity;
  residualSeverity: RiskSeverity;
  updateInherentSeverity: (likelihood: number, impact: number) => void;
  updateResidualSeverity: (likelihood: number, impact: number) => void;
}

const RiskAssessment = ({
  form,
  inherentSeverity,
  residualSeverity,
  updateInherentSeverity,
  updateResidualSeverity,
}: RiskAssessmentProps) => {
  return (
    <div className="space-y-6">
      <InherentRiskSection
        form={form}
        severity={inherentSeverity}
        onUpdate={updateInherentSeverity}
      />
      <ResidualRiskSection
        form={form}
        severity={residualSeverity}
        onUpdate={updateResidualSeverity}
      />
    </div>
  );
};

export default RiskAssessment;
