
import { Check, Trash2 } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface MitigationActionItemProps {
  action: {
    description: string;
    completed: boolean;
  };
  onToggleComplete: () => void;
  onRemove: () => void;
}

const MitigationActionItem = ({ 
  action, 
  onToggleComplete, 
  onRemove 
}: MitigationActionItemProps) => {
  return (
    <div className={cn(
      "flex items-center justify-between gap-2 p-2 rounded-md border border-gray-200 dark:border-gray-800 transition-colors duration-200",
      action.completed ? "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-900" : ""
    )}>
      <div className="flex items-center gap-2 flex-1 min-w-0">
        <Button
          type="button"
          variant={action.completed ? "default" : "outline"}
          size="icon"
          className={cn(
            "h-6 w-6 rounded-full transition-colors",
            action.completed ? "bg-green-600 hover:bg-green-700" : ""
          )}
          onClick={onToggleComplete}
          title={action.completed ? "Mark as incomplete" : "Mark as completed"}
        >
          {action.completed && <Check className="h-3 w-3" />}
        </Button>
        <span className={cn(
          "text-sm transition-opacity duration-200",
          action.completed ? "line-through opacity-70" : ""
        )}>
          {action.description}
        </span>
      </div>
      <Button
        type="button"
        variant="ghost"
        size="icon"
        className="h-7 w-7 text-muted-foreground hover:text-destructive"
        onClick={onRemove}
      >
        <Trash2 className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default MitigationActionItem;
