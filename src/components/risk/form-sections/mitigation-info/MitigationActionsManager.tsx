
import { UseFormReturn } from "react-hook-form";
import { RiskFormSchema } from "../../schema/riskFormSchema";
import { z } from "zod";
import { FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Trash2, Plus } from "lucide-react";
import { useState } from "react";

type FormValues = z.infer<typeof RiskFormSchema>;

interface MitigationActionsManagerProps {
  form: UseFormReturn<FormValues>;
}

const MitigationActionsManager = ({ form }: MitigationActionsManagerProps) => {
  const [newAction, setNewAction] = useState("");
  const mitigationActions = form.watch("mitigationActions") || [];

  const addAction = () => {
    if (newAction.trim()) {
      const updatedActions = [...mitigationActions, {
        description: newAction.trim(),
        completed: false
      }];
      form.setValue("mitigationActions", updatedActions);
      setNewAction("");
    }
  };

  const removeAction = (index: number) => {
    const updatedActions = mitigationActions.filter((_, i) => i !== index);
    form.setValue("mitigationActions", updatedActions);
  };

  const toggleAction = (index: number) => {
    const updatedActions = mitigationActions.map((action, i) => 
      i === index ? { ...action, completed: !action.completed } : action
    );
    form.setValue("mitigationActions", updatedActions);
  };

  return (
    <FormField
      control={form.control}
      name="mitigationActions"
      render={() => (
        <FormItem>
          <FormLabel>Mitigation Actions</FormLabel>
          <div className="space-y-3">
            {mitigationActions.map((action, index) => (
              <div key={index} className="flex items-center gap-2 p-3 border rounded-lg">
                <input
                  type="checkbox"
                  checked={action.completed}
                  onChange={() => toggleAction(index)}
                  className="mr-2"
                />
                <span className={action.completed ? "line-through text-muted-foreground" : ""}>
                  {action.description}
                </span>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeAction(index)}
                  className="ml-auto"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            ))}
            
            <div className="flex gap-2">
              <Input
                placeholder="Add new mitigation action..."
                value={newAction}
                onChange={(e) => setNewAction(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addAction())}
              />
              <Button type="button" onClick={addAction} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default MitigationActionsManager;
