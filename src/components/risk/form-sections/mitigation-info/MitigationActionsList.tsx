import { useState } from "react";
import { Plus, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { ButtonLoading } from "@/components/ui/button-loading";

interface MitigationActionsListProps {
  actions: { id: string; description: string; completed: boolean }[];
  onActionsChange: (actions: { id: string; description: string; completed: boolean }[]) => void;
  isEditable?: boolean;
}

const MitigationActionsList = ({ actions, onActionsChange, isEditable = true }: MitigationActionsListProps) => {
  const [newAction, setNewAction] = useState("");
  const [isAddingAction, setIsAddingAction] = useState(false);

  const handleActionChange = (id: string, updatedAction: Partial<{ description: string; completed: boolean }>) => {
    const updatedActions = actions.map(action =>
      action.id === id ? { ...action, ...updatedAction } : action
    );
    onActionsChange(updatedActions);
  };

  const handleRemoveAction = (id: string) => {
    const updatedActions = actions.filter(action => action.id !== id);
    onActionsChange(updatedActions);
  };

  const handleAddAction = async () => {
    if (newAction.trim() === "") return;
    setIsAddingAction(true);
    
    // Simulate adding action (replace with actual API call if needed)
    await new Promise(resolve => setTimeout(resolve, 500));
    
    const newActionItem = {
      id: Math.random().toString(36).substring(7),
      description: newAction.trim(),
      completed: false
    };
    onActionsChange([...actions, newActionItem]);
    setNewAction("");
    setIsAddingAction(false);
  };

  return (
    <div className="space-y-3">
      {actions.map(action => (
        <div key={action.id} className="flex items-center gap-2">
          <Checkbox
            id={`action-${action.id}`}
            checked={action.completed}
            onCheckedChange={(checked) => handleActionChange(action.id, { completed: !!checked })}
            disabled={!isEditable}
          />
          <Input
            id={`action-${action.id}-description`}
            value={action.description}
            onChange={(e) => handleActionChange(action.id, { description: e.target.value })}
            className="flex-1"
            disabled={!isEditable}
          />
          {isEditable && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => handleRemoveAction(action.id)}
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      ))}
      
      {isEditable && (
        <div className="flex gap-2">
          <Input
            placeholder="Add new mitigation action..."
            value={newAction}
            onChange={(e) => setNewAction(e.target.value)}
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                handleAddAction();
              }
            }}
            className="flex-1"
          />
          <ButtonLoading
            type="button"
            size="sm"
            onClick={handleAddAction}
            loading={isAddingAction}
            loadingText="Adding..."
          >
            <Plus className="h-4 w-4" />
            Add
          </ButtonLoading>
        </div>
      )}
      
      {actions.length === 0 && isEditable && (
        <p className="text-sm text-muted-foreground">
          No mitigation actions added yet. Add some actions to mitigate the risk.
        </p>
      )}
    </div>
  );
};

export default MitigationActionsList;
