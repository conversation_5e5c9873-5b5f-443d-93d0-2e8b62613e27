
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from "@/components/ui/form";
import { Textarea } from "@/components/ui/textarea";
import { UseFormReturn } from "react-hook-form";
import { RiskFormValues } from "../../schema/riskFormSchema";

interface MitigationApproachProps {
  form: UseFormReturn<RiskFormValues>;
}

const MitigationApproach = ({ form }: MitigationApproachProps) => {
  return (
    <FormField
      control={form.control}
      name="mitigationApproach"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Mitigation Approach</FormLabel>
          <FormControl>
            <Textarea placeholder="What approach will you take to mitigate this risk?" {...field} />
          </FormControl>
          <FormDescription>
            Describe the overall approach to address this risk
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

export default MitigationApproach;
