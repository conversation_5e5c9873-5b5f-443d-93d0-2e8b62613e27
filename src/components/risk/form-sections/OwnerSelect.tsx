import { useState, useEffect } from "react";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { UseFormReturn } from "react-hook-form";
import { Skeleton } from "@/components/ui/skeleton";
import { User, UserRole } from "@/types";
import { getRiskOwnerCandidates } from "@/services/user";
import { useAuth } from "@/contexts/auth";
import { RiskFormValues } from "../schema/riskFormSchema";
interface OwnerSelectProps {
  form: UseFormReturn<RiskFormValues>;
}
const OwnerSelect = ({ form }: OwnerSelectProps) => {
  const [owners, setOwners] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const { user: currentUser } = useAuth();
  useEffect(() => {
    const fetchOwners = async () => {
      try {
        setLoading(true);
        if (!currentUser) return;
        // Get users who can be risk owners (admin and risk_owner roles)
        const { data, error } = await getRiskOwnerCandidates(currentUser.id);
        if (error) {
          return;
        }
        // Add current user to the list
        const allOwners = currentUser ? [currentUser, ...(data ?? [])] : (data ?? []);
        setOwners(allOwners);
      } catch (err) {
        // Error caught and handled
      } finally {
        setLoading(false);
      }
    };
    fetchOwners();
  }, [currentUser]);
  return (
    <FormField
      control={form.control}
      name="ownerId"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Risk Owner</FormLabel>
          {loading ? (
            <Skeleton className="h-10 w-full" />
          ) : (
            <Select onValueChange={field.onChange} defaultValue={field.value || ""} value={field.value || ""}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select a risk owner" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {owners.map(owner => (
                  <SelectItem key={owner.id} value={owner.id}>
                    {owner.name}{" "}
                    {owner.role === UserRole.ADMIN
                      ? "(Admin)"
                      : owner.role === UserRole.RISK_OWNER
                        ? "(Risk Owner)"
                        : ""}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
export default OwnerSelect;
