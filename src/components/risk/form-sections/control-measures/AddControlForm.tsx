
import { useState } from "react";
import { PlusCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { EffectivenessType } from "../../schema/riskFormSchema";
import { getEffectivenessColor, getEffectivenessDescription } from "../../utils/controlEffectivenessUtils";

interface AddControlFormProps {
  onAddControl: (description: string, effectiveness: EffectivenessType) => void;
}

const AddControlForm = ({ onAddControl }: AddControlFormProps) => {
  const [newControlDescription, setNewControlDescription] = useState("");
  const [newControlEffectiveness, setNewControlEffectiveness] = useState<EffectivenessType>("Medium");
  
  const handleAddControl = () => {
    if (newControlDescription.trim().length < 3) return;
    
    onAddControl(newControlDescription.trim(), newControlEffectiveness);
    setNewControlDescription("");
  };
  
  return (
    <div className="flex items-end gap-2 pt-2">
      <div className="flex-1">
        <Input 
          value={newControlDescription} 
          onChange={(e) => setNewControlDescription(e.target.value)} 
          placeholder="Add a new current control measure..."
          className="flex-1"
        />
      </div>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Select 
              value={newControlEffectiveness} 
              onValueChange={(value) => setNewControlEffectiveness(value as EffectivenessType)}
            >
              <SelectTrigger className={`w-[110px] ${getEffectivenessColor(newControlEffectiveness)}`}>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="High" className="text-green-600">High</SelectItem>
                <SelectItem value="Medium" className="text-amber-600">Medium</SelectItem>
                <SelectItem value="Low" className="text-red-600">Low</SelectItem>
              </SelectContent>
            </Select>
          </TooltipTrigger>
          <TooltipContent side="right">
            {getEffectivenessDescription(newControlEffectiveness)}
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
      <Button 
        type="button" 
        variant="outline" 
        size="sm" 
        onClick={handleAddControl}
        disabled={newControlDescription.trim().length < 3}
        className="flex items-center gap-1"
      >
        <PlusCircle className="h-4 w-4" /> Add
      </Button>
    </div>
  );
};

export default AddControlForm;
