import { X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { getEffectivenessColor, getEffectivenessDescription } from "../../utils/controlEffectivenessUtils";

interface ControlMeasureItemProps {
  index: number;
  description: string;
  effectiveness: string;
  implemented: boolean;
  onDescriptionChange: (value: string) => void;
  onEffectivenessChange: (value: string) => void;
  onImplementedChange: (value: boolean) => void;
  onRemove: () => void;
}

const ControlMeasureItem = ({
  index,
  description,
  effectiveness,
  implemented,
  onDescriptionChange,
  onEffectivenessChange,
  onImplementedChange,
  onRemove
}: ControlMeasureItemProps) => {
  
  return (
    <div className="flex items-start gap-2 border p-3 rounded-md">
      <div className="flex-1 space-y-2">
        <div className="flex justify-between">
          <Input 
            value={description} 
            onChange={(e) => onDescriptionChange(e.target.value)} 
            className="flex-1" 
            placeholder="Control description"
            data-testid={`control-${index}-description`}
          />
          <Button 
            type="button" 
            variant="ghost" 
            size="sm" 
            onClick={onRemove}
            className="px-2"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">Status:</span>
            <div className="flex items-center space-x-2">
              <Checkbox 
                id={`control-${index}-implemented`}
                checked={implemented}
                onCheckedChange={(checked) => onImplementedChange(!!checked)}
              />
              <label 
                htmlFor={`control-${index}-implemented`}
                className="text-sm"
              >
                Implemented
              </label>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">Control Effectiveness:</span>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Select
                    value={effectiveness ?? "Medium"}
                    onValueChange={(value) => onEffectivenessChange(value)}
                  >
                    <SelectTrigger className={`w-[110px] h-8 ${getEffectivenessColor(effectiveness ?? "Medium")}`}>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="High" className="text-green-600">High</SelectItem>
                      <SelectItem value="Medium" className="text-amber-600">Medium</SelectItem>
                      <SelectItem value="Low" className="text-red-600">Low</SelectItem>
                    </SelectContent>
                  </Select>
                </TooltipTrigger>
                <TooltipContent side="right">
                  {getEffectivenessDescription(effectiveness ?? "Medium")}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ControlMeasureItem;
