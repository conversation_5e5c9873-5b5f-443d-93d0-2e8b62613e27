import { UseFormReturn } from "react-hook-form";
import {
  RiskFormSchema,
} from "../../schema/riskFormSchema";
import { z } from "zod";
import { FormField, FormItem, FormMessage } from "@/components/ui/form";
import { But<PERSON> } from "@/components/ui/button";
import ControlMeasureItem from "./ControlMeasureItem";
type FormValues = z.infer<typeof RiskFormSchema>;
interface ControlMeasuresManagerProps {
  form: UseFormReturn<FormValues>;
}
const ControlMeasuresManager = ({ form }: ControlMeasuresManagerProps) => {
  const controlMeasures = form.watch("controlMeasures") || [];
  const currentControls = form.watch("currentControls");
  const convertLegacyControls = () => {
    if (currentControls && controlMeasures.length === 0) {
      const lines = currentControls.split(/\r?\n/).filter(line => line.trim().length > 0);
      if (lines.length > 0) {
        const newControls = lines.map(line => ({
          description: line.trim(),
          effectiveness: "Medium" as const,
          implemented: true,
        }));
        form.setValue("controlMeasures", newControls, {
          shouldDirty: true,
          shouldValidate: true,
        });
        form.setValue("currentControls", "", {
          shouldDirty: true,
        });
      }
    }
  };
  // handleAddControl function removed as it's replaced by the one in ControlMeasuresSection
  const handleRemoveControl = (index: number) => {
    const updatedControls = [...controlMeasures];
    updatedControls.splice(index, 1);
    form.setValue("controlMeasures", updatedControls, {
      shouldDirty: true,
      shouldValidate: true,
    });
  };
  const handleControlChange = (index: number, field: string, value: string | boolean) => {
    const updatedControls = [...controlMeasures];
    updatedControls[index] = {
      ...updatedControls[index],
      [field]: value,
    } as any;
    form.setValue("controlMeasures", updatedControls, {
      shouldDirty: true,
      shouldValidate: true,
    });
  };
  return (
    <FormField
      control={form.control}
      name="controlMeasures"
      render={() => (
        <FormItem>
          {currentControls && controlMeasures.length === 0 && (
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={convertLegacyControls}
              className="mb-4"
            >
              Convert Legacy Controls to Structured Format
            </Button>
          )}
          <div className="space-y-3">
            {controlMeasures.map((control, index) => (
              <ControlMeasureItem
                key={index}
                index={index}
                description={control.description || ""}
                effectiveness={control.effectiveness ?? "Medium"}
                implemented={!!control.implemented}
                onDescriptionChange={value => handleControlChange(index, "description", value)}
                onEffectivenessChange={value => handleControlChange(index, "effectiveness", value)}
                onImplementedChange={value => handleControlChange(index, "implemented", value)}
                onRemove={() => handleRemoveControl(index)}
              />
            ))}
          </div>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
export default ControlMeasuresManager;
