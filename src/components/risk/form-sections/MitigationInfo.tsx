
import { UseFormReturn } from "react-hook-form";
import { RiskFormSchema } from "../schema/riskFormSchema";
import { z } from "zod";
import StatusSelector from "./mitigation-info/StatusSelector";
import MitigationApproach from "./mitigation-info/MitigationApproach";
import MitigationActionsManager from "./mitigation-info/MitigationActionsManager";
import DueDatePicker from "./mitigation-info/DueDatePicker";
import MitigationGuide from "./mitigation-info/MitigationGuide";

type FormValues = z.infer<typeof RiskFormSchema>;

interface MitigationInfoProps {
  form: UseFormReturn<FormValues>;
}

const MitigationInfo = ({ form }: MitigationInfoProps) => {
  return (
    <div className="space-y-6">
      <StatusSelector form={form} />
      <MitigationApproach form={form} />
      <MitigationActionsManager form={form} />
      <DueDatePicker form={form} />
      <MitigationGuide />
    </div>
  );
};

export default MitigationInfo;
