
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { FileText } from "lucide-react";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";
import { RiskFormSchema } from "../schema/riskFormSchema";
import { RiskSeverity, RiskTemplate } from "@/types";
import BasicRiskInfo from "./BasicRiskInfo";
import RiskAssessment from "./RiskAssessment";
import MitigationInfo from "./MitigationInfo";
import ControlMeasuresSection from "./ControlMeasuresSection";
import OwnerSelect from "./OwnerSelect";

type FormValues = z.infer<typeof RiskFormSchema>;

interface BaseRiskFormProps {
  mode: 'create' | 'edit';
  form: UseFormReturn<FormValues>;
  inherentSeverity: RiskSeverity;
  residualSeverity: RiskSeverity;
  submitting: boolean;
  template?: RiskTemplate;
  loadingTemplate?: boolean;
  updateInherentSeverity: (likelihood: number, impact: number) => void;
  updateResidualSeverity: (likelihood: number, impact: number) => void;
  onSubmit: (values: FormValues) => Promise<void>;
  onSuccess?: () => void;
  onCancel?: () => void;
}

const BaseRiskForm = ({
  mode,
  form,
  inherentSeverity,
  residualSeverity,
  submitting,
  template,
  loadingTemplate,
  updateInherentSeverity,
  updateResidualSeverity,
  onSubmit,
  onCancel
}: BaseRiskFormProps) => {
  const isCreateMode = mode === 'create';
  
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {isCreateMode && template && (
          <Alert>
            <FileText className="h-4 w-4" />
            <AlertDescription>
              Using template: <strong>{template.name}</strong>. Default values have been applied.
            </AlertDescription>
          </Alert>
        )}
        
        <BasicRiskInfo form={form} />
        <OwnerSelect form={form} />
        <RiskAssessment 
          form={form} 
          inherentSeverity={inherentSeverity}
          residualSeverity={residualSeverity}
          updateInherentSeverity={updateInherentSeverity}
          updateResidualSeverity={updateResidualSeverity}
        />
        <ControlMeasuresSection form={form} />
        <MitigationInfo form={form} />

        <div className="flex justify-end space-x-4 pt-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Cancel
            </Button>
          )}
          <Button type="submit" disabled={submitting || loadingTemplate}>
            {submitting 
              ? (isCreateMode ? "Saving Risk..." : "Saving Changes...") 
              : (isCreateMode ? "Save Risk" : "Save Changes")
            }
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default BaseRiskForm;
