
import { Risk } from "@/types";
import { TableCell, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "lucide-react";

interface RiskTableRowProps {
  risk: Risk;
  onClick: (riskId: string) => void;
}

export const RiskTableRow = ({ risk, onClick }: RiskTableRowProps) => {
  const getSeverityColor = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'critical': return 'bg-red-500 text-white';
      case 'high': return 'bg-orange-500 text-white';
      case 'medium': return 'bg-yellow-500 text-black';
      case 'low': return 'bg-green-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Identified': return 'bg-blue-500 text-white';
      case 'In Progress': return 'bg-orange-500 text-white';
      case 'Mitigated': return 'bg-green-500 text-white';
      case 'Accepted': return 'bg-yellow-500 text-black';
      case 'Closed': return 'bg-gray-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const formatDate = (dateString: string | Date | null | undefined) => {
    if (!dateString) return 'No due date';
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return date.toLocaleDateString();
  };

  const formatStatus = (status: string) => {
    return status ?? 'Unknown';
  };

  return (
    <TableRow 
      className="cursor-pointer hover:bg-muted/50 transition-colors"
      onClick={() => onClick(risk.id)}
    >
      <TableCell className="font-medium">{risk.title}</TableCell>
      <TableCell>
        <span className="text-sm text-muted-foreground">
          {risk.category ?? "Uncategorized"}
        </span>
      </TableCell>
      <TableCell>
        <span className="text-sm">{risk.ownerName ?? "Unassigned"}</span>
      </TableCell>
      <TableCell>
        <Badge className={getSeverityColor(risk.severity)}>
          {risk.severity ?? 'Unknown'}
        </Badge>
      </TableCell>
      <TableCell>
        <Badge className={getStatusColor(risk.status)}>
          {formatStatus(risk.status)}
        </Badge>
      </TableCell>
      <TableCell>
        <div className="flex items-center gap-2 text-sm">
          <Calendar className="h-4 w-4 text-muted-foreground" />
          <span className={!risk.dueDate ? 'text-muted-foreground' : ''}>
            {formatDate(risk.dueDate)}
          </span>
        </div>
      </TableCell>
    </TableRow>
  );
};
