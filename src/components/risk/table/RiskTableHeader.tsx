
import { TableHeader, TableRow } from "@/components/ui/table";
import SortableTableHeader from "./SortableTableHeader";
import { RiskSeverity, RiskStatus } from "@/types";

interface Category {
  id: string;
  name: string;
}

interface User {
  id: string;
  name: string;
}

interface RiskTableHeaderProps {
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
  onFilterChange: (filterType: string, value: string | null) => void;
  onSortChange: (field: string, direction: 'asc' | 'desc' | null) => void;
  categories?: Category[];
  owners?: User[];
  activeFilters: {
    severities?: RiskSeverity[];
    statuses?: RiskStatus[];
    categories?: string[];
    ownerIds?: string[];
  };
  isLoadingFilters?: boolean;
}

const RiskTableHeader = ({
  sortField,
  sortOrder,
  onSortChange
}: RiskTableHeaderProps) => {
  return (
    <TableHeader>
      <TableRow>
        <SortableTableHeader
          title="Title"
          field="title"
          currentSortField={sortField}
          sortDirection={sortField === "title" ? sortOrder : undefined}
          onSort={onSortChange}
        />
        <SortableTableHeader
          title="Category"
          field="category"
          currentSortField={sortField}
          sortDirection={sortField === "category" ? sortOrder : undefined}
          onSort={onSortChange}
        />
        <SortableTableHeader
          title="Owner"
          field="owner"
          currentSortField={sortField}
          sortDirection={sortField === "owner" ? sortOrder : undefined}
          onSort={onSortChange}
        />
        <SortableTableHeader
          title="Severity"
          field="severity"
          currentSortField={sortField}
          sortDirection={sortField === "severity" ? sortOrder : undefined}
          onSort={onSortChange}
        />
        <SortableTableHeader
          title="Status"
          field="status"
          currentSortField={sortField}
          sortDirection={sortField === "status" ? sortOrder : undefined}
          onSort={onSortChange}
        />
        <SortableTableHeader
          title="Due Date"
          field="dueDate"
          currentSortField={sortField}
          sortDirection={sortField === "dueDate" ? sortOrder : undefined}
          onSort={onSortChange}
        />
      </TableRow>
    </TableHeader>
  );
};

export default RiskTableHeader;
