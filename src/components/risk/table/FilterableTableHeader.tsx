
import { ChevronDown } from "lucide-react";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Checkbox } from "@/components/ui/checkbox";
import { TableHead } from "@/components/ui/table";

interface FilterOption {
  id: string;
  name: string;
}

interface FilterableTableHeaderProps {
  title: string;
  options: FilterOption[];
  selectedId?: string | null;
  onFilterChange: (value: string | null) => void;
  isLoading?: boolean;
}

const FilterableTableHeader = ({
  title,
  options,
  selectedId,
  onFilterChange,
  isLoading = false
}: FilterableTableHeaderProps) => {
  return (
    <TableHead>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <button className="flex items-center hover:text-primary focus:outline-none">
            {title} <ChevronDown className="h-4 w-4 ml-1 opacity-70" />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-48">
          <DropdownMenuLabel>Filter by {title}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            {isLoading ? (
              <DropdownMenuItem disabled>Loading...</DropdownMenuItem>
            ) : options.length > 0 ? (
              options.map((option) => (
                <DropdownMenuItem key={option.id} onSelect={(e) => e.preventDefault()}>
                  <div className="flex items-center space-x-2 w-full" 
                      onClick={() => onFilterChange(selectedId === option.id ? null : option.id)}>
                    <Checkbox 
                      checked={selectedId === option.id} 
                      onCheckedChange={() => onFilterChange(selectedId === option.id ? null : option.id)} 
                      aria-label={`Filter by ${option.name}`}
                    />
                    <span>{option.name}</span>
                  </div>
                </DropdownMenuItem>
              ))
            ) : (
              <DropdownMenuItem disabled>No options available</DropdownMenuItem>
            )}
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </TableHead>
  );
};

export default FilterableTableHeader;
