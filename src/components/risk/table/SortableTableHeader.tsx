
import { ArrowDown, ArrowUp } from "lucide-react";
import { TableHead } from "@/components/ui/table";
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip";

interface SortableTableHeaderProps {
  title: string;
  field: string;
  currentSortField?: string | undefined;
  sortDirection?: 'asc' | 'desc' | undefined;
  onSort: (field: string, direction: 'asc' | 'desc' | null) => void;
}

const SortableTableHeader = ({ 
  title,
  field,
  currentSortField,
  sortDirection,
  onSort
}: SortableTableHeaderProps) => {
  const isActive = currentSortField === field;
  
  const handleSort = () => {
    if (!isActive) {
      // First click: sort descending
      onSort(field, 'desc');
    } else if (sortDirection === 'desc') {
      // Second click: switch to ascending
      onSort(field, 'asc');
    } else {
      // Third click: remove sorting
      onSort(field, null);
    }
  };

  return (
    <TableHead>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <button 
              className="flex items-center text-left font-medium text-muted-foreground hover:text-foreground"
              onClick={handleSort}
              aria-label={`Sort by ${title}`}
            >
              {title}
              {isActive && (
                sortDirection === 'asc' 
                  ? <ArrowUp className="ml-1.5 h-4 w-4" /> 
                  : <ArrowDown className="ml-1.5 h-4 w-4" />
              )}
            </button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Sort by {title}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </TableHead>
  );
};

export default SortableTableHeader;
