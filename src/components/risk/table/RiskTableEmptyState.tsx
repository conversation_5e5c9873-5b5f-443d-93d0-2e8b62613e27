
import { TableBody, TableCell, TableRow } from "@/components/ui/table";
import { AlertOctagon } from "lucide-react";

interface RiskTableEmptyStateProps {
  message?: string;
}

const RiskTableEmptyState = ({ message = "No risks found." }: RiskTableEmptyStateProps) => {
  return (
    <TableBody>
      <TableRow>
        <TableCell colSpan={6} className="text-center py-8">
          <div className="flex flex-col items-center justify-center text-muted-foreground">
            <AlertOctagon className="h-8 w-8 mb-2 opacity-50" />
            <p>{message}</p>
          </div>
        </TableCell>
      </TableRow>
    </TableBody>
  );
};

export default RiskTableEmptyState;
