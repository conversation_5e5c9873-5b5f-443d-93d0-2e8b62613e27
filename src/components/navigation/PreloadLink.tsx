import React, { useRef, useEffect } from "react";
import { Link, LinkProps } from "react-router-dom";
import { useInteractionPreloader } from "@/hooks/useRoutePreloader";

interface PreloadLinkProps extends LinkProps {
  routeImport: () => Promise<{ default: React.ComponentType<{}> }>;
  preloadOnHover?: boolean;
  preloadOnClick?: boolean;
  preloadOnVisibility?: boolean;
  children: React.ReactNode;
}

/**
 * Enhanced Link component that preloads routes on user interaction
 * Now includes intelligent preloading and visibility-based preloading
 */
export const PreloadLink: React.FC<PreloadLinkProps> = ({
  routeImport,
  preloadOnHover = true,
  preloadOnClick = false,
  preloadOnVisibility = false,
  children,
  to,
  ...linkProps
}) => {
  const {
    preloadOnHover: getHoverProps,
    preloadOnClick: getClickProps,
    preloadOnVisibility: getVisibilityProps,
  } = useInteractionPreloader();

  const linkRef = useRef<HTMLAnchorElement>(null);
  const route = typeof to === "string" ? to : (to.pathname ?? "");

  // Set up intersection observer for visibility-based preloading
  useEffect(() => {
    if (!preloadOnVisibility || !linkRef.current) return;

    const observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const visibilityProps = getVisibilityProps(routeImport, route);
            visibilityProps.onIntersect();
            observer.unobserve(entry.target);
          }
        });
      },
      {
        rootMargin: "50px", // Preload when link is 50px away from viewport
        threshold: 0.1,
      }
    );

    observer.observe(linkRef.current);

    return () => {
      observer.disconnect();
    };
  }, [preloadOnVisibility, routeImport, route, getVisibilityProps]);

  const hoverProps = preloadOnHover ? getHoverProps(routeImport, route) : {};
  const clickProps = preloadOnClick ? getClickProps(routeImport, route) : {};

  return (
    <Link ref={linkRef} to={to} {...linkProps} {...hoverProps} {...clickProps}>
      {children}
    </Link>
  );
};

/**
 * Route import functions for common routes
 */
export const ROUTE_IMPORTS = {
  dashboard: () => import("@/pages/Dashboard"),
  risks: () => import("@/pages/RiskRegister"),
  riskCreate: () => import("@/pages/RiskCreate"),
  riskDetails: () => import("@/pages/RiskDetails"),
  riskTemplates: () => import("@/pages/RiskTemplates"),
  incidents: () => import("@/pages/Incidents"),
  incidentCreate: () => import("@/pages/IncidentCreate"),
  incidentDetails: () => import("@/pages/IncidentDetails"),
  incidentEdit: () => import("@/pages/IncidentEdit"),
  reports: () => import("@/pages/Reports"),
  administration: () => import("@/pages/Administration"),
  organizationManagement: () => import("@/pages/OrganizationManagement"),
  organization: () => import("@/pages/OrganizationPage"),
  policies: () => import("@/pages/Policies"),
  profile: () => import("@/pages/Profile"),
} as const;

export default PreloadLink;
