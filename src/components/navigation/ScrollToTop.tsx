import { useEffect } from "react";
import { useLocation } from "react-router-dom";
/**
 * ScrollToTop component that automatically scrolls to the top of the page
 * when the route changes, ensuring a consistent user experience.
 */
export function ScrollToTop() {
  const { pathname } = useLocation();
  useEffect(() => {
    // Scroll to top when pathname changes
    window.scrollTo({
      top: 0,
      left: 0,
      behavior: "instant", // Use 'instant' for immediate scroll, 'smooth' for animated
    });
  }, [pathname]);
  return null;
}
/**
 * Hook for manual scroll restoration with more control
 */
export function useScrollToTop() {
  const scrollToTop = (behavior: ScrollBehavior = "smooth") => {
    // Use requestAnimationFrame to ensure smooth execution
    requestAnimationFrame(() => {
      // First try to scroll the main content area (for SPA layouts)
      const mainContent = document.getElementById("main-content");
      if (mainContent) {
        mainContent.scrollTo({
          top: 0,
          left: 0,
          behavior,
        });
      }
      // Also scroll the window (for full page layouts)
      window.scrollTo({
        top: 0,
        left: 0,
        behavior,
      });
    });
  };
  const scrollToElement = (elementId: string, behavior: ScrollBehavior = "smooth") => {
    const element = document.getElementById(elementId);
    if (element) {
      element.scrollIntoView({ behavior, block: "start" });
    }
  };
  const scrollMainContentToTop = (behavior: ScrollBehavior = "instant") => {
    requestAnimationFrame(() => {
      const mainContent = document.getElementById("main-content");
      if (mainContent) {
        mainContent.scrollTo({
          top: 0,
          left: 0,
          behavior,
        });
      }
    });
  };
  return {
    scrollToTop,
    scrollToElement,
    scrollMainContentToTop,
  };
}
/**
 * Enhanced scroll restoration that preserves scroll position for specific routes
 * and restores to top for others
 */
export function useSmartScrollRestoration(
  options: {
    preserveScrollRoutes?: string[];
    scrollToTopRoutes?: string[];
    defaultBehavior?: "preserve" | "top";
  } = {}
) {
  const location = useLocation();
  const { preserveScrollRoutes = [], scrollToTopRoutes = [], defaultBehavior = "top" } = options;
  useEffect(() => {
    const currentPath = location.pathname;
    // Check if current route should preserve scroll
    const shouldPreserve = preserveScrollRoutes.some(route => currentPath.startsWith(route));
    // Check if current route should scroll to top
    const shouldScrollToTop = scrollToTopRoutes.some(route => currentPath.startsWith(route));
    if (shouldScrollToTop || (defaultBehavior === "top" && !shouldPreserve)) {
      // Small delay to ensure the new content is rendered
      setTimeout(() => {
        window.scrollTo({
          top: 0,
          left: 0,
          behavior: "instant",
        });
      }, 0);
    }
  }, [location.pathname, preserveScrollRoutes, scrollToTopRoutes, defaultBehavior]);
}
/**
 * Hook for handling scroll restoration with hash links
 */
export function useHashScrollRestoration() {
  const location = useLocation();
  useEffect(() => {
    // Handle hash links (e.g., #section-id)
    if (location.hash) {
      const elementId = location.hash.substring(1);
      const element = document.getElementById(elementId);
      if (element) {
        // Small delay to ensure content is rendered
        setTimeout(() => {
          element.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }, 100);
      }
    } else {
      // No hash, scroll to top
      setTimeout(() => {
        window.scrollTo({
          top: 0,
          left: 0,
          behavior: "instant",
        });
      }, 0);
    }
  }, [location.pathname, location.hash]);
}
/**
 * Component that handles both scroll restoration and hash navigation
 */
export function ScrollRestoration({
  preserveScrollRoutes = [],
  behavior = "instant" as ScrollBehavior,
}: {
  preserveScrollRoutes?: string[];
  behavior?: ScrollBehavior;
}) {
  const location = useLocation();

  useEffect(() => {
    const currentPath = location.pathname;

    // Check if current route should preserve scroll position
    const shouldPreserve = preserveScrollRoutes.some(route => currentPath.startsWith(route));

    if (!shouldPreserve) {
      // Handle hash links first
      if (location.hash) {
        const elementId = location.hash.substring(1);
        const element = document.getElementById(elementId);
        if (element) {
          setTimeout(() => {
            element.scrollIntoView({
              behavior,
              block: "start",
            });
          }, 100);
        }
      } else {
        // No hash, scroll to top
        setTimeout(() => {
          window.scrollTo({
            top: 0,
            left: 0,
            behavior,
          });
        }, 0);
      }
    }
  }, [location.pathname, location.hash, preserveScrollRoutes, behavior]);

  return null;
}

export default ScrollToTop;
