
import { User, UserRole } from "@/types";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Card, CardContent } from "@/components/ui/card";
import { Shield, User as UserIcon } from "lucide-react";

interface ProfileHeaderProps {
  user: User;
  riskCount: number;
  incidentCount: number;
}

const ProfileHeader = ({ user, riskCount, incidentCount }: ProfileHeaderProps) => {
  const getRoleLabel = (): string => {
    switch (user.role) {
      case UserRole.ADMIN:
        return "Administrator";
      case UserRole.RISK_OWNER:
        return "Risk Owner";
      case UserRole.STAFF:
        return "Staff";
      case UserRole.BOARD_MEMBER:
        return "Board Member";
      default:
        return user.role;
    }
  };

  // Get user initials for avatar fallback
  const getUserInitials = (): string => {
    if (!user.name) return "U";
    return user.name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <div className="flex flex-col lg:flex-row gap-6 mb-8">
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center flex-grow">
        <Avatar className="h-20 w-20">
          <AvatarImage src={user.avatar} alt={user.name} />
          <AvatarFallback className="text-2xl">{getUserInitials()}</AvatarFallback>
        </Avatar>
        <div>
          <h1 className="text-2xl font-bold">{user.name}</h1>
          <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3 text-muted-foreground mt-1">
            <div className="flex items-center gap-1">
              <UserIcon className="h-4 w-4" />
              <span>{user.email}</span>
            </div>
            <div className="hidden sm:block">•</div>
            <div className="flex items-center gap-1">
              <Shield className="h-4 w-4" />
              <span>{getRoleLabel()}</span>
            </div>
            {user.department && (
              <>
                <div className="hidden sm:block">•</div>
                <span>{user.department}</span>
              </>
            )}
          </div>
        </div>
      </div>

      <Card className="min-w-[240px]">
        <CardContent className="p-4 grid grid-cols-2 gap-4">
          <div className="text-center p-3">
            <p className="text-3xl font-bold">{riskCount}</p>
            <p className="text-xs text-muted-foreground">Risks Owned</p>
          </div>
          <div className="text-center p-3">
            <p className="text-3xl font-bold">{incidentCount}</p>
            <p className="text-xs text-muted-foreground">Incidents Reported</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileHeader;
