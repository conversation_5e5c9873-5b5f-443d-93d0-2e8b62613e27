
import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar, MessageSquare } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { Comment } from "@/types";

interface ProfileCommentsTableProps {
  comments: Comment[];
  loading: boolean;
}

const ProfileCommentsTable = ({ comments, loading }: ProfileCommentsTableProps) => {
  const [filter, setFilter] = useState<"all" | "risk" | "incident">("all");
  
  // Apply filters to comments
  const filteredComments = comments.filter(comment => {
    if (filter === "all") return true;
    return comment.entityType === filter;
  });
  
  // Get user initials for avatar fallback
  const getUserInitials = (name: string): string => {
    if (!name) return "U";
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Comments on Your Items</h3>
          <div className="w-32 h-8 bg-muted rounded animate-pulse" />
        </div>
        {[1, 2, 3].map((i) => (
          <div key={i} className="border rounded-md p-4 space-y-2">
            <div className="w-3/4 h-4 bg-muted rounded animate-pulse" />
            <div className="w-1/2 h-4 bg-muted rounded animate-pulse" />
          </div>
        ))}
      </div>
    );
  }

  if (comments.length === 0) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-medium">Comments on Your Items</h3>
        </div>
        <div className="border rounded-md p-8 text-center">
          <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
          <p className="text-muted-foreground">No comments found on your risks or incidents.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Comments on Your Items</h3>
        <Select
          value={filter}
          onValueChange={(value) => setFilter(value as "all" | "risk" | "incident")}
        >
          <SelectTrigger className="w-36">
            <SelectValue placeholder="Filter by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Comments</SelectItem>
            <SelectItem value="risk">Risk Comments</SelectItem>
            <SelectItem value="incident">Incident Comments</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="border rounded-md overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Comment</TableHead>
              <TableHead>From</TableHead>
              <TableHead>On</TableHead>
              <TableHead>Time</TableHead>
              <TableHead className="text-right">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredComments.map((comment) => (
              <TableRow key={comment.id}>
                <TableCell className="max-w-xs truncate">
                  {comment.content}
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={comment.userAvatar} alt={comment.userName} />
                      <AvatarFallback>
                        {getUserInitials(comment.userName || "")}
                      </AvatarFallback>
                    </Avatar>
                    <span>{comment.userName}</span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Badge variant={comment.entityType === "risk" ? "outline" : "secondary"}>
                      {comment.entityType === "risk" ? "Risk" : "Incident"}
                    </Badge>
                    <span className="truncate max-w-[150px]" title={comment.entityTitle}>
                      {comment.entityTitle}
                    </span>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3 text-muted-foreground" />
                    <span className="text-muted-foreground text-xs">
                      {formatDistanceToNow(comment.createdAt, { addSuffix: true })}
                    </span>
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    asChild
                  >
                    <Link to={`/${comment.entityType}s/${comment.entityId}`}>
                      View {comment.entityType === "risk" ? "Risk" : "Incident"}
                    </Link>
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};

export default ProfileCommentsTable;
