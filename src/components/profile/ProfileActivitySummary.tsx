
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";

interface ProfileActivitySummaryProps {
  riskStats: {
    totalRisks: number;
    byStatus: Record<string, number>;
    bySeverity: Record<string, number>;
  };
  incidentStats: {
    totalIncidents: number;
    byStatus: Record<string, number>;
    bySeverity: Record<string, number>;
  };
}

const ProfileActivitySummary = ({
  riskStats,
  incidentStats,
}: ProfileActivitySummaryProps) => {
  return (
    <div className="grid gap-4 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Risk Ownership</CardTitle>
          <CardDescription>
            Overview of risks you currently own
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium mb-1">By Status</h4>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(riskStats.byStatus).map(([status, count]) => (
                  <div key={status} className="flex justify-between items-center p-2 bg-muted/50 rounded-md">
                    <span className="text-sm">{status}</span>
                    <span className="text-sm font-semibold">{count}</span>
                  </div>
                ))}
                {Object.keys(riskStats.byStatus).length === 0 && (
                  <p className="text-sm text-muted-foreground">No risks assigned</p>
                )}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-1">By Severity</h4>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(riskStats.bySeverity).map(([severity, count]) => (
                  <div key={severity} className="flex justify-between items-center p-2 bg-muted/50 rounded-md">
                    <span className="text-sm">{severity}</span>
                    <span className="text-sm font-semibold">{count}</span>
                  </div>
                ))}
                {Object.keys(riskStats.bySeverity).length === 0 && (
                  <p className="text-sm text-muted-foreground">No risks assigned</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Incident Reporting</CardTitle>
          <CardDescription>
            Overview of incidents you've reported
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium mb-1">By Status</h4>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(incidentStats.byStatus).map(([status, count]) => (
                  <div key={status} className="flex justify-between items-center p-2 bg-muted/50 rounded-md">
                    <span className="text-sm">{status}</span>
                    <span className="text-sm font-semibold">{count}</span>
                  </div>
                ))}
                {Object.keys(incidentStats.byStatus).length === 0 && (
                  <p className="text-sm text-muted-foreground">No incidents reported</p>
                )}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-1">By Severity</h4>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(incidentStats.bySeverity).map(([severity, count]) => (
                  <div key={severity} className="flex justify-between items-center p-2 bg-muted/50 rounded-md">
                    <span className="text-sm">{severity}</span>
                    <span className="text-sm font-semibold">{count}</span>
                  </div>
                ))}
                {Object.keys(incidentStats.bySeverity).length === 0 && (
                  <p className="text-sm text-muted-foreground">No incidents reported</p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ProfileActivitySummary;
