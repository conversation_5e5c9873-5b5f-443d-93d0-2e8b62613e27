
import { CheckCir<PERSON>, AlertTriangle } from "lucide-react";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface FormFieldStatusProps {
  isValid: boolean;
  isInvalid?: boolean;
  showValidation?: boolean;
  validMessage?: string;
  className?: string;
  size?: "sm" | "md" | "lg";
}

export function FormFieldStatus({
  isValid,
  isInvalid = false,
  showValidation = true,
  validMessage = "Looks good!",
  className,
  size = "md"
}: FormFieldStatusProps) {
  // Don't show anything if we're neither valid nor invalid
  if (!isValid && !isInvalid) return null;
  
  // Don't show valid indicators unless showValidation is true
  if (isValid && !showValidation) return null;
  
  const iconSizes = {
    sm: "h-3.5 w-3.5",
    md: "h-4 w-4", 
    lg: "h-5 w-5"
  };
  
  const textSizes = {
    sm: "text-xs",
    md: "text-sm",
    lg: "text-base"
  };

  return (
    <motion.div
      className={cn(
        "flex items-center gap-1.5 mt-1.5 text-sm transition-opacity",
        isValid ? "text-green-600 dark:text-green-500" : "text-red-600 dark:text-red-500",
        textSizes[size],
        className
      )}
      initial={{ opacity: 0, y: -8 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
    >
      {isValid ? (
        <>
          <CheckCircle className={cn("shrink-0", iconSizes[size])} />
          {validMessage && <span>{validMessage}</span>}
        </>
      ) : (
        <>
          <AlertTriangle className={cn("shrink-0", iconSizes[size])} />
          {isInvalid && <span>Please check this field</span>}
        </>
      )}
    </motion.div>
  );
}
