import * as React from "react";
import DOMPurify from "dompurify";
import { cn } from "@/lib/utils";

interface SafeHTMLProps {
  html: string;
  className?: string | undefined;
  allowedTags?: string[];
  allowedAttributes?: string[];
  as?: keyof JSX.IntrinsicElements;
}

/**
 * SafeHTML Component
 *
 * A secure alternative to dangerouslySetInnerHTML that uses DOMPurify
 * to sanitize HTML content before rendering.
 *
 * @param html - The HTML string to sanitize and render
 * @param className - Optional CSS classes to apply
 * @param allowedTags - Array of allowed HTML tags (optional)
 * @param allowedAttributes - Array of allowed HTML attributes (optional)
 * @param as - HTML element type to render as (default: 'div')
 */
export const SafeHTML: React.FC<SafeHTMLProps> = ({
  html,
  className,
  allowedTags,
  allowedAttributes,
  as: Component = "div",
}) => {
  const sanitizedHTML = React.useMemo(() => {
    if (!html) return "";

    // Configure DOMPurify options with comprehensive security settings
    const config: any = {
      // Remove all script tags and event handlers by default
      FORBID_TAGS: ["script", "object", "embed", "form", "input", "iframe", "frame", "frameset"],
      FORBID_ATTR: [
        "onerror",
        "onload",
        "onclick",
        "onmouseover",
        "onfocus",
        "onblur",
        "onsubmit",
        "onchange",
        "onkeydown",
        "onkeyup",
        "onkeypress",
        "onmousedown",
        "onmouseup",
        "onmousemove",
        "onmouseout",
        "onmouseenter",
        "onmouseleave",
      ],
      // Ensure safe parsing
      WHOLE_DOCUMENT: false,
      RETURN_DOM: false,
      RETURN_DOM_FRAGMENT: false,
      SANITIZE_DOM: true,
    };

    // Apply custom allowed tags if provided
    if (allowedTags && allowedTags.length > 0) {
      config.ALLOWED_TAGS = allowedTags;
    }

    // Apply custom allowed attributes if provided
    if (allowedAttributes && allowedAttributes.length > 0) {
      config.ALLOWED_ATTR = allowedAttributes;
    }

    // Sanitize the HTML with proper type casting
    return DOMPurify.sanitize(html, config) as unknown as string;
  }, [html, allowedTags, allowedAttributes]);

  // Use a ref to set textContent safely
  const elementRef = React.useRef<HTMLElement>(null);

  React.useEffect(() => {
    if (elementRef.current && sanitizedHTML) {
      elementRef.current.innerHTML = sanitizedHTML;
    }
  }, [sanitizedHTML]);

  // Return early after all hooks have been called
  if (!html) {
    return null;
  }

  return React.createElement(Component, {
    ref: elementRef,
    className: cn(className),
  });
};

/**
 * SafeHTMLInline Component
 *
 * For inline HTML content with stricter sanitization
 */
export const SafeHTMLInline: React.FC<Omit<SafeHTMLProps, "as">> = ({
  html,
  className,
  allowedTags = ["strong", "em", "u", "br"],
  allowedAttributes = [],
}) => {
  return (
    <SafeHTML
      html={html}
      className={className}
      allowedTags={allowedTags}
      allowedAttributes={allowedAttributes}
      as="span"
    />
  );
};

/**
 * SafeHTMLContent Component
 *
 * For rich content with more permissive sanitization
 */
export const SafeHTMLContent: React.FC<Omit<SafeHTMLProps, "as">> = ({
  html,
  className,
  allowedTags = [
    "p",
    "br",
    "strong",
    "em",
    "u",
    "ol",
    "ul",
    "li",
    "h1",
    "h2",
    "h3",
    "h4",
    "h5",
    "h6",
    "blockquote",
    "a",
    "img",
    "table",
    "thead",
    "tbody",
    "tr",
    "td",
    "th",
  ],
  allowedAttributes = ["href", "src", "alt", "title", "class", "id"],
}) => {
  return (
    <SafeHTML
      html={html}
      className={className}
      allowedTags={allowedTags}
      allowedAttributes={allowedAttributes}
      as="div"
    />
  );
};

/**
 * Utility function to sanitize HTML string without rendering
 */
export const sanitizeHTML = (
  html: string,
  options?: {
    allowedTags?: string[];
    allowedAttributes?: string[];
  }
): string => {
  if (!html) return "";

  const config: any = {
    FORBID_TAGS: ["script", "object", "embed", "form", "input", "iframe", "frame", "frameset"],
    FORBID_ATTR: [
      "onerror",
      "onload",
      "onclick",
      "onmouseover",
      "onfocus",
      "onblur",
      "onsubmit",
      "onchange",
      "onkeydown",
      "onkeyup",
      "onkeypress",
      "onmousedown",
      "onmouseup",
      "onmousemove",
      "onmouseout",
      "onmouseenter",
      "onmouseleave",
    ],
    // Ensure safe parsing
    WHOLE_DOCUMENT: false,
    RETURN_DOM: false,
    RETURN_DOM_FRAGMENT: false,
    SANITIZE_DOM: true,
  };

  if (options?.allowedTags) {
    config.ALLOWED_TAGS = options.allowedTags;
  }

  if (options?.allowedAttributes) {
    config.ALLOWED_ATTR = options.allowedAttributes;
  }

  return DOMPurify.sanitize(html, config) as unknown as string;
};

export default SafeHTML;
