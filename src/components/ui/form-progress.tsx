
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";

interface FormProgressProps {
  steps: Array<{
    id: string;
    label: string;
    isCompleted: boolean;
    isActive?: boolean;
  }>;
  currentStep: number;
  className?: string;
}

export function FormProgress({ steps, className }: FormProgressProps) {
  const completedSteps = steps.filter(step => step.isCompleted).length;
  const progress = Math.round((completedSteps / steps.length) * 100);
  
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between">
        <h4 className="text-sm font-medium text-foreground">Form Progress</h4>
        <span className="text-xs font-medium text-muted-foreground">
          {completedSteps}/{steps.length} completed
        </span>
      </div>
      
      <Progress 
        value={progress} 
        className="h-2"
      />
      
      <div className="flex flex-wrap gap-2 mt-4">
        {steps.map((step, index) => (
          <motion.div 
            key={step.id}
            className={cn(
              "px-3 py-1 rounded-full text-xs font-medium border",
              step.isActive ? "bg-primary text-primary-foreground border-primary" : 
              step.isCompleted ? "bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-400 dark:border-green-800" : 
              "bg-background text-muted-foreground border-input"
            )}
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: index * 0.1, duration: 0.2 }}
          >
            {step.label}
          </motion.div>
        ))}
      </div>
    </div>
  );
}
