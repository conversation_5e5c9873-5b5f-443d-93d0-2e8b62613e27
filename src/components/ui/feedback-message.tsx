
import { AlertCircle, CheckCircle, Info } from "lucide-react";
import { cn } from "@/lib/utils";

type FeedbackType = "success" | "error" | "info" | "warning";

interface FeedbackMessageProps {
  type: FeedbackType;
  message: string;
  className?: string;
}

export function FeedbackMessage({ type, message, className }: FeedbackMessageProps) {
  const iconMap = {
    success: <CheckCircle className="h-4 w-4" />,
    error: <AlertCircle className="h-4 w-4" />,
    info: <Info className="h-4 w-4" />,
    warning: <AlertCircle className="h-4 w-4" />,
  };

  const colorMap = {
    success: "text-green-600 bg-green-50 dark:bg-green-950/30 dark:text-green-400 border-green-100 dark:border-green-900/50",
    error: "text-red-600 bg-red-50 dark:bg-red-950/30 dark:text-red-400 border-red-100 dark:border-red-900/50",
    info: "text-blue-600 bg-blue-50 dark:bg-blue-950/30 dark:text-blue-400 border-blue-100 dark:border-blue-900/50",
    warning: "text-yellow-600 bg-yellow-50 dark:bg-yellow-950/30 dark:text-yellow-400 border-yellow-100 dark:border-yellow-900/50",
  };

  return (
    <div className={cn(
      "flex items-center gap-2 px-3 py-2 rounded-md border text-sm",
      colorMap[type],
      className
    )}>
      {iconMap[type]}
      <span>{message}</span>
    </div>
  );
}
