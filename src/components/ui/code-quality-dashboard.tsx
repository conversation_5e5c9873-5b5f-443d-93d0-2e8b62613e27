/**
 * Code Quality Dashboard Component
 * Displays code quality metrics, trends, and alerts in a comprehensive dashboard
 */
import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./card";
import { Badge } from "./badge";
import { Button } from "./button";
import { Alert, AlertDescription, AlertTitle } from "./alert";
import { Progress } from "./progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./tabs";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts";
import {
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Code,
  TestTube,
  Package,
  Activity,
  RefreshCw,
} from "lucide-react";
import {
  codeQualityMetricsService,
  CodeQualityMetrics,
  QualityTrend,
  QualityAlert,
} from "../../services/codeQualityMetricsService";
interface CodeQualityDashboardProps {
  refreshInterval?: number; // in milliseconds
  showTrends?: boolean;
  showAlerts?: boolean;
}
export const CodeQualityDashboard: React.FC<CodeQualityDashboardProps> = ({
  refreshInterval = 300000, // 5 minutes
  showTrends = true,
  showAlerts = true,
}) => {
  const [currentMetrics, setCurrentMetrics] = useState<CodeQualityMetrics | null>(null);
  const [trends, setTrends] = useState<QualityTrend[]>([]);
  const [alerts, setAlerts] = useState<QualityAlert[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [error, setError] = useState<Error | null>(null);
  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      // Collect fresh metrics
      const metrics = await codeQualityMetricsService.collectMetrics();
      await codeQualityMetricsService.storeMetrics(metrics);
      // Get current data
      setCurrentMetrics(metrics);
      setTrends(codeQualityMetricsService.getQualityTrends(7));
      setAlerts(codeQualityMetricsService.getActiveAlerts());
      setLastUpdated(new Date());
    } catch (error) {
      setError(error instanceof Error ? error : new Error("Unknown error occurred"));
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    loadData();
    const interval = setInterval(loadData, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshInterval]);
  const handleRefresh = () => {
    loadData();
  };
  const handleResolveAlert = (alertId: string) => {
    codeQualityMetricsService.resolveAlert(alertId);
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
  };
  const getQualityScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 80) return "text-yellow-600";
    if (score >= 70) return "text-orange-600";
    return "text-red-600";
  };
  const getQualityScoreBadgeVariant = (score: number) => {
    if (score >= 90) return "default";
    if (score >= 80) return "secondary";
    if (score >= 70) return "outline";
    return "destructive";
  };
  const formatTrendData = (trends: QualityTrend[]) => {
    return trends
      .map(trend => ({
        date: new Date(trend.date).toLocaleDateString(),
        qualityScore: trend.metrics.qualityScore,
        typeScriptErrors: trend.metrics.typeScriptErrors,
        testCoverage: trend.metrics.testCoverage.lines,
        bundleSize: Math.round(trend.metrics.bundleSize.totalSize / 1024), // KB
      }))
      .reverse();
  };
  const coverageData = currentMetrics
    ? [
        { name: "Lines", value: currentMetrics.testCoverage.lines, color: "#8884d8" },
        { name: "Functions", value: currentMetrics.testCoverage.functions, color: "#82ca9d" },
        { name: "Branches", value: currentMetrics.testCoverage.branches, color: "#ffc658" },
        { name: "Statements", value: currentMetrics.testCoverage.statements, color: "#ff7300" },
      ]
    : [];
  if (error) {
    return (
      <Alert variant="destructive" className="mb-4">
        <AlertTriangle className="h-4 w-4 mr-2" />
        <AlertTitle>Error loading metrics</AlertTitle>
        <AlertDescription>{error.message}</AlertDescription>
      </Alert>
    );
  }
  if (loading && !currentMetrics) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading quality metrics...</span>
      </div>
    );
  }
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Code Quality Dashboard</h2>
          <p className="text-muted-foreground">Monitor code quality metrics, trends, and alerts</p>
        </div>
        <div className="flex items-center space-x-2">
          {lastUpdated && (
            <span className="text-sm text-muted-foreground">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </span>
          )}
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>
      {/* Alerts Section */}
      {showAlerts && alerts.length > 0 && (
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">Active Alerts</h3>
          {alerts.map(alert => (
            <Alert key={alert.id} variant={alert.type === "error" ? "destructive" : "default"}>
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle className="flex items-center justify-between">
                {alert.message}
                <Button onClick={() => handleResolveAlert(alert.id)} variant="ghost" size="sm">
                  Resolve
                </Button>
              </AlertTitle>
              <AlertDescription>
                Current: {alert.currentValue} | Threshold: {alert.threshold}
              </AlertDescription>
            </Alert>
          ))}
        </div>
      )}
      {/* Main Metrics Cards */}
      {currentMetrics && (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Quality Score</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div
                className={`text-2xl font-bold ${getQualityScoreColor(currentMetrics.qualityScore)}`}
              >
                {currentMetrics.qualityScore.toFixed(1)}
              </div>
              <Badge variant={getQualityScoreBadgeVariant(currentMetrics.qualityScore)}>
                {currentMetrics.qualityScore >= 90
                  ? "Excellent"
                  : currentMetrics.qualityScore >= 80
                    ? "Good"
                    : currentMetrics.qualityScore >= 70
                      ? "Fair"
                      : "Poor"}
              </Badge>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">TypeScript Errors</CardTitle>
              <Code className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{currentMetrics.typeScriptErrors}</div>
              <div className="flex items-center text-sm text-muted-foreground">
                {currentMetrics.typeScriptErrors === 0 ? (
                  <>
                    <CheckCircle className="h-3 w-3 mr-1 text-green-600" />
                    No errors
                  </>
                ) : (
                  <>
                    <AlertTriangle className="h-3 w-3 mr-1 text-red-600" />
                    Needs attention
                  </>
                )}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Test Coverage</CardTitle>
              <TestTube className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {currentMetrics.testCoverage.lines.toFixed(1)}%
              </div>
              <Progress value={currentMetrics.testCoverage.lines} className="mt-2" />
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Bundle Size</CardTitle>
              <Package className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(currentMetrics.bundleSize.totalSize / 1024)}KB
              </div>
              <div className="text-sm text-muted-foreground">
                {Math.round(currentMetrics.bundleSize.gzippedSize / 1024)}KB gzipped
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      {/* Detailed Metrics */}
      {currentMetrics && (
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="coverage">Coverage</TabsTrigger>
            <TabsTrigger value="complexity">Complexity</TabsTrigger>
            {showTrends && <TabsTrigger value="trends">Trends</TabsTrigger>}
          </TabsList>
          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Code Quality Metrics</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span>TypeScript Errors:</span>
                    <Badge
                      variant={currentMetrics.typeScriptErrors === 0 ? "default" : "destructive"}
                    >
                      {currentMetrics.typeScriptErrors}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>ESLint Warnings:</span>
                    <Badge variant="secondary">{currentMetrics.eslintWarnings}</Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>ESLint Errors:</span>
                    <Badge variant={currentMetrics.eslintErrors === 0 ? "default" : "destructive"}>
                      {currentMetrics.eslintErrors}
                    </Badge>
                  </div>
                  <div className="flex justify-between">
                    <span>Maintainability Index:</span>
                    <Badge variant="outline">
                      {currentMetrics.maintainabilityIndex.toFixed(1)}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Bundle Analysis</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex justify-between">
                    <span>Total Size:</span>
                    <span>{Math.round(currentMetrics.bundleSize.totalSize / 1024)}KB</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Gzipped Size:</span>
                    <span>{Math.round(currentMetrics.bundleSize.gzippedSize / 1024)}KB</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Chunk Count:</span>
                    <span>{currentMetrics.bundleSize.chunkCount}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Duplicate Dependencies:</span>
                    <Badge
                      variant={
                        currentMetrics.bundleSize.duplicateDependencies === 0
                          ? "default"
                          : "destructive"
                      }
                    >
                      {currentMetrics.bundleSize.duplicateDependencies}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="coverage" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>Coverage Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={coverageData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, value }) => `${name}: ${value.toFixed(1)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {coverageData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
              <Card>
                <CardHeader>
                  <CardTitle>Coverage Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {coverageData.map(item => (
                    <div key={item.name} className="space-y-2">
                      <div className="flex justify-between">
                        <span>{item.name}:</span>
                        <span>{item.value.toFixed(1)}%</span>
                      </div>
                      <Progress value={item.value} />
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </TabsContent>
          <TabsContent value="complexity" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Code Complexity Metrics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>Cyclomatic Complexity:</span>
                      <span>{currentMetrics.codeComplexity.cyclomaticComplexity.toFixed(1)}</span>
                    </div>
                    <Progress
                      value={Math.min(100, currentMetrics.codeComplexity.cyclomaticComplexity * 5)}
                    />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>Cognitive Complexity:</span>
                      <span>{currentMetrics.codeComplexity.cognitiveComplexity.toFixed(1)}</span>
                    </div>
                    <Progress
                      value={Math.min(100, currentMetrics.codeComplexity.cognitiveComplexity * 10)}
                    />
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>Lines of Code:</span>
                      <span>{currentMetrics.codeComplexity.linesOfCode.toLocaleString()}</span>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between mb-2">
                      <span>Technical Debt:</span>
                      <span>{currentMetrics.codeComplexity.technicalDebt.toFixed(1)}h</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          {showTrends && (
            <TabsContent value="trends" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Quality Trends (Last 7 Days)</CardTitle>
                  <CardDescription>
                    Track quality metrics over time to identify improvements and regressions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={400}>
                    <LineChart data={formatTrendData(trends)}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Line
                        type="monotone"
                        dataKey="qualityScore"
                        stroke="#8884d8"
                        name="Quality Score"
                      />
                      <Line
                        type="monotone"
                        dataKey="testCoverage"
                        stroke="#82ca9d"
                        name="Test Coverage %"
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Improvements</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {trends.slice(0, 3).map((trend, index) => (
                      <div key={index} className="space-y-2">
                        {trend.improvements.map((improvement, i) => (
                          <div key={i} className="flex items-center text-sm text-green-600">
                            <TrendingUp className="h-3 w-3 mr-2" />
                            {improvement}
                          </div>
                        ))}
                      </div>
                    ))}
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Regressions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {trends.slice(0, 3).map((trend, index) => (
                      <div key={index} className="space-y-2">
                        {trend.regressions.map((regression, i) => (
                          <div key={i} className="flex items-center text-sm text-red-600">
                            <TrendingDown className="h-3 w-3 mr-2" />
                            {regression}
                          </div>
                        ))}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          )}
        </Tabs>
      )}
    </div>
  );
};
