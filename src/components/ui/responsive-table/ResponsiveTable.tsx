import React from "react";
import { useIsMobile } from "@/hooks/use-mobile";
import { Table } from "@/components/ui/table";
import { MobileVirtualList } from "@/components/ui/virtual-list";
import { PullToRefresh } from "@/components/ui/pull-to-refresh";
import { SwipeableCard } from "@/components/ui/swipeable-card";
import { cn } from "@/lib/utils";

interface ResponsiveTableProps<T> {
  data: T[];
  isLoading?: boolean;
  tableHeader: React.ReactNode;
  tableBody: React.ReactNode;
  CardComponent: React.ComponentType<{ item: T; className?: string }>;
  emptyState?: React.ReactNode;
  className?: string;
  // Enhanced mobile features
  enableVirtualization?: boolean;
  enablePullToRefresh?: boolean;
  enableSwipeActions?: boolean;
  onRefresh?: () => Promise<void>;
  itemHeight?: number;
  swipeActions?: {
    left?: Array<{
      id: string;
      label: string;
      icon: React.ReactNode;
      color: "destructive" | "warning" | "success" | "primary";
      action: (item: T) => void;
    }>;
    right?: Array<{
      id: string;
      label: string;
      icon: React.ReactNode;
      color: "destructive" | "warning" | "success" | "primary";
      action: (item: T) => void;
    }>;
  };
}

export function ResponsiveTable<T>({
  data,
  isLoading,
  tableHeader,
  tableBody,
  CardComponent,
  emptyState,
  className,
  enableVirtualization = false,
  enablePullToRefresh = false,
  enableSwipeActions = false,
  onRefresh,
  itemHeight = 120,
  swipeActions,
}: ResponsiveTableProps<T>) {
  const isMobile = useIsMobile();

  if (isLoading) {
    return (
      <div className="space-y-4">
        {isMobile ? (
          // Mobile loading skeleton
          Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="p-4 border border-border rounded-lg bg-card animate-pulse">
              <div className="space-y-3">
                <div className="h-5 bg-muted rounded w-3/4" />
                <div className="flex gap-2">
                  <div className="h-6 bg-muted rounded w-16" />
                  <div className="h-6 bg-muted rounded w-20" />
                </div>
                <div className="h-4 bg-muted rounded w-1/2" />
              </div>
            </div>
          ))
        ) : (
          // Desktop loading skeleton
          <div className="border border-border rounded-lg bg-card">
            <Table>
              {tableHeader}
              <tbody>
                {Array.from({ length: 5 }).map((_, i) => (
                  <tr key={i} className="border-b border-border">
                    {Array.from({ length: 6 }).map((_, j) => (
                      <td key={j} className="p-4">
                        <div className="h-4 bg-muted rounded animate-pulse" />
                      </td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </Table>
          </div>
        )}
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className={cn("border border-border rounded-lg bg-card", className)}>
        {emptyState || (
          <div className="p-8 text-center text-muted-foreground">No data available</div>
        )}
      </div>
    );
  }

  // Enhanced mobile rendering with new features
  if (isMobile) {
    const renderMobileCard = (item: T, index: number) => {
      const cardContent = (
        <CardComponent
          item={item}
          className="border border-border rounded-lg bg-card shadow-sm hover:shadow-md transition-shadow duration-200"
        />
      );

      // Wrap with swipeable functionality if enabled
      if (enableSwipeActions && swipeActions) {
        const leftActions =
          swipeActions.left?.map(action => ({
            ...action,
            action: () => action.action(item),
          })) || [];

        const rightActions =
          swipeActions.right?.map(action => ({
            ...action,
            action: () => action.action(item),
          })) || [];

        return (
          <SwipeableCard
            key={index}
            leftActions={leftActions}
            rightActions={rightActions}
            className="mb-3"
          >
            {cardContent}
          </SwipeableCard>
        );
      }

      return (
        <div key={index} className="mb-3">
          {cardContent}
        </div>
      );
    };

    // Use virtual list for large datasets
    if (enableVirtualization && data.length > 50) {
      const virtualListProps = {
        items: data,
        itemHeight: itemHeight,
        renderItem: renderMobileCard,
        pullToRefresh: enablePullToRefresh,
        ...(className && { className }),
        ...(onRefresh && { onRefresh }),
        ...(typeof isLoading === "boolean" && { loading: isLoading }),
      };

      const content = <MobileVirtualList {...virtualListProps} />;

      return enablePullToRefresh && !enableVirtualization ? (
        <PullToRefresh onRefresh={onRefresh || (() => Promise.resolve())}>{content}</PullToRefresh>
      ) : (
        content
      );
    }

    // Regular mobile list with optional pull-to-refresh
    const mobileList = (
      <div className={cn("space-y-3", className)}>{data.map(renderMobileCard)}</div>
    );

    return enablePullToRefresh ? (
      <PullToRefresh onRefresh={onRefresh || (() => Promise.resolve())}>{mobileList}</PullToRefresh>
    ) : (
      mobileList
    );
  }

  return (
    <div className={cn("border border-border rounded-lg bg-card", className)}>
      <Table>
        {tableHeader}
        {tableBody}
      </Table>
    </div>
  );
}
