
import React from "react";
import { Incident } from "@/types";
import { Button } from "@/components/ui/button";
import { ArrowUpRight, Calendar, User, Link2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useNavigate } from "react-router-dom";
import SeverityBadge from "@/components/incident/SeverityBadge";
import StatusIndicator from "@/components/incident/StatusIndicator";

interface IncidentCardProps {
  item: Incident;
  className?: string;
  onClick?: (incidentId: string) => void;
}

export function IncidentCard({ item: incident, className, onClick }: IncidentCardProps) {
  const navigate = useNavigate();
  const isClosed = incident.status === "Closed" || incident.status === "Resolved";

  const handleCardClick = () => {
    if (onClick) {
      onClick(incident.id);
    } else {
      navigate(`/incidents/${incident.id}`);
    }
  };

  const handleRelatedRiskClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (incident.relatedRiskId) {
      navigate(`/risks/${incident.relatedRiskId}`);
    }
  };

  return (
    <div
      className={cn(
        "p-4 cursor-pointer hover:bg-accent/40 transition-colors duration-200",
        "touch-manipulation", // Better touch performance
        isClosed && "opacity-75",
        className
      )}
      onClick={handleCardClick}
    >
      <div className="space-y-3">
        {/* Primary: Incident Title */}
        <div className="flex items-start justify-between gap-3">
          <h3 className="font-medium text-foreground leading-snug line-clamp-2 flex-1">
            {incident.title}
          </h3>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 shrink-0"
            onClick={(e) => {
              e.stopPropagation();
              handleCardClick();
            }}
          >
            <ArrowUpRight className="h-4 w-4" />
          </Button>
        </div>

        {/* Secondary: Status and Severity Badges */}
        <div className="flex items-center gap-2 flex-wrap">
          <SeverityBadge severity={incident.severity} />
          <StatusIndicator status={incident.status} />
        </div>

        {/* Description */}
        {incident.description && (
          <p className="text-sm text-muted-foreground line-clamp-2">
            {incident.description}
          </p>
        )}

        {/* Tertiary: Metadata */}
        <div className="space-y-2 text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 shrink-0" />
            <span className="truncate">
              Reported by {incident.reporterName}
            </span>
          </div>
          
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-1 min-w-0">
              <Calendar className="h-4 w-4 shrink-0" />
              <span className="text-xs">
                {incident.date.toLocaleDateString()}
              </span>
            </div>
            
            {incident.relatedRiskId && (
              <Button
                variant="ghost"
                size="sm"
                className="h-auto p-1 text-xs text-blue-600 hover:text-blue-800"
                onClick={handleRelatedRiskClick}
              >
                <Link2 className="h-3 w-3 mr-1" />
                View Risk
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
