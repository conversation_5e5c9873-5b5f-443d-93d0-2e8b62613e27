
import { Risk } from "@/types";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, User, Tag } from "lucide-react";

interface RiskCardProps {
  item: Risk;
  className?: string;
}

export const RiskCard = ({ item: risk, className }: RiskCardProps) => {
  const getSeverityColor = (severity: string) => {
    switch (severity?.toLowerCase()) {
      case 'critical': return 'bg-red-500 text-white';
      case 'high': return 'bg-orange-500 text-white';
      case 'medium': return 'bg-yellow-500 text-black';
      case 'low': return 'bg-green-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Identified': return 'bg-blue-500 text-white';
      case 'In Progress': return 'bg-orange-500 text-white';
      case 'Mitigated': return 'bg-green-500 text-white';
      case 'Accepted': return 'bg-yellow-500 text-black';
      case 'Closed': return 'bg-gray-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const formatDate = (dateString: string | Date | null) => {
    if (!dateString) return 'No due date';
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return date.toLocaleDateString();
  };

  const formatStatus = (status: string) => {
    return status ?? 'Unknown';
  };

  return (
    <Card 
      className={`cursor-pointer hover:shadow-md transition-shadow ${className ?? ''}`}
    >
      <CardHeader className="pb-3">
        <CardTitle className="text-base">{risk.title}</CardTitle>
        <div className="flex flex-wrap gap-2">
          <Badge className={getSeverityColor(risk.severity)}>
            {risk.severity ?? 'Unknown'}
          </Badge>
          <Badge className={getStatusColor(risk.status)}>
            {formatStatus(risk.status)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-2 text-sm">
          <div className="flex items-center gap-2">
            <Tag className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">
              {risk.category ?? "Uncategorized"}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-muted-foreground" />
            <span>{risk.ownerName ?? "Unassigned"}</span>
          </div>
          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-muted-foreground" />
            <span className={!risk.dueDate ? 'text-muted-foreground' : ''}>
              {risk.dueDate ? formatDate(risk.dueDate) : 'No due date'}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
