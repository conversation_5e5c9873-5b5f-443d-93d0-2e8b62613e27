import { Card, CardContent } from "@/components/ui/card";
import { WizardProgressIndicator } from "./WizardProgressIndicator";
import { WizardNavigation } from "./WizardNavigation";
import { WizardContainerProps } from "@/types/wizard";

export function WizardContainer({
  steps,
  currentStep,
  canProceed,
  isSubmitting = false,
  onPrevious,
  onNext,
  onCancel,
  nextButtonText,
  validationHintText,
  children,
}: WizardContainerProps) {
  return (
    <div className="max-w-4xl mx-auto space-y-8">
      <WizardProgressIndicator steps={steps} currentStep={currentStep} />

      <Card className="min-h-[500px]">
        <CardContent className="pt-6">{children}</CardContent>
      </Card>

      <WizardNavigation
        currentStep={currentStep}
        totalSteps={steps.length}
        canProceed={canProceed}
        isSubmitting={isSubmitting}
        onPrevious={onPrevious}
        onNext={onNext}
        showValidationHint={!!validationHintText}
        {...(onCancel && { onCancel })}
        {...(nextButtonText && { nextButtonText })}
        {...(validationHintText && { validationHintText })}
      />
    </div>
  );
}
