
import { Check } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { WizardStep } from "@/types/wizard";

interface WizardProgressIndicatorProps {
  steps: WizardStep[];
  currentStep: number;
}

export function WizardProgressIndicator({ steps, currentStep }: WizardProgressIndicatorProps) {
  return (
    <div className="space-y-4">
      {/* Progress indicator */}
      <div className="flex items-center justify-center">
        <div className="flex items-center">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`
                w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium transition-colors
                ${currentStep > step.id ? 'bg-green-500 text-white' : 
                  currentStep === step.id ? 'bg-blue-500 text-white' : 
                  'bg-gray-200 text-gray-600'}
              `}>
                {currentStep > step.id ? <Check className="h-5 w-5" /> : step.id}
              </div>
              {index < steps.length - 1 && (
                <div className={`w-16 h-0.5 mx-2 transition-colors ${
                  currentStep > step.id ? 'bg-green-500' : 'bg-gray-200'
                }`} />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Step info */}
      <div className="text-center">
        <Badge variant="outline" className="mb-2">
          Step {currentStep} of {steps.length}
        </Badge>
        <h2 className="text-2xl font-bold">
          {steps[currentStep - 1]?.title}
        </h2>
        <p className="text-muted-foreground mt-1">
          {steps[currentStep - 1]?.description}
        </p>
      </div>
    </div>
  );
}
