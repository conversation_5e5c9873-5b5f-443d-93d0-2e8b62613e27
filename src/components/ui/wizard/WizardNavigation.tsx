
import { Button } from "@/components/ui/button";
import { ArrowLeft, ArrowRight } from "lucide-react";

interface WizardNavigationProps {
  currentStep: number;
  totalSteps: number;
  canProceed: boolean;
  isSubmitting?: boolean;
  onPrevious: () => void;
  onNext: () => void;
  onCancel?: () => void;
  nextButtonText?: string;
  showValidationHint?: boolean;
  validationHintText?: string;
}

export function WizardNavigation({
  currentStep,
  totalSteps,
  canProceed,
  isSubmitting = false,
  onPrevious,
  onNext,
  onCancel,
  nextButtonText,
  showValidationHint = false,
  validationHintText
}: WizardNavigationProps) {
  const getNextButtonText = () => {
    if (nextButtonText) return nextButtonText;
    if (currentStep === totalSteps) {
      return isSubmitting ? "Submitting..." : "Submit";
    }
    return "Next Step";
  };

  const isFirstStep = currentStep === 1;
  const isLastStep = currentStep === totalSteps;

  return (
    <div className="space-y-4">
      {/* Navigation buttons */}
      <div className="flex justify-between items-center">
        <Button 
          variant="outline" 
          onClick={isFirstStep ? onCancel : onPrevious}
          disabled={isSubmitting}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          {isFirstStep ? 'Cancel' : 'Previous'}
        </Button>
        
        <div className="text-sm text-muted-foreground">
          {isLastStep && (
            <span>Ready to submit</span>
          )}
        </div>
        
        <Button 
          onClick={onNext}
          disabled={!canProceed || isSubmitting}
          className={isLastStep ? "bg-green-600 hover:bg-green-700" : ""}
        >
          {getNextButtonText()}
          {!isLastStep && <ArrowRight className="h-4 w-4 ml-2" />}
        </Button>
      </div>

      {/* Validation hint */}
      {showValidationHint && !canProceed && validationHintText && (
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            {validationHintText}
          </p>
        </div>
      )}
    </div>
  );
}
