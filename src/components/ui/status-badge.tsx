type StatusType =
  | "pending"
  | "approved"
  | "rejected"
  | "success"
  | "error"
  | "warning"
  | "info"
  | "low"
  | "medium"
  | "high"
  | "critical";

const statusColorMap: Record<StatusType, string> = {
  pending: "status-badge",
  approved: "status-badge",
  rejected: "status-badge",
  success: "status-badge",
  error: "status-badge",
  warning: "status-badge",
  info: "status-badge",
  low: "status-badge low",
  medium: "status-badge medium",
  high: "status-badge high",
  critical: "status-badge critical",
};

interface StatusBadgeProps {
  status: string;
  className?: string;
}

export function StatusBadge({ status, className }: StatusBadgeProps) {
  // Handle empty or undefined status
  if (!status || status.trim() === "") {
    return <span className={`status-badge ${className ?? ""}`}>Unknown</span>;
  }

  const normalizedStatus = status.toLowerCase() as StatusType;
  const badgeClass = statusColorMap[normalizedStatus] ?? "status-badge";

  return (
    <span className={`${badgeClass} ${className ?? ""}`}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </span>
  );
}
