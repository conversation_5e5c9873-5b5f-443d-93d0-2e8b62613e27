/**
 * Tests for Code Quality Dashboard Component
 */


import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { CodeQualityDashboard } from '../code-quality-dashboard';
import { codeQualityMetricsService } from '../../../services/codeQualityMetricsService';

// Mock the metrics service
vi.mock('../../../services/codeQualityMetricsService', () => ({
  codeQualityMetricsService: {
    collectMetrics: vi.fn(),
    storeMetrics: vi.fn(),
    getQualityTrends: vi.fn(),
    getActiveAlerts: vi.fn(),
    resolveAlert: vi.fn()
  }
}));

// Mock Recharts components
vi.mock('recharts', () => ({
  LineChart: ({ children }: any) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  ResponsiveContainer: ({ children }: any) => <div data-testid="responsive-container">{children}</div>,
  BarChart: ({ children }: any) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
  PieChart: ({ children }: any) => <div data-testid="pie-chart">{children}</div>,
  Pie: () => <div data-testid="pie" />,
  Cell: () => <div data-testid="cell" />
}));

const mockMetrics = {
  timestamp: '2024-01-01T00:00:00.000Z',
  typeScriptErrors: 0,
  eslintWarnings: 3,
  eslintErrors: 0,
  testCoverage: {
    lines: 85.5,
    functions: 82.3,
    branches: 78.9,
    statements: 86.1,
    threshold: {
      lines: 80,
      functions: 80,
      branches: 75,
      statements: 80
    }
  },
  codeComplexity: {
    cyclomaticComplexity: 12.5,
    cognitiveComplexity: 8.3,
    maintainabilityIndex: 78.2,
    linesOfCode: 15420,
    technicalDebt: 2.5
  },
  maintainabilityIndex: 78.2,
  bundleSize: {
    totalSize: 450 * 1024,
    gzippedSize: 125 * 1024,
    chunkCount: 8,
    duplicateDependencies: 0,
    unusedCode: 5
  },
  qualityScore: 87.5
};

const mockTrends = [
  {
    date: '2024-01-01T00:00:00.000Z',
    metrics: mockMetrics,
    regression: false,
    improvements: ['Test coverage improved by 2.1%'],
    regressions: []
  }
];

const mockAlerts = [
  {
    id: 'test-alert-1',
    type: 'warning' as const,
    severity: 'medium' as const,
    message: 'Test coverage below threshold',
    metric: 'testCoverage',
    threshold: 80,
    currentValue: 75,
    timestamp: '2024-01-01T00:00:00.000Z',
    resolved: false
  }
];

describe('CodeQualityDashboard', () => {
  beforeEach(() => {
    vi.mocked(codeQualityMetricsService.collectMetrics).mockResolvedValue(mockMetrics);
    vi.mocked(codeQualityMetricsService.storeMetrics).mockResolvedValue();
    vi.mocked(codeQualityMetricsService.getQualityTrends).mockReturnValue(mockTrends);
    vi.mocked(codeQualityMetricsService.getActiveAlerts).mockReturnValue([]);
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('should render dashboard title and description', async () => {
    render(<CodeQualityDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Code Quality Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Monitor code quality metrics, trends, and alerts')).toBeInTheDocument();
    });
  });

  it('should display loading state initially', () => {
    render(<CodeQualityDashboard />);
    
    expect(screen.getByText('Loading quality metrics...')).toBeInTheDocument();
  });

  it('should display quality metrics cards after loading', async () => {
    render(<CodeQualityDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Quality Score')).toBeInTheDocument();
      expect(screen.getByText('87.5')).toBeInTheDocument();
      expect(screen.getByText('TypeScript Errors')).toBeInTheDocument();
      expect(screen.getByText('Test Coverage')).toBeInTheDocument();
      expect(screen.getByText('Bundle Size')).toBeInTheDocument();
    });
  });

  it('should display correct quality score badge', async () => {
    render(<CodeQualityDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Good')).toBeInTheDocument();
    });
  });

  it('should display TypeScript errors status correctly', async () => {
    render(<CodeQualityDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('TypeScript Errors')).toBeInTheDocument();
    });
  });

  it('should display test coverage percentage and progress', async () => {
    render(<CodeQualityDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('85.5%')).toBeInTheDocument();
    });
  });

  it('should display bundle size information', async () => {
    render(<CodeQualityDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Bundle Size')).toBeInTheDocument();
    });
  });

  it('should handle refresh button click', async () => {
    render(<CodeQualityDashboard />);
    
    await waitFor(() => {
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      fireEvent.click(refreshButton);
    });
    
    expect(codeQualityMetricsService.collectMetrics).toHaveBeenCalledTimes(2); // Initial load + refresh
  });

  it('should display alerts when present', async () => {
    vi.mocked(codeQualityMetricsService.getActiveAlerts).mockReturnValue(mockAlerts);
    
    render(<CodeQualityDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Active Alerts')).toBeInTheDocument();
      expect(screen.getByText('Test coverage below threshold')).toBeInTheDocument();
    });
  });

  it('should handle alert resolution', async () => {
    vi.mocked(codeQualityMetricsService.getActiveAlerts).mockReturnValue(mockAlerts);
    
    render(<CodeQualityDashboard />);
    
    await waitFor(() => {
      const resolveButton = screen.getByRole('button', { name: /resolve/i });
      fireEvent.click(resolveButton);
    });
    
    expect(codeQualityMetricsService.resolveAlert).toHaveBeenCalledWith('test-alert-1');
  });

  it('should not display alerts section when showAlerts is false', async () => {
    vi.mocked(codeQualityMetricsService.getActiveAlerts).mockReturnValue(mockAlerts);
    
    render(<CodeQualityDashboard showAlerts={false} />);
    
    await waitFor(() => {
      expect(screen.queryByText('Active Alerts')).not.toBeInTheDocument();
    });
  });

  it('should display tabs for different views', async () => {
    render(<CodeQualityDashboard />);
    
    await waitFor(() => {
      expect(screen.getByRole('tab', { name: /overview/i })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: /coverage/i })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: /complexity/i })).toBeInTheDocument();
      expect(screen.getByRole('tab', { name: /trends/i })).toBeInTheDocument();
    });
  });

  it('should have clickable tabs', async () => {
    render(<CodeQualityDashboard />);
    
    await waitFor(() => {
      const coverageTab = screen.getByRole('tab', { name: /coverage/i });
      expect(coverageTab).toBeInTheDocument();
      fireEvent.click(coverageTab);
      // Just verify the tab is clickable, not the content change
    });
  });

  it('should display complexity metrics in complexity tab', async () => {
    render(<CodeQualityDashboard />);
    
    await waitFor(() => {
      expect(screen.getByRole('tab', { name: /complexity/i })).toBeInTheDocument();
    });
  });

  it('should not display trends tab when showTrends is false', async () => {
    render(<CodeQualityDashboard showTrends={false} />);
    
    await waitFor(() => {
      expect(screen.queryByRole('tab', { name: /trends/i })).not.toBeInTheDocument();
    });
  });

  it('should display trends tab when showTrends is true', async () => {
    render(<CodeQualityDashboard showTrends={true} />);
    
    await waitFor(() => {
      expect(screen.getByRole('tab', { name: /trends/i })).toBeInTheDocument();
    });
  });

  it('should handle errors gracefully', async () => {
    vi.mocked(codeQualityMetricsService.collectMetrics).mockRejectedValue(new Error('Test error'));
    
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
    
    render(<CodeQualityDashboard />);
    
    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith('Failed to load quality metrics:', expect.any(Error));
    });
    
    consoleSpy.mockRestore();
  });

  it('should update last updated timestamp', async () => {
    render(<CodeQualityDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText(/Last updated:/)).toBeInTheDocument();
    });
  });

  it('should display correct quality score colors', async () => {
    // Test excellent score (90+)
    const excellentMetrics = { ...mockMetrics, qualityScore: 95 };
    vi.mocked(codeQualityMetricsService.collectMetrics).mockResolvedValue(excellentMetrics);
    
    render(<CodeQualityDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('95.0')).toBeInTheDocument();
      expect(screen.getByText('Excellent')).toBeInTheDocument();
    });
  });

  it('should display poor quality score correctly', async () => {
    // Test poor score (<70)
    const poorMetrics = { ...mockMetrics, qualityScore: 65 };
    vi.mocked(codeQualityMetricsService.collectMetrics).mockResolvedValue(poorMetrics);
    
    render(<CodeQualityDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('65.0')).toBeInTheDocument();
      expect(screen.getByText('Poor')).toBeInTheDocument();
    });
  });
});