import * as React from "react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";

export interface VirtualListProps<T> {
  items: T[];
  itemHeight: number | ((index: number, item: T) => number);
  renderItem: (item: T, index: number) => React.ReactNode;
  className?: string;
  containerHeight?: number;
  overscan?: number;
  onScroll?: (scrollTop: number) => void;
  onEndReached?: () => void;
  endReachedThreshold?: number;
  loading?: boolean;
  loadingComponent?: React.ReactNode;
  emptyComponent?: React.ReactNode;
}

export function VirtualList<T>({
  items,
  itemHeight,
  renderItem,
  className,
  containerHeight = 400,
  overscan = 5,
  onScroll,
  onEndReached,
  endReachedThreshold = 0.8,
  loading = false,
  loadingComponent,
  emptyComponent,
}: VirtualListProps<T>) {
  const isMobile = useIsMobile();
  const containerRef = React.useRef<HTMLDivElement>(null);
  const [scrollTop, setScrollTop] = React.useState(0);
  const [containerRect, setContainerRect] = React.useState({ height: containerHeight });

  // Calculate item heights
  const getItemHeight = React.useCallback(
    (index: number, item: T): number => {
      return typeof itemHeight === "function" ? itemHeight(index, item) : itemHeight;
    },
    [itemHeight]
  );

  // Calculate total height
  const totalHeight = React.useMemo(() => {
    return items.reduce((acc, item, index) => acc + getItemHeight(index, item), 0);
  }, [items, getItemHeight]);

  // Calculate visible range
  const visibleRange = React.useMemo(() => {
    if (items.length === 0) return { start: 0, end: 0 };

    let accumulatedHeight = 0;
    let startIndex = 0;
    let endIndex = 0;

    // Find start index
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (!item) continue;
      const height = getItemHeight(i, item);
      if (accumulatedHeight + height > scrollTop) {
        startIndex = Math.max(0, i - overscan);
        break;
      }
      accumulatedHeight += height;
    }

    // Find end index
    accumulatedHeight = 0;
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (!item) continue;
      const height = getItemHeight(i, item);
      accumulatedHeight += height;
      if (accumulatedHeight > scrollTop + containerRect.height) {
        endIndex = Math.min(items.length - 1, i + overscan);
        break;
      }
    }

    if (endIndex === 0) endIndex = items.length - 1;

    return { start: startIndex, end: endIndex };
  }, [items, scrollTop, containerRect.height, overscan, getItemHeight]);

  // Calculate offset for visible items
  const offsetY = React.useMemo(() => {
    let offset = 0;
    for (let i = 0; i < visibleRange.start; i++) {
      const item = items[i];
      if (item) {
        offset += getItemHeight(i, item);
      }
    }
    return offset;
  }, [visibleRange.start, items, getItemHeight]);

  // Handle scroll
  const handleScroll = React.useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const newScrollTop = e.currentTarget.scrollTop;
      setScrollTop(newScrollTop);
      onScroll?.(newScrollTop);

      // Check if end reached
      if (onEndReached) {
        const scrollHeight = e.currentTarget.scrollHeight;
        const clientHeight = e.currentTarget.clientHeight;
        const scrollRatio = (newScrollTop + clientHeight) / scrollHeight;

        if (scrollRatio >= endReachedThreshold) {
          onEndReached();
        }
      }
    },
    [onScroll, onEndReached, endReachedThreshold]
  );

  // Update container dimensions
  React.useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setContainerRect({ height: rect.height });
      }
    };

    updateDimensions();
    window.addEventListener("resize", updateDimensions);
    return () => window.removeEventListener("resize", updateDimensions);
  }, []);

  // Optimize scroll performance on mobile
  const scrollProps = React.useMemo(() => {
    if (isMobile) {
      return {
        style: {
          WebkitOverflowScrolling: "touch",
          overscrollBehavior: "contain",
        } as React.CSSProperties,
      };
    }
    return {
      // Implementation needed
    };
  }, [isMobile]);

  if (items.length === 0 && !loading) {
    return (
      <div className={cn("flex items-center justify-center p-8", className)}>
        {emptyComponent || (
          <div className="text-center text-muted-foreground">
            <div className="text-4xl mb-2">📋</div>
            <div>No items to display</div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className={cn("overflow-auto", className)}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
      {...scrollProps}
    >
      <div style={{ height: totalHeight, position: "relative" }}>
        <div
          style={{
            transform: `translateY(${offsetY}px)`,
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
          }}
        >
          {items.slice(visibleRange.start, visibleRange.end + 1).map((item, index) => {
            const actualIndex = visibleRange.start + index;
            return (
              <div key={actualIndex} style={{ height: getItemHeight(actualIndex, item) }}>
                {renderItem(item, actualIndex)}
              </div>
            );
          })}
        </div>
      </div>

      {loading && (
        <div className="flex items-center justify-center p-4">
          {loadingComponent || (
            <div className="flex items-center gap-2 text-muted-foreground">
              <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
              <span>Loading...</span>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

// Specialized virtual list for mobile with touch optimizations
export interface MobileVirtualListProps<T>
  extends Omit<VirtualListProps<T>, "overscan" | "containerHeight"> {
  pullToRefresh?: boolean;
  onRefresh?: () => Promise<void>;
  refreshThreshold?: number;
}

export function MobileVirtualList<T>({
  pullToRefresh = false,
  onRefresh,
  refreshThreshold = 80,
  ...props
}: MobileVirtualListProps<T>) {
  const isMobile = useIsMobile();
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  const [pullDistance, setPullDistance] = React.useState(0);
  const startYRef = React.useRef<number>(0);
  const containerRef = React.useRef<HTMLDivElement>(null);

  const handleTouchStart = React.useCallback(
    (e: TouchEvent) => {
      if (!pullToRefresh || !isMobile || !e.touches[0]) return;
      startYRef.current = e.touches[0].clientY;
    },
    [pullToRefresh, isMobile]
  );

  const handleTouchMove = React.useCallback(
    (e: TouchEvent) => {
      if (!pullToRefresh || !isMobile || isRefreshing || !e.touches[0]) return;

      const currentY = e.touches[0].clientY;
      const deltaY = currentY - startYRef.current;
      const container = containerRef.current;

      if (container && container.scrollTop === 0 && deltaY > 0) {
        e.preventDefault();
        const distance = Math.min(deltaY * 0.5, refreshThreshold * 1.5);
        setPullDistance(distance);
      }
    },
    [pullToRefresh, isMobile, isRefreshing, refreshThreshold]
  );

  const handleTouchEnd = React.useCallback(async () => {
    if (!pullToRefresh || !isMobile || isRefreshing) return;

    if (pullDistance >= refreshThreshold && onRefresh) {
      setIsRefreshing(true);
      try {
        await onRefresh();
      } finally {
        setIsRefreshing(false);
        setPullDistance(0);
      }
    } else {
      setPullDistance(0);
    }
  }, [pullToRefresh, isMobile, isRefreshing, pullDistance, refreshThreshold, onRefresh]);

  React.useEffect(() => {
    const container = containerRef.current;
    if (!container || !isMobile) return;

    container.addEventListener("touchstart", handleTouchStart, { passive: false });
    container.addEventListener("touchmove", handleTouchMove, { passive: false });
    container.addEventListener("touchend", handleTouchEnd, { passive: true });

    return () => {
      container.removeEventListener("touchstart", handleTouchStart);
      container.removeEventListener("touchmove", handleTouchMove);
      container.removeEventListener("touchend", handleTouchEnd);
    };
  }, [handleTouchStart, handleTouchMove, handleTouchEnd, isMobile]);

  const containerHeight = React.useMemo(() => {
    // Use viewport height on mobile for better experience
    return isMobile ? window.innerHeight - 120 : 400;
  }, [isMobile]);

  return (
    <div className="relative">
      {/* Pull to refresh indicator */}
      {pullToRefresh && isMobile && (pullDistance > 0 || isRefreshing) && (
        <div
          className="absolute top-0 left-0 right-0 flex items-center justify-center bg-background/95 backdrop-blur-sm border-b z-10 transition-all duration-200"
          style={{
            height: Math.max(pullDistance, isRefreshing ? 60 : 0),
            transform: `translateY(${isRefreshing ? 0 : -60 + pullDistance}px)`,
          }}
        >
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            {isRefreshing ? (
              <>
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                <span>Refreshing...</span>
              </>
            ) : (
              <>
                <div
                  className="h-4 w-4 transition-transform duration-200"
                  style={{
                    transform: `rotate(${Math.min((pullDistance / refreshThreshold) * 180, 180)}deg)`,
                  }}
                >
                  ↓
                </div>
                <span>
                  {pullDistance >= refreshThreshold ? "Release to refresh" : "Pull to refresh"}
                </span>
              </>
            )}
          </div>
        </div>
      )}

      <div
        ref={containerRef}
        style={{
          transform: `translateY(${pullDistance}px)`,
          transition: pullDistance === 0 ? "transform 0.2s ease-out" : "none",
        }}
      >
        <VirtualList
          {...props}
          containerHeight={containerHeight}
          overscan={isMobile ? 3 : 5} // Reduce overscan on mobile
        />
      </div>
    </div>
  );
}
