import { toast } from "@/hooks/use-toast";
import { AlertCircle, CheckCircle, Info, AlertTriangle } from "lucide-react";
import { ToastActionElement } from "@/components/ui/toast";
import { motion } from "framer-motion";

type ToastVariant = "default" | "destructive" | "success" | "warning" | "info";

interface EnhancedToastOptions {
  title: string;
  description?: string;
  variant?: ToastVariant;
  duration?: number;
  action?: ToastActionElement;
}

// Map of icons to use for each variant
const iconMap = {
  default: undefined,
  destructive: <AlertCircle className="h-5 w-5" />,
  success: <CheckCircle className="h-5 w-5" />,
  warning: <AlertTriangle className="h-5 w-5" />,
  info: <Info className="h-5 w-5" />,
};

export function showToast({
  title,
  description,
  variant = "default",
  duration = 5000,
  action,
}: EnhancedToastOptions) {
  const icon = iconMap[variant];

  const toastOptions: any = {
    title,
    description,
    duration,
    variant:
      variant === "success" || variant === "warning" || variant === "info" ? "default" : variant,
    children: icon ? (
      <motion.div
        className="flex items-start gap-3"
        initial={{ opacity: 0, y: -5 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2 }}
      >
        <div className={`text-${getColorForVariant(variant)}`}>{icon}</div>
        <div>
          <h3 className="font-medium">{title}</h3>
          {description && <p className="text-muted-foreground text-sm">{description}</p>}
        </div>
      </motion.div>
    ) : undefined,
  };

  if (action) {
    toastOptions.action = action;
  }

  return toast(toastOptions);
}

// Helper function to get color class for variant
function getColorForVariant(variant: ToastVariant): string {
  switch (variant) {
    case "destructive":
      return "destructive";
    case "success":
      return "green-600 dark:text-green-500";
    case "warning":
      return "yellow-600 dark:text-yellow-500";
    case "info":
      return "blue-600 dark:text-blue-500";
    default:
      return "foreground";
  }
}

// Export convenience methods with improved animations

export const successToast = (options: Omit<EnhancedToastOptions, "variant">) =>
  showToast({ ...options, variant: "success" });

export const errorToast = (options: Omit<EnhancedToastOptions, "variant">) =>
  showToast({ ...options, variant: "destructive" });

export const warningToast = (options: Omit<EnhancedToastOptions, "variant">) =>
  showToast({ ...options, variant: "warning" });

export const infoToast = (options: Omit<EnhancedToastOptions, "variant">) =>
  showToast({ ...options, variant: "info" });
