/**
 * Error Monitoring Dashboard Component
 * Displays real-time error metrics, alerts, and patterns
 */
import React, { useState, useEffect, useCallback } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  Activity,
  AlertCircle,
  BarChart3,
  RefreshCw,
} from "lucide-react";
import {
  errorMonitoringService,
  ErrorMetrics,
  ErrorAlert,
  ErrorPattern,
} from "@/services/errorMonitoringService";
import { ErrorSeverity, ErrorCategory } from "@/types";
interface ErrorMonitoringDashboardProps {
  refreshInterval?: number;
  showPatterns?: boolean;
  showAlerts?: boolean;
  showMetrics?: boolean;
}
export const ErrorMonitoringDashboard: React.FC<ErrorMonitoringDashboardProps> = ({
  refreshInterval = 30000, // 30 seconds
  showPatterns = true,
  showAlerts = true,
  showMetrics = true,
}) => {
  const [metrics, setMetrics] = useState<ErrorMetrics | null>(null);
  const [alerts, setAlerts] = useState<ErrorAlert[]>([]);
  const [patterns, setPatterns] = useState<ErrorPattern[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  // Refresh data
  const refreshData = useCallback(async () => {
    try {
      setIsLoading(true);
      if (showMetrics) {
        setMetrics(errorMonitoringService.getMetrics());
      }
      if (showAlerts) {
        setAlerts(errorMonitoringService.getAlerts(20));
      }
      if (showPatterns) {
        setPatterns(errorMonitoringService.getErrorPatterns());
      }
      setLastUpdate(new Date());
    } catch (error) {
      // Error caught and handled
    } finally {
      setIsLoading(false);
    }
  }, [showMetrics, showAlerts, showPatterns]);
  // Auto-refresh data
  useEffect(() => {
    refreshData();
    const interval = setInterval(refreshData, refreshInterval);
    return () => clearInterval(interval);
  }, [refreshData, refreshInterval]);
  // Resolve alert handler
  const handleResolveAlert = (alertId: string) => {
    if (errorMonitoringService.resolveAlert(alertId)) {
      setAlerts(prev =>
        prev.map(alert => (alert.id === alertId ? { ...alert, resolved: true } : alert))
      );
    }
  };
  // Get severity color
  const getSeverityColor = (severity: ErrorSeverity): string => {
    switch (severity) {
      case ErrorSeverity.LOW:
        return "text-blue-600 bg-blue-50 border-blue-200";
      case ErrorSeverity.MEDIUM:
        return "text-yellow-600 bg-yellow-50 border-yellow-200";
      case ErrorSeverity.HIGH:
        return "text-orange-600 bg-orange-50 border-orange-200";
      case ErrorSeverity.CRITICAL:
        return "text-red-600 bg-red-50 border-red-200";
      default:
        return "text-gray-600 bg-gray-50 border-gray-200";
    }
  };
  // Get category color
  const getCategoryColor = (category: ErrorCategory): string => {
    const colors = {
      [ErrorCategory.VALIDATION]: "bg-blue-100 text-blue-800",
      [ErrorCategory.NETWORK]: "bg-green-100 text-green-800",
      [ErrorCategory.AUTHENTICATION]: "bg-red-100 text-red-800",
      [ErrorCategory.AUTHORIZATION]: "bg-orange-100 text-orange-800",
      [ErrorCategory.BUSINESS_LOGIC]: "bg-purple-100 text-purple-800",
      [ErrorCategory.SYSTEM]: "bg-gray-100 text-gray-800",
      [ErrorCategory.USER_INPUT]: "bg-yellow-100 text-yellow-800",
      [ErrorCategory.DATA_PROCESSING]: "bg-indigo-100 text-indigo-800",
      [ErrorCategory.EXTERNAL_SERVICE]: "bg-pink-100 text-pink-800",
    };
    return colors[category] || "bg-gray-100 text-gray-800";
  };
  if (isLoading && !metrics) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="flex items-center gap-2">
          <RefreshCw className="h-4 w-4 animate-spin" />
          <span>Loading error monitoring data...</span>
        </div>
      </div>
    );
  }
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Error Monitoring</h2>
          <p className="text-muted-foreground">Real-time error tracking and analysis</p>
        </div>
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">
            Last updated: {lastUpdate.toLocaleTimeString()}
          </span>
          <Button variant="outline" size="sm" onClick={refreshData} disabled={isLoading}>
            <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
          </Button>
        </div>
      </div>
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          {showAlerts && <TabsTrigger value="alerts">Alerts</TabsTrigger>}
          {showPatterns && <TabsTrigger value="patterns">Patterns</TabsTrigger>}
          {showMetrics && <TabsTrigger value="metrics">Metrics</TabsTrigger>}
        </TabsList>
        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          {metrics && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Total Errors */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Errors</CardTitle>
                  <Activity className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metrics.totalErrors}</div>
                  <p className="text-xs text-muted-foreground">{metrics.errorRate} errors/min</p>
                </CardContent>
              </Card>
              {/* Critical Errors */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Critical Errors</CardTitle>
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">{metrics.criticalErrors}</div>
                  <p className="text-xs text-muted-foreground">Requires immediate attention</p>
                </CardContent>
              </Card>
              {/* Recoverable Errors */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Recoverable</CardTitle>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {metrics.recoverableErrors}
                  </div>
                  <p className="text-xs text-muted-foreground">Can be automatically retried</p>
                </CardContent>
              </Card>
              {/* Error Rate */}
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
                  <TrendingUp className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{metrics.errorRate}</div>
                  <p className="text-xs text-muted-foreground">Errors per minute</p>
                </CardContent>
              </Card>
            </div>
          )}
          {/* Recent Alerts */}
          {showAlerts && alerts.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertCircle className="h-5 w-5" />
                  Recent Alerts
                </CardTitle>
                <CardDescription>Latest error alerts and notifications</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {alerts.slice(0, 5).map(alert => (
                    <Alert key={alert.id} className={getSeverityColor(alert.severity)}>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertTitle className="flex items-center justify-between">
                        <span>{alert.type.toUpperCase()}</span>
                        <Badge variant={alert.resolved ? "secondary" : "destructive"}>
                          {alert.resolved ? "Resolved" : "Active"}
                        </Badge>
                      </AlertTitle>
                      <AlertDescription>
                        {alert.message}
                        <div className="text-xs text-muted-foreground mt-1">
                          {alert.timestamp.toLocaleString()}
                        </div>
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
        {/* Alerts Tab */}
        {showAlerts && (
          <TabsContent value="alerts" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Error Alerts</CardTitle>
                <CardDescription>Manage and resolve error alerts</CardDescription>
              </CardHeader>
              <CardContent>
                {alerts.length === 0 ? (
                  <div className="text-center py-8">
                    <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                    <h3 className="text-lg font-medium">No Active Alerts</h3>
                    <p className="text-muted-foreground">All systems are running smoothly</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {alerts.map(alert => (
                      <div
                        key={alert.id}
                        className={`border rounded-lg p-4 ${getSeverityColor(alert.severity)}`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant="outline">{alert.type.toUpperCase()}</Badge>
                              <Badge variant={alert.resolved ? "secondary" : "destructive"}>
                                {alert.resolved ? "Resolved" : "Active"}
                              </Badge>
                              <span className="text-sm text-muted-foreground">
                                {alert.severity.toUpperCase()}
                              </span>
                            </div>
                            <p className="font-medium mb-1">{alert.message}</p>
                            <div className="text-sm text-muted-foreground">
                              <div>Component: {alert.context.component}</div>
                              <div>Time: {alert.timestamp.toLocaleString()}</div>
                              {alert.threshold && alert.currentValue && (
                                <div>
                                  Threshold: {alert.currentValue}/{alert.threshold}
                                </div>
                              )}
                            </div>
                          </div>
                          {!alert.resolved && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleResolveAlert(alert.id)}
                            >
                              Resolve
                            </Button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        )}
        {/* Patterns Tab */}
        {showPatterns && (
          <TabsContent value="patterns" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Error Patterns</CardTitle>
                <CardDescription>Recurring error patterns and trends</CardDescription>
              </CardHeader>
              <CardContent>
                {patterns.length === 0 ? (
                  <div className="text-center py-8">
                    <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <h3 className="text-lg font-medium">No Patterns Detected</h3>
                    <p className="text-muted-foreground">No recurring error patterns found</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {patterns.map(pattern => (
                      <div key={pattern.pattern} className="border rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium">{pattern.pattern}</h4>
                          <Badge className={getSeverityColor(pattern.severity)}>
                            {pattern.severity.toUpperCase()}
                          </Badge>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">Count:</span>
                            <div className="font-medium">{pattern.count}</div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">First Seen:</span>
                            <div className="font-medium">
                              {pattern.firstSeen.toLocaleDateString()}
                            </div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Last Seen:</span>
                            <div className="font-medium">
                              {pattern.lastSeen.toLocaleDateString()}
                            </div>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Components:</span>
                            <div className="font-medium">{pattern.components.join(", ")}</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        )}
        {/* Metrics Tab */}
        {showMetrics && metrics && (
          <TabsContent value="metrics" className="space-y-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Errors by Category */}
              <Card>
                <CardHeader>
                  <CardTitle>Errors by Category</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(metrics.errorsByCategory).map(([category, count]) => (
                      <div key={category} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge className={getCategoryColor(category as ErrorCategory)}>
                            {category}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{count}</span>
                          <div className="w-20">
                            <Progress value={(count / metrics.totalErrors) * 100} className="h-2" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
              {/* Errors by Severity */}
              <Card>
                <CardHeader>
                  <CardTitle>Errors by Severity</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(metrics.errorsBySeverity).map(([severity, count]) => (
                      <div key={severity} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge className={getSeverityColor(severity as ErrorSeverity)}>
                            {severity}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{count}</span>
                          <div className="w-20">
                            <Progress value={(count / metrics.totalErrors) * 100} className="h-2" />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
              {/* Top Components */}
              <Card>
                <CardHeader>
                  <CardTitle>Top Error Components</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(metrics.errorsByComponent)
                      .sort(([, a], [, b]) => b - a)
                      .slice(0, 10)
                      .map(([component, count]) => (
                        <div key={component} className="flex items-center justify-between">
                          <span className="font-medium">{component}</span>
                          <div className="flex items-center gap-2">
                            <span>{count}</span>
                            <div className="w-20">
                              <Progress
                                value={(count / metrics.totalErrors) * 100}
                                className="h-2"
                              />
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                </CardContent>
              </Card>
              {/* System Health */}
              <Card>
                <CardHeader>
                  <CardTitle>System Health</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span>Error Rate</span>
                      <span className="font-medium">{metrics.errorRate}/min</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Recovery Rate</span>
                      <span className="font-medium">
                        {metrics.totalErrors > 0
                          ? Math.round((metrics.recoverableErrors / metrics.totalErrors) * 100)
                          : 0}
                        %
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Critical Error Rate</span>
                      <span className="font-medium text-red-600">
                        {metrics.totalErrors > 0
                          ? Math.round((metrics.criticalErrors / metrics.totalErrors) * 100)
                          : 0}
                        %
                      </span>
                    </div>
                    {metrics.lastErrorTime && (
                      <div className="flex items-center justify-between">
                        <span>Last Error</span>
                        <span className="font-medium">
                          {metrics.lastErrorTime.toLocaleString()}
                        </span>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
};
export default ErrorMonitoringDashboard;
