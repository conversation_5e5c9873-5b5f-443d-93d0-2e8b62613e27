import * as React from "react";
import { cn } from "@/lib/utils";
import { useIsMobile } from "@/hooks/use-mobile";
import { MobileButton } from "@/components/ui/mobile-button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ChevronDown, ChevronUp } from "lucide-react";

export interface MobileFormField {
  id: string;
  type:
    | "text"
    | "email"
    | "password"
    | "number"
    | "tel"
    | "url"
    | "textarea"
    | "select"
    | "checkbox"
    | "radio"
    | "date"
    | "time";
  label: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  value?: unknown;
  options?: Array<{ value: string; label: string }>;
  validation?: {
    pattern?: RegExp;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    custom?: (value: Record<string, unknown>) => string | null;
  };
  description?: string;
  autoComplete?: string;
  inputMode?: "none" | "text" | "decimal" | "numeric" | "tel" | "search" | "email" | "url";
}

export interface MobileFormSection {
  id: string;
  title: string;
  description?: string;
  fields: MobileFormField[];
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

export interface MobileFormProps {
  sections: MobileFormSection[];
  onSubmit: (data: Record<string, unknown>) => void | Promise<void>;
  onCancel?: () => void;
  submitText?: string;
  cancelText?: string;
  loading?: boolean;
  className?: string;
  stickyActions?: boolean;
  autoSave?: boolean;
  autoSaveDelay?: number;
  onAutoSave?: (data: Record<string, unknown>) => void;
}

export function MobileForm({
  sections,
  onSubmit,
  onCancel,
  submitText = "Submit",
  cancelText = "Cancel",
  loading = false,
  className,
  stickyActions = true,
  autoSave = false,
  autoSaveDelay = 2000,
  onAutoSave,
}: MobileFormProps) {
  const isMobile = useIsMobile();
  const [formData, setFormData] = React.useState<Record<string, unknown>>({});
  const [errors, setErrors] = React.useState<Record<string, string>>({});
  const [expandedSections, setExpandedSections] = React.useState<Set<string>>(
    new Set(sections.filter(s => s.defaultExpanded !== false).map(s => s.id))
  );
  const [focusedField, setFocusedField] = React.useState<string | null>(null);
  const autoSaveTimeoutRef = React.useRef<NodeJS.Timeout>();

  // Initialize form data
  React.useEffect(() => {
    const initialData: Record<string, unknown> = {
      // Implementation needed
    };
    sections.forEach(section => {
      section.fields.forEach(field => {
        if (field.value !== undefined) {
          initialData[field.id] = field.value;
        }
      });
    });
    setFormData(initialData);
  }, [sections]);

  // Auto-save functionality
  React.useEffect(() => {
    if (!autoSave || !onAutoSave) return;

    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    autoSaveTimeoutRef.current = setTimeout(() => {
      onAutoSave(formData);
    }, autoSaveDelay);

    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, [formData, autoSave, onAutoSave, autoSaveDelay]);

  const validateField = React.useCallback(
    (field: MobileFormField, value: unknown): string | null => {
      if (field.required && (!value || value === "")) {
        return `${field.label} is required`;
      }

      if (field.validation) {
        const { pattern, minLength, maxLength, min, max, custom } = field.validation;

        if (pattern && value && typeof value === "string" && !pattern.test(value)) {
          return `${field.label} format is invalid`;
        }

        if (minLength && typeof value === "string" && value.length < minLength) {
          return `${field.label} must be at least ${minLength} characters`;
        }

        if (maxLength && typeof value === "string" && value.length > maxLength) {
          return `${field.label} must be no more than ${maxLength} characters`;
        }

        if (min !== undefined && value !== undefined && Number(value) < min) {
          return `${field.label} must be at least ${min}`;
        }

        if (max !== undefined && value !== undefined && Number(value) > max) {
          return `${field.label} must be no more than ${max}`;
        }

        if (custom) {
          const customError = custom({ [field.id]: value });
          if (customError) return customError;
        }
      }

      return null;
    },
    []
  );

  const handleFieldChange = React.useCallback(
    (fieldId: string, value: unknown) => {
      setFormData(prev => ({ ...prev, [fieldId]: value }));

      // Clear error when user starts typing
      if (errors[fieldId]) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors[fieldId];
          return newErrors;
        });
      }
    },
    [errors]
  );

  const handleSubmit = React.useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      // Validate all fields
      const newErrors: Record<string, string> = {
        // Implementation needed
      };
      sections.forEach(section => {
        section.fields.forEach(field => {
          const error = validateField(field, formData[field.id]);
          if (error) {
            newErrors[field.id] = error;
          }
        });
      });

      setErrors(newErrors);

      if (Object.keys(newErrors).length === 0) {
        await onSubmit(formData);
      }
    },
    [formData, sections, validateField, onSubmit]
  );

  const toggleSection = React.useCallback((sectionId: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sectionId)) {
        newSet.delete(sectionId);
      } else {
        newSet.add(sectionId);
      }
      return newSet;
    });
  }, []);

  const renderField = React.useCallback(
    (field: MobileFormField) => {
      const value = formData[field.id] || "";
      const stringValue = typeof value === "string" ? value : "";
      const error = errors[field.id];
      const isFocused = focusedField === field.id;

      const commonProps = {
        id: field.id,
        disabled: field.disabled || loading,
        required: field.required,
        autoComplete: field.autoComplete,
        onFocus: () => setFocusedField(field.id),
        onBlur: () => setFocusedField(null),
        className: cn(
          "transition-all duration-200",
          isMobile && "h-12 text-base", // Larger touch targets on mobile
          error && "border-destructive focus:border-destructive",
          isFocused && "ring-2 ring-primary/20"
        ),
      };

      const renderInput = () => {
        switch (field.type) {
          case "textarea":
            return (
              <Textarea
                {...commonProps}
                placeholder={field.placeholder}
                value={stringValue}
                onChange={e => handleFieldChange(field.id, e.target.value)}
                rows={isMobile ? 4 : 3}
              />
            );

          case "select":
            return (
              <Select
                value={stringValue}
                onValueChange={newValue => handleFieldChange(field.id, newValue)}
                disabled={field.disabled || loading}
              >
                <SelectTrigger className={commonProps.className}>
                  <SelectValue placeholder={field.placeholder} />
                </SelectTrigger>
                <SelectContent>
                  {field.options?.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            );

          case "checkbox":
            return (
              <div className="flex items-center space-x-2">
                <Checkbox
                  id={field.id}
                  checked={!!value}
                  onCheckedChange={checked => handleFieldChange(field.id, checked)}
                  disabled={field.disabled || loading}
                />
                <Label htmlFor={field.id} className="text-sm font-normal">
                  {field.label}
                </Label>
              </div>
            );

          case "radio":
            return (
              <RadioGroup
                value={stringValue}
                onValueChange={newValue => handleFieldChange(field.id, newValue)}
                disabled={field.disabled || loading}
              >
                {field.options?.map(option => (
                  <div key={option.value} className="flex items-center space-x-2">
                    <RadioGroupItem value={option.value} id={`${field.id}-${option.value}`} />
                    <Label htmlFor={`${field.id}-${option.value}`} className="text-sm font-normal">
                      {option.label}
                    </Label>
                  </div>
                ))}
              </RadioGroup>
            );

          default:
            return (
              <Input
                {...commonProps}
                type={field.type}
                placeholder={field.placeholder}
                value={stringValue}
                onChange={e => handleFieldChange(field.id, e.target.value)}
                inputMode={field.inputMode}
              />
            );
        }
      };

      if (field.type === "checkbox") {
        return (
          <div key={field.id} className="space-y-2">
            {renderInput()}
            {field.description && (
              <p className="text-xs text-muted-foreground">{field.description}</p>
            )}
            {error && <p className="text-xs text-destructive">{error}</p>}
          </div>
        );
      }

      return (
        <div key={field.id} className="space-y-2">
          <Label
            htmlFor={field.id}
            className={cn(
              "text-sm font-medium",
              field.required && "after:content-['*'] after:text-destructive after:ml-1"
            )}
          >
            {field.label}
          </Label>
          {renderInput()}
          {field.description && (
            <p className="text-xs text-muted-foreground">{field.description}</p>
          )}
          {error && <p className="text-xs text-destructive">{error}</p>}
        </div>
      );
    },
    [formData, errors, focusedField, loading, isMobile, handleFieldChange]
  );

  const actionButtons = (
    <div className={cn("flex gap-3", isMobile ? "flex-col" : "flex-row justify-end")}>
      {onCancel && (
        <MobileButton
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={loading}
          size={isMobile ? "mobile-full" : "default"}
        >
          {cancelText}
        </MobileButton>
      )}
      <MobileButton
        type="submit"
        loading={loading}
        size={isMobile ? "mobile-full" : "default"}
        hapticFeedback
      >
        {submitText}
      </MobileButton>
    </div>
  );

  return (
    <form onSubmit={handleSubmit} className={cn("space-y-6", className)}>
      {sections.map(section => {
        const isExpanded = expandedSections.has(section.id);

        return (
          <Card key={section.id}>
            <CardHeader
              className={cn(section.collapsible && "cursor-pointer select-none", "pb-4")}
              onClick={section.collapsible ? () => toggleSection(section.id) : undefined}
            >
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg">{section.title}</CardTitle>
                  {section.description && (
                    <p className="text-sm text-muted-foreground mt-1">{section.description}</p>
                  )}
                </div>
                {section.collapsible && (
                  <div className="ml-4">
                    {isExpanded ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </div>
                )}
              </div>
            </CardHeader>

            {(!section.collapsible || isExpanded) && (
              <CardContent className="space-y-4">{section.fields.map(renderField)}</CardContent>
            )}
          </Card>
        );
      })}

      {/* Action buttons */}
      <div
        className={cn(
          stickyActions &&
            isMobile &&
            "sticky bottom-0 bg-background/95 backdrop-blur-sm border-t p-4 -mx-4 -mb-6"
        )}
      >
        {actionButtons}
      </div>
    </form>
  );
}
