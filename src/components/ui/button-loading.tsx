
import { Button, ButtonProps } from "@/components/ui/button";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { cn } from "@/lib/utils";

interface ButtonLoadingProps extends ButtonProps {
  loading?: boolean;
  loadingText?: string;
}

export const ButtonLoading = ({ 
  loading = false, 
  loadingText,
  children, 
  disabled,
  className,
  ...props 
}: ButtonLoadingProps) => {
  return (
    <Button
      disabled={disabled || loading}
      className={cn(className)}
      {...props}
    >
      {loading ? (
        <div className="flex items-center gap-2">
          <LoadingSpinner size="sm" />
          {loadingText ?? "Loading..."}
        </div>
      ) : (
        children
      )}
    </Button>
  );
};
