import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";
import { useTouchGestures } from "@/hooks/use-touch-gestures";
import { useIsMobile } from "@/hooks/use-mobile";

const mobileButtonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow hover:bg-primary/90 active:bg-primary/80",
        destructive:
          "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90 active:bg-destructive/80",
        outline:
          "border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground active:bg-accent/80",
        secondary:
          "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 active:bg-secondary/70",
        ghost: "hover:bg-accent hover:text-accent-foreground active:bg-accent/80",
        link: "text-primary underline-offset-4 hover:underline active:text-primary/80",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        xl: "h-12 rounded-lg px-6 text-base", // Mobile-optimized large size
        icon: "h-9 w-9",
        "mobile-touch": "h-12 min-w-12 px-6 text-base", // Optimized for touch
        "mobile-full": "h-12 w-full px-6 text-base", // Full-width mobile button
      },
      touchFeedback: {
        none: "",
        subtle: "active:scale-[0.98] transition-transform duration-75",
        strong: "active:scale-95 transition-transform duration-100",
        bounce: "active:scale-95 hover:scale-[1.02] transition-transform duration-150",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      touchFeedback: "subtle",
    },
  }
);

export interface MobileButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof mobileButtonVariants> {
  asChild?: boolean;
  loading?: boolean;
  hapticFeedback?: boolean;
  longPressAction?: () => void;
  longPressDelay?: number;
}

const MobileButton = React.forwardRef<HTMLButtonElement, MobileButtonProps>(
  (
    {
      className,
      variant,
      size,
      touchFeedback,
      asChild = false,
      loading = false,
      hapticFeedback = false,
      longPressAction,
      longPressDelay = 500,
      onClick,
      disabled,
      children,
      ...props
    },
    ref
  ) => {
    const isMobile = useIsMobile();
    const [isPressed, setIsPressed] = React.useState(false);
    const [isLongPressed, setIsLongPressed] = React.useState(false);

    // Provide haptic feedback if supported and enabled
    const triggerHapticFeedback = React.useCallback(() => {
      if (hapticFeedback && "vibrate" in navigator && isMobile) {
        navigator.vibrate(10); // Short vibration
      }
    }, [hapticFeedback, isMobile]);

    // Enhanced touch gestures for mobile
    const gestureHandlers = useTouchGestures({
      onTap: point => {
        if (!disabled && !loading && onClick) {
          triggerHapticFeedback();
          const syntheticEvent = new MouseEvent("click", {
            clientX: point.x,
            clientY: point.y,
            bubbles: true,
            cancelable: true,
          }) as any;
          onClick(syntheticEvent);
        }
      },
      onLongPress: () => {
        if (!disabled && !loading && longPressAction) {
          setIsLongPressed(true);
          triggerHapticFeedback();
          longPressAction();
          // Reset long press state after a short delay
          setTimeout(() => setIsLongPressed(false), 200);
        }
      },
      longPressDelay,
    });

    // Auto-adjust size for mobile if not explicitly set
    const effectiveSize = React.useMemo(() => {
      if (size) return size;
      return isMobile ? "mobile-touch" : "default";
    }, [size, isMobile]);

    // Auto-adjust touch feedback for mobile
    const effectiveTouchFeedback = React.useMemo(() => {
      if (touchFeedback) return touchFeedback;
      return isMobile ? "subtle" : "none";
    }, [touchFeedback, isMobile]);

    const Comp = asChild ? Slot : "button";

    const buttonProps = {
      className: cn(
        mobileButtonVariants({
          variant,
          size: effectiveSize,
          touchFeedback: effectiveTouchFeedback,
          className,
        }),
        // Additional mobile-specific classes
        isMobile && [
          "touch-manipulation", // Optimize for touch
          "select-none", // Prevent text selection on touch
          "tap-highlight-transparent", // Remove tap highlight
        ],
        isPressed && "scale-95",
        isLongPressed && "ring-2 ring-primary/50",
        loading && "cursor-wait opacity-70"
      ),
      disabled: disabled || loading,
      ref,
      ...props,
      // Add gesture handlers for mobile
      ...(isMobile ? gestureHandlers : {}),
      // Handle mouse events for desktop
      ...(!isMobile && {
        onClick: (e: React.MouseEvent<HTMLButtonElement>) => {
          if (!disabled && !loading && onClick) {
            triggerHapticFeedback();
            onClick(e);
          }
        },
        onMouseDown: () => setIsPressed(true),
        onMouseUp: () => setIsPressed(false),
        onMouseLeave: () => setIsPressed(false),
      }),
    };

    return (
      <Comp {...buttonProps}>
        {loading ? (
          <div className="flex items-center gap-2">
            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            {typeof children === "string" ? "Loading..." : children}
          </div>
        ) : (
          children
        )}
      </Comp>
    );
  }
);

MobileButton.displayName = "MobileButton";

export { MobileButton, mobileButtonVariants };

// Specialized mobile button components
export const MobilePrimaryButton = React.forwardRef<HTMLButtonElement, MobileButtonProps>(
  (props, ref) => (
    <MobileButton
      ref={ref}
      variant="default"
      size="mobile-full"
      touchFeedback="bounce"
      hapticFeedback
      {...props}
    />
  )
);

export const MobileSecondaryButton = React.forwardRef<HTMLButtonElement, MobileButtonProps>(
  (props, ref) => (
    <MobileButton
      ref={ref}
      variant="outline"
      size="mobile-full"
      touchFeedback="subtle"
      {...props}
    />
  )
);

export const MobileIconButton = React.forwardRef<HTMLButtonElement, MobileButtonProps>(
  (props, ref) => (
    <MobileButton
      ref={ref}
      variant="ghost"
      size="mobile-touch"
      touchFeedback="strong"
      hapticFeedback
      {...props}
    />
  )
);

MobilePrimaryButton.displayName = "MobilePrimaryButton";
MobileSecondaryButton.displayName = "MobileSecondaryButton";
MobileIconButton.displayName = "MobileIconButton";
