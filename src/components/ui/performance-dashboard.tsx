/**
 * Performance Dashboard Component
 * Displays comprehensive performance metrics and validation results
 */
import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./card";
import { Badge } from "./badge";
import { Progress } from "./progress";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "./tabs";
import { Alert, AlertDescription, AlertTitle } from "./alert";
import { Button } from "./button";
import {
  NetworkConditionTester,
  formatPerformanceMetrics,
  getPerformanceGrade,
} from "../../utils/network-condition-tester";
import { useMemoryMonitor, formatMemorySize } from "../../utils/memory-usage-monitor";
import {
  Activity,
  Zap,
  Wifi,
  Monitor,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  RefreshCw,
} from "lucide-react";
interface PerformanceValidationReport {
  timestamp: string;
  bundleSizeReduction: {
    baseline: { total: number };
    current: number;
    reduction: number;
    targetMet: boolean;
  };
  webVitals: {
    firstContentfulPaint: { target: number; results: any[] };
    largestContentfulPaint: { target: number; results: any[] };
    timeToInteractive: { target: number; results: any[] };
    cumulativeLayoutShift: { target: number; results: any[] };
  };
  memoryUsage: {
    estimatedUsage: number;
    threshold: number;
    withinThreshold: boolean;
    leakChecks: Array<{
      name: string;
      passed: boolean;
      message: string;
    }>;
  };
  summary: {
    bundleSizeTargetMet: boolean;
    fcpTargetMet: boolean;
    lcpTargetMet: boolean;
    memoryTargetMet: boolean;
    overallScore: number;
  };
}
export function PerformanceDashboard() {
  const [validationReport, setValidationReport] = useState<PerformanceValidationReport | null>(
    null
  );
  const [networkReport, setNetworkReport] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const { memoryStats, startMonitoring, stopMonitoring, takeSnapshot, forceGC } =
    useMemoryMonitor();
  // Load performance validation report
  const loadValidationReport = async () => {
    try {
      const response = await fetch("/performance-validation-report.json");
      if (response.ok) {
        const report = await response.json();
        setValidationReport(report);
        setLastUpdated(new Date(report.timestamp));
      }
    } catch (error) {
      // Error caught and handled
    }
  };
  // Generate network performance report
  const generateNetworkReport = () => {
    const bundleSize = validationReport?.bundleSizeReduction.current || 107132;
    const tester = new NetworkConditionTester(bundleSize);
    const report = tester.generateNetworkPerformanceReport();
    setNetworkReport(report);
  };
  // Refresh all performance data
  const refreshPerformanceData = async () => {
    setIsLoading(true);
    try {
      await loadValidationReport();
      generateNetworkReport();
      takeSnapshot();
    } finally {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    loadValidationReport();
    startMonitoring();
    return () => {
      stopMonitoring();
    };
  }, [startMonitoring, stopMonitoring]);
  useEffect(() => {
    if (validationReport) {
      generateNetworkReport();
    }
  }, [validationReport, generateNetworkReport]);
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return "0 B";
    const k = 1024;
    const sizes = ["B", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };
  const getScoreColor = (score: number) => {
    if (score >= 90) return "text-green-600";
    if (score >= 75) return "text-yellow-600";
    if (score >= 50) return "text-orange-600";
    return "text-red-600";
  };
  const getScoreBadgeVariant = (
    score: number
  ): "default" | "secondary" | "destructive" | "outline" => {
    if (score >= 90) return "default";
    if (score >= 75) return "secondary";
    if (score >= 50) return "outline";
    return "destructive";
  };
  if (!validationReport) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Performance Dashboard
          </CardTitle>
          <CardDescription>Loading performance validation data...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <RefreshCw className="h-8 w-8 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Performance Dashboard</h1>
          <p className="text-muted-foreground">
            Comprehensive performance metrics and validation results
          </p>
        </div>
        <Button
          onClick={refreshPerformanceData}
          disabled={isLoading}
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? "animate-spin" : ""}`} />
          Refresh Data
        </Button>
      </div>
      {/* Overall Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Overall Performance Score
          </CardTitle>
          <CardDescription>
            Last updated: {lastUpdated?.toLocaleString() || "Unknown"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="text-4xl font-bold">
              <span className={getScoreColor(validationReport.summary.overallScore)}>
                {validationReport.summary.overallScore}
              </span>
              <span className="text-2xl text-muted-foreground">/100</span>
            </div>
            <div className="flex-1">
              <Progress value={validationReport.summary.overallScore} className="h-3" />
            </div>
            <Badge variant={getScoreBadgeVariant(validationReport.summary.overallScore)}>
              {validationReport.summary.overallScore >= 90
                ? "Excellent"
                : validationReport.summary.overallScore >= 75
                  ? "Good"
                  : validationReport.summary.overallScore >= 50
                    ? "Fair"
                    : "Poor"}
            </Badge>
          </div>
        </CardContent>
      </Card>
      {/* Performance Metrics Tabs */}
      <Tabs defaultValue="bundle" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="bundle">Bundle Analysis</TabsTrigger>
          <TabsTrigger value="network">Network Performance</TabsTrigger>
          <TabsTrigger value="memory">Memory Usage</TabsTrigger>
          <TabsTrigger value="vitals">Web Vitals</TabsTrigger>
        </TabsList>
        {/* Bundle Analysis Tab */}
        <TabsContent value="bundle" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Current Bundle Size</CardTitle>
                <Monitor className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {formatBytes(validationReport.bundleSizeReduction.current)}
                </div>
                <p className="text-xs text-muted-foreground">
                  Target: {formatBytes(validationReport.bundleSizeReduction.baseline.total)}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Size Reduction</CardTitle>
                {validationReport.bundleSizeReduction.reduction > 0 ? (
                  <TrendingDown className="h-4 w-4 text-green-600" />
                ) : (
                  <TrendingUp className="h-4 w-4 text-red-600" />
                )}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(validationReport.bundleSizeReduction.reduction * 100).toFixed(1)}%
                </div>
                <p className="text-xs text-muted-foreground">Target: 30% reduction</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Bundle Status</CardTitle>
                {validationReport.summary.bundleSizeTargetMet ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                )}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {validationReport.summary.bundleSizeTargetMet ? "PASS" : "FAIL"}
                </div>
                <p className="text-xs text-muted-foreground">Size optimization target</p>
              </CardContent>
            </Card>
          </div>
          {!validationReport.summary.bundleSizeTargetMet && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Bundle Size Optimization Needed</AlertTitle>
              <AlertDescription>
                The current bundle size does not meet the 30% reduction target. Consider
                implementing additional code splitting, tree shaking, or removing unused
                dependencies.
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>
        {/* Network Performance Tab */}
        <TabsContent value="network" className="space-y-4">
          {networkReport && (
            <>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Excellent</CardTitle>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-600">
                      {networkReport.summary.excellentCount}
                    </div>
                    <p className="text-xs text-muted-foreground">Network conditions</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Good</CardTitle>
                    <CheckCircle className="h-4 w-4 text-yellow-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-yellow-600">
                      {networkReport.summary.goodCount}
                    </div>
                    <p className="text-xs text-muted-foreground">Network conditions</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Fair</CardTitle>
                    <AlertTriangle className="h-4 w-4 text-orange-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-orange-600">
                      {networkReport.summary.fairCount}
                    </div>
                    <p className="text-xs text-muted-foreground">Network conditions</p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Poor</CardTitle>
                    <AlertTriangle className="h-4 w-4 text-red-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-red-600">
                      {networkReport.summary.poorCount}
                    </div>
                    <p className="text-xs text-muted-foreground">Network conditions</p>
                  </CardContent>
                </Card>
              </div>
              <Card>
                <CardHeader>
                  <CardTitle>Network Condition Performance</CardTitle>
                  <CardDescription>
                    Performance metrics across different network conditions
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {networkReport.results.map((result: any, index: number) => {
                      const grade = getPerformanceGrade(result.estimatedUserExperience);
                      const metrics = formatPerformanceMetrics(result.metrics);
                      return (
                        <div
                          key={index}
                          className="flex items-center justify-between p-4 border rounded-lg"
                        >
                          <div className="flex items-center gap-3">
                            <Wifi className="h-5 w-5" />
                            <div>
                              <div className="font-medium">{result.condition.name}</div>
                              <div className="text-sm text-muted-foreground">
                                {result.condition.description}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-4">
                            <div className="text-right text-sm">
                              <div>FCP: {metrics["firstContentfulPaint"]}</div>
                              <div>LCP: {metrics["largestContentfulPaint"]}</div>
                            </div>
                            <Badge
                              variant={
                                result.estimatedUserExperience === "excellent"
                                  ? "default"
                                  : result.estimatedUserExperience === "good"
                                    ? "secondary"
                                    : result.estimatedUserExperience === "fair"
                                      ? "outline"
                                      : "destructive"
                              }
                            >
                              {grade.emoji} {grade.grade}
                            </Badge>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
              {networkReport.recommendations.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle>Recommendations</CardTitle>
                    <CardDescription>Suggestions to improve network performance</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {networkReport.recommendations.map((rec: string, index: number) => (
                        <li key={index} className="flex items-start gap-2">
                          <div className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                          <span className="text-sm">{rec}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </TabsContent>
        {/* Memory Usage Tab */}
        <TabsContent value="memory" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Current Usage</CardTitle>
                <Monitor className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatMemorySize(memoryStats.current)}</div>
                <p className="text-xs text-muted-foreground">
                  Peak: {formatMemorySize(memoryStats.peak)}
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Utilization</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(memoryStats.utilization * 100).toFixed(1)}%
                </div>
                <Progress value={memoryStats.utilization * 100} className="mt-2" />
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Trend</CardTitle>
                {memoryStats.trend === "increasing" ? (
                  <TrendingUp className="h-4 w-4 text-red-600" />
                ) : memoryStats.trend === "decreasing" ? (
                  <TrendingDown className="h-4 w-4 text-green-600" />
                ) : (
                  <Activity className="h-4 w-4 text-blue-600" />
                )}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold capitalize">{memoryStats.trend}</div>
                <p className="text-xs text-muted-foreground">Memory usage trend</p>
              </CardContent>
            </Card>
          </div>
          <Card>
            <CardHeader>
              <CardTitle>Memory Leak Prevention</CardTitle>
              <CardDescription>Checks for common memory leak patterns</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {validationReport.memoryUsage.leakChecks.map((check, index) => (
                  <div key={index} className="flex items-center gap-3">
                    {check.passed ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <AlertTriangle className="h-5 w-5 text-yellow-600" />
                    )}
                    <div>
                      <div className="font-medium">{check.name}</div>
                      <div className="text-sm text-muted-foreground">{check.message}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
          <div className="flex gap-2">
            <Button onClick={takeSnapshot} variant="outline">
              Take Snapshot
            </Button>
            <Button onClick={forceGC} variant="outline">
              Force GC
            </Button>
          </div>
        </TabsContent>
        {/* Web Vitals Tab */}
        <TabsContent value="vitals" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">FCP</CardTitle>
                {validationReport.summary.fcpTargetMet ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                )}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {validationReport.summary.fcpTargetMet ? "PASS" : "FAIL"}
                </div>
                <p className="text-xs text-muted-foreground">
                  Target: {validationReport.webVitals.firstContentfulPaint.target}ms
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">LCP</CardTitle>
                {validationReport.summary.lcpTargetMet ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                )}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {validationReport.summary.lcpTargetMet ? "PASS" : "FAIL"}
                </div>
                <p className="text-xs text-muted-foreground">
                  Target: {validationReport.webVitals.largestContentfulPaint.target}ms
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">TTI</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">N/A</div>
                <p className="text-xs text-muted-foreground">
                  Target: {validationReport.webVitals.timeToInteractive.target}ms
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">CLS</CardTitle>
                <Activity className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">N/A</div>
                <p className="text-xs text-muted-foreground">
                  Target: {validationReport.webVitals.cumulativeLayoutShift.target}
                </p>
              </CardContent>
            </Card>
          </div>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Web Vitals Data Unavailable</AlertTitle>
            <AlertDescription>
              Real web vitals data requires Lighthouse testing. Run the performance validation with
              Lighthouse enabled to get actual FCP, LCP, TTI, and CLS measurements.
            </AlertDescription>
          </Alert>
        </TabsContent>
      </Tabs>
    </div>
  );
}
