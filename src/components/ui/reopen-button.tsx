import { useState } from "react";
import { RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
interface ReopenButtonProps {
  onReopen: () => Promise<void>;
  itemType: "risk" | "incident";
  disabled?: boolean;
  className?: string;
}
export function ReopenButton({
  onReopen,
  itemType,
  disabled = false,
  className,
}: ReopenButtonProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const handleReopen = async () => {
    setIsLoading(true);
    try {
      await onReo<PERSON>();
    } catch (error) {
      // Error caught and handled
    } finally {
      setIsLoading(false);
      setIsDialogOpen(false);
    }
  };
  return (
    <>
      <Button
        variant="outline"
        size="default"
        className={className}
        onClick={() => setIsDialogOpen(true)}
        disabled={disabled || isLoading}
      >
        <RefreshCw className="mr-2 h-4 w-4" />
        Reopen {itemType === "risk" ? "Risk" : "Incident"}
      </Button>
      <AlertDialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Reopen this {itemType}?</AlertDialogTitle>
            <AlertDialogDescription>
              This will change the status from "Closed" to{" "}
              {itemType === "risk" ? '"In Progress"' : '"Open"'}. The {itemType} will be available
              for editing again. Are you sure?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleReopen} disabled={isLoading}>
              {isLoading ? "Reopening..." : `Reopen ${itemType}`}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
