import * as React from "react"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"

export interface OptimizedImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string
  alt: string
  width?: number
  height?: number
  priority?: boolean
  quality?: number
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  sizes?: string
  className?: string
  fallback?: string
  onLoad?: () => void
  onError?: () => void
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  priority = false,
  quality = 75,
  placeholder = 'empty',
  blurDataURL,
  sizes,
  className,
  fallback = '/placeholder.svg',
  onLoad,
  onError,
  ...props
}: OptimizedImageProps) {
  const isMobile = useIsMobile()
  const [isLoaded, setIsLoaded] = React.useState(false)
  const [hasError, setHasError] = React.useState(false)
  const [currentSrc, setCurrentSrc] = React.useState<string>('')
  const imgRef = React.useRef<HTMLImageElement>(null)
  const observerRef = React.useRef<IntersectionObserver | null>(null)

  // Generate responsive image URLs based on device capabilities
  const generateResponsiveSrc = React.useCallback((originalSrc: string, targetWidth?: number) => {
    // In a real implementation, you would integrate with an image CDN like Cloudinary or ImageKit
    // For now, we'll return the original src with query parameters for optimization hints
    const url = new URL(originalSrc, window.location.origin)
    
    if (targetWidth) {
      url.searchParams.set('w', targetWidth.toString())
    }
    
    url.searchParams.set('q', quality.toString())
    
    // Add format optimization for modern browsers
    if (supportsWebP()) {
      url.searchParams.set('f', 'webp')
    } else if (supportsAVIF()) {
      url.searchParams.set('f', 'avif')
    }
    
    return url.toString()
  }, [quality])

  // Generate srcSet for responsive images
  const generateSrcSet = React.useCallback((originalSrc: string) => {
    if (!width) return undefined

    const breakpoints = [0.5, 1, 1.5, 2] // Different pixel densities
    return breakpoints
      .map(multiplier => {
        const targetWidth = Math.round(width * multiplier)
        const responsiveSrc = generateResponsiveSrc(originalSrc, targetWidth)
        return `${responsiveSrc} ${multiplier}x`
      })
      .join(', ')
  }, [width, generateResponsiveSrc])

  // Lazy loading with Intersection Observer
  React.useEffect(() => {
    if (priority) {
      // Load immediately for priority images
      setCurrentSrc(generateResponsiveSrc(src, width))
      return
    }

    const img = imgRef.current
    if (!img) return

    // Use Intersection Observer for lazy loading
    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setCurrentSrc(generateResponsiveSrc(src, width))
            observerRef.current?.unobserve(img)
          }
        })
      },
      {
        rootMargin: '50px', // Start loading 50px before image enters viewport
        threshold: 0.1
      }
    )

    observerRef.current.observe(img)

    return () => {
      observerRef.current?.disconnect()
    }
  }, [src, width, priority, generateResponsiveSrc])

  const handleLoad = React.useCallback(() => {
    setIsLoaded(true)
    onLoad?.()
  }, [onLoad])

  const handleError = React.useCallback(() => {
    setHasError(true)
    setCurrentSrc(fallback)
    onError?.()
  }, [fallback, onError])

  // Calculate optimal sizes based on viewport and container
  const calculateSizes = React.useCallback(() => {
    if (sizes) return sizes
    
    if (isMobile) {
      return '(max-width: 768px) 100vw, 50vw'
    }
    
    return '(max-width: 1200px) 50vw, 33vw'
  }, [sizes, isMobile])

  const imageProps = {
    ref: imgRef,
    src: currentSrc,
    alt,
    width,
    height,
    srcSet: generateSrcSet(src),
    sizes: calculateSizes(),
    loading: priority ? 'eager' as const : 'lazy' as const,
    decoding: 'async' as const,
    onLoad: handleLoad,
    onError: handleError,
    className: cn(
      'transition-opacity duration-300',
      !isLoaded && placeholder === 'blur' && 'blur-sm',
      !isLoaded && 'opacity-0',
      isLoaded && 'opacity-100',
      className
    ),
    ...props
  }

  return (
    <div className="relative overflow-hidden">
      {/* Placeholder */}
      {!isLoaded && placeholder === 'blur' && blurDataURL && (
        <img
          src={blurDataURL}
          alt=""
          className={cn(
            'absolute inset-0 w-full h-full object-cover blur-sm scale-110',
            className
          )}
          aria-hidden="true"
        />
      )}
      
      {/* Main image */}
      <img {...imageProps} />
      
      {/* Error fallback */}
      {hasError && (
        <div className={cn(
          'absolute inset-0 flex items-center justify-center bg-muted text-muted-foreground',
          className
        )}>
          <div className="text-center">
            <div className="text-2xl mb-2">📷</div>
            <div className="text-sm">Image not available</div>
          </div>
        </div>
      )}
    </div>
  )
}

// Utility functions for format detection
function supportsWebP(): boolean {
  if (typeof window === 'undefined') return false
  
  const canvas = document.createElement('canvas')
  canvas.width = 1
  canvas.height = 1
  return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0
}

function supportsAVIF(): boolean {
  if (typeof window === 'undefined') return false
  
  const canvas = document.createElement('canvas')
  canvas.width = 1
  canvas.height = 1
  return canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0
}

// Avatar component with optimized image
export interface OptimizedAvatarProps {
  src?: string
  alt: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  fallback?: string
  className?: string
}

export function OptimizedAvatar({
  src,
  alt,
  size = 'md',
  fallback,
  className
}: OptimizedAvatarProps) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-10 h-10',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  }

  const sizePixels = {
    sm: 32,
    md: 40,
    lg: 48,
    xl: 64
  }

  if (!src) {
    return (
      <div className={cn(
        'rounded-full bg-muted flex items-center justify-center text-muted-foreground font-medium',
        sizeClasses[size],
        className
      )}>
        {fallback || alt.charAt(0).toUpperCase()}
      </div>
    )
  }

  return (
    <div className={cn('rounded-full overflow-hidden', sizeClasses[size], className)}>
      <OptimizedImage
        src={src}
        alt={alt}
        width={sizePixels[size]}
        height={sizePixels[size]}
        className="w-full h-full object-cover"
        priority={size === 'xl'} // Prioritize larger avatars
      />
    </div>
  )
}

// Background image component with optimization
export interface OptimizedBackgroundProps {
  src: string
  alt: string
  children: React.ReactNode
  className?: string
  overlay?: boolean
  overlayOpacity?: number
}

export function OptimizedBackground({
  src,
  alt,
  children,
  className,
  overlay = false,
  overlayOpacity = 0.5
}: OptimizedBackgroundProps) {
  return (
    <div className={cn('relative overflow-hidden', className)}>
      <OptimizedImage
        src={src}
        alt={alt}
        className="absolute inset-0 w-full h-full object-cover"
        priority
        sizes="100vw"
      />
      
      {overlay && (
        <div 
          className="absolute inset-0 bg-black"
          style={{ opacity: overlayOpacity }}
        />
      )}
      
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}
