
import { Loader2 } from "lucide-react";

interface PageLoaderProps {
  message?: string;
  size?: "small" | "default" | "large";
}

export function PageLoader({ message, size = "default" }: PageLoaderProps) {
  const sizeClasses = {
    small: "h-4 w-4",
    default: "h-8 w-8",
    large: "h-12 w-12",
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-[200px] w-full space-y-4">
      <Loader2 className={`${sizeClasses[size]} animate-spin text-primary`} />
      {message && <p className="text-sm text-muted-foreground">{message}</p>}
    </div>
  );
}
