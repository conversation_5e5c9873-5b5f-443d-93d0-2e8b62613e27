import * as React from "react"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"
import { RefreshCw, ChevronDown } from "lucide-react"

export interface PullToRefreshProps {
  children: React.ReactNode
  onRefresh: () => Promise<void>
  threshold?: number
  disabled?: boolean
  className?: string
  refreshingText?: string
  pullText?: string
  releaseText?: string
}

export function PullToRefresh({
  children,
  onRefresh,
  threshold = 80,
  disabled = false,
  className,
  refreshingText = "Refreshing...",
  pullText = "Pull to refresh",
  releaseText = "Release to refresh"
}: PullToRefreshProps) {
  const isMobile = useIsMobile()
  const [pullDistance, setPullDistance] = React.useState(0)
  const [isRefreshing, setIsRefreshing] = React.useState(false)
  const [isPulling, setIsPulling] = React.useState(false)
  const [canRefresh, setCanRefresh] = React.useState(false)
  const containerRef = React.useRef<HTMLDivElement>(null)
  const startYRef = React.useRef<number>(0)
  const currentYRef = React.useRef<number>(0)

  const maxPullDistance = threshold * 1.5

  const handleRefresh = React.useCallback(async () => {
    if (disabled || isRefreshing) return
    
    setIsRefreshing(true)
    try {
      await onRefresh()
    } finally {
      setIsRefreshing(false)
      setPullDistance(0)
      setIsPulling(false)
      setCanRefresh(false)
    }
  }, [onRefresh, disabled, isRefreshing])

  const handleTouchStart = React.useCallback((e: TouchEvent) => {
    if (disabled || !isMobile) return
    
    // Only allow pull-to-refresh when at the top of the page
    const isAtTop = window.scrollY === 0
    if (!isAtTop) return

    startYRef.current = e.touches[0]?.clientY || 0
    setIsPulling(true)
  }, [disabled, isMobile])

  const handleTouchMove = React.useCallback((e: TouchEvent) => {
    if (disabled || !isMobile || !isPulling) return

    currentYRef.current = e.touches[0]?.clientY || 0
    const deltaY = currentYRef.current - startYRef.current

    if (deltaY > 0) {
      // Prevent default scrolling when pulling down
      e.preventDefault()
      
      // Apply resistance to the pull distance
      const resistance = 0.5
      const distance = Math.min(deltaY * resistance, maxPullDistance)
      
      setPullDistance(distance)
      setCanRefresh(distance >= threshold)
    }
  }, [disabled, isMobile, isPulling, threshold, maxPullDistance])

  const handleTouchEnd = React.useCallback(() => {
    if (disabled || !isMobile || !isPulling) return

    if (canRefresh && pullDistance >= threshold) {
      handleRefresh()
    } else {
      // Reset pull state
      setPullDistance(0)
      setIsPulling(false)
      setCanRefresh(false)
    }
  }, [disabled, isMobile, isPulling, canRefresh, pullDistance, threshold, handleRefresh])

  // Add touch event listeners
  React.useEffect(() => {
    const container = containerRef.current
    if (!container || !isMobile) return

    container.addEventListener('touchstart', handleTouchStart, { passive: false })
    container.addEventListener('touchmove', handleTouchMove, { passive: false })
    container.addEventListener('touchend', handleTouchEnd, { passive: true })

    return () => {
      container.removeEventListener('touchstart', handleTouchStart)
      container.removeEventListener('touchmove', handleTouchMove)
      container.removeEventListener('touchend', handleTouchEnd)
    }
  }, [handleTouchStart, handleTouchMove, handleTouchEnd, isMobile])

  // Calculate pull progress for animations
  const pullProgress = Math.min(pullDistance / threshold, 1)
  const iconRotation = pullProgress * 180

  const renderPullIndicator = () => {
    if (!isMobile || (!isPulling && !isRefreshing)) return null

    return (
      <div 
        className={cn(
          "absolute top-0 left-0 right-0 flex items-center justify-center transition-all duration-200 ease-out z-10",
          "bg-background/95 backdrop-blur-sm border-b"
        )}
        style={{
          height: Math.max(pullDistance, isRefreshing ? 60 : 0),
          transform: `translateY(${isRefreshing ? 0 : -60 + pullDistance}px)`
        }}
      >
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          {isRefreshing ? (
            <>
              <RefreshCw className="h-4 w-4 animate-spin" />
              <span>{refreshingText}</span>
            </>
          ) : (
            <>
              <ChevronDown 
                className="h-4 w-4 transition-transform duration-200" 
                style={{ transform: `rotate(${iconRotation}deg)` }}
              />
              <span>
                {canRefresh ? releaseText : pullText}
              </span>
            </>
          )}
        </div>
      </div>
    )
  }

  if (!isMobile) {
    // On desktop, render without pull-to-refresh functionality
    return <div className={className}>{children}</div>
  }

  return (
    <div 
      ref={containerRef}
      className={cn("relative", className)}
      style={{
        transform: `translateY(${isPulling ? pullDistance : 0}px)`,
        transition: isPulling ? 'none' : 'transform 0.2s ease-out'
      }}
    >
      {renderPullIndicator()}
      {children}
    </div>
  )
}

// Hook for easier integration with existing components
export function usePullToRefreshState(onRefresh: () => Promise<void>) {
  const [isRefreshing, setIsRefreshing] = React.useState(false)

  const handleRefresh = React.useCallback(async () => {
    setIsRefreshing(true)
    try {
      await onRefresh()
    } finally {
      setIsRefreshing(false)
    }
  }, [onRefresh])

  return {
    isRefreshing,
    handleRefresh
  }
}

// Example usage with a list component
export function RefreshableList({ 
  children, 
  onRefresh, 
  isLoading 
}: {
  children: React.ReactNode
  onRefresh: () => Promise<void>
  isLoading?: boolean
}) {
  return (
    <PullToRefresh onRefresh={onRefresh}>
      <div className="space-y-2">
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        )}
        {children}
      </div>
    </PullToRefresh>
  )
}
