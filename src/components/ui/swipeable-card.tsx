import * as React from "react";
import { cn } from "@/lib/utils";
import { useTouchGestures } from "@/hooks/use-touch-gestures";
import { useIsMobile } from "@/hooks/use-mobile";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Trash2, Archive, Edit, MoreHorizontal } from "lucide-react";
import { Incident } from "@/types";

export interface SwipeAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  color: "destructive" | "warning" | "success" | "primary";
  action: () => void;
}

export interface SwipeableCardProps {
  children: React.ReactNode;
  className?: string;
  leftActions?: SwipeAction[];
  rightActions?: SwipeAction[];
  onSwipe?: (direction: "left" | "right", actionId?: string) => void;
  swipeThreshold?: number;
  disabled?: boolean;
  showActionsOnHover?: boolean;
}

const actionColors = {
  destructive: "bg-destructive text-destructive-foreground",
  warning: "bg-orange-500 text-white",
  success: "bg-green-500 text-white",
  primary: "bg-primary text-primary-foreground",
};

export function SwipeableCard({
  children,
  className,
  leftActions = [],
  rightActions = [],
  onSwipe,
  swipeThreshold = 80,
  disabled = false,
  showActionsOnHover = false,
}: SwipeableCardProps) {
  const isMobile = useIsMobile();
  const [swipeOffset, setSwipeOffset] = React.useState(0);
  const [isRevealed, setIsRevealed] = React.useState(false);
  const [, setRevealedSide] = React.useState<"left" | "right" | null>(null);
  const [isAnimating, setIsAnimating] = React.useState(false);
  const cardRef = React.useRef<HTMLDivElement>(null);

  const maxSwipeDistance = 120; // Maximum distance to reveal actions

  const resetCard = React.useCallback(() => {
    setIsAnimating(true);
    setSwipeOffset(0);
    setIsRevealed(false);
    setRevealedSide(null);
    setTimeout(() => setIsAnimating(false), 200);
  }, []);

  const executeAction = React.useCallback(
    (action: SwipeAction) => {
      resetCard();
      setTimeout(() => action.action(), 200); // Delay to allow animation
    },
    [resetCard]
  );

  const gestureHandlers = useTouchGestures({
    onSwipe: gesture => {
      if (disabled) return;

      const { direction, distance } = gesture;

      if (
        (direction === "left" && rightActions.length > 0) ||
        (direction === "right" && leftActions.length > 0)
      ) {
        if (distance > swipeThreshold) {
          const actions = direction === "left" ? rightActions : leftActions;
          const targetOffset = direction === "left" ? -maxSwipeDistance : maxSwipeDistance;

          setSwipeOffset(targetOffset);
          setIsRevealed(true);
          setRevealedSide(direction === "left" ? "right" : "left");

          onSwipe?.(direction);

          // Auto-execute first action if only one action available
          if (actions.length === 1 && actions[0]) {
            setTimeout(() => executeAction(actions[0]!), 300);
          }
        } else {
          resetCard();
        }
      }
    },
    swipeThreshold: 20, // Lower threshold for gesture detection
    preventScroll: false,
  });

  // Handle click outside to reset
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent | TouchEvent) => {
      if (cardRef.current && !cardRef.current.contains(event.target as Node) && isRevealed) {
        resetCard();
      }
    };

    if (isRevealed) {
      document.addEventListener("mousedown", handleClickOutside as EventListener);
      document.addEventListener("touchstart", handleClickOutside as EventListener);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside as EventListener);
      document.removeEventListener("touchstart", handleClickOutside as EventListener);
    };
  }, [isRevealed, resetCard]);

  const renderActions = (actions: SwipeAction[], side: "left" | "right") => {
    if (actions.length === 0) return null;

    return (
      <div
        className={cn(
          "absolute top-0 bottom-0 flex items-center",
          side === "left" ? "left-0" : "right-0"
        )}
        style={{
          width: maxSwipeDistance,
          [side === "left" ? "transform" : "transform"]:
            side === "left"
              ? `translateX(${Math.min(0, swipeOffset + maxSwipeDistance)}px)`
              : `translateX(${Math.max(0, swipeOffset - maxSwipeDistance)}px)`,
        }}
      >
        {actions.map(action => (
          <Button
            key={action.id}
            variant="ghost"
            size="sm"
            className={cn(
              "h-full rounded-none flex-1 flex flex-col items-center justify-center gap-1 text-xs",
              actionColors[action.color]
            )}
            onClick={() => executeAction(action)}
          >
            {action.icon}
            <span className="text-xs">{action.label}</span>
          </Button>
        ))}
      </div>
    );
  };

  if (!isMobile && !showActionsOnHover) {
    // On desktop, render as regular card without swipe functionality
    return (
      <Card className={className}>
        <CardContent className="p-0">{children}</CardContent>
      </Card>
    );
  }

  return (
    <div ref={cardRef} className={cn("relative overflow-hidden", className)}>
      {/* Left actions */}
      {renderActions(leftActions, "left")}

      {/* Right actions */}
      {renderActions(rightActions, "right")}

      {/* Main card content */}
      <Card
        className={cn(
          "relative z-10 transition-transform duration-200 ease-out",
          isAnimating && "transition-transform",
          !disabled && isMobile && "touch-manipulation"
        )}
        style={{
          transform: `translateX(${swipeOffset}px)`,
        }}
        {...(isMobile && !disabled ? gestureHandlers : {})}
      >
        <CardContent className="p-0">{children}</CardContent>
      </Card>

      {/* Desktop hover actions */}
      {!isMobile && showActionsOnHover && (leftActions.length > 0 || rightActions.length > 0) && (
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button variant="ghost" size="sm">
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
}

// Predefined action sets for common use cases
export const commonSwipeActions = {
  delete: (onDelete: () => void): SwipeAction => ({
    id: "delete",
    label: "Delete",
    icon: <Trash2 className="h-4 w-4" />,
    color: "destructive",
    action: onDelete,
  }),

  archive: (onArchive: () => void): SwipeAction => ({
    id: "archive",
    label: "Archive",
    icon: <Archive className="h-4 w-4" />,
    color: "warning",
    action: onArchive,
  }),

  edit: (onEdit: () => void): SwipeAction => ({
    id: "edit",
    label: "Edit",
    icon: <Edit className="h-4 w-4" />,
    color: "primary",
    action: onEdit,
  }),
};

// Example usage component
export function SwipeableIncidentCard({
  incident,
  onEdit,
  onDelete,
  onArchive,
}: {
  incident: Incident;
  onEdit: () => void;
  onDelete: () => void;
  onArchive: () => void;
}) {
  const leftActions = [commonSwipeActions.edit(onEdit)];

  const rightActions = [commonSwipeActions.archive(onArchive), commonSwipeActions.delete(onDelete)];

  return (
    <SwipeableCard leftActions={leftActions} rightActions={rightActions} className="mb-3">
      <div className="p-4">
        <h3 className="font-semibold text-sm mb-2">{incident.title}</h3>
        <p className="text-xs text-muted-foreground mb-2">{incident.description}</p>
        <div className="flex items-center justify-between text-xs">
          <span
            className={cn(
              "px-2 py-1 rounded-full",
              incident.severity === "High" && "bg-red-100 text-red-800",
              incident.severity === "Medium" && "bg-yellow-100 text-yellow-800",
              incident.severity === "Low" && "bg-green-100 text-green-800"
            )}
          >
            {incident.severity}
          </span>
          <span className="text-muted-foreground">
            {new Date(incident.date).toLocaleDateString()}
          </span>
        </div>
      </div>
    </SwipeableCard>
  );
}
