
import { Textarea } from "@/components/ui/textarea";
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { PolicyRequestFormData } from "./schema";

interface JustificationInputProps {
  form: UseFormReturn<PolicyRequestFormData>;
}

export function JustificationInput({ form }: JustificationInputProps) {
  return (
    <FormField
      control={form.control}
      name="justification"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Business Justification</FormLabel>
          <FormControl>
            <Textarea 
              placeholder="Why is this policy needed?"
              className="min-h-[100px]"
              {...field} 
            />
          </FormControl>
          <FormDescription>
            Explain why this policy is necessary and how it will benefit the organization
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
