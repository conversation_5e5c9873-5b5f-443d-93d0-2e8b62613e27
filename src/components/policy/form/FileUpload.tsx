
import { Button } from "@/components/ui/button";
import { FormDescription, FormLabel } from "@/components/ui/form";
import { Upload } from "lucide-react";

interface FileUploadProps {
  referenceFile: File | null;
  setReferenceFile: (file: File | null) => void;
}

export function FileUpload({ referenceFile, setReferenceFile }: FileUploadProps) {
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      setReferenceFile(files[0] || null);
    }
  };

  return (
    <div>
      <FormLabel className="block mb-2">Reference Document (optional)</FormLabel>
      <div className="flex items-center space-x-2">
        <label className="cursor-pointer flex items-center gap-2 border rounded-md px-4 py-2 hover:bg-slate-100 transition-colors">
          <Upload size={18} />
          <span>{referenceFile ? referenceFile.name : "Upload file"}</span>
          <input
            type="file"
            onChange={handleFileChange}
            className="hidden"
            accept=".pdf,.doc,.docx,.txt"
          />
        </label>
        {referenceFile && (
          <Button
            type="button"
            variant="outline" 
            size="sm"
            onClick={() => setReferenceFile(null)}
          >
            Remove
          </Button>
        )}
      </div>
      <FormDescription className="mt-2">
        Attach any reference documents that may help with policy creation (PDF, Word, or TXT)
      </FormDescription>
    </div>
  );
}
