
import { Input } from "@/components/ui/input";
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { PolicyRequestFormData } from "./schema";

interface TitleInputProps {
  form: UseFormReturn<PolicyRequestFormData>;
}

export function TitleInput({ form }: TitleInputProps) {
  return (
    <FormField
      control={form.control}
      name="title"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Policy Title</FormLabel>
          <FormControl>
            <Input placeholder="Enter policy title" {...field} />
          </FormControl>
          <FormDescription>
            A descriptive title for the requested policy
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
