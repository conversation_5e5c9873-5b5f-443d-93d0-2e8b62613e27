import { z } from "zod";

export const policyRequestSchema = z.object({
  title: z.string().min(5, { message: "Title must be at least 5 characters long" }),
  category: z.string().min(1, { message: "Please select a category" }),
  description: z.string().min(20, { message: "Description must be at least 20 characters long" }),
  justification: z
    .string()
    .min(20, { message: "Justification must be at least 20 characters long" }),
});

export type PolicyRequestFormData = z.infer<typeof policyRequestSchema>;
