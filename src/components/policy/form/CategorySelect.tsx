
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { UseFormReturn } from "react-hook-form";
import { PolicyRequestFormData } from "./schema";
import { policyCategories } from "@/components/policy/admin/form/PolicySchema";

interface CategorySelectProps {
  form: UseFormReturn<PolicyRequestFormData>;
}

export function CategorySelect({ form }: CategorySelectProps) {
  return (
    <FormField
      control={form.control}
      name="category"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Category</FormLabel>
          <Select onValueChange={field.onChange} defaultValue={field.value}>
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Select a category" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {policyCategories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormDescription>
            Select the category that best fits your requested policy
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
