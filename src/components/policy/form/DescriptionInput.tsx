
import { Textarea } from "@/components/ui/textarea";
import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { PolicyRequestFormData } from "./schema";

interface DescriptionInputProps {
  form: UseFormReturn<PolicyRequestFormData>;
}

export function DescriptionInput({ form }: DescriptionInputProps) {
  return (
    <FormField
      control={form.control}
      name="description"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Policy Description</FormLabel>
          <FormControl>
            <Textarea 
              placeholder="Describe what this policy should cover"
              className="min-h-[100px]"
              {...field} 
            />
          </FormControl>
          <FormDescription>
            Provide detailed information about what this policy should address
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
