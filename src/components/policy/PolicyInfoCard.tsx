
import { Card, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";

interface PolicyInfoCardProps {
  category: string;
  status?: string;
  effectiveDate?: string;
}

export function PolicyInfoCard({ category, status = 'published', effectiveDate }: PolicyInfoCardProps) {
  // Format category for display
  const formattedCategory = 
    category === "security" ? "Security Policy" : 
    category === "operational" ? "Operational Policy" : 
    category === "governance" ? "Governance Policy" : category;
  
  // Format status for display with appropriate styling
  const getStatusDisplay = () => {
    switch(status.toLowerCase()) {
      case 'published':
        return (
          <span className="inline-block px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
            Active
          </span>
        );
      case 'draft':
        return (
          <span className="inline-block px-2 py-1 text-xs font-medium rounded-full bg-amber-100 text-amber-800">
            Draft
          </span>
        );
      case 'archived':
        return (
          <span className="inline-block px-2 py-1 text-xs font-medium rounded-full bg-gray-100 text-gray-800">
            Archived
          </span>
        );
      default:
        return (
          <span className="inline-block px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
            {status}
          </span>
        );
    }
  };

  // Calculate next review date (one year after effective date or current date)
  const getNextReviewDate = () => {
    const baseDate = effectiveDate ? new Date(effectiveDate) : new Date();
    const reviewDate = new Date(baseDate);
    reviewDate.setFullYear(reviewDate.getFullYear() + 1);
    return reviewDate.toLocaleDateString();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Policy Overview</CardTitle>
        <CardDescription>Key information about this policy</CardDescription>
      </CardHeader>
      <CardContent>
        <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <dt className="text-sm font-medium text-muted-foreground">Category</dt>
            <dd className="text-base">{formattedCategory}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-muted-foreground">Status</dt>
            <dd className="text-base">{getStatusDisplay()}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-muted-foreground">Effective Date</dt>
            <dd className="text-base">{effectiveDate ?? "Not yet effective"}</dd>
          </div>
          <div>
            <dt className="text-sm font-medium text-muted-foreground">Next Review</dt>
            <dd className="text-base">{getNextReviewDate()}</dd>
          </div>
        </dl>
      </CardContent>
    </Card>
  );
}
