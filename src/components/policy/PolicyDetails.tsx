import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { PolicyHeader } from "./PolicyHeader";
import { PolicyInfoCard } from "./PolicyInfoCard";
import { PolicyDescriptionCard } from "./PolicyDescriptionCard";
import { PolicyDocumentCard } from "./PolicyDocumentCard";
import { PolicyPreviewDialog } from "./PolicyPreviewDialog";
import { usePolicyDocument } from "./hooks/usePolicyDocument";
import { updatePolicy } from "@/services/policy";
import { Policy } from "@/types/policy";
import { errorToast, successToast } from "@/components/ui/enhanced-toast";
import { useAuth } from "@/contexts/auth";
interface PolicyDetailsProps {
  policy: Policy;
}
export function PolicyDetails({ policy }: PolicyDetailsProps) {
  const [previewOpen, setPreviewOpen] = useState(false);
  const [currentDocumentUrl, setCurrentDocumentUrl] = useState(policy.documentUrl);
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();
  // Log for debugging
  useEffect(() => {}, [policy, user, isAuthenticated]);
  const {
    pdfUrl,
    handleUpload,
    handleDownload,
    isUploading,
    policyDocument,
    error,
    isLoadingUrl,
    handleFileChange,
  } = usePolicyDocument(currentDocumentUrl);
  // Log document state changes
  useEffect(() => {
    if (pdfUrl) {
      // Condition handled
    }
    if (error) {
      // Condition handled
    }
  }, [pdfUrl, error]);
  const handlePreviewClick = () => {
    if (!isAuthenticated) {
      errorToast({
        title: "Authentication required",
        description: "You need to log in to preview documents",
      });
      navigate("/login", { state: { returnTo: location.pathname } });
      return;
    }
    setPreviewOpen(true);
  };
  const handleUploadComplete = async () => {
    if (!isAuthenticated) {
      errorToast({
        title: "Authentication required",
        description: "You need to log in to upload documents",
      });
      navigate("/login", { state: { returnTo: location.pathname } });
      return;
    }
    try {
      const path = await handleUpload();
      if (path) {
        setCurrentDocumentUrl(path);
        // Update the policy document URL in the database
        await updatePolicy(policy.id, { documentUrl: path });
        successToast({
          title: "Policy updated",
          description: "Document URL has been updated in the policy",
        });
      }
    } catch (error: unknown) {
      errorToast({
        title: "Failed to update policy",
        description: (error as Error).message ?? "An error occurred while updating the policy",
      });
    }
  };
  // Format date for display
  const formattedLastUpdated = new Date(policy.updatedAt).toLocaleDateString();
  const formattedEffectiveDate = policy.effectiveDate
    ? new Date(policy.effectiveDate).toLocaleDateString()
    : undefined;
  // Extract filename from URL for display
  const filename = currentDocumentUrl
    ? `${policy.title}${currentDocumentUrl.endsWith(".pdf") ? "" : ".pdf"}`
    : `${policy.title}.pdf`;
  return (
    <div className="space-y-6">
      <PolicyHeader
        title={policy.title}
        lastUpdated={formattedLastUpdated}
        version={policy.version}
        documentUrl={currentDocumentUrl ?? ""}
        onDownload={currentDocumentUrl ? () => handleDownload(currentDocumentUrl) : () => {}}
      />
      <PolicyInfoCard
        category={policy.category}
        status={policy.status}
        effectiveDate={formattedEffectiveDate ?? ""}
      />
      <PolicyDescriptionCard description={policy.description} />
      <PolicyDocumentCard
        title={policy.title}
        documentUrl={currentDocumentUrl ?? ""}
        onPreview={handlePreviewClick}
        onUploadComplete={handleUploadComplete}
        isUploading={isUploading}
        policyDocument={policyDocument}
        handleFileChange={handleFileChange}
      />
      <PolicyPreviewDialog
        open={previewOpen}
        onOpenChange={setPreviewOpen}
        pdfUrl={pdfUrl}
        filename={filename}
        isLoading={isLoadingUrl}
      />
    </div>
  );
}
