import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Policy } from "@/types/policy";
import { FileText, ChevronRight } from "lucide-react";
import { useState } from "react";
interface PolicyCardProps {
  policy: Policy;
  onClick: () => void;
}
export function PolicyCard({ policy, onClick }: PolicyCardProps) {
  const [isHovering, setIsHovering] = useState(false);
  const [isClicking, setIsClicking] = useState(false);
  const formattedDate = new Date(policy.updatedAt).toLocaleDateString();
  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsClicking(true);
    // Add a small delay to show the click state before navigating
    setTimeout(() => {
      onClick();
      setIsClicking(false);
    }, 150);
  };
  return (
    <Card
      className={`mb-4 transition-all duration-200 cursor-pointer ${
        isHovering ? "bg-accent/20 shadow-md" : ""
      } ${isClicking ? "bg-accent/40 scale-[0.99]" : ""}`}
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
      onClick={handleClick}
    >
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-2">
            <div className="bg-primary/10 p-1 rounded">
              <FileText className="h-5 w-5 text-primary" />
            </div>
            <CardTitle className="text-lg">{policy.title}</CardTitle>
          </div>
          <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded-full">
            v{policy.version}
          </span>
        </div>
        <CardDescription>{policy.description}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <span className="text-xs text-muted-foreground">Last updated: {formattedDate}</span>
          <Button size="sm" variant="outline" onClick={handleClick} className="gap-1 group">
            View Policy
            <ChevronRight className="h-4 w-4 group-hover:translate-x-0.5 transition-transform" />
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
