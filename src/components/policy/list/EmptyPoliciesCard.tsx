
import { Card, CardContent } from "@/components/ui/card";
import { FileText } from "lucide-react";

interface EmptyPoliciesCardProps {
  searchTerm: string;
}

export function EmptyPoliciesCard({ searchTerm }: EmptyPoliciesCardProps) {
  return (
    <Card>
      <CardContent className="flex flex-col items-center justify-center py-10">
        <FileText className="h-10 w-10 text-muted-foreground mb-4" />
        <p className="text-center text-muted-foreground">
          {searchTerm 
            ? "No policies match your search" 
            : "No policies found in this category"}
        </p>
      </CardContent>
    </Card>
  );
}
