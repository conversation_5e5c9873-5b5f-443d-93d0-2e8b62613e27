
import { But<PERSON> } from "@/components/ui/button";
import { FileText } from "lucide-react";
import { PolicyCategoriesList } from "./PolicyCategoriesList";

interface PolicyListHeaderProps {
  onRequestPolicy: () => void;
  activeCategory: string;
  setActiveCategory: (category: string) => void;
}

export function PolicyListHeader({ onRequestPolicy, activeCategory, setActiveCategory }: PolicyListHeaderProps) {
  return (
    <div className="flex flex-col md:flex-row gap-4 md:items-center md:justify-between">
      <div className="w-full md:w-1/3">
        <PolicyCategoriesList 
          activeCategory={activeCategory} 
          setActiveCategory={setActiveCategory}
        />
      </div>
      
      <Button 
        onClick={onRequestPolicy}
        className="w-full md:w-auto"
      >
        <FileText className="mr-2 h-4 w-4" />
        Request New Policy
      </Button>
    </div>
  );
}
