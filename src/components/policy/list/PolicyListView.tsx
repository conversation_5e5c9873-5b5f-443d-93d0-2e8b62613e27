import { useState, useEffect } from "react";
import { Policy } from "@/types/policy";
import { PolicySearchBar } from "./PolicySearchBar";
import { PolicyCard } from "./PolicyCard";
import { PolicyCardSkeleton } from "./PolicyCardSkeleton";
import { EmptyPoliciesCard } from "./EmptyPoliciesCard";
import { FeedbackMessage } from "@/components/ui/feedback-message";
interface PolicyListViewProps {
  policies: Policy[];
  loading: boolean;
  handleViewPolicy: (policy: Policy) => void;
  activeCategory: string;
  setActiveCategory: (category: string) => void;
}
export function PolicyListView({
  policies,
  loading,
  handleViewPolicy,
  activeCategory,
}: PolicyListViewProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredPolicies, setFilteredPolicies] = useState<Policy[]>([]);
  // Filter policies whenever search term or category changes
  useEffect(() => {
    const filtered = policies
      .filter(
        policy =>
          policy.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          policy.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .filter(policy => {
        if (activeCategory === "all") return true;
        // Case insensitive and trimmed category matching
        const policyCategory = policy.category.trim().toLowerCase();
        const selectedCategory = activeCategory.trim().toLowerCase();
        const isMatch = policyCategory === selectedCategory;
        return isMatch;
      });
    setFilteredPolicies(filtered);
  }, [policies, searchTerm, activeCategory]);
  return (
    <div className="space-y-6">
      <div className="w-full">
        <PolicySearchBar searchTerm={searchTerm} setSearchTerm={setSearchTerm} />
      </div>
      {activeCategory !== "all" && (
        <FeedbackMessage
          type="info"
          message={`Showing ${filteredPolicies.length} policies in the "${activeCategory}" category`}
        />
      )}
      {loading ? (
        <div className="grid gap-6">
          {Array.from({ length: 3 }, (_, i) => (
            <PolicyCardSkeleton key={i} />
          ))}
        </div>
      ) : filteredPolicies.length > 0 ? (
        <div className="grid gap-6">
          {filteredPolicies.map(policy => (
            <PolicyCard
              key={policy.id}
              policy={policy}
              onClick={() => {
                handleViewPolicy(policy);
              }}
            />
          ))}
        </div>
      ) : (
        <EmptyPoliciesCard searchTerm={searchTerm} />
      )}
    </div>
  );
}
