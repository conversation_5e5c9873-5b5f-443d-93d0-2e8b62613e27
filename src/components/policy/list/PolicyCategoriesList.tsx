
import { Button } from "@/components/ui/button";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuItem
} from "@/components/ui/dropdown-menu";
import { Shield, FileText, BookOpen, Users, FileCheck, CreditCard, Filter } from "lucide-react";
import { policyCategories } from "@/components/policy/admin/form/PolicySchema";

// Map policy categories to their respective icons
const categoryIcons: Record<string, React.ElementType> = {
  "Information Security": Shield,
  "Human Resources": Users,
  "Compliance": FileCheck,
  "Operational": FileText,
  "Governance": BookOpen,
  "Finance": CreditCard
};

interface PolicyCategoriesListProps {
  activeCategory: string;
  setActiveCategory: (category: string) => void;
}

export function PolicyCategoriesList({ activeCategory, setActiveCategory }: PolicyCategoriesListProps) {
  // Get the active category icon or use a default
  const ActiveIcon = activeCategory !== "all" 
    ? (categoryIcons[activeCategory] || FileText) 
    : Filter;
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="w-full justify-between flex items-center gap-2">
          <div className="flex items-center gap-2">
            <ActiveIcon className="h-4 w-4" />
            <span>
              {activeCategory === "all" 
                ? "All Policies" 
                : activeCategory}
            </span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent className="w-56 bg-popover z-50">
        <DropdownMenuLabel>Filter by Category</DropdownMenuLabel>
        <DropdownMenuItem 
          className={`flex items-center gap-2 ${activeCategory === 'all' ? 'bg-accent text-accent-foreground' : ''}`}
          onClick={() => setActiveCategory("all")}
        >
          <Filter className="h-4 w-4" />
          All Policies
        </DropdownMenuItem>
        
        {policyCategories.map((category) => {
          const Icon = categoryIcons[category] || FileText;
          const isActive = category === activeCategory;
          return (
            <DropdownMenuItem 
              key={category}
              className={`flex items-center gap-2 ${isActive ? 'bg-accent text-accent-foreground' : ''}`}
              onClick={() => setActiveCategory(category)}
            >
              <Icon className="h-4 w-4" />
              {category}
            </DropdownMenuItem>
          );
        })}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
