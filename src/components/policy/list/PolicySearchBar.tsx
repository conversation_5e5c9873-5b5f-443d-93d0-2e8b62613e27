
import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";

interface PolicySearchBarProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
}

export function PolicySearchBar({ searchTerm, setSearchTerm }: PolicySearchBarProps) {
  return (
    <div className="relative flex-grow">
      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
      <Input
        className="w-full pl-9"
        placeholder="Search policies..."
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
      />
    </div>
  );
}
