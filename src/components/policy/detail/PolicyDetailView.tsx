
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { FileText } from "lucide-react";
import { PolicyDetails } from "../PolicyDetails";
import { Policy } from "@/types/policy";

interface PolicyDetailViewProps {
  policy: Policy | null;
  loading: boolean;
  handleBackToList: () => void;
}

export function PolicyDetailView({ 
  policy, 
  loading, 
  handleBackToList
}: PolicyDetailViewProps) {
  return (
    <div>
      <Button 
        variant="outline" 
        className="mb-4"
        onClick={handleBackToList}
      >
        ← Back to Policies
      </Button>
      
      {loading ? (
        <div className="space-y-4">
          <Skeleton className="h-10 w-3/4" />
          <Skeleton className="h-64 w-full" />
        </div>
      ) : policy ? (
        <PolicyDetails policy={policy} />
      ) : (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <FileText className="h-10 w-10 text-muted-foreground mb-4" />
            <p className="text-center text-muted-foreground">
              Policy not found
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
