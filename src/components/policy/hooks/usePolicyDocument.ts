import { useState, useEffect } from "react";
import { getSignedUrl, downloadPolicyDocument, uploadPolicyDocument } from "@/services/policy";
import { errorToast, successToast, infoToast } from "@/components/ui/enhanced-toast";
import { useAuth } from "@/contexts/auth";
export function usePolicyDocument(documentUrl: string | undefined) {
  const [pdfUrl, setPdfUrl] = useState<string>("");
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [uploadProgress, setUploadProgress] = useState<number>(0);
  const [policyDocument, setPolicyDocument] = useState<File | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoadingUrl, setIsLoadingUrl] = useState<boolean>(false);
  const { isAuthenticated, user } = useAuth();
  useEffect(() => {
    if (!documentUrl) {
      setPdfUrl("");
      setError(null);
      return;
    }
    const fetchSignedUrl = async () => {
      try {
        setIsLoadingUrl(true);
        // Get a signed URL for the document (valid for 1 hour)
        const url = await getSignedUrl(documentUrl, 3600);
        setPdfUrl(url);
        setError(null);
      } catch (err: unknown) {
        setError((err as Error).message ?? "Could not retrieve document URL");
        setPdfUrl("");
        errorToast({
          title: "Error loading document",
          description: (err as Error).message ?? "Could not retrieve the document URL",
        });
      } finally {
        setIsLoadingUrl(false);
      }
    };
    fetchSignedUrl();
  }, [documentUrl, isAuthenticated, user?.id]);
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      const file = files[0];
      if (!file) return;
      // Validate file type and size
      if (file.type !== "application/pdf") {
        errorToast({
          title: "Invalid file type",
          description: "Only PDF files are supported",
        });
        return;
      }
      if (file.size > 10 * 1024 * 1024) {
        // 10MB limit
        errorToast({
          title: "File too large",
          description: "Maximum file size is 10MB",
        });
        return;
      }
      setPolicyDocument(file ?? null);
      if (file) {
        infoToast({
          title: "File selected",
          description: `"${file.name}" is ready to upload. Click "Upload PDF" to proceed.`,
        });
      }
    }
  };
  const handleUpload = async (): Promise<string | null> => {
    if (!policyDocument) {
      errorToast({
        title: "No document selected",
        description: "Please select a PDF file to upload",
      });
      return null;
    }
    if (!isAuthenticated) {
      errorToast({
        title: "Authentication required",
        description: "You need to be logged in to upload documents",
      });
      return null;
    }
    try {
      setIsUploading(true);
      setUploadProgress(0);
      // Show upload started toast
      infoToast({
        title: "Uploading document",
        description: `"${policyDocument.name}" is being uploaded...`,
      });
      // More frequent progress updates
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          const increment = Math.random() * 10; // Smaller increments for more updates
          const newValue = Math.min(prev + increment, 90); // Cap at 90% until complete
          return newValue;
        });
      }, 200);
      // Ensure we're passing the correct parameters
      const path = await uploadPolicyDocument(policyDocument);
      // Clear the interval and set to 100%
      clearInterval(progressInterval);
      setUploadProgress(100);
      // Get signed URL for the uploaded document
      const signedUrl = await getSignedUrl(path);
      setPdfUrl(signedUrl);
      setPolicyDocument(null);
      setError(null);
      successToast({
        title: "Document uploaded",
        description: "Policy document has been uploaded successfully",
      });
      return path;
    } catch (error: unknown) {
      // Enhanced error handling with specific messages
      let errorMessage = "Failed to upload document";
      const errorMsg = (error as Error).message;
      if (errorMsg.includes("permission")) {
        errorMessage = "Permission denied. Please check your access rights.";
      } else if (errorMsg.includes("network")) {
        errorMessage = "Network error. Please check your connection and try again.";
      } else if (errorMsg.includes("size")) {
        errorMessage = "File size exceeds the maximum allowed limit.";
      }
      setError(errorMessage);
      errorToast({
        title: "Upload failed",
        description: errorMessage,
      });
      return null;
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };
  const handleDownload = async (path: string) => {
    if (!isAuthenticated) {
      errorToast({
        title: "Authentication required",
        description: "You need to be logged in to download documents",
      });
      return;
    }
    try {
      infoToast({
        title: "Downloading document",
        description: "Your download will begin shortly...",
      });
      const blob = await downloadPolicyDocument(path);
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = path.split("/").pop() ?? "policy-document.pdf";
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      successToast({
        title: "Download complete",
        description: "Document downloaded successfully",
      });
    } catch (error: unknown) {
      // Enhanced error handling with specific messages
      let errorMessage = "Failed to download document";
      const errorMsg = (error as Error).message;
      if (errorMsg.includes("permission")) {
        errorMessage = "Permission denied. Please check your access rights.";
      } else if (errorMsg.includes("network")) {
        errorMessage = "Network error. Please check your connection and try again.";
      } else if (errorMsg.includes("not found")) {
        errorMessage = "Document not found. It may have been deleted or moved.";
      }
      errorToast({
        title: "Download failed",
        description: errorMessage,
      });
    }
  };
  return {
    pdfUrl,
    isUploading,
    uploadProgress,
    policyDocument,
    error,
    isLoadingUrl,
    handleFileChange,
    handleUpload,
    handleDownload,
  };
}
