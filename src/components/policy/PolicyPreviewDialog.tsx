import { <PERSON><PERSON>, DialogContent, DialogClose, DialogTitle } from "@/components/ui/dialog";
import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { useIsMobile } from "@/hooks/use-mobile";
import { X, ExternalLink, FileText } from "lucide-react";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
interface PolicyPreviewDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  pdfUrl: string;
  filename: string;
  isLoading?: boolean;
}
export function PolicyPreviewDialog({
  open,
  onOpenChange,
  pdfUrl,
  filename,
  isLoading = false,
}: PolicyPreviewDialogProps) {
  const isMobile = useIsMobile();
  const [loadError, setLoadError] = useState(false);
  const [browserSupportsPdf, setBrowserSupportsPdf] = useState(true);
  // Reset error state when dialog opens or URL changes
  useEffect(() => {
    if (open && pdfUrl) {
      setLoadError(false);
      // Check if browser is likely to support PDF embedding
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      const isMobileChrome = /Android.*Chrome/.test(navigator.userAgent);
      // iOS and some mobile browsers have known issues with embedded PDFs
      setBrowserSupportsPdf(!(isIOS || (isMobile && isMobileChrome)));
    }
  }, [open, pdfUrl, isMobile]);
  if (!open) return null;
  // Add view=FitH to the URL for better PDF viewing if it's a PDF URL
  const enhancedPdfUrl = pdfUrl?.includes(".pdf") ? `${pdfUrl}#view=FitH` : pdfUrl;
  const handleOpenInNewTab = () => {
    window.open(pdfUrl, "_blank");
  };
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent
        className="flex flex-col max-w-5xl p-0 overflow-hidden border rounded-lg shadow-lg"
        style={{
          height: isMobile ? "85vh" : "90vh",
          maxHeight: "calc(100vh - 40px)",
          width: isMobile ? "95vw" : "90vw",
        }}
      >
        {/* Accessible title for screen readers */}
        <VisuallyHidden>
          <DialogTitle>Policy Document Preview: {filename}</DialogTitle>
        </VisuallyHidden>
        {/* Custom header with minimal padding */}
        <div className="flex items-center justify-between p-2 border-b bg-background sticky top-0 z-10">
          <h2 className="text-base font-medium px-2 truncate" title={`Previewing: ${filename}`}>
            Previewing: {filename}
          </h2>
          <div className="flex items-center gap-1">
            {pdfUrl && (
              <Button
                variant="ghost"
                size="sm"
                className="text-xs"
                onClick={handleOpenInNewTab}
                title="Open in new tab"
              >
                <ExternalLink className="h-3.5 w-3.5 mr-1" />
                Open in new tab
              </Button>
            )}
            <DialogClose className="rounded-full p-1 hover:bg-muted focus:outline-none focus:ring-2 focus:ring-primary">
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </DialogClose>
          </div>
        </div>
        {/* Content area with flex-grow to fill available space */}
        <div className="flex-1 w-full overflow-hidden">
          {isLoading ? (
            <div className="w-full h-full flex items-center justify-center">
              <div className="flex flex-col items-center">
                <div className="w-10 h-10 border-t-2 border-primary rounded-full animate-spin mb-2"></div>
                <p>Loading document...</p>
              </div>
            </div>
          ) : pdfUrl ? (
            // If we're on mobile or we already know browser has issues with PDFs, show direct button to open in new tab
            !browserSupportsPdf || loadError ? (
              <div className="w-full h-full flex flex-col items-center justify-center p-6 text-center">
                <div className="bg-slate-50 dark:bg-slate-900 border border-slate-200 dark:border-slate-800 rounded-lg p-6 max-w-md">
                  <div className="flex flex-col items-center mb-4">
                    <div className="p-3 bg-slate-100 dark:bg-slate-800 rounded-full mb-3">
                      <FileText className="h-8 w-8 text-primary" />
                    </div>
                    <p className="font-semibold text-lg">PDF Preview Not Available</p>
                  </div>
                  <p className="mb-6 text-sm text-muted-foreground">
                    {loadError
                      ? "The PDF couldn't be displayed directly in this window due to browser security restrictions."
                      : "Your device doesn't fully support in-app PDF preview."}
                  </p>
                  <Button onClick={handleOpenInNewTab} className="w-full mb-2">
                    <ExternalLink className="mr-2 h-4 w-4" />
                    Open PDF in new tab
                  </Button>
                  {loadError && browserSupportsPdf && (
                    <Button
                      variant="outline"
                      onClick={() => setLoadError(false)}
                      className="w-full mt-2"
                    >
                      Try again
                    </Button>
                  )}
                </div>
              </div>
            ) : (
              // For desktop browsers, try the object tag first as it has better native PDF support
              <object
                data={enhancedPdfUrl}
                type="application/pdf"
                className="w-full h-full"
                onError={() => setLoadError(true)}
              >
                <iframe
                  src={enhancedPdfUrl}
                  className="w-full h-full border-0"
                  style={{ display: "block" }}
                  title={`${filename} Preview`}
                  sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-top-navigation"
                  loading="lazy"
                  onError={() => setLoadError(true)}
                  onLoad={e => {
                    try {
                      const iframe = e.target as HTMLIFrameElement;
                      // This will throw an error if cross-origin restrictions apply
                      if (
                        iframe.contentWindow &&
                        iframe.contentWindow.document.body.innerHTML === ""
                      ) {
                        setLoadError(true);
                      }
                    } catch (error) {
                      setLoadError(true);
                    }
                  }}
                >
                  <p>Your browser does not support PDF viewing.</p>
                </iframe>
              </object>
            )
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <p className="text-red-500">Unable to load document. Please try again later.</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
