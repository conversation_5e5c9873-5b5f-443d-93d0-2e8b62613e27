
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { FileText, Upload } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface PolicyDocumentCardProps {
  title: string;
  documentUrl?: string;
  onPreview: () => void;
  onUploadComplete: () => void;
  isUploading: boolean;
  uploadProgress?: number;
  policyDocument: File | null;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export function PolicyDocumentCard({ 
  title, 
  documentUrl,
  onPreview, 
  onUploadComplete,
  isUploading,
  uploadProgress = 0,
  policyDocument,
  handleFileChange
}: PolicyDocumentCardProps) {
  const documentName = policyDocument 
    ? policyDocument.name 
    : documentUrl 
      ? documentUrl.split('/').pop() 
      : `${title}.pdf`;

  const documentSize = policyDocument 
    ? `${(policyDocument.size / (1024 * 1024)).toFixed(2)} MB` 
    : documentUrl 
      ? 'PDF Document' 
      : 'No document uploaded yet';

  return (
    <Card>
      <CardHeader>
        <CardTitle>Policy Document</CardTitle>
      </CardHeader>
      <CardContent className="flex flex-col space-y-4">
        <div className="flex items-center gap-4">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div 
                  className={`p-4 bg-muted rounded-lg ${documentUrl ? 'cursor-pointer hover:bg-muted/80 transition-colors' : ''}`} 
                  onClick={documentUrl ? onPreview : undefined}
                  role={documentUrl ? "button" : undefined}
                  aria-label={documentUrl ? "Preview document" : "No document available"}
                  tabIndex={documentUrl ? 0 : -1}
                >
                  <FileText className={`h-8 w-8 ${documentUrl ? 'text-primary' : 'text-muted-foreground'}`} />
                </div>
              </TooltipTrigger>
              <TooltipContent>
                {documentUrl ? "View PDF document" : "No document available for preview"}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <div className="flex-1">
            <h3 className="font-medium">{documentName}</h3>
            <p className="text-sm text-muted-foreground">{documentSize}</p>
            
            {isUploading && (
              <div className="mt-2">
                <Progress value={uploadProgress} className="h-2" />
                <p className="text-xs text-muted-foreground mt-1">
                  Uploading: {Math.round(uploadProgress)}%
                </p>
              </div>
            )}
          </div>
        </div>
        
        <div className="flex justify-end">
          <input
            type="file"
            id="policy-upload"
            accept=".pdf"
            onChange={handleFileChange}
            className="hidden"
          />
          <label htmlFor="policy-upload">
            <Button variant="outline" className="cursor-pointer" asChild disabled={isUploading}>
              <span>
                <Upload className="mr-2 h-4 w-4" />
                {policyDocument ? "Change PDF" : "Select PDF"}
              </span>
            </Button>
          </label>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end border-t pt-4 gap-2">
        {policyDocument ? (
          <Button 
            onClick={onUploadComplete}
            disabled={isUploading}
          >
            {isUploading ? (
              <>
                <div className="w-4 h-4 mr-2 border-2 border-t-transparent border-white rounded-full animate-spin" />
                Uploading... {uploadProgress ? `${Math.round(uploadProgress)}%` : ''}
              </>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Upload PDF
              </>
            )}
          </Button>
        ) : documentUrl ? null : (
          <Button disabled>
            <Upload className="mr-2 h-4 w-4" />
            No Document Selected
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
