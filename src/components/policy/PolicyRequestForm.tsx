import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useToast } from "@/hooks/use-toast";
import { Form } from "@/components/ui/form";
import { usePolicyRequest } from "@/hooks/usePolicyRequest";
import { useAuth } from "@/contexts/auth";
// Import our form components
import { TitleInput } from "./form/TitleInput";
import { CategorySelect } from "./form/CategorySelect";
import { DescriptionInput } from "./form/DescriptionInput";
import { JustificationInput } from "./form/JustificationInput";
import { FileUpload } from "./form/FileUpload";
import { SubmitButton } from "./form/SubmitButton";
import { policyRequestSchema, PolicyRequestFormData } from "./form/schema";
interface PolicyRequestFormProps {
  onSuccess?: () => void;
}
export function PolicyRequestForm({ onSuccess }: PolicyRequestFormProps) {
  const { toast } = useToast();
  const { user } = useAuth();
  const { handlePolicyRequest, isSubmitting } = usePolicyRequest();
  const [referenceFile, setReferenceFile] = useState<File | null>(null);
  const form = useForm<PolicyRequestFormData>({
    resolver: zodResolver(policyRequestSchema),
    defaultValues: {
      title: "",
      category: "",
      description: "",
      justification: "",
    },
  });
  async function onSubmit(formData: PolicyRequestFormData) {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "You must be logged in to submit a policy request.",
        variant: "destructive",
      });
      return;
    }
    try {
      await handlePolicyRequest(formData, referenceFile ?? undefined);
      toast({
        title: "Policy request submitted",
        description: "Your policy request has been submitted successfully.",
      });
      // Reset form and file input
      form.reset();
      setReferenceFile(null);
      if (onSuccess) onSuccess();
    } catch (error: unknown) {
      toast({
        title: "Submission failed",
        description:
          error instanceof Error
            ? error.message
            : "An error occurred while submitting your request.",
        variant: "destructive",
      });
    }
  }
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <TitleInput form={form} />
        <CategorySelect form={form} />
        <DescriptionInput form={form} />
        <JustificationInput form={form} />
        <FileUpload referenceFile={referenceFile} setReferenceFile={setReferenceFile} />
        <SubmitButton isSubmitting={isSubmitting} />
      </form>
    </Form>
  );
}
