
import { Calendar, Download } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

interface PolicyHeaderProps {
  title: string;
  lastUpdated: string;
  version: string;
  documentUrl?: string;
  onDownload?: () => void;
}

export function PolicyHeader({ 
  title, 
  lastUpdated, 
  version, 
  documentUrl, 
  onDownload 
}: PolicyHeaderProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="space-y-1">
        <h2 className="text-2xl font-bold">{title}</h2>
        <div className="flex items-center gap-2 text-muted-foreground">
          <span className="flex items-center">
            <Calendar className="mr-1 h-4 w-4" />
            Last updated: {lastUpdated}
          </span>
          <span>|</span>
          <span>Version {version}</span>
        </div>
      </div>
      {documentUrl && onDownload && (
        <Button onClick={onDownload}>
          <Download className="mr-2 h-4 w-4" />
          Download PDF
        </Button>
      )}
      {!documentUrl && (
        <Button disabled>
          <Download className="mr-2 h-4 w-4" />
          No PDF Available
        </Button>
      )}
    </div>
  );
}
