
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>onte<PERSON>, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { PolicyRequest } from "@/types/policy";
import { formatDate } from "@/utils/dateUtils";

interface PolicyRequestRowProps {
  request: PolicyRequest;
  onView: () => void;
}

export function PolicyRequestRow({ request, onView }: PolicyRequestRowProps) {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-500';
      case 'rejected':
        return 'bg-red-500';
      default:
        return 'bg-yellow-500';
    }
  };

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <h3 className="font-medium">{request.title}</h3>
          <Badge className={getStatusColor(request.status)}>
            {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground">
          Requested on {formatDate(request.createdAt)}
        </p>
      </CardHeader>
      <CardContent>
        <p className="text-sm line-clamp-2">{request.description}</p>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button variant="outline" onClick={onView}>View Details</Button>
      </CardFooter>
    </Card>
  );
}
