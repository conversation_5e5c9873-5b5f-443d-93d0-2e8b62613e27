
import { useState, useEffect, useCallback } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Clock, FileText, Download, ChevronRight } from "lucide-react";
import { Policy } from "@/types/policy";
import { fetchAllPolicies } from "@/services/policy";
import { useToast } from "@/hooks/use-toast";
import { formatDate } from "@/utils/dateUtils";

export function PolicyVersionHistory() {
  const [policies, setPolicies] = useState<Policy[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedPolicy, setSelectedPolicy] = useState<string>("");
  const { toast } = useToast();

  const loadPolicies = useCallback(async () => {
    try {
      setLoading(true);
      const data = await fetchAllPolicies();
      setPolicies(data);
      if (data?.length > 0) {
        setSelectedPolicy(data[0]?.id ?? "");
      }
    } catch (error: unknown) {
      toast({
        title: "Failed to load policies",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    loadPolicies();
  }, [loadPolicies]);

  // Group policies by title to track versions
  const policyGroups = policies.reduce((groups, policy) => {
    if (!groups[policy.title]) {
      groups[policy.title] = [];
    }
    groups[policy.title]?.push(policy);
    return groups;
  }, {} as Record<string, Policy[]>);

  const currentPolicy = policies.find(p => p.id === selectedPolicy);
  const policyVersions = currentPolicy && policyGroups[currentPolicy.title]
    ? policyGroups[currentPolicy.title]?.sort((a, b) => 
        new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      ) || []
    : [];

  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-10 bg-gray-200 rounded w-full"></div>
        <div className="h-64 bg-gray-200 rounded w-full"></div>
      </div>
    );
  }

  if (policies.length === 0) {
    return (
      <Card className="p-6 text-center">
        <p className="text-muted-foreground">No policies found</p>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <Label htmlFor="policy-select">Select Policy</Label>
        <Select 
          value={selectedPolicy} 
          onValueChange={setSelectedPolicy}
        >
          <SelectTrigger className="mt-2">
            <SelectValue placeholder="Select policy" />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(policyGroups).map(([title, policies]) => (
              <SelectItem key={policies[0]?.id || title} value={policies[0]?.id ?? ""}>
                {title}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {currentPolicy && (
        <div className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-medium mb-2">Current Version</h3>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">Title</p>
                <p className="font-medium">{currentPolicy.title}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Version</p>
                <p className="font-medium">{currentPolicy.version}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Status</p>
                <Badge className={
                  currentPolicy.status === 'published' ? 'bg-green-500' :
                  currentPolicy.status === 'draft' ? 'bg-yellow-500' : 'bg-gray-500'
                }>
                  {currentPolicy.status.charAt(0).toUpperCase() + currentPolicy.status.slice(1)}
                </Badge>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Last Updated</p>
                <p className="font-medium">{formatDate(currentPolicy.updatedAt)}</p>
              </div>
            </div>

            <div className="mt-4">
              <p className="text-sm text-muted-foreground">Description</p>
              <p className="mt-1">{currentPolicy.description}</p>
            </div>

            <div className="flex gap-2 mt-4">
              <Button variant="outline" size="sm" asChild>
                <a href={`/policies/${currentPolicy.id}`}>
                  <FileText className="mr-1 h-4 w-4" />
                  View
                </a>
              </Button>
              {currentPolicy.documentUrl && (
                <Button size="sm" asChild>
                  <a href={currentPolicy.documentUrl} target="_blank" rel="noopener noreferrer">
                    <Download className="mr-1 h-4 w-4" />
                    Download Document
                  </a>
                </Button>
              )}
            </div>
          </Card>

          <div>
            <h3 className="text-lg font-medium mb-2">Version History</h3>
            {policyVersions.length <= 1 ? (
              <Card>
                <CardContent className="p-6 text-center">
                  <p className="text-muted-foreground">No previous versions available</p>
                </CardContent>
              </Card>
            ) : (
              <div className="space-y-2">
                {policyVersions.slice(1).map(version => (
                  <Card key={version.id} className="p-4">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 text-muted-foreground mr-2" />
                        <div>
                          <p className="font-medium">Version {version.version}</p>
                          <p className="text-sm text-muted-foreground">
                            Updated on {formatDate(version.updatedAt)}
                          </p>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm" asChild>
                        <a href={`/policies/${version.id}`}>
                          View
                          <ChevronRight className="ml-1 h-4 w-4" />
                        </a>
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
