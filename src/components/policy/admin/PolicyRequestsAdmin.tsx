
import { useState } from "react";
import { PolicyRequest } from "@/types/policy";
import { PolicyRequestDetailDialog } from "./PolicyRequestDetailDialog";
import { PendingRequestsSection } from "./components/PendingRequestsSection";
import { PastRequestsSection } from "./components/PastRequestsSection";
import { usePolicyRequests } from "./hooks/usePolicyRequests";

export function PolicyRequestsAdmin() {
  const [selectedRequest, setSelectedRequest] = useState<PolicyRequest | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  
  const {
    pendingRequests,
    pastRequests,
    loading,
    loadRequests,
    handleApprove,
    handleReject
  } = usePolicyRequests();

  const handleViewRequest = (request: PolicyRequest) => {
    setSelectedRequest(request);
    setDialogOpen(true);
  };

  const onApprove = async (requestId: string, reviewerId: string, feedback?: string) => {
    await handleApprove(requestId, reviewerId, feedback);
    setDialogOpen(false);
  };

  const onReject = async (requestId: string, reviewerId: string, feedback: string) => {
    await handleReject(requestId, reviewerId, feedback);
    setDialogOpen(false);
  };

  return (
    <div className="space-y-4">
      <PendingRequestsSection
        requests={pendingRequests}
        loading={loading}
        onViewRequest={handleViewRequest}
        onRefresh={loadRequests}
      />
      
      <PastRequestsSection
        requests={pastRequests}
        onViewRequest={handleViewRequest}
      />
      
      {selectedRequest && (
        <PolicyRequestDetailDialog
          open={dialogOpen}
          onOpenChange={setDialogOpen}
          request={selectedRequest}
          onApprove={onApprove}
          onReject={onReject}
        />
      )}
    </div>
  );
}
