
import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { PolicyFormTitle } from "./PolicyFormTitle";
import { PolicyFormCategory } from "./PolicyFormCategory";
import { PolicyFormDescription } from "./PolicyFormDescription";
import { PolicyFormStatus } from "./PolicyFormStatus";
import { PolicyFormEffectiveDate } from "./PolicyFormEffectiveDate";
import { DialogFooter } from "@/components/ui/dialog";
import { PolicyFormData } from "./PolicySchema";
import { UseFormReturn } from "react-hook-form";

interface PolicyFormProps {
  form: UseFormReturn<PolicyFormData>;
  onSubmit: (values: PolicyFormData) => Promise<void>;
  onCancel: () => void;
  isSubmitting: boolean;
  isEdit?: boolean;
}

export function PolicyForm({ 
  form, 
  onSubmit, 
  onCancel, 
  isSubmitting, 
  isEdit = false 
}: PolicyFormProps) {
  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <PolicyFormTitle form={form} />
        <PolicyFormCategory form={form} />
        <PolicyFormDescription form={form} />
        <PolicyFormStatus form={form} />
        <PolicyFormEffectiveDate form={form} />
        
        <DialogFooter>
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting 
              ? (isEdit ? "Updating..." : "Creating...") 
              : (isEdit ? "Update Policy" : "Create Policy")
            }
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
}
