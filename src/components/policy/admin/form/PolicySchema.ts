import { z } from "zod";

export const policyCategories = [
  "Information Security",
  "Human Resources",
  "Compliance",
  "Operational",
  "Governance",
  "Finance",
];

export const policySchema = z.object({
  title: z.string().min(5, "Title must be at least 5 characters"),
  description: z.string().min(20, "Description must be at least 20 characters"),
  category: z.string().min(1, "Category is required"),
  status: z.enum(["draft", "published", "archived"]),
  effectiveDate: z.date().optional(),
});

export type PolicyFormData = z.infer<typeof policySchema>;
