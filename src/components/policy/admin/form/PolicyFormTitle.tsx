
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { UseFormReturn } from "react-hook-form";
import { PolicyFormData } from "./PolicySchema";

interface PolicyFormTitleProps {
  form: UseFormReturn<PolicyFormData>;
}

export function PolicyFormTitle({ form }: PolicyFormTitleProps) {
  return (
    <FormField
      control={form.control}
      name="title"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Policy Title</FormLabel>
          <FormControl>
            <Input placeholder="Enter policy title" {...field} />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
