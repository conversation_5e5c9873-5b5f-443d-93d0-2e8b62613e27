
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Plus } from "lucide-react";
import { PolicyCreateDialog } from "./PolicyCreateDialog";
import { PolicySearchBar } from "./components/PolicySearchBar";
import { PolicyCard } from "./components/PolicyCard";
import { usePolicyManagement } from "./hooks/usePolicyManagement";
import { useNavigate } from "react-router-dom";

export function PolicyManagement() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const { policies, loading, searchTerm, setSearchTerm, loadPolicies } = usePolicyManagement();
  const navigate = useNavigate();

  const handleCreateSuccess = () => {
    loadPolicies();
    setCreateDialogOpen(false);
  };
  
  const handleEditPolicy = (policyId: string) => {
    navigate(`/policy-admin/edit/${policyId}`);
  };
  
  const handleViewPolicy = (policyId: string) => {
    navigate(`/policies/${policyId}`);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="p-4">
            <div className="animate-pulse">
              <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="flex justify-between">
                <div className="h-8 bg-gray-200 rounded w-24"></div>
                <div className="h-8 bg-gray-200 rounded w-24"></div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row gap-4 justify-between">
        <PolicySearchBar searchTerm={searchTerm} onSearchChange={setSearchTerm} />
        <Button onClick={() => setCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Policy
        </Button>
      </div>

      <div className="mt-6 grid gap-4">
        {policies.length === 0 ? (
          <Card className="p-6 text-center">
            <p className="text-muted-foreground">No policies found</p>
          </Card>
        ) : (
          policies.map(policy => (
            <PolicyCard
              key={policy.id}
              policy={policy}
              onView={handleViewPolicy}
              onEdit={handleEditPolicy}
            />
          ))
        )}
      </div>
      
      <PolicyCreateDialog 
        open={createDialogOpen} 
        onOpenChange={setCreateDialogOpen} 
        onSuccess={handleCreateSuccess} 
      />
    </div>
  );
}
