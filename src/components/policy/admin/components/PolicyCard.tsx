
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Policy } from "@/types/policy";

interface PolicyCardProps {
  policy: Policy;
  onView: (policyId: string) => void;
  onEdit: (policyId: string) => void;
}

export function PolicyCard({ policy, onView, onEdit }: PolicyCardProps) {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-2 bg-gray-50">
        <div className="flex justify-between items-center">
          <h3 className="font-medium">{policy.title}</h3>
          <div className="flex gap-2">
            <Badge className={
              policy.status === 'published' ? 'bg-green-500' :
              policy.status === 'draft' ? 'bg-yellow-500' : 'bg-gray-500'
            }>
              {policy.status.charAt(0).toUpperCase() + policy.status.slice(1)}
            </Badge>
            <Badge variant="outline">{policy.category}</Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="py-3">
        <div className="text-sm line-clamp-2">{policy.description}</div>
        <div className="text-xs text-muted-foreground mt-2">
          Version: {policy.version}
          {policy.effectiveDate && ` | Effective: ${new Date(policy.effectiveDate).toLocaleDateString()}`}
        </div>
      </CardContent>
      
      <CardFooter className="bg-gray-50 flex justify-between py-2">
        <Button variant="outline" size="sm" onClick={() => onView(policy.id)}>
          View
        </Button>
        <Button variant="default" size="sm" onClick={() => onEdit(policy.id)}>
          Edit
        </Button>
      </CardFooter>
    </Card>
  );
}
