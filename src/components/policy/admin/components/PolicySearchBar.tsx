
import { Input } from "@/components/ui/input";
import { Search } from "lucide-react";

interface PolicySearchBarProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
}

export function PolicySearchBar({ searchTerm, onSearchChange }: PolicySearchBarProps) {
  return (
    <div className="relative flex-grow">
      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
      <Input
        className="pl-9"
        placeholder="Search policies..."
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
      />
    </div>
  );
}
