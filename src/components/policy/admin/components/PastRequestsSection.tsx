
import { PolicyRequest } from "@/types/policy";
import { PolicyRequestRow } from "../PolicyRequestRow";

interface PastRequestsSectionProps {
  requests: PolicyRequest[];
  onViewRequest: (request: PolicyRequest) => void;
}

export function PastRequestsSection({ requests, onViewRequest }: PastRequestsSectionProps) {
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold mt-8">Past Reviews</h2>
      
      {requests.map(request => (
        <PolicyRequestRow
          key={request.id}
          request={request}
          onView={() => onViewRequest(request)}
        />
      ))}
    </div>
  );
}
