
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { PolicyRequest } from "@/types/policy";
import { PolicyRequestRow } from "../PolicyRequestRow";

interface PendingRequestsSectionProps {
  requests: PolicyRequest[];
  loading: boolean;
  onViewRequest: (request: PolicyRequest) => void;
  onRefresh: () => void;
}

export function PendingRequestsSection({ 
  requests, 
  loading, 
  onViewRequest, 
  onRefresh 
}: PendingRequestsSectionProps) {
  if (loading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="p-4">
            <div className="animate-pulse">
              <div className="h-5 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
              <div className="flex justify-between">
                <div className="h-8 bg-gray-200 rounded w-24"></div>
                <div className="h-8 bg-gray-200 rounded w-24"></div>
              </div>
            </div>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">Pending Requests</h2>
        <Button 
          variant="outline" 
          size="sm"
          onClick={onRefresh}
        >
          Refresh
        </Button>
      </div>
      
      {requests.length === 0 ? (
        <Card className="p-6 text-center">
          <p className="text-muted-foreground">No pending requests</p>
        </Card>
      ) : (
        requests.map(request => (
          <PolicyRequestRow
            key={request.id}
            request={request}
            onView={() => onViewRequest(request)}
          />
        ))
      )}
    </div>
  );
}
