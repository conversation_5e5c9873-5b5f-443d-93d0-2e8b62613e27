
import { useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { PolicyForm } from "./form/PolicyForm";
import { usePolicyForm } from "./hooks/usePolicyForm";
import { usePolicy } from "@/hooks/usePolicies";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

export function PolicyEditDialog() {
  const { policyId } = useParams<{ policyId: string }>();
  const navigate = useNavigate();
  const { policy, loading } = usePolicy(policyId);
  
  const handleClose = () => {
    navigate("/policy-admin");
  };
  
  const handleSuccess = () => {
    navigate("/policy-admin");
  };
  
  const { form, isSubmitting, handleSubmit, handleCancel } = usePolicyForm({
    policyId: policyId ?? "",
    initialData: policy,
    isEdit: true,
    onSuccess: handleSuccess,
    onCancel: handleClose,
  });

  useEffect(() => {
    if (!loading && !policy) {
      // If policy not found, redirect back to policy admin
      navigate("/policy-admin");
    }
  }, [policy, loading, navigate]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[300px]">
        <LoadingSpinner />
      </div>
    );
  }
  
  if (!policy) {
    return null;
  }

  return (
    <Dialog open={true} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Policy</DialogTitle>
        </DialogHeader>
        
        <PolicyForm 
          form={form}
          onSubmit={handleSubmit} 
          onCancel={handleCancel} 
          isSubmitting={isSubmitting}
          isEdit={true}
        />
      </DialogContent>
    </Dialog>
  );
}
