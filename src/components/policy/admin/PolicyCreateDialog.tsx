
import { Dialog, DialogContent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { PolicyForm } from "./form/PolicyForm";
import { usePolicyForm } from "./hooks/usePolicyForm";

interface PolicyCreateDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
}

export function PolicyCreateDialog({ open, onOpenChange, onSuccess }: PolicyCreateDialogProps) {
  const { form, isSubmitting, handleSubmit, handleCancel } = usePolicyForm({
    onSuccess,
    onCancel: () => onOpenChange(false),
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Create New Policy</DialogTitle>
        </DialogHeader>
        
        <PolicyForm 
          form={form}
          onSubmit={handleSubmit} 
          onCancel={handleCancel} 
          isSubmitting={isSubmitting} 
        />
      </DialogContent>
    </Dialog>
  );
}
