import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Label } from "@/components/ui/label";
import { PolicyRequest } from "@/types/policy";
import { formatDate } from "@/utils/dateUtils";
import { useAuth } from "@/contexts/auth";

interface PolicyRequestDetailDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  request: PolicyRequest;
  onApprove: (requestId: string, reviewerId: string, feedback?: string) => void;
  onReject: (requestId: string, reviewerId: string, feedback: string) => void;
}

export function PolicyRequestDetailDialog({
  open,
  onOpenChange,
  request,
  onApprove,
  onReject,
}: PolicyRequestDetailDialogProps) {
  const [feedback, setFeedback] = useState("");
  const { user } = useAuth();
  const isPending = request.status === "pending";

  const handleApprove = () => {
    if (!user) return;
    onApprove(request.id, user.id, feedback);
  };

  const handleReject = () => {
    if (!user || !feedback.trim()) return;
    onReject(request.id, user.id, feedback);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Policy Request Details</DialogTitle>
          <div className="flex justify-between items-center">
            <span>{request.title}</span>
            <Badge className={
              request.status === 'approved' ? 'bg-green-500' :
              request.status === 'rejected' ? 'bg-red-500' : 'bg-yellow-500'
            }>
              {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
            </Badge>
          </div>
          <div className="text-sm text-muted-foreground">
            Requested on {formatDate(request.createdAt)}
          </div>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label>Description</Label>
            <div className="mt-2 p-3 border rounded-md bg-gray-50">
              {request.description}
            </div>
          </div>
          
          <div>
            <Label>Justification</Label>
            <div className="mt-2 p-3 border rounded-md bg-gray-50">
              {request.reason}
            </div>
          </div>
          
          {request.referenceDocumentUrl && (
            <div>
              <Label>Reference Document</Label>
              <div className="mt-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => window.open(request.referenceDocumentUrl, '_blank')}
                >
                  View Document
                </Button>
              </div>
            </div>
          )}
          
          {isPending ? (
            <div>
              <Label>Feedback (required for rejection)</Label>
              <Textarea
                value={feedback}
                onChange={(e) => setFeedback(e.target.value)}
                className="mt-2"
                placeholder="Enter feedback for the requestor..."
              />
            </div>
          ) : request.feedback ? (
            <div>
              <Label>Feedback</Label>
              <div className="mt-2 p-3 border rounded-md bg-gray-50">
                {request.feedback}
              </div>
            </div>
          ) : null}
        </div>
        
        <DialogFooter>
          {isPending ? (
            <>
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                Cancel
              </Button>
              <Button variant="destructive" onClick={handleReject} disabled={!feedback.trim()}>
                Reject
              </Button>
              <Button onClick={handleApprove}>
                Approve
              </Button>
            </>
          ) : (
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Close
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}