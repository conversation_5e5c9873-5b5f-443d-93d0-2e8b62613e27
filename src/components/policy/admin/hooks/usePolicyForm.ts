import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/contexts/auth";
import { createPolicy, updatePolicy } from "@/services/policy";
import { PolicyFormData, policySchema } from "../form/PolicySchema";
import { PolicyFormValues } from "@/types/policy";
import { Policy } from "@/types/policy";

interface UsePolicyFormProps {
  policyId?: string;
  initialData?: Policy | null;
  isEdit?: boolean;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const usePolicyForm = ({
  policyId,
  initialData,
  isEdit = false,
  onSuccess,
  onCancel,
}: UsePolicyFormProps = {}) => {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<PolicyFormData>({
    resolver: zodResolver(policySchema),
    defaultValues: {
      title: "",
      description: "",
      category: "",
      status: "draft",
      effectiveDate: undefined,
    },
  });

  // Update form values when editing an existing policy
  useEffect(() => {
    if (isEdit && initialData) {
      const effectiveDate = initialData.effectiveDate
        ? new Date(initialData.effectiveDate)
        : undefined;

      form.reset({
        title: initialData.title,
        description: initialData.description,
        category: initialData.category,
        status: initialData.status as "draft" | "published" | "archived",
        effectiveDate: effectiveDate,
      });
    }
  }, [isEdit, initialData, form]);

  const handleSubmit = async (values: PolicyFormData) => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "You must be logged in to manage policies",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Ensure all required fields are present in the policy values
      const policyValues: PolicyFormValues = {
        title: values.title,
        description: values.description,
        category: values.category,
        status: values.status,
        effectiveDate: values.effectiveDate ?? undefined,
      };

      if (isEdit && policyId) {
        // Update existing policy
        await updatePolicy(policyId, policyValues);

        toast({
          title: "Policy updated",
          description: "The policy has been updated successfully",
        });
      } else {
        // Create new policy
        await createPolicy(policyValues, user.id);

        toast({
          title: "Policy created",
          description: "The new policy has been created successfully",
        });
      }

      if (onSuccess) onSuccess();
    } catch (error: unknown) {
      toast({
        title: isEdit ? "Failed to update policy" : "Failed to create policy",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (onCancel) onCancel();
  };

  return {
    form,
    isSubmitting,
    handleSubmit,
    handleCancel,
  };
};
