
import { useState, useEffect, useCallback } from "react";
import { fetchPolicyRequests, updatePolicyRequestStatus } from "@/services/policy";
import { PolicyRequest } from "@/types/policy";
import { useToast } from "@/hooks/use-toast";

export function usePolicyRequests() {
  const [requests, setRequests] = useState<PolicyRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  const loadRequests = useCallback(async () => {
    try {
      setLoading(true);
      const data = await fetchPolicyRequests();
      setRequests(data);
    } catch (error: unknown) {
      toast({
        title: "Failed to load requests",
        description: (error as Error).message ?? "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    loadRequests();
  }, [loadRequests]);

  const handleApprove = async (requestId: string, reviewerId: string, feedback?: string) => {
    try {
      await updatePolicyRequestStatus(requestId, "approved", feedback, reviewerId);
      toast({
        title: "Request approved",
        description: "The policy request has been approved"
      });
      loadRequests();
    } catch (error: unknown) {
      toast({
        title: "Failed to approve request",
        description: (error as Error).message ?? "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  const handleReject = async (requestId: string, reviewerId: string, feedback: string) => {
    try {
      await updatePolicyRequestStatus(requestId, "rejected", feedback, reviewerId);
      toast({
        title: "Request rejected",
        description: "The policy request has been rejected"
      });
      loadRequests();
    } catch (error: unknown) {
      toast({
        title: "Failed to reject request",
        description: (error as Error).message ?? "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  const pendingRequests = requests.filter(req => req.status === "pending");
  const pastRequests = requests.filter(req => req.status !== "pending");

  return {
    requests,
    pendingRequests,
    pastRequests,
    loading,
    loadRequests,
    handleApprove,
    handleReject
  };
}
