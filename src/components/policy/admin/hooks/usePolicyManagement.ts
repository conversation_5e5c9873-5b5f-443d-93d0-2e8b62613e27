
import { useState, useEffect, useCallback } from "react";
import { Policy } from "@/types/policy";
import { fetchAllPolicies } from "@/services/policy";
import { useToast } from "@/hooks/use-toast";

export function usePolicyManagement() {
  const [policies, setPolicies] = useState<Policy[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const { toast } = useToast();

  const loadPolicies = useCallback(async () => {
    try {
      setLoading(true);
      const data = await fetchAllPolicies();
      setPolicies(data);
    } catch (error: unknown) {
      toast({
        title: "Failed to load policies",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  }, [toast]);

  useEffect(() => {
    loadPolicies();
  }, [loadPolicies]);

  const filteredPolicies = policies.filter(policy => 
    policy.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    policy.category.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return {
    policies: filteredPolicies,
    loading,
    searchTerm,
    setSearchTerm,
    loadPolicies
  };
}
