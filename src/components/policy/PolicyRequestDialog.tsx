
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { PolicyRequestForm } from "./PolicyRequestForm";

interface PolicyRequestDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function PolicyRequestDialog({ open, onOpenChange }: PolicyRequestDialogProps) {
  const handleSuccess = () => {
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] h-[90vh] flex flex-col p-0">
        <DialogHeader className="flex-shrink-0 px-6 pt-6 pb-4">
          <DialogTitle>Request New Policy</DialogTitle>
          <DialogDescription>
            Fill out the form below to request a new policy. Our policy team will review your request.
            You can also upload any reference documents that might be helpful.
          </DialogDescription>
        </DialogHeader>
        <ScrollArea className="flex-1 px-6 pb-6">
          <PolicyRequestForm onSuccess={handleSuccess} />
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
