
import { useLocation, useNavigate } from "react-router-dom"
import { useAuth } from "@/contexts/auth"
import { useSidebar } from "@/components/ui/sidebar"
import { useIsMobile } from "@/hooks/use-mobile"
import { useScrollToTop } from "@/components/navigation/ScrollToTop"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { UserRole } from "@/types"
import { 
  Home, 
  Shield, 
  AlertTriangle, 
  FileText, 
  BarChart3, 
  Settings,
  Building2,
  Menu,
  X
} from "lucide-react"

interface NavigationItem {
  title: string
  url: string
  icon: React.ForwardRefExoticComponent<
    Omit<React.SVGProps<SVGSVGElement>, "ref"> & 
    { size?: string | number } & 
    React.RefAttributes<SVGSVGElement>
  >
  badge?: string | number
  description?: string
}

export function MobileNavigation() {
  const { user, hasPermission } = useAuth()
  const { openMobile, setOpenMobile } = useSidebar()
  const isMobile = useIsMobile()
  const location = useLocation()
  const navigate = useNavigate()
  const { scrollToTop } = useScrollToTop()

  // Don't render on desktop
  if (!isMobile) return null

  const navigationItems: NavigationItem[] = [
    { 
      title: "Dashboard", 
      url: "/dashboard", 
      icon: Home,
      description: "Overview and key metrics"
    },
    { 
      title: "Risk Register", 
      url: "/risks", 
      icon: Shield,
      description: "Manage organizational risks"
    },
    { 
      title: "Incidents", 
      url: "/incidents", 
      icon: AlertTriangle,
      description: "Track and manage incidents"
    },
    { 
      title: "Policies", 
      url: "/policies", 
      icon: FileText,
      description: "Policy library and compliance"
    },
    { 
      title: "Reports", 
      url: "/reports", 
      icon: BarChart3,
      description: "Analytics and reporting"
    },
  ]

  const adminItems: NavigationItem[] = [
    { 
      title: "Risk Framework", 
      url: "/administration", 
      icon: Settings,
      description: "Configure risk categories and templates"
    },
    { 
      title: "Organization", 
      url: "/organization", 
      icon: Building2,
      description: "Organization settings and users"
    },
  ]

  const isActive = (path: string) => {
    if (path === "/dashboard") {
      return location.pathname === "/dashboard"
    }
    if (path === "/administration") {
      return location.pathname.startsWith("/policy-admin") || 
             location.pathname.startsWith("/risk-categories") ||
             location.pathname.startsWith("/administration")
    }
    return location.pathname.startsWith(path)
  }

  const handleNavigation = (url: string) => {
    navigate(url)
    setOpenMobile(false)
    // Ensure we scroll to top after navigation
    setTimeout(() => {
      scrollToTop('instant')
    }, 100)
  }

  const renderNavigationItem = (item: NavigationItem) => (
    <button
      key={item.title}
      onClick={() => handleNavigation(item.url)}
      className={cn(
        "w-full flex items-center gap-4 p-4 text-left rounded-lg transition-colors",
        "hover:bg-accent hover:text-accent-foreground",
        "focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
        "active:scale-[0.98] transition-transform",
        isActive(item.url) 
          ? "bg-primary/10 text-primary border border-primary/20" 
          : "text-muted-foreground"
      )}
    >
      <div className={cn(
        "flex items-center justify-center w-10 h-10 rounded-lg",
        isActive(item.url) 
          ? "bg-primary/20 text-primary" 
          : "bg-muted text-muted-foreground"
      )}>
        <item.icon className="h-5 w-5" />
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2">
          <span className="font-medium truncate">{item.title}</span>
          {item.badge && (
            <Badge variant="secondary" className="text-xs">
              {item.badge}
            </Badge>
          )}
        </div>
        {item.description && (
          <p className="text-xs text-muted-foreground mt-1 truncate">
            {item.description}
          </p>
        )}
      </div>
    </button>
  )

  return (
    <>
      {/* Overlay */}
      {openMobile && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 md:hidden"
          onClick={() => setOpenMobile(false)}
        />
      )}
      
      {/* Mobile Navigation Drawer */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 w-80 bg-background border-r transform transition-transform duration-300 ease-in-out md:hidden",
        "flex flex-col",
        openMobile ? "translate-x-0" : "-translate-x-full"
      )}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <Shield className="h-5 w-5 text-primary-foreground" />
            </div>
            <div>
              <h2 className="font-semibold text-lg">Risk Compass</h2>
              <p className="text-xs text-muted-foreground">
                {user?.organizationId ? 'Organization' : 'No Organization'}
              </p>
            </div>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
            onClick={() => setOpenMobile(false)}
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close menu</span>
          </Button>
        </div>

        {/* Navigation Content */}
        <div className="flex-1 overflow-y-auto p-4 space-y-6">
          {/* Main Navigation */}
          <div>
            <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wider mb-3">
              Navigation
            </h3>
            <div className="space-y-2">
              {navigationItems.map(renderNavigationItem)}
            </div>
          </div>

          {/* Admin Navigation */}
          {user && hasPermission([UserRole.ADMIN]) && (
            <div>
              <h3 className="text-xs font-medium text-muted-foreground uppercase tracking-wider mb-3">
                Administration
              </h3>
              <div className="space-y-2">
                {adminItems.map(renderNavigationItem)}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t bg-muted/30">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
              <span className="text-xs font-medium text-primary">
                {user?.name?.charAt(0)?.toUpperCase() ?? 'U'}
              </span>
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium truncate">{user?.name}</p>
              <p className="text-xs text-muted-foreground truncate">{user?.email}</p>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

// Quick access floating action button for mobile
export function MobileQuickAccess() {
  const isMobile = useIsMobile()
  const { setOpenMobile } = useSidebar()
  const location = useLocation()

  // Don't show on certain pages
  const hiddenPaths = ['/login', '/signup', '/']
  if (!isMobile || hiddenPaths.includes(location.pathname)) {
    return null
  }

  return (
    <Button
      onClick={() => setOpenMobile(true)}
      className={cn(
        "fixed bottom-6 right-6 z-30 h-14 w-14 rounded-full shadow-lg",
        "bg-primary hover:bg-primary/90 text-primary-foreground",
        "border-2 border-background",
        "transition-all duration-200 hover:scale-105 active:scale-95"
      )}
      aria-label="Open navigation menu"
    >
      <Menu className="h-6 w-6" />
    </Button>
  )
}
