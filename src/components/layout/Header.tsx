// src/components/layout/Header.tsx
// Enhanced Mobile Header Component with Linear styling
import { Button } from "@/components/ui/button";
import UserMenu from "./UserMenu";
import { User } from "@/types";
import { useNavigate, useLocation } from "react-router-dom";
import { SidebarTrigger, useSidebar } from "@/components/ui/sidebar";
import { OrganizationSwitcher } from "@/components/organization/OrganizationSwitcher";
import { useIsMobile } from "@/hooks/use-mobile";
import { useScrollToTop } from "@/components/navigation/ScrollToTop";
import { Search, Menu, ArrowLeft } from "lucide-react";
interface HeaderProps {
  user: User;
}
const Header = ({ user }: HeaderProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useIsMobile();

  const { toggleSidebar } = useSidebar();
  const { scrollToTop } = useScrollToTop();
  const getPageTitle = () => {
    const path = location.pathname;
    if (path === "/dashboard") return "Dashboard";
    if (path === "/risks") return "Risk Register";
    if (path.startsWith("/risks/") && !path.startsWith("/risks/templates")) return "Risk Details";
    if (path === "/risks/templates") return "Risk Templates";
    if (path === "/incidents") return "Incidents";
    if (path.startsWith("/incidents/new")) return "Report Incident";
    if (path.startsWith("/incidents/")) return "Incident Details";
    if (path === "/reports") return "Reports & Analytics";
    if (path === "/policies" || path.startsWith("/policies/")) return "Policy Library";
    if (path === "/users") return "User Management";
    if (path === "/risk-categories") return "Risk Categories";
    if (path === "/organization") return "Organization Settings";
    return "";
  };
  const canGoBack = () => {
    const path = location.pathname;
    return path !== "/dashboard" && !["/", "/login", "/signup"].includes(path);
  };
  const handleBackNavigation = () => {
    if (window.history.length > 1) {
      navigate(-1);
    } else {
      navigate("/dashboard");
    }
    // Ensure we scroll to top after navigation
    setTimeout(() => {
      scrollToTop("instant");
    }, 100);
  };
  if (isMobile) {
    return (
      <header className="header-mobile flex items-center justify-between px-4 safe-area-top">
        <div className="flex items-center flex-1 min-w-0">
          {/* Mobile navigation - hamburger menu or back button */}
          {canGoBack() ? (
            <Button
              variant="ghost"
              size="sm"
              className="h-9 w-9 p-0 mr-3 flex-shrink-0"
              onClick={handleBackNavigation}
              aria-label="Go back"
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
          ) : (
            <Button
              variant="ghost"
              size="sm"
              className="h-9 w-9 p-0 mr-3 flex-shrink-0"
              onClick={toggleSidebar}
              aria-label="Open menu"
            >
              <Menu className="h-5 w-5" />
            </Button>
          )}
          {/* Page title */}
          <h1 className="text-lg font-semibold truncate">{getPageTitle()}</h1>
        </div>
        {/* Right section with actions */}
        <div className="flex items-center gap-2 flex-shrink-0">
          {/* Search button */}
          <Button
            variant="ghost"
            size="sm"
            className="h-9 w-9 p-0"
            onClick={() => {
              // You can implement search functionality here
            }}
            aria-label="Search"
          >
            <Search className="h-4 w-4" />
          </Button>
          {/* User menu */}
          <UserMenu user={user} />
        </div>
      </header>
    );
  }
  // Desktop header
  return (
    <header className="h-16 border-b flex items-center justify-between px-4 lg:px-6">
      <div className="flex items-center flex-1 min-w-0">
        <SidebarTrigger className="mr-3 flex-shrink-0" />
        {/* Page title - responsive */}
        <h1 className="text-xl font-semibold truncate hidden md:block">{getPageTitle()}</h1>
      </div>
      {/* Center section with Organization Switcher */}
      <div className="flex-1 flex justify-center max-w-sm mx-4">
        <OrganizationSwitcher />
      </div>
      {/* Right section with user menu */}
      <div className="flex items-center gap-2 flex-shrink-0">
        <UserMenu user={user} />
      </div>
    </header>
  );
};
export default Header;
