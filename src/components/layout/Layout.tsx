// src/components/layout/Layout.tsx
// Enhanced Mobile Layout Component with Linear styling

import { useAuth } from "@/contexts/auth";
import AppSidebar from "./AppSidebar";
import Header from "@/components/layout/Header";
import { MobileNavigation, MobileQuickAccess } from "@/components/layout/MobileNavigation";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useIsMobile } from "@/hooks/use-mobile";

interface LayoutProps {
  children?: React.ReactNode;
}

export const Layout = ({ children }: LayoutProps) => {
  const { user } = useAuth();
  const isMobile = useIsMobile();

  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full bg-background">
        {/* Enhanced AppSidebar with mobile optimizations */}
        <AppSidebar />

        {/* Mobile Navigation Drawer */}
        <MobileNavigation />

        {/* Main content area with mobile-specific styling */}
        <SidebarInset className="flex flex-col flex-1">
          {/* Enhanced Header with mobile optimizations */}
          <Header user={user!} />

          {/* Mobile-optimized main content */}
          <main
            id="main-content"
            className={`
              flex-1
              ${isMobile ? 'mobile-container' : 'p-4 md:p-6'}
              safe-area-bottom
            `}
          >
            <div className={isMobile ? 'mobile-section' : ''}>
              {children}
            </div>
          </main>
        </SidebarInset>

        {/* Mobile Quick Access Button */}
        <MobileQuickAccess />
      </div>
    </SidebarProvider>
  );
};

export default Layout;