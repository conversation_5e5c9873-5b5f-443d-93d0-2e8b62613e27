import { useState, useEffect, useCallback } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { validateInviteCode } from "@/contexts/auth/inviteCodeValidation";
interface InviteCodeInputProps {
  value: string;
  onChange: (value: string) => void;
  onValidationChange: (validation: InviteValidation) => void;
  disabled?: boolean;
}
interface InviteValidation {
  loading: boolean;
  valid: boolean;
  orgName?: string;
}
export function InviteCodeInput({
  value,
  onChange,
  onValidationChange,
  disabled = false,
}: InviteCodeInputProps) {
  const [validation, setValidation] = useState<InviteValidation>({
    loading: false,
    valid: false,
  });
  const validateInviteCodeAsync = useCallback(
    async (code: string) => {
      const loadingState = { loading: true, valid: false };
      setValidation(loadingState);
      onValidationChange(loadingState);
      try {
        const result = await validateInviteCode(code);
        if (result.success) {
          const validState = {
            loading: false,
            valid: true,
            orgName: "Organization", // Could be enhanced with actual org name
          };
          setValidation(validState);
          onValidationChange(validState);
        } else {
          const invalidState = { loading: false, valid: false };
          setValidation(invalidState);
          onValidationChange(invalidState);
        }
      } catch (err) {
        const errorState = { loading: false, valid: false };
        setValidation(errorState);
        onValidationChange(errorState);
      }
    },
    [onValidationChange]
  );
  useEffect(() => {
    if (value?.trim()) {
      validateInviteCodeAsync(value.trim());
    } else {
      const resetValidation = { loading: false, valid: false };
      setValidation(resetValidation);
      onValidationChange(resetValidation);
    }
  }, [value, validateInviteCodeAsync, onValidationChange]);
  return (
    <div className="space-y-2">
      <Label htmlFor="invite-code">Invite Code (Optional)</Label>
      <Input
        id="invite-code"
        value={value}
        onChange={e => onChange(e.target.value)}
        placeholder="Enter invite code if you have one"
        disabled={disabled}
      />
      {validation.loading && (
        <p className="text-xs text-muted-foreground">Validating invite code...</p>
      )}
      {value?.trim() && validation.valid && validation.orgName && (
        <p className="text-xs text-green-600">✓ Valid invite for {validation.orgName}</p>
      )}
    </div>
  );
}
export type { InviteValidation };
