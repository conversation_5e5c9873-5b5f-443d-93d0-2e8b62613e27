import { Alert, AlertDescription } from "@/components/ui/alert";
import { Info } from "lucide-react";

interface SignupInfoProps {
  hasInviteCode: boolean;
}

export function SignupInfo({ hasInviteCode }: SignupInfoProps) {
  const getInfoText = () => {
    if (hasInviteCode) {
      return "With an invite code, you'll join a specific organization with the assigned role.";
    }
    return "After creating your account, you'll be able to join an organization or create your own.";
  };

  return (
    <Alert variant="default" className="bg-muted">
      <Info className="h-4 w-4" />
      <AlertDescription className="text-sm">
        {getInfoText()}
      </AlertDescription>
    </Alert>
  );
}