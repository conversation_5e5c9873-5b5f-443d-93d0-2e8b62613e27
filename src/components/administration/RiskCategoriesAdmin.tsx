import { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/auth";
import { CategoryTable } from "./risk-categories/CategoryTable";
import { CategoryForm } from "./risk-categories/CategoryForm";
import { CategoryDeleteDialog } from "./risk-categories/CategoryDeleteDialog";
interface Category {
  id: string;
  name: string;
  description: string | null;
}
export const RiskCategoriesAdmin = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [deletingCategory, setDeletingCategory] = useState<Category | null>(null);
  const { toast } = useToast();
  const { organization } = useAuth();
  const fetchCategories = useCallback(async () => {
    if (!organization?.id) return;
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("risk_categories")
        .select("*")
        .eq("organization_id", organization.id)
        .order("name");
      if (error) throw error;
      setCategories(data ?? []);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load risk categories.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  }, [organization?.id, toast]);
  useEffect(() => {
    fetchCategories();
  }, [organization?.id, fetchCategories]);
  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    setIsFormOpen(true);
  };
  const handleDelete = (category: Category) => {
    setDeletingCategory(category);
    setIsDeleteDialogOpen(true);
  };
  const handleFormClose = () => {
    setIsFormOpen(false);
    setEditingCategory(null);
  };
  const handleDeleteDialogClose = () => {
    setIsDeleteDialogOpen(false);
    setDeletingCategory(null);
  };
  const handleSaved = () => {
    fetchCategories();
    handleFormClose();
  };
  const handleDeleted = () => {
    fetchCategories();
    handleDeleteDialogClose();
  };
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-semibold">Risk Categories</h2>
          <p className="text-muted-foreground">Manage risk categories for your organization</p>
        </div>
        <Button onClick={() => setIsFormOpen(true)} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          Add Category
        </Button>
      </div>
      <CategoryTable
        categories={categories}
        loading={loading}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />
      <CategoryForm
        isOpen={isFormOpen}
        onClose={handleFormClose}
        editingCategory={editingCategory}
        onSaved={handleSaved}
      />
      <CategoryDeleteDialog
        isOpen={isDeleteDialogOpen}
        onClose={handleDeleteDialogClose}
        category={deletingCategory}
        onDeleted={handleDeleted}
      />
    </div>
  );
};
