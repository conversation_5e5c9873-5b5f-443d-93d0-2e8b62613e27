import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/auth";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
interface Category {
  id: string;
  name: string;
  description: string | null;
}
interface CategoryDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  category: Category | null;
  onDeleted: () => void;
}
export const CategoryDeleteDialog = ({
  isOpen,
  onClose,
  category,
  onDeleted,
}: CategoryDeleteDialogProps) => {
  const { toast } = useToast();
  const { organization } = useAuth();
  const handleDelete = async () => {
    if (!category || !organization?.id) return;
    try {
      // Check if category is in use
      const { data: risksUsingCategory, error: checkError } = await supabase
        .from("risks")
        .select("id")
        .eq("category_id", category.id)
        .eq("organization_id", organization.id)
        .limit(1);
      if (checkError) throw checkError;
      if (risksUsingCategory?.length > 0) {
        toast({
          title: "Cannot Delete",
          description: "This category is being used by existing risks and cannot be deleted.",
          variant: "destructive",
        });
        onClose();
        return;
      }
      const { error } = await supabase
        .from("risk_categories")
        .delete()
        .eq("id", category.id)
        .eq("organization_id", organization.id);
      if (error) throw error;
      toast({
        title: "Success",
        description: "Category deleted successfully.",
      });
      onClose();
      onDeleted();
    } catch (error: unknown) {
      toast({
        title: "Error",
        description: (error as Error)?.message ?? "Failed to delete category.",
        variant: "destructive",
      });
    }
  };
  return (
    <AlertDialog open={isOpen} onOpenChange={onClose}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This will permanently delete the category "{category?.name}". This action cannot be
            undone.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={handleDelete}>Delete</AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
