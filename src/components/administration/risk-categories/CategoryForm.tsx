import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/auth";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
interface Category {
  id: string;
  name: string;
  description: string | null;
}
interface CategoryFormProps {
  isOpen: boolean;
  onClose: () => void;
  editingCategory: Category | null;
  onSaved: () => void;
}
export const CategoryForm = ({ isOpen, onClose, editingCategory, onSaved }: CategoryFormProps) => {
  const { toast } = useToast();
  const { organization } = useAuth();
  const [formData, setFormData] = useState({
    name: "",
    description: "",
  });
  // Update form data when editingCategory changes
  useEffect(() => {
    if (editingCategory) {
      setFormData({
        name: editingCategory.name,
        description: editingCategory.description ?? "",
      });
    } else {
      setFormData({ name: "", description: "" });
    }
  }, [editingCategory]);
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };
  const handleSave = async () => {
    if (!organization?.id) {
      toast({
        title: "Error",
        description: "No organization selected. Please try again.",
        variant: "destructive",
      });
      return;
    }
    try {
      if (!formData.name.trim()) {
        toast({
          title: "Error",
          description: "Category name is required.",
          variant: "destructive",
        });
        return;
      }
      if (editingCategory) {
        // Update existing category
        const { error } = await supabase
          .from("risk_categories")
          .update({
            name: formData.name,
            description: formData.description ?? null,
          })
          .eq("id", editingCategory.id)
          .eq("organization_id", organization.id);
        if (error) throw error;
        toast({
          title: "Success",
          description: "Category updated successfully.",
        });
      } else {
        // Create new category
        const { error } = await supabase.from("risk_categories").insert([
          {
            name: formData.name,
            description: formData.description ?? null,
            organization_id: organization.id,
          },
        ]);
        if (error) throw error;
        toast({
          title: "Success",
          description: "New category created successfully.",
        });
      }
      onClose();
      onSaved();
    } catch (error: unknown) {
      toast({
        title: "Error",
        description: (error as Error)?.message ?? "Failed to save category.",
        variant: "destructive",
      });
    }
  };
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{editingCategory ? "Edit Category" : "Add New Category"}</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <label
              htmlFor="name"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Category Name
            </label>
            <Input
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter category name"
            />
          </div>
          <div className="space-y-2">
            <label
              htmlFor="description"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              Description (Optional)
            </label>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Enter category description"
              rows={3}
            />
          </div>
        </div>
        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={onClose} className="w-full sm:w-auto">
            Cancel
          </Button>
          <Button onClick={handleSave} className="w-full sm:w-auto">
            {editingCategory ? "Update" : "Create"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
