import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON><PERSON>, Trash2 } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ResponsiveTable } from "@/components/ui/responsive-table";
import { Card, CardContent } from "@/components/ui/card";

interface Category {
  id: string;
  name: string;
  description: string | null;
}

interface CategoryTableProps {
  categories: Category[];
  loading: boolean;
  onEdit: (category: Category) => void;
  onDelete: (category: Category) => void;
}

interface CategoryCardProps {
  item: Category;
  className?: string;
  onEdit: (category: Category) => void;
  onDelete: (category: Category) => void;
}

const CategoryCard = ({ item: category, className, onEdit, onDelete }: CategoryCardProps) => {
  return (
    <Card className={className}>
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-start justify-between gap-3">
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-foreground truncate">{category.name}</h3>
              {category.description && (
                <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                  {category.description}
                </p>
              )}
              {!category.description && (
                <p className="text-sm text-muted-foreground italic mt-1">No description</p>
              )}
            </div>
            <div className="flex gap-2 shrink-0">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => onEdit(category)}
              >
                <Pencil className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8"
                onClick={() => onDelete(category)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export const CategoryTable = ({ categories, loading, onEdit, onDelete }: CategoryTableProps) => {
  const tableHeader = (
    <TableHeader>
      <TableRow>
        <TableHead className="min-w-[150px]">Category Name</TableHead>
        <TableHead className="min-w-[200px]">Description</TableHead>
        <TableHead className="w-[120px]">Actions</TableHead>
      </TableRow>
    </TableHeader>
  );

  const tableBody = (
    <TableBody>
      {categories.map(category => (
        <TableRow key={category.id}>
          <TableCell className="font-medium">{category.name}</TableCell>
          <TableCell>
            {category.description ?? (
              <span className="text-muted-foreground italic">No description</span>
            )}
          </TableCell>
          <TableCell>
            <div className="flex gap-2">
              <Button variant="outline" size="icon" onClick={() => onEdit(category)}>
                <Pencil className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="icon" onClick={() => onDelete(category)}>
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </TableCell>
        </TableRow>
      ))}
    </TableBody>
  );

  const emptyState = (
    <Table>
      {tableHeader}
      <TableBody>
        <TableRow>
          <TableCell colSpan={3} className="text-center py-8">
            No categories found. Create your first category.
          </TableCell>
        </TableRow>
      </TableBody>
    </Table>
  );

  const CategoryCardWithActions = (props: { item: Category; className?: string }) => (
    <CategoryCard {...props} onEdit={onEdit} onDelete={onDelete} />
  );

  return (
    <ResponsiveTable
      data={categories}
      isLoading={loading}
      tableHeader={tableHeader}
      tableBody={tableBody}
      CardComponent={CategoryCardWithActions}
      emptyState={emptyState}
      className="shadow-sm"
    />
  );
};
