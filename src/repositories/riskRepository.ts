// src/repositories/riskRepository.ts - Updated with organization filtering
import { supabase } from "@/integrations/supabase/client";
import { Risk, ControlMeasure, MitigationAction, RiskStatus } from "@/types";
import {
  DbRisk,
  DbControlMeasure,
  DbMitigationAction,
  DbRiskCategory,
} from "@/types/db";
import {
  mapDbRiskToDomain,
  mapDbControlMeasureToDomain,
  mapDbMitigationActionToDomain,
  mapDomainRiskToDb,
  mapDbRiskCategoryToDomain,
} from "@/utils/typeMappers";
import {
  isRisk,
  isDbRisk,
  isDbControlMeasure,
  isDbMitigationAction,
  isDbRiskCategory,
  hasProfiles,
  hasCategories,
} from "@/utils/typeGuards";
import {
  NetworkError,
  SystemError,
  logger,
} from "@/utils/errors";
/**
 * Repository for risk-related database operations
 * Follows consistent patterns for data transformation and error handling
 * Now includes organization-based data filtering for multi-tenancy
 */
export const riskRepository = {
  /**
   * Get a risk by ID with all related data (organization-filtered)
   */
  async getRiskById(id: string, organizationId?: string): Promise<Risk | null> {
    try {
      // Build the query with organization filtering
      let query = supabase
        .from("risks")
        .select(
          `
          *,
          profiles!risks_owner_id_fkey(name),
          categories:risk_categories(*)
        `
        )
        .eq("id", id);
      // Add organization filter if provided
      if (organizationId) {
        query = query.eq("organization_id", organizationId);
      }
      const { data: riskData, error: riskError } = await query.maybeSingle();
      if (riskError) {
        throw new NetworkError(`Database error: ${riskError.message}`, undefined, "risks", "GET", {
          component: "risk_repository",
          action: "get_risk_by_id",
          timestamp: new Date(),
          additionalData: { riskId: id, organizationId },
        });
      }
      if (!riskData) {
        return null;
      }
      // Type guard validation for the main risk data
      if (!isDbRisk(riskData)) {
        logger.error("Invalid risk data structure from database", undefined, {
          component: "risk_repository",
          action: "get_risk_by_id",
          timestamp: new Date(),
          additionalData: { riskId: id, riskData },
        });
        throw new SystemError(
          "Invalid risk data structure from database",
          "database",
          "INVALID_DATA_STRUCTURE"
        );
      }
      // Verify organization access (additional security check)
      if (organizationId && riskData.organization_id !== organizationId) {
        logger.warn("Access denied: Risk does not belong to organization", {
          component: "risk_repository",
          action: "get_risk_by_id",
          timestamp: new Date(),
          additionalData: { riskId: id, organizationId, riskOrgId: riskData.organization_id },
        });
        return null;
      }
      // Type guard validation for profiles
      if (!hasProfiles(riskData)) {
        logger.error("Risk data missing profiles structure", undefined, {
          component: "risk_repository",
          action: "get_risk_by_id",
          timestamp: new Date(),
          additionalData: { riskId: id },
        });
        throw new SystemError(
          "Risk data missing profiles structure",
          "database",
          "MISSING_PROFILES"
        );
      }
      // Type guard validation for categories
      if (!hasCategories(riskData)) {
        logger.error("Risk data missing categories structure", undefined, {
          component: "risk_repository",
          action: "get_risk_by_id",
          timestamp: new Date(),
          additionalData: { riskId: id },
        });
        throw new SystemError(
          "Risk data missing categories structure",
          "database",
          "MISSING_CATEGORIES"
        );
      }
      // Fetch the control measures for this risk (with organization filter)
      let controlQuery = supabase
        .from("control_measures")
        .select("*")
        .eq("risk_id", id)
        .order("created_at", { ascending: true });
      if (organizationId) {
        controlQuery = controlQuery.eq("organization_id", organizationId);
      }
      const { data: controlsData, error: controlsError } = await controlQuery;
      if (controlsError) {
        throw new NetworkError(
          `Failed to load control measures: ${controlsError.message}`,
          undefined,
          "control_measures",
          "GET",
          {
            component: "risk_repository",
            action: "get_control_measures",
            timestamp: new Date(),
            additionalData: { riskId: id, organizationId },
          }
        );
      }
      // Fetch the mitigation actions for this risk (with organization filter)
      let actionsQuery = supabase
        .from("mitigation_actions")
        .select("*")
        .eq("risk_id", id)
        .order("created_at", { ascending: true });
      if (organizationId) {
        actionsQuery = actionsQuery.eq("organization_id", organizationId);
      }
      const { data: actionsData, error: actionsError } = await actionsQuery;
      if (actionsError) {
        throw new NetworkError(
          `Failed to load mitigation actions: ${actionsError.message}`,
          undefined,
          "mitigation_actions",
          "GET",
          {
            component: "risk_repository",
            action: "get_mitigation_actions",
            timestamp: new Date(),
            additionalData: { riskId: id, organizationId },
          }
        );
      }
      // Validate and map control measures with proper type guards
      const controlMeasures: ControlMeasure[] = [];
      if (controlsData) {
        for (const control of controlsData) {
          if (isDbControlMeasure(control)) {
            controlMeasures.push(mapDbControlMeasureToDomain(control));
          } else {
            logger.warn("Invalid control measure data structure", {
              component: "risk_repository",
              action: "map_control_measures",
              timestamp: new Date(),
              additionalData: { riskId: id, controlId: control?.id },
            });
          }
        }
      }
      // Validate and map mitigation actions with proper type guards
      const mitigationActions: MitigationAction[] = [];
      if (actionsData) {
        for (const action of actionsData) {
          if (isDbMitigationAction(action)) {
            mitigationActions.push(mapDbMitigationActionToDomain(action));
          } else {
            logger.warn("Invalid mitigation action data structure", {
              component: "risk_repository",
              action: "map_mitigation_actions",
              timestamp: new Date(),
              additionalData: { riskId: id, actionId: action?.id },
            });
          }
        }
      }
      // Safely extract category name
      const category =
        riskData.categories && isDbRiskCategory(riskData.categories)
          ? mapDbRiskCategoryToDomain(riskData.categories).name
          : "Uncategorized";
      // Safely extract owner name
      const ownerName = riskData.profiles?.name ?? "Unassigned";
      // Create the complete risk object using the mapper
      const risk = mapDbRiskToDomain(riskData, {
        ownerName,
        category,
        controlMeasures,
        mitigationActions,
      });
      // Validate the final risk object for type safety
      if (!isRisk(risk)) {
        logger.error("Invalid risk data structure after mapping", undefined, {
          component: "risk_repository",
          action: "get_risk_by_id",
          timestamp: new Date(),
          additionalData: { riskId: id },
        });
        throw new SystemError(
          "Invalid risk data structure after mapping",
          "data_mapping",
          "INVALID_MAPPED_DATA"
        );
      }
      logger.debug("Risk retrieved successfully", {
        component: "risk_repository",
        action: "get_risk_by_id",
        timestamp: new Date(),
        additionalData: { riskId: id, organizationId },
      });
      return risk;
    } catch (error) {
      logger.error("Repository error getting risk by ID", error as Error, {
        component: "risk_repository",
        action: "get_risk_by_id",
        timestamp: new Date(),
        additionalData: { riskId: id, organizationId },
      });
      throw error;
    }
  },
  /**
   * Get all risks for an organization
   */
  async getRisksByOrganization(organizationId: string): Promise<Risk[]> {
    try {
      const { data: risksData, error: risksError } = await supabase
        .from("risks")
        .select(
          `
          *,
          profiles!risks_owner_id_fkey(name),
          categories:risk_categories(*)
        `
        )
        .eq("organization_id", organizationId)
        .order("created_at", { ascending: false });
      if (risksError) {
        throw new Error(`Database error: ${risksError.message}`);
      }
      if (!risksData) {
        return [];
      }
      const risks: Risk[] = [];
      for (const riskData of risksData) {
        if (!isDbRisk(riskData)) {
          continue;
        }
        // Get control measures and mitigation actions for each risk
        const fullRisk = await this.getRiskById(riskData.id, organizationId);
        if (fullRisk) {
          risks.push(fullRisk);
        }
      }
      return risks;
    } catch (error) {
      throw error;
    }
  },
  /**
   * Update a risk (with organization verification and proper control/action handling)
   */
  async updateRisk(id: string, risk: Partial<Risk>, organizationId?: string): Promise<Risk | null> {
    try {
      // First verify the risk belongs to the organization
      if (organizationId) {
        const existingRisk = await this.getRiskById(id, organizationId);
        if (!existingRisk) {
          throw new Error("Risk not found or access denied");
        }
      }
      const dbRisk = mapDomainRiskToDb(risk);
      // Build update query for main risk table
      let updateQuery = supabase
        .from("risks")
        .update({
          ...dbRisk,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id);
      // Add organization filter for additional security
      if (organizationId) {
        updateQuery = updateQuery.eq("organization_id", organizationId);
      }
      const { data, error } = await updateQuery.select().single();
      if (error || !data) {
        throw new Error(error?.message ?? "Failed to update risk");
      }
      // Validate the returned data
      if (!isDbRisk(data)) {
        throw new Error("Invalid risk data returned from database");
      }
      // Handle control measures updates with organization ID
      if (risk.controlMeasures !== undefined) {
        await this.updateControlMeasures(id, risk.controlMeasures, organizationId);
      }
      // Handle mitigation actions updates with organization ID
      if (risk.mitigationActions !== undefined) {
        await this.updateMitigationActions(id, risk.mitigationActions, organizationId);
      }
      // Log change to risk_history (with organization_id)
      const { error: historyError } = await supabase.from("risk_history").insert({
        risk_id: data.id,
        organization_id: data.organization_id,
        recorded_at: data.updated_at,
        likelihood: data.likelihood,
        impact: data.impact,
        severity: data.severity,
        status: data.status,
      });
      if (historyError) {
        // Not throwing error here as the primary operation (updateRisk) succeeded.
      }
      // Return the updated risk
      return this.getRiskById(id, organizationId);
    } catch (error) {
      throw error;
    }
  },
  /**
   * Update control measures for a risk
   */
  async updateControlMeasures(
    riskId: string,
    controlMeasures: ControlMeasure[],
    organizationId?: string
  ): Promise<void> {
    try {
      // Get existing controls to compare
      let existingQuery = supabase.from("control_measures").select("id").eq("risk_id", riskId);
      if (organizationId) {
        existingQuery = existingQuery.eq("organization_id", organizationId);
      }
      const { data: existingControls } = await existingQuery;
      const existingControlIds = new Set(existingControls?.map(c => c.id) || []);
      // Group controls by operation type (create, update)
      const controlsToCreate: unknown[] = [];
      const controlsToUpdate: unknown[] = [];
      // Track IDs that still exist to find deleted ones
      const remainingControlIds = new Set<string>();
      controlMeasures.forEach(control => {
        if (!control.id) {
          // New control to create
          controlsToCreate.push({
            risk_id: riskId,
            organization_id: organizationId,
            description: control.description,
            effectiveness: control.effectiveness ?? "Medium",
            implemented: control.implemented ?? true,
          });
        } else {
          // Existing control to update
          remainingControlIds.add(control.id);
          controlsToUpdate.push({
            id: control.id,
            risk_id: riskId,
            organization_id: organizationId,
            description: control.description,
            effectiveness: control.effectiveness ?? "Medium",
            implemented: control.implemented ?? true,
            updated_at: new Date().toISOString(),
          });
        }
      });
      // Find controls to delete (in existing but not in remaining)
      const controlsToDeleteIds = Array.from(existingControlIds).filter(
        id => !remainingControlIds.has(id as string)
      );
      // Perform the database operations
      // Create new controls
      if (controlsToCreate.length > 0) {
        const { error: createError } = await supabase
          .from("control_measures")
          .insert(controlsToCreate);
        if (createError) {
          throw createError;
        }
      }
      // Update existing controls
      for (const control of controlsToUpdate) {
        let updateQuery = supabase
          .from("control_measures")
          .update({
            description: control.description,
            effectiveness: control.effectiveness,
            implemented: control.implemented,
            updated_at: control.updated_at,
          })
          .eq("id", control.id);
        if (organizationId) {
          updateQuery = updateQuery.eq("organization_id", organizationId);
        }
        const { error: updateError } = await updateQuery;
        if (updateError) {
          throw updateError;
        }
      }
      // Delete removed controls
      if (controlsToDeleteIds.length > 0) {
        let deleteQuery = supabase.from("control_measures").delete().in("id", controlsToDeleteIds);
        if (organizationId) {
          deleteQuery = deleteQuery.eq("organization_id", organizationId);
        }
        const { error: deleteError } = await deleteQuery;
        if (deleteError) {
          throw deleteError;
        }
      }
    } catch (error) {
      throw error;
    }
  },
  /**
   * Update mitigation actions for a risk
   */
  async updateMitigationActions(
    riskId: string,
    mitigationActions: MitigationAction[],
    organizationId?: string
  ): Promise<void> {
    try {
      // Get existing actions to compare
      let existingQuery = supabase.from("mitigation_actions").select("id").eq("risk_id", riskId);
      if (organizationId) {
        existingQuery = existingQuery.eq("organization_id", organizationId);
      }
      const { data: existingActions } = await existingQuery;
      const existingActionIds = new Set(existingActions?.map(a => a.id) || []);
      // Group actions by operation type (create, update)
      const actionsToCreate: unknown[] = [];
      const actionsToUpdate: unknown[] = [];
      // Track IDs that still exist to find deleted ones
      const remainingActionIds = new Set<string>();
      mitigationActions.forEach(action => {
        if (!action.id) {
          // New action to create
          actionsToCreate.push({
            risk_id: riskId,
            organization_id: organizationId,
            description: action.description,
            completed: action.completed ?? false,
          });
        } else {
          // Existing action to update
          remainingActionIds.add(action.id);
          actionsToUpdate.push({
            id: action.id,
            risk_id: riskId,
            organization_id: organizationId,
            description: action.description,
            completed: action.completed ?? false,
            updated_at: new Date().toISOString(),
          });
        }
      });
      // Find actions to delete (in existing but not in remaining)
      const actionsToDeleteIds = Array.from(existingActionIds).filter(
        id => !remainingActionIds.has(id as string)
      );
      // Perform the database operations
      // Create new actions
      if (actionsToCreate.length > 0) {
        const { error: createError } = await supabase
          .from("mitigation_actions")
          .insert(actionsToCreate);
        if (createError) {
          throw createError;
        }
      }
      // Update existing actions
      for (const action of actionsToUpdate) {
        let updateQuery = supabase
          .from("mitigation_actions")
          .update({
            description: action.description,
            completed: action.completed,
            updated_at: action.updated_at,
          })
          .eq("id", action.id);
        if (organizationId) {
          updateQuery = updateQuery.eq("organization_id", organizationId);
        }
        const { error: updateError } = await updateQuery;
        if (updateError) {
          throw updateError;
        }
      }
      // Delete removed actions
      if (actionsToDeleteIds.length > 0) {
        let deleteQuery = supabase.from("mitigation_actions").delete().in("id", actionsToDeleteIds);
        if (organizationId) {
          deleteQuery = deleteQuery.eq("organization_id", organizationId);
        }
        const { error: deleteError } = await deleteQuery;
        if (deleteError) {
          throw deleteError;
        }
      }
    } catch (error) {
      throw error;
    }
  },
  /**
   * Create a new risk (with organization assignment)
   */
  async createRisk(
    risk: Omit<Risk, "id" | "createdAt" | "updatedAt">,
    organizationId: string,
    createdBy: string
  ): Promise<Risk | null> {
    try {
      const dbRisk = mapDomainRiskToDb(risk);
      const { data, error } = await supabase
        .from("risks")
        .insert({
          ...dbRisk,
          organization_id: organizationId,
          created_by: createdBy,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();
      if (error || !data) {
        throw new Error(error?.message ?? "Failed to create risk");
      }
      // Log initial state to risk_history
      const { error: historyError } = await supabase.from("risk_history").insert({
        risk_id: data.id,
        organization_id: data.organization_id,
        recorded_at: data.created_at,
        likelihood: data.likelihood,
        impact: data.impact,
        severity: data.severity,
        status: data.status,
        created_by: createdBy,
      });
      if (historyError) {
        // Condition handled
      }
      return this.getRiskById(data.id, organizationId);
    } catch (error) {
      throw error;
    }
  },
  /**
   * Update the status of a risk (with organization verification)
   */
  async updateRiskStatus(
    riskId: string,
    status: RiskStatus,
    organizationId?: string
  ): Promise<boolean> {
    try {
      if (!Object.values(RiskStatus).includes(status)) {
        throw new Error(`Invalid risk status: ${status}`);
      }
      // Verify organization access first
      if (organizationId) {
        const existingRisk = await this.getRiskById(riskId, organizationId);
        if (!existingRisk) {
          throw new Error("Risk not found or access denied");
        }
      }
      let updateQuery = supabase
        .from("risks")
        .update({
          status,
          updated_at: new Date().toISOString(),
        })
        .eq("id", riskId);
      if (organizationId) {
        updateQuery = updateQuery.eq("organization_id", organizationId);
      }
      const { error } = await updateQuery;
      if (error) {
        throw new Error(`Failed to update risk status: ${error.message}`);
      }
      // Fetch the updated risk to log its state to history
      const updatedRisk = await this.getRiskById(riskId, organizationId);
      if (updatedRisk) {
        const { error: historyError } = await supabase.from("risk_history").insert({
          risk_id: updatedRisk.id,
          organization_id: updatedRisk.organizationId,
          recorded_at: updatedRisk.updatedAt.toISOString(),
          likelihood: updatedRisk.likelihood,
          impact: updatedRisk.impact,
          severity: updatedRisk.severity,
          status: updatedRisk.status,
        });
        if (historyError) {
          // Not throwing error here as the primary operation succeeded.
        }
      } else {
        // Else case handled
      }
      return true;
    } catch (error) {
      throw error;
    }
  },
  /**
   * Delete a risk (with organization verification)
   */
  async deleteRisk(riskId: string, organizationId?: string): Promise<boolean> {
    try {
      // Verify organization access first
      if (organizationId) {
        const existingRisk = await this.getRiskById(riskId, organizationId);
        if (!existingRisk) {
          throw new Error("Risk not found or access denied");
        }
      }
      let deleteQuery = supabase.from("risks").delete().eq("id", riskId);
      if (organizationId) {
        deleteQuery = deleteQuery.eq("organization_id", organizationId);
      }
      const { error } = await deleteQuery;
      if (error) {
        throw new Error(`Failed to delete risk: ${error.message}`);
      }
      return true;
    } catch (error) {
      throw error;
    }
  },
};
