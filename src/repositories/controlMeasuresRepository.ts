import { supabase } from "@/integrations/supabase/client";
import { ControlMeasure } from "@/types";
import { DbControlMeasure } from "@/types/db";
import { mapDbControlMeasureToDomain, mapDomainControlMeasureToDb } from "@/utils/typeMappers";
import { validateEffectiveness } from "@/utils/typeValidation";
import { isControlMeasure, isDbControlMeasure } from "@/utils/typeGuards";
/**
 * Repository for control measure-related database operations
 * Follows consistent patterns for data transformation and error handling
 */
export const controlMeasuresRepository = {
  /**
   * Get control measures by risk ID - filtered by organization
   */
  async getControlMeasuresByRiskId(
    riskId: string,
    organizationId: string
  ): Promise<ControlMeasure[]> {
    try {
      const { data, error } = await supabase
        .from("control_measures")
        .select("*")
        .eq("risk_id", riskId)
        .eq("organization_id", organizationId) // Add organization filter
        .order("created_at", { ascending: true });
      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }
      if (!data?.length) {
        return [];
      }
      // Map database objects to domain objects with proper type guard validation
      const controlMeasures: ControlMeasure[] = [];
      for (const control of data) {
        if (isDbControlMeasure(control)) {
          const domainControl = mapDbControlMeasureToDomain(control);
          // Validate the control measure object for type safety
          if (isControlMeasure(domainControl)) {
            controlMeasures.push(domainControl);
          } else {
            // Else case handled
          }
        } else {
          // Else case handled
        }
      }
      return controlMeasures;
    } catch (error) {
      throw error;
    }
  },
  /**
   * Get a control measure by ID - with organization check
   */
  async getControlMeasureById(id: string, organizationId: string): Promise<ControlMeasure | null> {
    try {
      const { data, error } = await supabase
        .from("control_measures")
        .select("*")
        .eq("id", id)
        .eq("organization_id", organizationId) // Add organization filter
        .maybeSingle();
      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }
      if (!data) {
        return null;
      }
      // Validate database structure
      if (!isDbControlMeasure(data)) {
        throw new Error("Invalid control measure data from database");
      }
      const domainControl = mapDbControlMeasureToDomain(data);
      // Validate the control measure object for type safety
      if (!isControlMeasure(domainControl)) {
        throw new Error("Invalid control measure data structure");
      }
      return domainControl;
    } catch (error) {
      throw error;
    }
  },
  /**
   * Create a new control measure - with organization_id
   */
  async createControlMeasure(
    controlData: Partial<ControlMeasure> & { organizationId: string }
  ): Promise<ControlMeasure | null> {
    try {
      const dbControl = mapDomainControlMeasureToDb(controlData);
      // Ensure required fields are present
      if (!dbControl.description || !dbControl.risk_id || !controlData.organizationId) {
        throw new Error("Control measure requires description, risk_id, and organization_id");
      }
      // Ensure effectiveness is validated
      if (controlData.effectiveness) {
        dbControl.effectiveness = validateEffectiveness(controlData.effectiveness);
      }
      const { data, error } = await supabase
        .from("control_measures")
        .insert({
          description: dbControl.description,
          risk_id: dbControl.risk_id,
          organization_id: controlData.organizationId,
          effectiveness: dbControl.effectiveness,
          implemented: dbControl.implemented !== undefined ? dbControl.implemented : true,
        })
        .select()
        .single();
      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }
      if (!data) {
        return null;
      }
      // Validate returned data
      if (!isDbControlMeasure(data)) {
        throw new Error("Invalid control measure data returned from database");
      }
      return mapDbControlMeasureToDomain(data);
    } catch (error) {
      throw error;
    }
  },
  /**
   * Update a control measure - with organization check
   */
  async updateControlMeasure(
    id: string,
    controlData: Partial<ControlMeasure>,
    organizationId: string
  ): Promise<boolean> {
    try {
      const dbControl = mapDomainControlMeasureToDb(controlData);
      // Add updated_at timestamp
      dbControl.updated_at = new Date().toISOString();
      // Remove any undefined values to avoid overwriting with null
      const updateData: Record<string, any> = {};
      Object.entries(dbControl).forEach(([key, value]) => {
        if (value !== undefined) {
          updateData[key] = value;
        }
      });
      const { error } = await supabase
        .from("control_measures")
        .update(updateData)
        .eq("id", id)
        .eq("organization_id", organizationId); // Add organization filter
      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }
      return true;
    } catch (error) {
      throw error;
    }
  },
  /**
   * Delete a control measure - with organization check
   */
  async deleteControlMeasure(id: string, organizationId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from("control_measures")
        .delete()
        .eq("id", id)
        .eq("organization_id", organizationId); // Add organization filter
      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }
      return true;
    } catch (error) {
      throw error;
    }
  },
};
