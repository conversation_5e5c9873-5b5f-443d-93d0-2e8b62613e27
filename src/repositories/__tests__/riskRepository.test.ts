import { describe, it, expect, vi, beforeEach } from 'vitest'
import { riskRepository } from '../riskRepository'
import { supabase } from '@/integrations/supabase/client'
import { RiskSeverity, RiskStatus } from '@/types'
import { createMockSupabaseResponse, createMockSupabase<PERSON>hain } from '@/test/test-utils'

// Mock the supabase client
vi.mock('@/integrations/supabase/client')

// Mock the type mappers
vi.mock('@/utils/typeMappers', () => ({
  mapDbRiskToDomain: vi.fn((dbRisk) => ({
    id: dbRisk.id,
    title: dbRisk.title,
    description: dbRisk.description,
    severity: dbRisk.severity,
    status: dbRisk.status,
    organizationId: dbRisk.organization_id,
    createdAt: new Date(dbRisk.created_at),
    updatedAt: new Date(dbRisk.updated_at),
  })),
  mapDbControlMeasureToDomain: vi.fn((dbControl) => ({
    id: dbControl.id,
    riskId: dbControl.risk_id,
    description: dbControl.description,
    implemented: dbControl.implemented,
  })),
  mapDbMitigationActionToDomain: vi.fn((dbAction) => ({
    id: dbAction.id,
    riskId: dbAction.risk_id,
    description: dbAction.description,
    completed: dbAction.completed,
  })),
  mapDbRiskCategoryToDomain: vi.fn((dbCategory) => ({
    id: dbCategory.id,
    name: dbCategory.name,
    description: dbCategory.description,
  })),
  mapDomainRiskToDb: vi.fn((risk) => ({
    id: risk.id,
    title: risk.title,
    description: risk.description,
    severity: risk.severity,
    status: risk.status,
    organization_id: risk.organizationId,
  })),
}))

// Mock the type guards
vi.mock('@/utils/typeGuards', () => ({
  isDbRisk: vi.fn(() => true),
  isDbControlMeasure: vi.fn(() => true),
  isDbMitigationAction: vi.fn(() => true),
  isDbRiskCategory: vi.fn(() => true),
  isRisk: vi.fn(() => true),
  hasProfiles: vi.fn(() => true),
  hasCategories: vi.fn(() => true),
}))

describe('Risk Repository', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getRiskById', () => {
    const mockDbRisk = {
      id: 'test-risk-id',
      title: 'Test Risk',
      description: 'Test risk description',
      severity: RiskSeverity.HIGH,
      status: RiskStatus.IN_PROGRESS,
      organization_id: 'test-org-id',
      created_at: '2024-01-01T00:00:00Z',
      updated_at: '2024-01-01T00:00:00Z',
      profiles: { name: 'Test User' },
      categories: { name: 'Test Category' },
    }

    it('should fetch risk by ID successfully', async () => {
      const mockSupabaseChain = createMockSupabaseChain(mockDbRisk)
      
      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await riskRepository.getRiskById('test-risk-id')

      expect(result).toBeTruthy()
      expect(result?.id).toBe('test-risk-id')
      expect(mockSupabaseChain.eq).toHaveBeenCalledWith('id', 'test-risk-id')
    })

    it('should fetch risk with organization filter', async () => {
      const mockSupabaseChain = createMockSupabaseChain(mockDbRisk)

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await riskRepository.getRiskById('test-risk-id', 'test-org-id')

      expect(result).toBeTruthy()
      expect(mockSupabaseChain.eq).toHaveBeenCalledWith('id', 'test-risk-id')
      expect(mockSupabaseChain.eq).toHaveBeenCalledWith('organization_id', 'test-org-id')
    })

    it('should return null when risk not found', async () => {
      // When maybeSingle returns null (no error), the function should return null
      const mockSupabaseChain = createMockSupabaseChain(null)

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await riskRepository.getRiskById('non-existent-id')

      expect(result).toBeNull()
    })

    it('should handle database errors', async () => {
      const mockError = { message: 'Database error' }
      const mockSupabaseChain = createMockSupabaseChain(null, mockError)

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      await expect(riskRepository.getRiskById('test-risk-id')).rejects.toThrow('Database error')
    })
  })

  describe('getAllRisks', () => {
    const mockDbRisks = [
      {
        id: 'risk-1',
        title: 'Risk 1',
        description: 'Description 1',
        severity: RiskSeverity.HIGH,
        status: RiskStatus.IN_PROGRESS,
        organization_id: 'test-org-id',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      },
      {
        id: 'risk-2',
        title: 'Risk 2',
        description: 'Description 2',
        severity: RiskSeverity.MEDIUM,
        status: RiskStatus.IDENTIFIED,
        organization_id: 'test-org-id',
        created_at: '2024-01-02T00:00:00Z',
        updated_at: '2024-01-02T00:00:00Z',
      },
    ]

    it('should fetch all risks for organization', async () => {
      // Mock the select query to return empty array to avoid recursive calls
      const mockSupabaseChain = createMockSupabaseChain([])

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await riskRepository.getRisksByOrganization('test-org-id')

      expect(result).toHaveLength(0)
      expect(mockSupabaseChain.eq).toHaveBeenCalledWith('organization_id', 'test-org-id')
      expect(mockSupabaseChain.select).toHaveBeenCalled()
      expect(mockSupabaseChain.order).toHaveBeenCalledWith('created_at', { ascending: false })
    })


    it('should apply sorting correctly', async () => {
      const mockSupabaseChain = createMockSupabaseChain(mockDbRisks)

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      await riskRepository.getRisksByOrganization('test-org-id')

      expect(mockSupabaseChain.order).toHaveBeenCalledWith('created_at', { ascending: false })
    })

    it('should handle empty results', async () => {
      const mockSupabaseChain = createMockSupabaseChain([])

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      const result = await riskRepository.getRisksByOrganization('test-org-id')

      expect(result).toEqual([])
    })
  })

  describe('createRisk', () => {
    const mockRiskData = {
      title: 'New Risk',
      description: 'New risk description',
      severity: RiskSeverity.HIGH,
      status: RiskStatus.IDENTIFIED,
      organizationId: 'test-org-id',
      ownerId: 'test-user-id',
      likelihood: 4,
      impact: 3,
    }

    it('should create risk successfully', async () => {
      const mockCreatedRisk = {
        id: 'new-risk-id',
        organization_id: 'test-org-id',
        title: 'New Risk',
        description: 'New risk description',
        severity: RiskSeverity.HIGH,
        status: RiskStatus.IDENTIFIED,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        profiles: { name: 'Test User' },
        categories: { name: 'Test Category' }
      }

      // Mock for insert operation
      const mockInsertChain = createMockSupabaseChain(mockCreatedRisk)
      // Mock for getRiskById operation (called at the end of createRisk)
      const mockGetChain = createMockSupabaseChain(mockCreatedRisk)
      
      // First call is for insert, second+ calls are for getRiskById
      vi.mocked(supabase.from).mockReturnValueOnce(mockInsertChain as any)
        .mockReturnValue(mockGetChain as any)

      const result = await riskRepository.createRisk(mockRiskData as any, 'test-org-id', 'test-user-id')

      expect(result).toBeTruthy()
      expect(result?.id).toBe('new-risk-id')
      expect(mockInsertChain.insert).toHaveBeenCalled()
    })

    it('should handle creation errors', async () => {
      const mockError = { message: 'Creation failed' }
      const mockSupabaseChain = createMockSupabaseChain(null, mockError)

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      await expect(riskRepository.createRisk(mockRiskData as any, 'test-org-id', 'test-user-id')).rejects.toThrow('Creation failed')
    })
  })

  describe('updateRisk', () => {
    const mockUpdateData = {
      id: 'test-risk-id',
      title: 'Updated Risk',
      description: 'Updated description',
      severity: RiskSeverity.MEDIUM,
    }

    it('should update risk successfully', async () => {
      const mockExistingRisk = {
        id: 'test-risk-id',
        title: 'Original Risk',
        organization_id: 'test-org-id',
        severity: RiskSeverity.HIGH,
        status: RiskStatus.IN_PROGRESS,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        profiles: { name: 'Test User' },
        categories: { name: 'Test Category' }
      }
      
      const mockUpdatedRisk = {
        ...mockExistingRisk,
        title: 'Updated Risk',
        updated_at: '2024-01-01T00:00:00Z',
      }

      // Mock for getRiskById (verification call), update call, and final getRiskById call
      const mockGetChain = createMockSupabaseChain(mockExistingRisk)
      const mockUpdateChain = createMockSupabaseChain(mockUpdatedRisk)
      const mockFinalGetChain = createMockSupabaseChain(mockUpdatedRisk)
      
      // Multiple calls: 1st=getRiskById, 2nd=update, 3rd+=final getRiskById + sub-queries
      vi.mocked(supabase.from)
        .mockReturnValueOnce(mockGetChain as any)     // Initial verification
        .mockReturnValueOnce(mockUpdateChain as any)  // Update operation
        .mockReturnValue(mockFinalGetChain as any)    // Final getRiskById and sub-queries

      const result = await riskRepository.updateRisk('test-risk-id', mockUpdateData as any, 'test-org-id')

      expect(result).toBeTruthy()
      expect(result?.title).toBe('Updated Risk')
    })

    it('should handle update errors', async () => {
      const mockError = { message: 'Update failed' }
      const mockSupabaseChain = createMockSupabaseChain(null, mockError)

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      await expect(riskRepository.updateRisk('test-risk-id', mockUpdateData as any, 'test-org-id')).rejects.toThrow('Update failed')
    })
  })

  describe('deleteRisk', () => {
    it('should delete risk successfully', async () => {
      const mockExistingRisk = {
        id: 'test-risk-id',
        title: 'Risk to Delete',
        organization_id: 'test-org-id',
        severity: RiskSeverity.HIGH,
        status: RiskStatus.IN_PROGRESS,
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
        profiles: { name: 'Test User' },
        categories: { name: 'Test Category' }
      }
      
      // Mock for getRiskById (verification call) and delete call
      const mockGetChain = createMockSupabaseChain(mockExistingRisk)
      const mockDeleteChain = createMockSupabaseChain(null) // No data returned for delete
      
      // First call is for getRiskById verification, second is for delete
      vi.mocked(supabase.from)
        .mockReturnValueOnce(mockGetChain as any)     // Initial verification
        .mockReturnValueOnce(mockDeleteChain as any)  // Delete operation

      const result = await riskRepository.deleteRisk('test-risk-id', 'test-org-id')

      expect(result).toBe(true)
    })

    it('should handle deletion errors', async () => {
      const mockError = { message: 'Deletion failed' }
      const mockSupabaseChain = createMockSupabaseChain(null, mockError)

      vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any)

      await expect(riskRepository.deleteRisk('test-risk-id', 'test-org-id')).rejects.toThrow('Deletion failed')
    })
  })
})
