import { supabase } from "@/integrations/supabase/client";
import { MitigationAction } from "@/types";
import { DbMitigationAction } from "@/types/db";
import { mapDbMitigationActionToDomain, mapDomainMitigationActionToDb } from "@/utils/typeMappers";
import { isMitigationAction, isDbMitigationAction } from "@/utils/typeGuards";
/**
 * Repository for mitigation action-related database operations
 * Follows consistent patterns for data transformation and error handling
 */
export const mitigationActionsRepository = {
  /**
   * Get mitigation actions by risk ID - filtered by organization
   */
  async getMitigationActionsByRiskId(
    riskId: string,
    organizationId: string
  ): Promise<MitigationAction[]> {
    try {
      const { data, error } = await supabase
        .from("mitigation_actions")
        .select("*")
        .eq("risk_id", riskId)
        .eq("organization_id", organizationId) // Add organization filter
        .order("created_at", { ascending: true });
      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }
      if (!data?.length) {
        return [];
      }
      // Map database objects to domain objects with proper type guard validation
      const mitigationActions: MitigationAction[] = [];
      for (const action of data) {
        if (isDbMitigationAction(action)) {
          const domainAction = mapDbMitigationActionToDomain(action);
          // Validate the mitigation action object for type safety
          if (isMitigationAction(domainAction)) {
            mitigationActions.push(domainAction);
          } else {
            // Else case handled
          }
        } else {
          // Else case handled
        }
      }
      return mitigationActions;
    } catch (error) {
      throw error;
    }
  },
  /**
   * Get a mitigation action by ID - with organization check
   */
  async getMitigationActionById(
    id: string,
    organizationId: string
  ): Promise<MitigationAction | null> {
    try {
      const { data, error } = await supabase
        .from("mitigation_actions")
        .select("*")
        .eq("id", id)
        .eq("organization_id", organizationId) // Add organization filter
        .maybeSingle();
      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }
      if (!data) {
        return null;
      }
      // Validate database structure
      if (!isDbMitigationAction(data)) {
        throw new Error("Invalid mitigation action data from database");
      }
      const domainAction = mapDbMitigationActionToDomain(data);
      // Validate the mitigation action object for type safety
      if (!isMitigationAction(domainAction)) {
        throw new Error("Invalid mitigation action data structure");
      }
      return domainAction;
    } catch (error) {
      throw error;
    }
  },
  /**
   * Create a new mitigation action - with organization_id
   */
  async createMitigationAction(
    actionData: Partial<MitigationAction> & { organizationId: string }
  ): Promise<MitigationAction | null> {
    try {
      const dbAction = mapDomainMitigationActionToDb(actionData);
      // Ensure required fields are present
      if (!dbAction.description || !dbAction.risk_id || !actionData.organizationId) {
        throw new Error("Mitigation action requires description, risk_id, and organization_id");
      }
      const { data, error } = await supabase
        .from("mitigation_actions")
        .insert({
          risk_id: dbAction.risk_id,
          organization_id: actionData.organizationId,
          description: dbAction.description,
          completed: dbAction.completed !== undefined ? dbAction.completed : false,
        })
        .select()
        .single();
      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }
      if (!data) {
        return null;
      }
      // Validate returned data
      if (!isDbMitigationAction(data)) {
        throw new Error("Invalid mitigation action data returned from database");
      }
      return mapDbMitigationActionToDomain(data);
    } catch (error) {
      throw error;
    }
  },
  /**
   * Update a mitigation action - with organization check
   */
  async updateMitigationAction(
    id: string,
    actionData: Partial<MitigationAction>,
    organizationId: string
  ): Promise<boolean> {
    try {
      const dbAction = mapDomainMitigationActionToDb(actionData);
      // Add updated_at timestamp
      dbAction.updated_at = new Date().toISOString();
      // Remove any undefined values to avoid overwriting with null
      const updateData: Record<string, any> = {};
      Object.entries(dbAction).forEach(([key, value]) => {
        if (value !== undefined) {
          updateData[key] = value;
        }
      });
      const { error } = await supabase
        .from("mitigation_actions")
        .update(updateData)
        .eq("id", id)
        .eq("organization_id", organizationId); // Add organization filter
      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }
      return true;
    } catch (error) {
      throw error;
    }
  },
  /**
   * Delete a mitigation action - with organization check
   */
  async deleteMitigationAction(id: string, organizationId: string): Promise<boolean> {
    try {
      const { error } = await supabase
        .from("mitigation_actions")
        .delete()
        .eq("id", id)
        .eq("organization_id", organizationId); // Add organization filter
      if (error) {
        throw new Error(`Database error: ${error.message}`);
      }
      return true;
    } catch (error) {
      throw error;
    }
  },
};
