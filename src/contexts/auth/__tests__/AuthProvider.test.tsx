import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook, act, waitFor } from "@testing-library/react";
import { AuthProvider, useAuth } from "../AuthProvider";
import { supabase } from "@/integrations/supabase/client";
import { mockUser, createMockSupabaseResponse } from "@/test/test-utils";
import React from "react";

// Mock the supabase client
vi.mock("@/integrations/supabase/client");

// Mock the auth hooks
vi.mock("../hooks/useAuthState", () => ({
  useAuthState: () => ({
    user: mockUser,
    setUser: vi.fn(),
    isAuthenticated: true,
    isLoading: false,
  }),
}));

vi.mock("../signupService", () => ({
  useAuthActions: () => ({
    login: vi.fn(),
    signup: vi.fn(),
    logout: vi.fn(),
    hasPermission: vi.fn(),
    requestAdminRole: vi.fn(),
    generateInviteCode: vi.fn(),
  }),
}));

describe("AuthProvider", () => {
  const wrapper = ({ children }: { children: React.ReactNode }) => (
    <AuthProvider>{children}</AuthProvider>
  );

  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default mock for supabase.from with complete chain including limit method
    const createMockChain = (responses: unknown[] = []) => {
      let callIndex = 0;
      return {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        limit: vi.fn().mockImplementation(() => {
          const response = responses[callIndex] || createMockSupabaseResponse([]);
          callIndex++;
          return Promise.resolve(response);
        }),
        single: vi.fn().mockImplementation(() => {
          const response = responses[callIndex] || createMockSupabaseResponse(null);
          callIndex++;
          return Promise.resolve(response);
        }),
      };
    };

    // Default to returning empty results (no organization membership)
    vi.mocked(supabase.from).mockImplementation(() =>
      createMockChain([createMockSupabaseResponse([])])
    );
  });

  it("should provide auth context values", () => {
    const { result } = renderHook(() => useAuth(), { wrapper });

    expect(result.current).toEqual(
      expect.objectContaining({
        user: expect.any(Object),
        // organization can be null initially
        organization: expect.anything(),
        // organizationRole can be null initially
        organizationRole: expect.anything(),
        isAuthenticated: expect.any(Boolean),
        isLoading: expect.any(Boolean),
        needsOrganizationSetup: expect.any(Boolean),
        login: expect.any(Function),
        signup: expect.any(Function),
        logout: expect.any(Function),
        hasPermission: expect.any(Function),
        hasOrgPermission: expect.any(Function),
        requestAdminRole: expect.any(Function),
        generateInviteCode: expect.any(Function),
        switchOrganization: expect.any(Function),
        refreshAfterOrganizationSetup: expect.any(Function),
      })
    );
  });

  it("should throw error when used outside provider", () => {
    expect(() => {
      renderHook(() => useAuth());
    }).toThrow("useAuth must be used within an AuthProvider");
  });

  it("should load organization data when user is available", async () => {
    const mockMembershipData = {
      organization_id: "test-org-id",
      role: "admin",
    };

    const mockOrganizationData = {
      id: "test-org-id",
      name: "Test Organization",
      slug: "test-org",
      subscription_plan: "professional",
      subscription_status: "active",
      max_users: 100,
      max_risks: 1000,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
    };

    // Mock for checking organization setup (first query returns a result)
    const mockSetupChain = {
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      limit: vi.fn().mockResolvedValue(createMockSupabaseResponse([mockMembershipData])),
      single: vi.fn().mockResolvedValue(createMockSupabaseResponse([mockMembershipData])),
    };

    // Mock for loading org data (returns membership then organization)
    const mockLoadChain = {
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi
        .fn()
        .mockResolvedValueOnce(createMockSupabaseResponse(mockMembershipData))
        .mockResolvedValueOnce(createMockSupabaseResponse(mockOrganizationData)),
    };

    let callCount = 0;
    vi.mocked(supabase.from).mockImplementation(() => {
      callCount++;
      // First few calls are for setup check
      if (callCount <= 2) return mockSetupChain as any;
      // Later calls are for loading organization data
      return mockLoadChain as any;
    });

    const { result } = renderHook(() => useAuth(), { wrapper });

    await waitFor(() => {
      expect(result.current.organization).toBeTruthy();
      expect(result.current.organizationRole).toBe("admin");
    });
  });

  it("should handle organization membership not found", async () => {
    const mockSupabaseChain = {
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      limit: vi.fn().mockResolvedValue(createMockSupabaseResponse(null, { message: "Not found" })),
      single: vi.fn().mockResolvedValue(createMockSupabaseResponse(null, { message: "Not found" })),
    };

    vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any);

    const { result } = renderHook(() => useAuth(), { wrapper });

    await waitFor(() => {
      expect(result.current.needsOrganizationSetup).toBe(true);
      expect(result.current.organization).toBeNull();
    });
  });

  it("should check organization permissions correctly", async () => {
    // Set up mock to return membership data so the provider loads an organization role
    const mockMembershipData = {
      organization_id: "test-org-id",
      role: "admin",
    };

    const mockOrganizationData = {
      id: "test-org-id",
      name: "Test Organization",
      slug: "test-org",
      subscription_plan: "professional",
      subscription_status: "active",
      max_users: 100,
      max_risks: 1000,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
    };

    const mockSupabaseChain = {
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      limit: vi.fn().mockResolvedValue(createMockSupabaseResponse([mockMembershipData])),
      single: vi
        .fn()
        .mockResolvedValueOnce(createMockSupabaseResponse(mockMembershipData))
        .mockResolvedValueOnce(createMockSupabaseResponse(mockOrganizationData)),
    };

    vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any);

    const { result } = renderHook(() => useAuth(), { wrapper });

    // Wait for the organization data to load
    await waitFor(() => {
      expect(result.current.organizationRole).toBe("admin");
    });

    expect(result.current.hasOrgPermission(["admin"])).toBe(true);
    expect(result.current.hasOrgPermission(["owner"])).toBe(false);
    expect(result.current.hasOrgPermission(["admin", "member"])).toBe(true);
  });

  it("should handle organization switching", async () => {
    const newOrgId = "new-org-id";
    const mockMembershipData = {
      organization_id: newOrgId,
      role: "member",
    };

    const mockOrganizationData = {
      id: newOrgId,
      name: "New Organization",
      slug: "new-org",
      subscription_plan: "starter",
      subscription_status: "active",
      max_users: 50,
      max_risks: 500,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
    };

    const mockSupabaseChain = {
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      limit: vi.fn().mockResolvedValue(createMockSupabaseResponse([mockMembershipData])),
      single: vi
        .fn()
        .mockResolvedValueOnce(createMockSupabaseResponse(mockMembershipData))
        .mockResolvedValueOnce(createMockSupabaseResponse(mockOrganizationData)),
    };

    vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any);

    const { result } = renderHook(() => useAuth(), { wrapper });

    await act(async () => {
      await result.current.switchOrganization(newOrgId);
    });

    await waitFor(() => {
      expect(result.current.organization?.id).toBe(newOrgId);
      expect(result.current.organizationRole).toBe("member");
    });
  });

  it("should refresh after organization setup", async () => {
    const mockMembershipData = {
      organization_id: "setup-org-id",
      role: "owner",
    };

    const mockOrganizationData = {
      id: "setup-org-id",
      name: "Setup Organization",
      slug: "setup-org",
      subscription_plan: "free",
      subscription_status: "active",
      max_users: 10,
      max_risks: 100,
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
    };

    const mockSupabaseChain = {
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      limit: vi.fn().mockResolvedValue(createMockSupabaseResponse([mockMembershipData])),
      single: vi
        .fn()
        .mockResolvedValueOnce(createMockSupabaseResponse(mockMembershipData))
        .mockResolvedValueOnce(createMockSupabaseResponse(mockOrganizationData)),
    };

    vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any);

    const { result } = renderHook(() => useAuth(), { wrapper });

    await act(async () => {
      await result.current.refreshAfterOrganizationSetup();
    });

    await waitFor(() => {
      expect(result.current.needsOrganizationSetup).toBe(false);
      expect(result.current.organization?.id).toBe("setup-org-id");
      expect(result.current.organizationRole).toBe("owner");
    });
  });

  it("should handle loading states correctly", () => {
    const { result } = renderHook(() => useAuth(), { wrapper });

    // Initially should not be loading (mocked to false)
    expect(result.current.isLoading).toBe(false);
  });

  it("should handle organization permission edge cases", () => {
    const { result } = renderHook(() => useAuth(), { wrapper });

    // Test with no organization role
    expect(result.current.hasOrgPermission(["admin"])).toBe(false);

    // Test with empty required roles
    expect(result.current.hasOrgPermission([])).toBe(true);
  });

  it("should handle organization loading errors", async () => {
    const mockError = new Error("Database connection failed");
    const mockSupabaseChain = {
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      limit: vi.fn().mockRejectedValue(mockError),
      single: vi.fn().mockRejectedValue(mockError),
    };

    vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any);

    const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

    const { result } = renderHook(() => useAuth(), { wrapper });

    // Access result to avoid unused variable warning
    void result;

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining("Error loading organization"),
        mockError
      );
    });

    consoleSpy.mockRestore();
  });

  it("should handle organization switching errors", async () => {
    const mockError = new Error("Switch failed");
    const mockSupabaseChain = {
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      limit: vi.fn().mockRejectedValue(mockError),
      single: vi.fn().mockRejectedValue(mockError),
    };

    vi.mocked(supabase.from).mockReturnValue(mockSupabaseChain as any);

    const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {});

    const { result } = renderHook(() => useAuth(), { wrapper });

    await act(async () => {
      await result.current.switchOrganization("invalid-org-id");
    });

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining("Error switching organization"),
        mockError
      );
    });

    consoleSpy.mockRestore();
  });
});
