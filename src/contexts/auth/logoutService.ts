import { supabase, cleanupAuthState } from "@/integrations/supabase/client";
import { auditLog, AuditEventType } from "@/services/auditLoggingService";
/**
 * Logout function with Chrome-specific handling
 */
export const logout = async (): Promise<{ success: boolean; message?: string }> => {
  let userId: string | undefined;
  let userEmail: string | undefined;
  try {
    // Get current user info for audit logging before cleanup
    try {
      const {
        data: { user },
      } = await supabase.auth.getUser();
      userId = user?.id;
      userEmail = user?.email;
    } catch (error) {
      // Error caught and handled
    }
    // Clean up auth state first
    cleanupAuthState();
    // Clear any pending organization setup data
    localStorage.removeItem("pendingOrganizationSetup");
    // Attempt global sign out (will work even if token is invalid)
    try {
      await supabase.auth.signOut({ scope: "global" });
    } catch (error) {
      // Continue even if this fails
    }
    // Log successful logout
    if (userId) {
      await auditLog.auth(AuditEventType.USER_LOGOUT, userId, userEmail, {
        timestamp: new Date().toISOString(),
        logoutMethod: "manual",
      });
    }
    // Force a complete page refresh with immediate navigation
    // Use replace instead of href assignment for better compatibility
    window.location.replace("/");
    return { success: true, message: "You have been successfully logged out" };
  } catch (error: unknown) {
    // Log failed logout attempt if we have user info
    if (userId) {
      try {
        await auditLog.auth(AuditEventType.USER_LOGOUT, userId, userEmail, {
          timestamp: new Date().toISOString(),
          logoutMethod: "manual",
          error: error instanceof Error ? error.message : "Unknown error",
        });
      } catch (auditError) {
        // Error caught and handled
      }
    }
    // Even if there's an error, force a page refresh to clear state
    window.location.replace("/");
    return { success: false, message: "An unexpected error occurred" };
  }
};
