import { User, UserRole } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { requestAdminAccess } from "@/services/user/adminRequestService";
import { generateInviteCode as generateInviteCodeService } from "@/services/user/inviteCodeService";
import { auditLog, AuditEventType } from "@/services/auditLoggingService";
/**
 * Fetch user profile data
 */
export const fetchUserProfile = async (userId: string): Promise<User | null> => {
  try {
    // First try the normal query
    let { data: profile, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();
    // If we get a 406 error (RLS blocking), try to create the profile first
    if (error && (error.code === "PGRST116" || error.message.includes("406"))) {
      // Get user info from auth
      const { data: authUser } = await supabase.auth.getUser();
      if (authUser.user && authUser.user.id === userId) {
        // Try to create the profile
        const { error: createError } = await supabase.from("profiles").insert({
          id: userId,
          name:
            authUser.user.user_metadata?.["name"] ?? (authUser.user.email?.split("@")[0] || "User"),
          email: authUser.user.email ?? "<EMAIL>",
          role: "staff",
          organization_id: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
        if (createError && !createError.message.includes("duplicate")) {
          // Implementation needed
        }
        // Try to fetch again
        const { data: newProfile, error: newError } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", userId)
          .single();
        if (!newError) {
          profile = newProfile;
          error = null;
        }
      }
    }
    if (error) {
      return null;
    }
    if (profile) {
      return {
        id: userId,
        name: profile.name ?? "User",
        email: profile.email ?? "No email",
        role: (profile.role as UserRole) || UserRole.STAFF, // Default to STAFF if role is not specified
        ...(profile.department && { department: profile.department }),
        ...(profile.avatar_url && { avatar: profile.avatar_url }),
        ...(profile.organization_id && { organizationId: profile.organization_id }),
      };
    }
    return null;
  } catch (err) {
    return null;
  }
};
/**
 * Request admin role
 */
export const requestAdminRole = async (
  userId: string,
  justification: string
): Promise<{ success: boolean; message?: string }> => {
  try {
    const { success, error } = await requestAdminAccess(userId, justification);
    if (!success) {
      // Log failed admin access request
      await auditLog.admin(AuditEventType.ADMIN_ACCESS_REQUESTED, userId, {
        justification,
        outcome: "failure",
        error: error?.message,
        timestamp: new Date().toISOString(),
      });
      return { success: false, message: error?.message ?? "Failed to submit admin request" };
    }
    // Log successful admin access request
    await auditLog.admin(AuditEventType.ADMIN_ACCESS_REQUESTED, userId, {
      justification,
      outcome: "success",
      timestamp: new Date().toISOString(),
    });
    return {
      success: true,
      message: "Admin request submitted successfully. You'll be notified when it's reviewed.",
    };
  } catch (error: unknown) {
    // Log exception during admin request
    await auditLog.admin(AuditEventType.ADMIN_ACCESS_REQUESTED, userId, {
      justification,
      outcome: "failure",
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
    return { success: false, message: "An unexpected error occurred" };
  }
};
/**
 * Generate invite code - wrapper for the service function
 */
export const generateInviteCode = async (
  role: UserRole,
  expiresAt?: Date
): Promise<string | null> => {
  try {
    const { data: sessionData } = await supabase.auth.getSession();
    if (!sessionData.session?.user) {
      return null;
    }
    const userId = sessionData.session.user.id;
    const { code, error } = await generateInviteCodeService(role, userId, expiresAt);
    if (error || !code) {
      return null;
    }
    return code;
  } catch (error) {
    return null;
  }
};
/**
 * Make current user admin
 */
export const makeCurrentUserAdmin = async (): Promise<{ success: boolean; message?: string }> => {
  try {
    const { data: sessionData } = await supabase.auth.getSession();
    if (!sessionData.session?.user) {
      return { success: false, message: "No user is currently logged in" };
    }
    const userId = sessionData.session.user.id;
    return await requestAdminRole(userId, "Self-service admin request");
  } catch (error: unknown) {
    return { success: false, message: "An unexpected error occurred" };
  }
};
