import { supabase } from "@/integrations/supabase/client";
import { User } from "@/types";
interface ActionResult {
  success: boolean;
  message?: string;
}
interface GenerateInviteResult {
  success: boolean;
  code?: string;
  message?: string;
}
export const hasPermission = (user: User | null, requiredRoles: string[]): boolean => {
  if (!user?.role) return false;
  return requiredRoles.includes(user.role);
};
export const requestAdminRole = async (
  user: User | null,
  justification: string
): Promise<ActionResult> => {
  if (!user?.id) {
    return { success: false, message: "User not authenticated" };
  }
  try {
    const { error } = await supabase.from("admin_requests").insert({
      user_id: user.id,
      organization_id: user.organizationId ?? null,
      justification,
      status: "pending",
    });
    if (error) {
      return { success: false, message: "Failed to submit admin request" };
    }
    return { success: true, message: "Admin request submitted successfully" };
  } catch (error) {
    return { success: false, message: "An unexpected error occurred" };
  }
};
export const generateInviteCode = async (
  user: User | null,
  role: "admin" | "member",
  expiresInDays: number = 7
): Promise<GenerateInviteResult> => {
  if (!user?.id || !user?.organizationId) {
    return {
      success: false,
      message: "User not authenticated or no organization",
    };
  }
  try {
    // Calculate expiration date
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + expiresInDays);
    // Generate a cryptographically secure invite code
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    const code = Array.from(array, byte => byte.toString(36))
      .join("")
      .substring(0, 16)
      .toUpperCase();
    const { data, error } = await supabase
      .from("invite_codes")
      .insert({
        code,
        organization_id: user.organizationId,
        role,
        created_by: user.id,
        expires_at: expiresAt.toISOString(),
        is_valid: true,
      })
      .select()
      .single();
    if (error) {
      return {
        success: false,
        message: "Failed to create invite code",
      };
    }
    return {
      success: true,
      code: data.code,
    };
  } catch (error) {
    return {
      success: false,
      message: "An unexpected error occurred",
    };
  }
};
