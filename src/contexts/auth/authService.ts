import { supabase } from "@/integrations/supabase/client";
import { User, UserRole } from "@/types";
export const fetchUserProfile = async (userId: string): Promise<User | null> => {
  try {
    // First try the normal query
    let { data: profile, error } = await supabase
      .from("profiles")
      .select("*")
      .eq("id", userId)
      .single();
    // If we get a 406 error (RLS blocking), try to create the profile first
    if (error && (error.code === "PGRST116" || error.message.includes("406"))) {
      // Get user info from auth
      const { data: authUser } = await supabase.auth.getUser();
      if (authUser.user && authUser.user.id === userId) {
        // Try to create the profile
        const { error: createError } = await supabase.from("profiles").insert({
          id: userId,
          name:
            authUser.user.user_metadata?.["name"] ?? (authUser.user.email?.split("@")[0] || "User"),
          email: authUser.user.email ?? "<EMAIL>",
          role: "staff",
          organization_id: null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        });
        if (createError && !createError.message.includes("duplicate")) {
          // Implementation needed
        }
        // Try to fetch again
        const { data: newProfile, error: newError } = await supabase
          .from("profiles")
          .select("*")
          .eq("id", userId)
          .single();
        if (!newError) {
          profile = newProfile;
          error = null;
        }
      }
    }
    if (error) {
      return null;
    }
    if (!profile) {
      return null;
    }
    // Return the user profile - let the organization assignment be handled elsewhere
    return {
      id: profile.id,
      name: profile.name,
      email: profile.email,
      role: profile.role as UserRole,
      ...(profile.department && { department: profile.department }),
      ...(profile.avatar_url && { avatar: profile.avatar_url }),
      ...(profile.organization_id && { organizationId: profile.organization_id }),
    };
  } catch (error) {
    return null;
  }
};
