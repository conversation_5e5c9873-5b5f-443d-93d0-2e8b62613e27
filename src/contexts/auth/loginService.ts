import { supabase, cleanupAuthState } from "@/integrations/supabase/client";
import { auditLog, AuditEventType } from "@/services/auditLoggingService";
/**
 * Login function
 */
export const login = async (
  email: string,
  password: string
): Promise<{ success: boolean; message?: string }> => {
  try {
    // Clean up any existing auth state first
    cleanupAuthState();
    // Attempt to sign out globally to ensure no leftover sessions
    try {
      await supabase.auth.signOut({ scope: "global" });
    } catch (error) {
      // Continue even if this fails
    }
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    if (error) {
      // Log failed login attempt
      await auditLog.auth(
        AuditEventType.LOGIN_FAILED,
        email, // Use email as identifier for failed attempts
        email,
        {
          error: error.message,
          loginMethod: "email_password",
          timestamp: new Date().toISOString(),
        }
      );
      return { success: false, message: error.message };
    }
    if (data.user) {
      // Log successful login
      await auditLog.auth(AuditEventType.USER_LOGIN, data.user.id, data.user.email || email, {
        loginMethod: "email_password",
        timestamp: new Date().toISOString(),
      });
      return { success: true, message: "Welcome back!" };
    }
    return { success: false, message: "Unknown error occurred" };
  } catch (error: unknown) {
    return { success: false, message: "An unexpected error occurred" };
  }
};
