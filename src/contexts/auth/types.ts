// src/contexts/auth/types.ts - Updated with new fields

import { User, UserRole, Organization, OrganizationSetupData } from "@/types";

export interface AuthContextType {
  user: User | null;
  setUser: (user: User | null) => void;
  organization: Organization | null;
  organizationRole: 'owner' | 'admin' | 'member' | null;
  needsOrganizationSetup: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; message?: string }>;
  signup: (email: string, password: string, name: string, organizationChoice: string, organizationData?: OrganizationSetupData, inviteCode?: string) => Promise<{ success: boolean; message?: string }>;
  logout: () => Promise<void>;
  isAuthenticated: boolean;
  isLoading: boolean;
  hasPermission: (requiredRoles: UserRole[]) => boolean;
  hasOrgPermission: (requiredOrgRoles: ('owner' | 'admin' | 'member')[]) => boolean;
  requestAdminRole: (justification: string) => Promise<{ success: boolean; message?: string }>;
  generateInviteCode: (role: 'admin' | 'member', expiresInDays?: number) => Promise<{ success: boolean; code?: string; message?: string }>;
  switchOrganization: (organizationId: string) => Promise<{ success: boolean; message?: string }>;
  refreshAfterOrganizationSetup: () => Promise<void>;
}