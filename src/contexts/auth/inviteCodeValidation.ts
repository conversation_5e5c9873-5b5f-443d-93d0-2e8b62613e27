import { supabase } from "@/integrations/supabase/client";
interface InviteValidationResult {
  success: boolean;
  organizationId?: string;
  role?: string;
  message?: string;
}
export const validateInviteCode = async (inviteCode: string): Promise<InviteValidationResult> => {
  try {
    // Use the security definer function that bypasses RLS
    const { data, error } = await supabase.rpc("validate_invite_code_secure", {
      invite_code_param: inviteCode.trim(),
    });
    if (error) {
      return {
        success: false,
        message: "Error validating invite code",
      };
    }
    if (!data || data.length === 0) {
      return {
        success: false,
        message: "Error validating invite code",
      };
    }
    const result = data[0];
    if (!result) {
      return {
        success: false,
        message: "Invalid response from invite code validation",
      };
    }
    if (!result.success) {
      return {
        success: false,
        message: result.message || "Invite code validation failed",
      };
    }
    return {
      success: true,
      organizationId: result.organization_id,
      role: result.role,
    };
  } catch (error: unknown) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "Failed to validate invite code",
    };
  }
};
export const markInviteCodeAsUsed = async (
  inviteCode: string,
  userId: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { error: updateError } = await supabase
      .from("invite_codes")
      .update({
        is_valid: false,
        used_by: userId,
        used_at: new Date().toISOString(),
      })
      .eq("code", inviteCode.trim());
    if (updateError) {
      return { success: false, error: updateError.message };
    }
    return { success: true };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to mark invite code as used",
    };
  }
};
