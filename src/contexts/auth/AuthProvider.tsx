// src/contexts/auth/AuthProvider.tsx - Complete updated version
import React, { createContext, useContext, useState, useEffect } from "react";
import { Organization } from "@/types";
import { AuthContextType } from "./types";
import { useAuthState } from "./hooks/useAuthState";
import { useAuthActions } from "./signupService";
import { supabase } from "@/integrations/supabase/client";
// Create the auth context
const AuthContext = createContext<AuthContextType | undefined>(undefined);
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  // Use our custom hooks to manage auth state and actions
  const { user, setUser, isAuthenticated, isLoading } = useAuthState();
  const {
    login,
    signup: originalSignup,
    logout,
    hasPermission,
    requestAdminRole,
    generateInviteCode,
  } = useAuthActions(user);

  // Create a wrapper for signup to match the expected interface
  const signup = async (
    email: string,
    password: string,
    name: string,
    organizationChoice: string,
    _organizationData?: any,
    inviteCode?: string
  ) => {
    // Convert organizationChoice string to the expected type
    const choice = organizationChoice as "create-organization" | "use-invite" | undefined;
    return originalSignup(email, password, name, inviteCode, choice);
  };
  // Organization-specific state
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [organizationRole, setOrganizationRole] = useState<"owner" | "admin" | "member" | null>(
    null
  );
  const [orgLoading, setOrgLoading] = useState(false);
  const [needsOrganizationSetup, setNeedsOrganizationSetup] = useState(false);
  // Check if user needs organization setup
  useEffect(() => {
    const checkOrganizationSetup = async () => {
      if (!user?.id || isLoading) {
        setNeedsOrganizationSetup(false);
        return;
      }
      try {
        // Check if user has any organization membership
        const { data: membership, error } = await supabase
          .from("organization_users")
          .select("organization_id, role")
          .eq("user_id", user.id)
          .limit(1);
        if (error) {
          setNeedsOrganizationSetup(true);
          return;
        }
        const needsSetup = !membership || membership.length === 0;
        setNeedsOrganizationSetup(needsSetup);
        // If user doesn't need setup but we don't have organization loaded, trigger org loading
        if (!needsSetup && !organization) {
          // Condition handled
        }
      } catch (error) {
        setNeedsOrganizationSetup(true);
      }
    };
    checkOrganizationSetup();
  }, [user?.id, isLoading, organization]);
  // Load organization data when user changes and has membership
  useEffect(() => {
    const loadOrganizationData = async () => {
      if (!user?.id || needsOrganizationSetup) {
        setOrganization(null);
        setOrganizationRole(null);
        return;
      }
      setOrgLoading(true);
      try {
        // STEP 1: Get user's organization membership
        const { data: membershipData, error: membershipError } = await supabase
          .from("organization_users")
          .select("organization_id, role")
          .eq("user_id", user.id)
          .single();
        if (membershipError || !membershipData) {
          setNeedsOrganizationSetup(true);
          setOrganization(null);
          setOrganizationRole(null);
          return;
        }
        // STEP 2: Get the organization details
        const { data: orgData, error: orgError } = await supabase
          .from("organizations")
          .select("*")
          .eq("id", membershipData.organization_id)
          .single();
        if (orgError || !orgData) {
          setOrganization(null);
          setOrganizationRole(null);
          return;
        }
        // STEP 3: Map database organization to domain model
        const org: Organization = {
          id: orgData.id,
          name: orgData.name,
          slug: orgData.slug,
          subscriptionPlan: orgData.subscription_plan as Organization["subscriptionPlan"],
          subscriptionStatus: orgData.subscription_status as Organization["subscriptionStatus"],
          maxUsers: orgData.max_users ?? 0,
          maxRisks: orgData.max_risks ?? 0,
          createdAt: new Date(orgData.created_at),
          updatedAt: new Date(orgData.updated_at),
          ...(orgData.domain && { domain: orgData.domain }),
          ...(orgData.logo_url && { logoUrl: orgData.logo_url }),
        };
        setOrganization(org);
        setOrganizationRole(membershipData.role as "owner" | "admin" | "member");
        setNeedsOrganizationSetup(false);
      } catch (error) {
        setOrganization(null);
        setOrganizationRole(null);
      } finally {
        setOrgLoading(false);
      }
    };
    loadOrganizationData();
  }, [user?.id, needsOrganizationSetup]);
  // Log organization state changes
  useEffect(() => {}, [organization, organizationRole, orgLoading, needsOrganizationSetup]);
  // Organization permission checker
  const hasOrgPermission = (requiredOrgRoles: ("owner" | "admin" | "member")[]): boolean => {
    // If no roles are required, permission is granted
    if (requiredOrgRoles.length === 0) return true;
    // If user has no role, permission is denied
    if (!organizationRole) return false;
    return requiredOrgRoles.includes(organizationRole);
  };
  // Switch organization function (for future multi-org users)
  const switchOrganization = async (
    organizationId: string
  ): Promise<{ success: boolean; message?: string }> => {
    if (!user?.id) {
      return { success: false, message: "User not authenticated" };
    }
    try {
      // Check if user has access to this organization using two separate queries
      const { data: membershipData, error: membershipError } = await supabase
        .from("organization_users")
        .select("organization_id, role")
        .eq("user_id", user.id)
        .eq("organization_id", organizationId)
        .single();
      if (membershipError || !membershipData) {
        return { success: false, message: "Access denied to organization" };
      }
      const { data: orgData, error: orgError } = await supabase
        .from("organizations")
        .select("*")
        .eq("id", organizationId)
        .single();
      if (orgError || !orgData) {
        return { success: false, message: "Organization not found" };
      }
      const org: Organization = {
        id: orgData.id,
        name: orgData.name,
        slug: orgData.slug,
        subscriptionPlan: orgData.subscription_plan as Organization["subscriptionPlan"],
        subscriptionStatus: orgData.subscription_status as Organization["subscriptionStatus"],
        maxUsers: orgData.max_users ?? 0,
        maxRisks: orgData.max_risks ?? 0,
        createdAt: new Date(orgData.created_at),
        updatedAt: new Date(orgData.updated_at),
        ...(orgData.domain && { domain: orgData.domain }),
        ...(orgData.logo_url && { logoUrl: orgData.logo_url }),
      };
      setOrganization(org);
      setOrganizationRole(membershipData.role as "owner" | "admin" | "member");
      setNeedsOrganizationSetup(false);
      return { success: true };
    } catch (error) {
      return { success: false, message: "Failed to switch organization" };
    }
  };
  // Refresh organization data after setup completion
  const refreshAfterOrganizationSetup = async (): Promise<void> => {
    setNeedsOrganizationSetup(false);
    // The organization loading effect will automatically trigger
  };
  // Create the context value object with all auth-related state and functions
  const value: AuthContextType = {
    user,
    setUser,
    organization,
    organizationRole,
    needsOrganizationSetup,
    login,
    signup,
    logout,
    isAuthenticated,
    isLoading: isLoading || orgLoading,
    hasPermission,
    hasOrgPermission,
    requestAdminRole: (justification: string) => requestAdminRole(justification),
    generateInviteCode,
    switchOrganization,
    refreshAfterOrganizationSetup,
  };
  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
