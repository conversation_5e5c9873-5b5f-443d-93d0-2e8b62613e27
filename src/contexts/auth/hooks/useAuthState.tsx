import { useState, useEffect } from "react";
import { User } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import {
  handleUserSession,
  checkAndClearStuckState,
  forceLogout,
} from "../services/authStateHelpers";
export function useAuthState() {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  useEffect(() => {
    // Force logout and redirect if user is stuck
    if (checkAndClearStuckState()) {
      return;
    }
    // Set up auth state listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((event, session) => {
      if (session?.user) {
        // Only do synchronous state updates here
        setIsAuthenticated(true);
        setIsLoading(false);
        // Defer all async Supabase operations to prevent deadlock
        setTimeout(() => {
          handleUserSession(session.user.id, event, setUser, setIsAuthenticated);
        }, 0);
      } else {
        setUser(null);
        setIsAuthenticated(false);
        setIsLoading(false);
      }
    });
    // Get the initial session
    supabase.auth
      .getSession()
      .then(({ data: { session } }) => {
        if (!session?.user) {
          setIsLoading(false);
        }
      })
      .catch(() => {
        setIsLoading(false);
      });
    return () => {
      subscription.unsubscribe();
    };
  }, []);
  // Add timeout to detect stuck states
  useEffect(() => {
    if (isLoading) {
      const timeout = setTimeout(() => {
        forceLogout();
      }, 10000); // 10 second timeout
      return () => clearTimeout(timeout);
    }
    // Return undefined for the else case
    return undefined;
  }, [isLoading]);
  return {
    user,
    setUser,
    isAuthenticated,
    isLoading,
    forceLogout,
  };
}
