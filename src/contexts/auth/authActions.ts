import { supabase } from "@/integrations/supabase/client";
import { validateInviteCode, markInviteCodeAsUsed } from "./inviteCodeValidation";
import { auditLog, AuditEventType } from "@/services/auditLoggingService";
interface SignupResult {
  success: boolean;
  message?: string;
}
export const login = async (email: string, password: string): Promise<SignupResult> => {
  try {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    if (error) {
      return {
        success: false,
        message: error.message ?? "Login failed",
      };
    }
    if (data.user) {
      return { success: true };
    }
    return { success: false, message: "Lo<PERSON> failed" };
  } catch (error: unknown) {
    return {
      success: false,
      message: error instanceof Error ? error.message : "An unexpected error occurred during login",
    };
  }
};
export const signup = async (
  email: string,
  password: string,
  name: string,
  inviteCode?: string,
  organizationChoice?: "create-organization" | "use-invite"
): Promise<SignupResult> => {
  try {
    // If using an invite code, validate it first and get organization info
    let organizationId: string | undefined;
    let userRole: string = "staff"; // default role
    if (organizationChoice === "use-invite" && inviteCode) {
      const validation = await validateInviteCode(inviteCode);
      if (!validation.success) {
        return {
          success: false,
          message: validation.message ?? "Invalid invite code",
        };
      }
      organizationId = validation.organizationId;
      userRole = validation.role ?? "staff";
    }
    // Get the current domain for email confirmation redirect
    const redirectUrl = `${window.location.origin}/dashboard`;
    // Create the user account with proper email confirmation
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: redirectUrl,
        data: {
          name: name,
          organization_choice: organizationChoice,
          invite_code: inviteCode,
          organization_id: organizationId,
          role: userRole,
        },
      },
    });
    if (error) {
      return {
        success: false,
        message: error.message ?? "Failed to create account",
      };
    }
    if (!data.user) {
      return {
        success: false,
        message: "Failed to create user account",
      };
    }
    // Log successful signup
    await auditLog.auth(AuditEventType.USER_SIGNUP, data.user.id, data.user.email || email, {
      name,
      organizationChoice,
      hasInviteCode: !!inviteCode,
      organizationId,
      userRole,
      timestamp: new Date().toISOString(),
    });
    // Create user profile immediately after signup
    try {
      const { error: profileError } = await supabase.from("profiles").insert({
        id: data.user.id,
        name: name,
        email: email,
        role: userRole,
        organization_id: organizationId ?? null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });
      if (profileError) {
        // Don't fail the signup for this, but log it
      } else {
        // Else case handled
      }
    } catch (profileCreationError) {
      // Don't fail the signup for this
    }
    // Store pending setup for completion after email confirmation
    if (organizationChoice) {
      const pendingSetup = {
        userId: data.user.id,
        choice: organizationChoice,
        ...(organizationChoice === "use-invite" && inviteCode
          ? {
              inviteCode,
              organizationId,
              role: userRole,
            }
          : {}),
      };
      localStorage.setItem("pendingOrganizationSetup", JSON.stringify(pendingSetup));
    }
    // If using invite code, mark it as used immediately
    if (organizationChoice === "use-invite" && inviteCode) {
      const markResult = await markInviteCodeAsUsed(inviteCode, data.user.id);
      if (!markResult.success) {
        // Don't fail the signup for this, just log it
      }
    }
    // Check if email confirmation is required
    if (!data.session) {
      // Condition handled
    }
    return { success: true };
  } catch (error: unknown) {
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "An unexpected error occurred during signup",
    };
  }
};
export const logout = async (): Promise<void> => {
  try {
    // Clear any pending organization setup data
    localStorage.removeItem("pendingOrganizationSetup");
    const { error } = await supabase.auth.signOut();
    if (error) {
      // Condition handled
    } else {
      // Else case handled
    }
  } catch (error) {
    // Error caught and handled
  }
};
