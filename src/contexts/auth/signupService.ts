import { User } from "@/types";
import { login, signup, logout } from "./authActions";
import { hasPermission, requestAdminRole, generateInviteCode } from "./permissionActions";

export const useAuthActions = (user: User | null) => {
  return {
    login,
    signup, // Signup should be available without user being logged in
    logout: () => {
      if (!user) throw new Error("User must be logged in to logout");
      return logout();
    },
    hasPermission: (requiredRoles: string[]) => {
      if (!user) return false; // Return false if no user instead of throwing
      return hasPermission(user, requiredRoles);
    },
    requestAdminRole: (justification: string) => {
      if (!user) throw new Error("User must be logged in to request admin role");
      return requestAdminRole(user, justification);
    },
    generateInviteCode: (role: "admin" | "member", expiresInDays: number = 7) => {
      if (!user) throw new Error("User must be logged in to generate invite codes");
      return generateInviteCode(user, role, expiresInDays);
    },
  };
};
