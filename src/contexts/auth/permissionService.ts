import { User, UserRole } from "@/types";

/**
 * Check if user has required role(s)
 */

export const hasPermission = (user: User | null, requiredRoles: UserRole[]): boolean => {
  if (!user) return false;

  // Admins have access to everything
  if (user.role === UserRole.ADMIN) return true;

  // Risk Owners have access to everything except admin-specific features
  if (user.role === UserRole.RISK_OWNER && !requiredRoles.includes(UserRole.ADMIN)) return true;

  // Check if user's role is in the required roles list
  return requiredRoles.includes(user.role);
};

/**
 * Check if user can edit incidents (only reporter, risk owners, or admins)
 */

export const canEditIncident = (user: User | null, reporterId: string): boolean => {
  if (!user) return false;

  // Admin and Risk Owners can edit any incident
  if (user.role === UserRole.ADMIN || user.role === UserRole.RISK_OWNER) return true;

  // Staff can only edit incidents they reported
  return user.role === UserRole.STAFF && user.id === reporterId;
};

/**
 * Check if user can create risks (only risk owners or admins)
 */

export const canCreateRisk = (user: User | null): boolean => {
  if (!user) return false;
  return user.role === UserRole.ADMIN || user.role === UserRole.RISK_OWNER;
};

/**
 * Check if user can create incidents (all users except board members)
 */

export const canCreateIncident = (user: User | null): boolean => {
  if (!user) return false;
  return user.role !== UserRole.BOARD_MEMBER;
};

/**
 * Check if user can manage users (only admins)
 */

export const canManageUsers = (user: User | null): boolean => {
  if (!user) return false;
  return user.role === UserRole.ADMIN;
};
