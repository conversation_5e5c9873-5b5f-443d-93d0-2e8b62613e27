import { UserRole } from "@/types";
import { supabase } from "@/integrations/supabase/client";
// Define a type for invite code data to improve type safety
interface InviteCodeData {
  id: string;
  code: string;
  is_valid: boolean;
  role: UserRole;
  created_at: string;
  expires_at?: string | null;
  used_by?: string | null;
  used_at?: string | null;
  created_by: string;
}
/**
 * Validates an invite code and returns the invite data if valid
 */
export const validateInviteCode = async (
  inviteCode: string
): Promise<{
  valid: boolean;
  data?: InviteCodeData;
  error?: string;
}> => {
  try {
    if (!inviteCode || inviteCode.trim() === "") {
      return { valid: false, error: "Invite code cannot be empty" };
    }
    // Use direct supabase client call instead of problematic safeFrom helper
    const { data, error } = await supabase
      .from("invite_codes")
      .select("*")
      .eq("code", inviteCode.trim())
      .single();
    if (error || !data) {
      return { valid: false, error: "Invalid invite code" };
    }
    // Type assertion to tell TypeScript that data has the structure we expect
    const inviteData = data as unknown as InviteCodeData;
    // Check if the invite code is valid with safe property access
    if (!inviteData.is_valid) {
      return { valid: false, error: "This invite code has already been used" };
    }
    // Check if the invite code has expired
    if (inviteData.expires_at && new Date(inviteData.expires_at) < new Date()) {
      return { valid: false, error: "This invite code has expired" };
    }
    return { valid: true, data: inviteData };
  } catch (error: unknown) {
    return {
      valid: false,
      error: error instanceof Error ? error.message : "Failed to validate invite code",
    };
  }
};
/**
 * Update invite code status to used
 */
export const markInviteCodeAsUsed = async (
  inviteCode: string,
  userId: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    // Use direct supabase client call
    const { error: updateError } = await supabase
      .from("invite_codes")
      .update({
        is_valid: false,
        used_by: userId,
        used_at: new Date().toISOString(),
      })
      .eq("code", inviteCode);
    if (updateError) {
      return { success: false, error: updateError.message };
    }
    return { success: true };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to mark invite code as used",
    };
  }
};
/**
 * Update user profile role
 */
export const updateUserProfileRole = async (
  userId: string,
  role: UserRole
): Promise<{ success: boolean; error?: string }> => {
  try {
    const { error: profileError } = await supabase
      .from("profiles")
      .update({ role })
      .eq("id", userId);
    if (profileError) {
      return { success: false, error: profileError.message };
    }
    return { success: true };
  } catch (error: unknown) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update user profile role",
    };
  }
};
