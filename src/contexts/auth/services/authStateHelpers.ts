import { User } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { fetchUserProfile } from "../authService";
import { handlePendingOrganizationSetup } from "./organizationSetupService";
export const handleUserSession = async (
  userId: string,
  event: string,
  setUser: (user: User | null) => void,
  setIsAuthenticated: (auth: boolean) => void
) => {
  try {
    // Handle pending organization setup after email confirmation
    if (event === "SIGNED_IN") {
      const completed = await handlePendingOrganizationSetup(userId);
      if (completed) {
        // Force reload user profile after organization setup
        setTimeout(async () => {
          const userData = await fetchUserProfile(userId);
          if (userData) {
            setUser(userData);
          }
        }, 500);
        return;
      }
    }
    const userData = await fetchUserProfile(userId);
    if (userData) {
      setUser(userData);
    } else {
      // Try to create a profile for the user
      try {
        // Get user info from Supabase Auth
        const { data: authUser } = await supabase.auth.getUser();
        if (authUser.user) {
          const { error: createError } = await supabase.from("profiles").insert({
            id: userId,
            name:
              authUser.user.user_metadata?.["name"] ??
              (authUser.user.email?.split("@")[0] || "User"),
            email: authUser.user.email ?? "<EMAIL>",
            role: "staff", // Default role
            organization_id: null, // Will be set during organization setup
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });
          if (createError) {
            setUser(null);
            setIsAuthenticated(false);
          } else {
            // Try to fetch the profile again
            const newUserData = await fetchUserProfile(userId);
            if (newUserData) {
              setUser(newUserData);
            } else {
              setUser(null);
              setIsAuthenticated(false);
            }
          }
        } else {
          setUser(null);
          setIsAuthenticated(false);
        }
      } catch (createError) {
        setUser(null);
        setIsAuthenticated(false);
      }
    }
  } catch (error) {
    setUser(null);
    setIsAuthenticated(false);
  }
};
export const checkAndClearStuckState = (): boolean => {
  const isStuck = localStorage.getItem("authStuckState");
  if (isStuck) {
    localStorage.removeItem("authStuckState");
    window.location.replace("/");
    return true;
  }
  return false;
};
export const forceLogout = () => {
  localStorage.setItem("authStuckState", "true");
  window.location.replace("/");
};
