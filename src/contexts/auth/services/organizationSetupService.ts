import { supabase } from "@/integrations/supabase/client";
export const handlePendingOrganizationSetup = async (userId: string): Promise<boolean> => {
  try {
    const pendingSetup = localStorage.getItem("pendingOrganizationSetup");
    if (!pendingSetup) return false;
    const setupData = JSON.parse(pendingSetup);
    if (setupData.userId !== userId) return false;
    // Clear the pending setup first
    localStorage.removeItem("pendingOrganizationSetup");
    if (setupData.choice === "use-invite" && setupData.inviteCode) {
      return await joinOrganizationViaInvite(userId, setupData.inviteCode);
    }
    // For create-organization, just return false so user goes through normal org setup
    return false;
  } catch (error) {
    return false;
  }
};
export const joinOrganizationViaInvite = async (
  userId: string,
  inviteCode: string
): Promise<boolean> => {
  try {
    // Use the security definer function to validate the invite code
    const { data: validationData, error: validationError } = await supabase.rpc(
      "validate_invite_code_secure",
      {
        invite_code_param: inviteCode.trim(),
      }
    );
    if (validationError || !validationData || validationData.length === 0) {
      return false;
    }
    const validation = validationData[0];
    if (!validation?.success) {
      return false;
    }
    // Add user to organization_users table with correct role mapping
    const orgRole = validation.role === "admin" ? "admin" : "member";
    const { error: membershipError } = await supabase.from("organization_users").insert({
      user_id: userId,
      organization_id: validation.organization_id,
      role: orgRole,
    });
    if (membershipError) {
      // Don't throw here, continue with profile update
    } else {
      // Else case handled
    }
    // Update user profile with organization and correct role mapping
    // Map invite code roles to profile roles correctly
    let profileRole;
    if (validation && validation.role === "admin") {
      profileRole = "admin";
    } else if (validation && validation.role === "risk_owner") {
      profileRole = "risk_owner";
    } else {
      profileRole = "staff";
    }
    // First try to get the user's current profile to get their email and name
    const { data: currentProfile } = await supabase
      .from("profiles")
      .select("email, name")
      .eq("id", userId)
      .single();
    // Use upsert to handle both create and update cases
    const { error: profileError } = await supabase.from("profiles").upsert(
      {
        id: userId,
        organization_id: validation?.organization_id,
        role: profileRole,
        email: currentProfile?.email ?? "<EMAIL>",
        name: currentProfile?.name ?? "User",
        updated_at: new Date().toISOString(),
      },
      {
        onConflict: "id",
      }
    );
    if (profileError) {
      throw profileError;
    }
    // Mark invite code as used - get the invite code ID first
    const { data: inviteData, error: inviteSelectError } = await supabase
      .from("invite_codes")
      .select("id")
      .eq("code", inviteCode.trim())
      .single();
    if (inviteSelectError || !inviteData) {
      // Condition handled
    } else {
      const { error: inviteUpdateError } = await supabase
        .from("invite_codes")
        .update({
          is_valid: false,
          used_at: new Date().toISOString(),
          used_by: userId,
        })
        .eq("id", inviteData.id);
      if (inviteUpdateError) {
        // Don't fail the whole process for this
      } else {
        // Else case handled
      }
    }
    return true;
  } catch (error) {
    return false;
  }
};
