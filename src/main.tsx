
import React, { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON>outer } from "react-router-dom";
import App from "./App.tsx";
import "./index.css";

// Initialize logging service
import { initializeLogging, toLoggerConfig } from "./config/logging";
import { logger } from "./utils/errors/Logger";
import { log } from "./services/loggingService";

// Initialize logging with environment-specific configuration
const loggingConfig = initializeLogging();
logger.updateConfig(toLoggerConfig(loggingConfig));

// Start application correlation context
const appCorrelationId = log.debug('Application starting', { 
  environment: import.meta.env.MODE,
  timestamp: new Date().toISOString()
}, { 
  component: 'main',
  action: 'app_start'
});

log.info('Main.tsx loading', { 
  environment: import.meta.env.MODE 
}, { 
  component: 'main',
  action: 'module_load'
});

// Simple error boundary for the root level
class RootErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    log.error('Root error boundary caught an error', error, {
      component: 'root_error_boundary',
      action: 'error_caught',
      additionalData: { errorInfo }
    });
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{
          padding: '20px',
          textAlign: 'center',
          fontFamily: 'Arial, sans-serif',
          color: '#dc2626'
        }}>
          <h1>Application Error</h1>
          <p>Something went wrong loading the application.</p>
          <p>Please refresh the page or contact support.</p>
          <button
            onClick={() => window.location.reload()}
            style={{
              padding: '10px 20px',
              backgroundColor: '#3b82f6',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              marginTop: '20px'
            }}
          >
            Refresh Page
          </button>
          <details style={{ marginTop: '20px', textAlign: 'left' }}>
            <summary>Error Details</summary>
            <pre style={{ background: '#f5f5f5', padding: '10px', marginTop: '10px' }}>
              {this.state.error?.message ?? 'Unknown error'}
            </pre>
          </details>
        </div>
      );
    }

    return this.props.children;
  }
}

const rootElement = document.getElementById("root");
if (!rootElement) {
  throw new Error("Root element not found");
}

try {
  createRoot(rootElement).render(
    <StrictMode>
      <RootErrorBoundary>
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </RootErrorBoundary>
    </StrictMode>
  );
} catch (error) {
  log.error('Failed to render app', error as Error, {
    component: 'main',
    action: 'render_failure'
  });
  rootElement.innerHTML = `
    <div style="padding: 20px; text-align: center; font-family: Arial, sans-serif; color: #dc2626;">
      <h1>Application Error</h1>
      <p>Failed to initialize the application.</p>
      <p>Please refresh the page or contact support.</p>
      <button onclick="window.location.reload()" style="padding: 10px 20px; background-color: #3b82f6; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 20px;">
        Refresh Page
      </button>
    </div>
  `;
}
