src/components/ui/chart.tsx(70,9): error TS6133: 'variables' is declared but its value is never read.
src/components/ui/responsive-table/ResponsiveTable.tsx(154,10): error TS2375: Type '{ items: T[]; itemHeight: number; renderItem: (item: T, index: number) => Element; className: string | undefined; pullToRefresh: boolean; onRefresh: (() => Promise<void>) | undefined; loading: false | undefined; }' is not assignable to type 'MobileVirtualListProps<T>' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'onRefresh' are incompatible.
    Type '(() => Promise<void>) | undefined' is not assignable to type '() => Promise<void>'.
      Type 'undefined' is not assignable to type '() => Promise<void>'.
src/components/ui/safe-html.tsx(76,37): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type 'DOMPurify.Config' is not assignable to parameter of type 'import("/dev-server/node_modules/dompurify/dist/purify.es").Config' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Types of property 'PARSER_MEDIA_TYPE' are incompatible.
        Type 'string | undefined' is not assignable to type 'DOMParserSupportedType | undefined'.
          Type 'string' is not assignable to type 'DOMParserSupportedType | undefined'.
src/components/ui/safe-html.tsx(211,35): error TS2769: No overload matches this call.
  The last overload gave the following error.
    Argument of type 'DOMPurify.Config' is not assignable to parameter of type 'import("/dev-server/node_modules/dompurify/dist/purify.es").Config' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Types of property 'PARSER_MEDIA_TYPE' are incompatible.
        Type 'string | undefined' is not assignable to type 'DOMParserSupportedType | undefined'.
          Type 'string' is not assignable to type 'DOMParserSupportedType | undefined'.
src/components/ui/wizard/WizardContainer.tsx(26,8): error TS2375: Type '{ currentStep: number; totalSteps: number; canProceed: boolean; isSubmitting: boolean; onPrevious: () => void; onNext: () => void; onCancel: (() => void) | undefined; nextButtonText: string | undefined; showValidationHint: boolean; validationHintText: string | undefined; }' is not assignable to type 'WizardNavigationProps' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'onCancel' are incompatible.
    Type '(() => void) | undefined' is not assignable to type '() => void'.
      Type 'undefined' is not assignable to type '() => void'.
src/contexts/auth/AuthProvider.tsx(93,15): error TS2375: Type '{ id: string; name: string; slug: string; domain: string | undefined; logoUrl: string | undefined; subscriptionPlan: Organization["subscriptionPlan"]; subscriptionStatus: Organization["subscriptionStatus"]; maxUsers: number; maxRisks: number; createdAt: Date; updatedAt: Date; }' is not assignable to type 'Organization' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'domain' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
src/contexts/auth/AuthProvider.tsx(154,13): error TS2375: Type '{ id: string; name: string; slug: string; domain: string | undefined; logoUrl: string | undefined; subscriptionPlan: Organization["subscriptionPlan"]; subscriptionStatus: Organization["subscriptionStatus"]; maxUsers: number; maxRisks: number; createdAt: Date; updatedAt: Date; }' is not assignable to type 'Organization' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
  Types of property 'domain' are incompatible.
    Type 'string | undefined' is not assignable to type 'string'.
      Type 'undefined' is not assignable to type 'string'.
src/contexts/auth/AuthProvider.tsx(188,5): error TS2322: Type '(email: string, password: string, name: string, inviteCode?: string, organizationChoice?: "create-organization" | "use-invite") => Promise<SignupResult>' is not assignable to type '(email: string, password: string, name: string, organizationChoice: string, organizationData?: OrganizationSetupData | undefined, inviteCode?: string | undefined) => Promise<...>'.
  Types of parameters 'organizationChoice' and 'organizationData' are incompatible.
    Type 'OrganizationSetupData | undefined' is not assignable to type '"create-organization" | "use-invite" | undefined'.
      Type 'OrganizationSetupData' is not assignable to type '"create-organization" | "use-invite" | undefined'.
src/contexts/auth/__tests__/AuthProvider.test.tsx(5,20): error TS6133: 'mockOrganization' is declared but its value is never read.
src/contexts/auth/__tests__/AuthProvider.test.tsx(5,66): error TS6133: 'createMockSupabaseChain' is declared but its value is never read.
src/contexts/auth/__tests__/AuthProvider.test.tsx(321,11): error TS6133: 'result' is declared but its value is never read.
src/contexts/auth/authActions.ts(2,1): error TS6133: 'User' is declared but its value is never read.
src/contexts/auth/authActions.ts(9,11): error TS6196: 'GenerateInviteResult' is declared but never used.
src/contexts/auth/authActions.ts(33,23): error TS2339: Property 'message' does not exist on type '{}'.
src/contexts/auth/authActions.ts(146,23): error TS2339: Property 'message' does not exist on type '{}'.
src/contexts/auth/authService.ts(19,46): error TS4111: Property 'name' comes from an index signature, so it must be accessed with ['name'].
src/contexts/auth/authService.ts(53,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/contexts/auth/authService.ts(54,7): error TS2322: Type 'string | null' is not assignable to type 'string | undefined'.
  Type 'null' is not assignable to type 'string | undefined'.
src/contexts/auth/hooks/useAuthState.tsx(44,14): error TS6133: 'err' is declared but its value is never read.
src/contexts/auth/hooks/useAuthState.tsx(52,13): error TS7030: Not all code paths return a value.
src/contexts/auth/inviteCodeValidation.ts(27,10): error TS18048: 'result' is possibly 'undefined'.
src/contexts/auth/inviteCodeValidation.ts(30,18): error TS18048: 'result' is possibly 'undefined'.
src/contexts/auth/inviteCodeValidation.ts(35,23): error TS18048: 'result' is possibly 'undefined'.
src/contexts/auth/inviteCodeValidation.ts(36,13): error TS18048: 'result' is possibly 'undefined'.
src/contexts/auth/inviteCodeValidation.ts(41,23): error TS2339: Property 'message' does not exist on type '{}'.
src/contexts/auth/inviteCodeValidation.ts(63,44): error TS2339: Property 'message' does not exist on type '{}'.
src/contexts/auth/loginService.ts(2,1): error TS6133: 'forcePageReload' is declared but its value is never read.
src/contexts/auth/permissionActions.ts(24,61): error TS2769: No overload matches this call.
  Overload 1 of 2, '(values: { created_at?: string; feedback?: string | null; id?: string; justification: string; organization_id?: string | null; reviewer_id?: string | null; status?: string; user_id: string; }, options?: { ...; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Argument of type '{ user_id: string; organization_id: string | undefined; justification: string; status: string; }' is not assignable to parameter of type '{ created_at?: string; feedback?: string | null; id?: string; justification: string; organization_id?: string | null; reviewer_id?: string | null; status?: string; user_id: string; }' with 'exactOptionalPropertyTypes: true'. Consider adding 'undefined' to the types of the target's properties.
      Types of property 'organization_id' are incompatible.
        Type 'string | undefined' is not assignable to type 'string | null'.
          Type 'undefined' is not assignable to type 'string | null'.
  Overload 2 of 2, '(values: { created_at?: string; feedback?: string | null; id?: string; justification: string; organization_id?: string | null; reviewer_id?: string | null; status?: string; user_id: string; }[], options?: { ...; } | undefined): PostgrestFilterBuilder<...>', gave the following error.
    Object literal may only specify known properties, and 'user_id' does not exist in type '{ created_at?: string; feedback?: string | null; id?: string; justification: string; organization_id?: string | null; reviewer_id?: string | null; status?: string; user_id: string; }[]'.
src/contexts/auth/services/authStateHelpers.ts(38,44): error TS4111: Property 'name' comes from an index signature, so it must be accessed with ['name'].
src/contexts/auth/services/organizationSetupService.ts(35,10): error TS18048: 'validation' is possibly 'undefined'.
src/contexts/auth/services/organizationSetupService.ts(39,21): error TS18048: 'validation' is possibly 'undefined'.
src/contexts/auth/services/organizationSetupService.ts(42,24): error TS18048: 'validation' is possibly 'undefined'.
src/contexts/auth/services/organizationSetupService.ts(53,9): error TS18048: 'validation' is possibly 'undefined'.
src/contexts/auth/services/organizationSetupService.ts(55,16): error TS18048: 'validation' is possibly 'undefined'.
src/contexts/auth/services/organizationSetupService.ts(70,26): error TS18048: 'validation' is possibly 'undefined'.
src/contexts/auth/signupService.ts(10,11): error TS6196: 'GenerateInviteResult' is declared but never used.
src/contexts/auth/userService.ts(25,46): error TS4111: Property 'name' comes from an index signature, so it must be accessed with ['name'].
src/contexts/auth/userService.ts(51,7): error TS2375: Type '{ id: string; name: string; email: strin