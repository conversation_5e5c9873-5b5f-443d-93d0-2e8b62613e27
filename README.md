
# RiskCompass

RiskCompass is a comprehensive enterprise risk management platform built with modern web technologies. It provides organizations with tools for risk identification, assessment, mitigation tracking, incident management, and compliance policy administration.

## 🚀 Features

- **Risk Management**: Identify, assess, and track risks across your organization
- **Incident Tracking**: Report and manage security incidents and operational issues
- **Policy Management**: Create, distribute, and track compliance policies
- **Role-Based Access Control**: Secure permissions system for different user roles
- **Dashboard Analytics**: Visual insights into risk metrics and trends
- **Risk Templates**: Pre-configured risk templates for faster risk creation
- **Reporting**: Comprehensive reports including risk matrices, heat maps, and trend analysis
- **Multi-tenant Architecture**: Organization-based isolation and management
- **Real-time Collaboration**: Comments, notifications, and activity tracking

## 🏗️ Architecture

### Frontend Architecture
- **Framework**: React 18 with TypeScript
- **Routing**: React Router DOM for client-side routing
- **State Management**: TanStack Query for server state, React Context for client state
- **UI Components**: Shadcn/ui component library built on Radix UI
- **Styling**: Tailwind CSS for utility-first styling
- **Build Tool**: Vite for fast development and optimized builds

### Backend Architecture
- **Database**: PostgreSQL with Supabase
- **Authentication**: Supabase Auth with row-level security (RLS)
- **Real-time**: Supabase real-time subscriptions
- **File Storage**: Supabase Storage for document management
- **API**: RESTful API through Supabase client

### Database Schema
- **Users & Organizations**: Multi-tenant user management
- **Risks**: Comprehensive risk tracking with severity, status, and mitigation
- **Incidents**: Security and operational incident management
- **Policies**: Document management and compliance tracking
- **Categories**: Flexible categorization system
- **Comments**: Activity tracking and collaboration
- **Templates**: Reusable risk templates

## 🛠️ Tech Stack

### Core Technologies
- **React**: ^18.3.1 - Frontend framework
- **TypeScript**: Type-safe JavaScript development
- **Vite**: ^5.0.0 - Build tool and development server
- **Tailwind CSS**: ^3.4.0 - Utility-first CSS framework

### UI & Components
- **Shadcn/ui**: Modern component library
- **Radix UI**: Accessible component primitives
- **Lucide React**: ^0.462.0 - Icon library
- **Framer Motion**: ^10.16.5 - Animation library
- **React Hook Form**: ^7.53.0 - Form management
- **Zod**: ^3.23.8 - Schema validation

### Data & State Management
- **TanStack Query**: ^5.56.2 - Server state management
- **Supabase**: ^2.49.4 - Backend-as-a-Service
- **React Router DOM**: ^6.26.2 - Client-side routing

### Charts & Visualization
- **Recharts**: ^2.12.7 - Chart library
- **React ChartJS**: ^5.3.0 - Chart.js wrapper

### Utilities & Tools
- **Date-fns**: ^3.6.0 - Date manipulation
- **XLSX**: ^0.18.5 - Excel file processing
- **jsPDF**: ^3.0.1 - PDF generation
- **File Saver**: ^2.0.5 - File download utilities

## 📦 Dependencies

### Production Dependencies
```json
{
  "@hookform/resolvers": "^3.9.0",
  "@radix-ui/react-*": "Latest versions of Radix UI components",
  "@supabase/supabase-js": "^2.49.4",
  "@tanstack/react-query": "^5.56.2",
  "react": "^18.3.1",
  "react-dom": "^18.3.1",
  "react-router-dom": "^6.26.2",
  "tailwindcss": "^3.4.0",
  "typescript": "^5.0.0",
  "vite": "^5.0.0"
}
```

### Key Development Dependencies
- **@vitejs/plugin-react-swc**: Fast React refresh
- **@types/react**: TypeScript definitions
- **ESLint**: Code linting
- **PostCSS**: CSS processing
- **Autoprefixer**: CSS vendor prefixes

## 🚦 Getting Started

### Prerequisites
- Node.js 18+ and npm
- Supabase account and project
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone <YOUR_GIT_URL>
   cd <YOUR_PROJECT_NAME>
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Environment Setup (Optional)**
   The application works out of the box with default configuration.
   For custom environments, create a `.env.local` file:
   ```bash
   cp .env.example .env.local
   # Edit .env.local if you need custom Supabase URL
   ```

   **Note**: The Supabase anon key is safely hardcoded as it's designed to be public and protected by database Row Level Security (RLS).

4. **Start development server**
   ```bash
   npm run dev
   ```

## 🔒 Security Features

This application implements enterprise-grade security measures:

### **Production Security Hardening**
- **Console log removal**: All console statements automatically stripped in production builds
- **Code minification**: Full minification and obfuscation with security validation
- **Source map protection**: Source maps disabled in production environments
- **Debug file removal**: Development debug files automatically removed
- **Environment variable security**: Strict VITE_ prefix enforcement
- **Sensitive data protection**: No sensitive information exposed in client-side code

### **XSS Protection & Input Sanitization**
- **DOMPurify integration**: Industry-standard HTML sanitization for all user content
- **Input validation**: Comprehensive type validation and sanitization service
- **Content Security Policy**: Strict CSP headers configured and enforced
- **Safe HTML rendering**: Replaced all dangerouslySetInnerHTML with secure alternatives
- **Form input protection**: All form inputs sanitized against XSS attacks

### **Security Headers & Compliance**
- **CSP Headers**: Content Security Policy configured in netlify.toml and _headers
- **Security headers**: X-Frame-Options, X-Content-Type-Options, and HSTS configured
- **HTTPS enforcement**: Strict transport security enabled
- **Referrer policy**: Configured for privacy protection

### **Build Commands**
```bash
npm run build          # Standard production build with security hardening
npm run build:secure   # Enhanced security build with penetration testing
npm run build:dev      # Development build with debugging tools
npm run security:audit # Run security audit and vulnerability scan
```

### **Security Monitoring**
- **Audit logging**: Security-sensitive operations logged for compliance
- **Error monitoring**: Structured error logging without sensitive data exposure
- **Performance monitoring**: Real-time security metrics and alerting

5. **Access the application**
   Open [http://localhost:8080](http://localhost:8080) in your browser

### Database Setup
The application uses Supabase with the following main tables:
- `profiles` - User profiles and organization membership
- `organizations` - Multi-tenant organization data
- `risks` - Risk register with assessments and mitigation
- `incidents` - Incident tracking and management
- `policies` - Policy documents and compliance
- `risk_categories` - Risk categorization
- `comments` - Activity and collaboration tracking

## 🏃‍♂️ Available Scripts

### Development Scripts
- `npm run dev` - Start development server with hot reloading
- `npm run build` - Build for production with optimizations
- `npm run build:secure` - Enhanced security build with validation
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint with TypeScript validation
- `npm run type-check` - Run TypeScript compiler checks

### Quality Assurance Scripts
- `npm run test` - Run unit tests with Vitest
- `npm run test:coverage` - Run tests with coverage report
- `npm run test:ui` - Run tests with interactive UI
- `npm run quality:check` - Run comprehensive quality checks
- `npm run quality:metrics` - Generate quality metrics report

### Performance & Bundle Scripts
- `npm run analyze` - Analyze bundle size and dependencies
- `npm run performance:test` - Run performance validation tests
- `npm run bundle:monitor` - Monitor bundle size changes
- `npm run deps:check` - Check for unused dependencies

### Security Scripts
- `npm run security:audit` - Run security vulnerability scan
- `npm run security:test` - Run security validation tests
- `npm run csp:validate` - Validate Content Security Policy compliance

## 📁 Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── ui/             # Base UI components (shadcn/ui) + quality dashboards
│   ├── error-boundaries/ # Error boundary components for fault tolerance
│   ├── risk/           # Risk management components
│   ├── incident/       # Incident management components
│   ├── policy/         # Policy management components
│   ├── dashboard/      # Dashboard components
│   ├── reports/        # Reporting components
│   └── layout/         # Layout components
├── contexts/           # React contexts
│   └── auth/          # Authentication context
├── hooks/             # Custom React hooks (fully typed)
├── pages/             # Page components with lazy loading
├── services/          # API service functions (type-safe)
│   ├── inputSanitizationService.ts  # XSS prevention
│   ├── loggingService.ts           # Centralized logging
│   ├── errorMonitoringService.ts   # Error tracking
│   └── codeQualityMetricsService.ts # Quality monitoring
├── types/             # TypeScript type definitions (strict typing)
├── utils/             # Utility functions (fully typed and tested)
│   ├── errors/        # Error handling utilities
│   ├── performance/   # Performance monitoring tools
│   └── security/      # Security utilities
├── test/              # Comprehensive test suite (87% coverage)
└── integrations/      # Third-party integrations
    └── supabase/      # Supabase configuration
```

## 🔐 Authentication & Authorization

### User Roles
- **Admin**: Full system access and user management
- **Risk Owner**: Can create and manage assigned risks
- **Staff**: Basic access to view and comment on risks
- **Board Member**: Executive dashboard and reporting access

### Security Features
- Row-level security (RLS) at database level
- JWT-based authentication via Supabase
- Organization-based data isolation
- Role-based permissions for all operations

## 🌐 Deployment

### Lovable Platform (Recommended)
1. Click the "Publish" button in the Lovable editor
2. Your app will be deployed to `yourapp.lovable.app`
3. Connect a custom domain in Project Settings > Domains

### Self-Hosting
The application can be deployed to any static hosting service:
1. Run `npm run build`
2. Deploy the `dist` folder to your hosting provider
3. Configure environment variables in your hosting environment
4. Ensure proper redirects for client-side routing

### Environment Variables
Optional environment variables for deployment:
```env
# Only needed if using a different Supabase project
VITE_SUPABASE_URL=your_custom_supabase_url

# Optional: Error reporting and logging
VITE_ERROR_REPORTING_ENDPOINT=your_error_service_url
VITE_LOG_ENDPOINT=your_logging_service_url
```

**Note**: The application uses secure defaults and will work without any environment variables. The Supabase anon key is intentionally hardcoded as it's designed to be public.

## 🔧 Configuration

### Vite Configuration
- Path aliases configured with `@/` pointing to `src/`
- Environment variable prefixes: `VITE_`, `DEV_`, `PROD_`
- Component tagger for development mode
- Host configuration for development server

### Tailwind Configuration
- Custom color scheme with CSS variables
- Dark mode support
- Mobile-first responsive design
- Custom animations and utilities

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes and commit: `git commit -m 'Add new feature'`
4. Push to the branch: `git push origin feature/new-feature`
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the [Lovable Documentation](https://docs.lovable.dev/)
- Join the [Lovable Discord Community](https://discord.com/channels/1119885301872070706/1280461670979993613)
- Review the GitHub issues for common problems

## 🔄 Version History

- **v1.0.0** - Initial release with core risk management features
- **v1.1.0** - Added incident management and reporting
- **v1.2.0** - Policy management and compliance features
- **v1.3.0** - Enhanced dashboard and analytics

---

Built with ❤️ using [Lovable](https://lovable.dev)
