<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Static Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .info { background-color: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Netlify Static Test</h1>
        <div class="status success">✅ Static HTML is working</div>
        <div class="status info">📍 This file is served directly by Netlify</div>
        
        <h2>Test Results:</h2>
        <ul>
            <li>✅ HTML parsing</li>
            <li>✅ CSS loading</li>
            <li>✅ File serving</li>
            <li id="js-test">❌ JavaScript loading</li>
        </ul>
        
        <h2>Next Steps:</h2>
        <p>If you can see this page at <code>/test.html</code>, then Netlify is working correctly and the issue is with the React app build.</p>
        
        <h2>Debug Info:</h2>
        <p><strong>URL:</strong> <span id="current-url"></span></p>
        <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
        <p><strong>Timestamp:</strong> <span id="timestamp"></span></p>
    </div>

    <script>
        // Test JavaScript execution
        document.getElementById('js-test').innerHTML = '✅ JavaScript loading';
        document.getElementById('js-test').className = 'success';
        
        // Fill debug info
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;
        document.getElementById('timestamp').textContent = new Date().toISOString();
        
    </script>
</body>
</html>
