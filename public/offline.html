<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Risk Compass</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }

        .offline-container {
            max-width: 400px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .offline-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 30px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
        }

        h1 {
            font-size: 28px;
            margin-bottom: 15px;
            font-weight: 600;
        }

        p {
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }

        .retry-button {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .retry-button:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .features {
            margin-top: 40px;
            text-align: left;
        }

        .features h3 {
            font-size: 18px;
            margin-bottom: 15px;
            text-align: center;
        }

        .feature-list {
            list-style: none;
        }

        .feature-list li {
            padding: 8px 0;
            font-size: 14px;
            opacity: 0.8;
        }

        .feature-list li:before {
            content: "✓";
            margin-right: 10px;
            color: #4ade80;
            font-weight: bold;
        }

        .connection-status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 10px;
            font-size: 14px;
        }

        .offline {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .online {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        @media (max-width: 480px) {
            .offline-container {
                padding: 30px 20px;
            }
            
            h1 {
                font-size: 24px;
            }
            
            .offline-icon {
                width: 60px;
                height: 60px;
                font-size: 30px;
            }
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">
            📡
        </div>
        
        <h1>You're Offline</h1>
        <p>Don't worry! Risk Compass works offline too. You can still access your cached data and continue working.</p>
        
        <button class="retry-button" onclick="window.location.reload()">
            Try Again
        </button>
        
        <div class="connection-status offline" id="connectionStatus">
            🔴 No internet connection
        </div>
        
        <div class="features">
            <h3>Available Offline</h3>
            <ul class="feature-list">
                <li>View cached dashboard data</li>
                <li>Browse your risk register</li>
                <li>Review incident reports</li>
                <li>Access saved reports</li>
                <li>Create new entries (synced when online)</li>
            </ul>
        </div>
    </div>

    <script>
        // Monitor connection status
        function updateConnectionStatus() {
            const statusElement = document.getElementById('connectionStatus');
            
            if (navigator.onLine) {
                statusElement.className = 'connection-status online';
                statusElement.innerHTML = '🟢 Connection restored';
                
                // Auto-reload after a short delay when connection is restored
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                statusElement.className = 'connection-status offline';
                statusElement.innerHTML = '🔴 No internet connection';
            }
        }

        // Listen for connection changes
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // Initial status check
        updateConnectionStatus();

        // Periodically check connection
        setInterval(() => {
            // Try to fetch a small resource to verify actual connectivity
            fetch('/manifest.json', { 
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(() => {
                if (!navigator.onLine) {
                    // Connection is actually available, update status
                    navigator.onLine = true;
                    updateConnectionStatus();
                }
            })
            .catch(() => {
                // Connection is not available
                if (navigator.onLine) {
                    navigator.onLine = false;
                    updateConnectionStatus();
                }
            });
        }, 5000);

        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Animate the offline icon
            const icon = document.querySelector('.offline-icon');
            setInterval(() => {
                icon.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    icon.style.transform = 'scale(1)';
                }, 200);
            }, 3000);
        });
    </script>
</body>
</html>
