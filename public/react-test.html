<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React CDN Test</title>
</head>
<body>
    <div id="react-root"></div>
    
    <script>
    </script>
    
    <!-- Load React from CDN -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.production.min.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.production.min.js"></script>
    
    <script>
        
        // Create a simple React component
        const { createElement: h, useState } = React;
        const { createRoot } = ReactDOM;
        
        function TestApp() {
            const [count, setCount] = useState(0);
            
            return h('div', {
                style: {
                    padding: '20px',
                    fontFamily: 'Arial, sans-serif',
                    backgroundColor: '#f0f0f0',
                    minHeight: '100vh',
                    color: '#333'
                }
            }, [
                h('h1', { key: 'title', style: { color: '#2563eb' } }, '🚀 React CDN Test Working!'),
                h('div', {
                    key: 'content',
                    style: {
                        backgroundColor: 'white',
                        padding: '20px',
                        borderRadius: '8px',
                        marginTop: '20px',
                        boxShadow: '0 2px 10px rgba(0,0,0,0.1)'
                    }
                }, [
                    h('h2', { key: 'status' }, 'Status Check:'),
                    h('ul', { key: 'list', style: { listStyle: 'none', padding: 0 } }, [
                        h('li', { key: 'react', style: { padding: '5px 0', color: '#16a34a' } }, '✅ React is working'),
                        h('li', { key: 'dom', style: { padding: '5px 0', color: '#16a34a' } }, '✅ ReactDOM is working'),
                        h('li', { key: 'hooks', style: { padding: '5px 0', color: '#16a34a' } }, '✅ React Hooks are working'),
                        h('li', { key: 'render', style: { padding: '5px 0', color: '#16a34a' } }, '✅ Rendering is working')
                    ]),
                    h('div', { key: 'counter', style: { marginTop: '20px' } }, [
                        h('p', { key: 'count-text' }, `Counter: ${count}`),
                        h('button', {
                            key: 'count-btn',
                            onClick: () => setCount(count + 1),
                            style: {
                                padding: '10px 20px',
                                backgroundColor: '#2563eb',
                                color: 'white',
                                border: 'none',
                                borderRadius: '4px',
                                cursor: 'pointer'
                            }
                        }, 'Click me!')
                    ]),
                    h('div', { key: 'info', style: { marginTop: '20px' } }, [
                        h('h3', { key: 'info-title' }, 'Environment Info:'),
                        h('p', { key: 'url' }, `URL: ${window.location.href}`),
                        h('p', { key: 'time' }, `Timestamp: ${new Date().toISOString()}`),
                        h('p', { key: 'ua' }, `User Agent: ${navigator.userAgent.substring(0, 100)}...`)
                    ])
                ])
            ]);
        }
        
        // Render the app
        try {
            const root = createRoot(document.getElementById('react-root'));
            root.render(h(TestApp));
        } catch (error) {
            document.getElementById('react-root').innerHTML = `
                <div style="padding: 20px; color: red; font-family: Arial;">
                    <h1>React Rendering Failed</h1>
                    <p>Error: ${error.message}</p>
                </div>
            `;
        }
    </script>
</body>
</html>
