// Service Worker for Non-Profit Risk Compass
// Provides offline functionality, caching, and background sync

const CACHE_NAME = "risk-compass-v1";
const STATIC_CACHE_NAME = "risk-compass-static-v1";
const DYNAMIC_CACHE_NAME = "risk-compass-dynamic-v1";

// Assets to cache immediately
const STATIC_ASSETS = [
  "/",
  "/manifest.json",
  "/offline.html",
  // Add critical CSS and JS files here
];

// Routes that should work offline
const OFFLINE_ROUTES = ["/", "/dashboard", "/risks", "/incidents", "/reports", "/profile"];

// API endpoints to cache
const CACHEABLE_APIS = ["/api/risks", "/api/incidents", "/api/dashboard", "/api/profile"];

// Install event - cache static assets
self.addEventListener("install", event => {
  // Only log in development
  if (typeof importScripts !== "undefined" && self.location.hostname === "localhost") {
  }

  event.waitUntil(
    caches
      .open(STATIC_CACHE_NAME)
      .then(cache => {
        if (typeof importScripts !== "undefined" && self.location.hostname === "localhost") {
        }
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        if (typeof importScripts !== "undefined" && self.location.hostname === "localhost") {
        }
        return self.skipWaiting();
      })
      .catch(error => {})
  );
});

// Activate event - clean up old caches
self.addEventListener("activate", event => {
  if (typeof importScripts !== "undefined" && self.location.hostname === "localhost") {
  }

  event.waitUntil(
    caches
      .keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            if (
              cacheName !== STATIC_CACHE_NAME &&
              cacheName !== DYNAMIC_CACHE_NAME &&
              cacheName !== CACHE_NAME
            ) {
              if (typeof importScripts !== "undefined" && self.location.hostname === "localhost") {
              }
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        if (typeof importScripts !== "undefined" && self.location.hostname === "localhost") {
        }
        return self.clients.claim();
      })
  );
});

// Fetch event - handle requests with caching strategies
self.addEventListener("fetch", event => {
  const { request } = event;
  const url = new URL(request.url);

  // Skip non-GET requests
  if (request.method !== "GET") {
    return;
  }

  // Handle different types of requests
  if (url.pathname.startsWith("/api/")) {
    // API requests - Network First with fallback to cache
    event.respondWith(handleApiRequest(request));
  } else if (
    OFFLINE_ROUTES.some(route => url.pathname === route || url.pathname.startsWith(route))
  ) {
    // App routes - Cache First with network fallback
    event.respondWith(handleAppRoute(request));
  } else if (request.destination === "image") {
    // Images - Cache First
    event.respondWith(handleImageRequest(request));
  } else {
    // Other requests - Network First
    event.respondWith(handleOtherRequest(request));
  }
});

// Handle API requests with Network First strategy
async function handleApiRequest(request) {
  const cacheName = DYNAMIC_CACHE_NAME;

  try {
    // Try network first
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      // Cache successful responses
      const cache = await caches.open(cacheName);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    // Only log in development
    if (typeof importScripts !== "undefined" && self.location.hostname === "localhost") {
    }

    // Fallback to cache
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Return offline response for API requests
    return new Response(
      JSON.stringify({
        error: "Offline",
        message: "This data is not available offline",
      }),
      {
        status: 503,
        statusText: "Service Unavailable",
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}

// Handle app routes with Cache First strategy
async function handleAppRoute(request) {
  try {
    // Try cache first
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    // Fallback to network
    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    // Only log in development
    if (typeof importScripts !== "undefined" && self.location.hostname === "localhost") {
    }

    // Return offline page
    const offlineResponse = await caches.match("/offline.html");
    return offlineResponse || new Response("Offline", { status: 503 });
  }
}

// Handle image requests with Cache First strategy
async function handleImageRequest(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }

    const networkResponse = await fetch(request);

    if (networkResponse.ok) {
      const cache = await caches.open(DYNAMIC_CACHE_NAME);
      cache.put(request, networkResponse.clone());
    }

    return networkResponse;
  } catch (error) {
    // Return placeholder image or cached fallback
    return new Response("", { status: 503 });
  }
}

// Handle other requests with Network First strategy
async function handleOtherRequest(request) {
  try {
    const networkResponse = await fetch(request);
    return networkResponse;
  } catch (error) {
    const cachedResponse = await caches.match(request);
    return cachedResponse || new Response("Offline", { status: 503 });
  }
}

// Background sync for offline actions
self.addEventListener("sync", event => {
  // Only log in development
  if (typeof importScripts !== "undefined" && self.location.hostname === "localhost") {
  }

  if (event.tag === "background-sync-risks") {
    event.waitUntil(syncOfflineRisks());
  } else if (event.tag === "background-sync-incidents") {
    event.waitUntil(syncOfflineIncidents());
  }
});

// Sync offline risks when connection is restored
async function syncOfflineRisks() {
  try {
    // Get offline data from IndexedDB or localStorage
    const offlineRisks = await getOfflineData("risks");

    for (const risk of offlineRisks) {
      try {
        await fetch("/api/risks", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(risk),
        });

        // Remove from offline storage after successful sync
        await removeOfflineData("risks", risk.id);
      } catch (error) {}
    }
  } catch (error) {}
}

// Sync offline incidents when connection is restored
async function syncOfflineIncidents() {
  try {
    const offlineIncidents = await getOfflineData("incidents");

    for (const incident of offlineIncidents) {
      try {
        await fetch("/api/incidents", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(incident),
        });

        await removeOfflineData("incidents", incident.id);
      } catch (error) {}
    }
  } catch (error) {}
}

// Helper functions for offline data management
async function getOfflineData(type) {
  // This would typically use IndexedDB
  // For now, return empty array
  return [];
}

async function removeOfflineData(type, id) {
  // Remove item from offline storage
  // Only log in development
  if (typeof importScripts !== "undefined" && self.location.hostname === "localhost") {
  }
}

// Push notification handling
self.addEventListener("push", event => {
  // Only log in development
  if (typeof importScripts !== "undefined" && self.location.hostname === "localhost") {
  }

  const options = {
    body: event.data ? event.data.text() : "New notification",
    icon: "/icons/icon-192x192.png",
    badge: "/icons/badge-72x72.png",
    vibrate: [100, 50, 100],
    data: {
      dateOfArrival: Date.now(),
      primaryKey: 1,
    },
    actions: [
      {
        action: "explore",
        title: "View Details",
        icon: "/icons/checkmark.png",
      },
      {
        action: "close",
        title: "Close",
        icon: "/icons/xmark.png",
      },
    ],
  };

  event.waitUntil(self.registration.showNotification("Risk Compass", options));
});

// Handle notification clicks
self.addEventListener("notificationclick", event => {
  // Only log in development
  if (typeof importScripts !== "undefined" && self.location.hostname === "localhost") {
  }

  event.notification.close();

  if (event.action === "explore") {
    event.waitUntil(clients.openWindow("/dashboard"));
  }
});
