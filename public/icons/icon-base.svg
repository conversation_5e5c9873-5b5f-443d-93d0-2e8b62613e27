<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="256" cy="256" r="240" fill="url(#gradient)" stroke="#1e40af" stroke-width="4"/>
  
  <!-- Compass rose -->
  <g transform="translate(256,256)">
    <!-- Main compass points -->
    <g stroke="#ffffff" stroke-width="3" fill="#ffffff">
      <!-- North -->
      <polygon points="0,-180 -15,-150 15,-150" />
      <!-- South -->
      <polygon points="0,180 -15,150 15,150" />
      <!-- East -->
      <polygon points="180,0 150,-15 150,15" />
      <!-- West -->
      <polygon points="-180,0 -150,-15 -150,15" />
    </g>
    
    <!-- Secondary compass points -->
    <g stroke="#ffffff" stroke-width="2" fill="#ffffff" opacity="0.8">
      <!-- Northeast -->
      <polygon points="127,-127 112,-112 142,-112" />
      <!-- Southeast -->
      <polygon points="127,127 112,112 142,112" />
      <!-- Southwest -->
      <polygon points="-127,127 -112,112 -142,112" />
      <!-- Northwest -->
      <polygon points="-127,-127 -112,-112 -142,-112" />
    </g>
    
    <!-- Center circle -->
    <circle cx="0" cy="0" r="25" fill="#ffffff" stroke="#1e40af" stroke-width="2"/>
    
    <!-- Risk indicator (warning triangle) -->
    <g transform="translate(0,-5)">
      <polygon points="0,-12 -10,8 10,8" fill="#ef4444" stroke="#dc2626" stroke-width="1"/>
      <text x="0" y="4" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" font-weight="bold" fill="#ffffff">!</text>
    </g>
  </g>
  
  <!-- App name -->
  <text x="256" y="450" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="#ffffff">RISK</text>
  <text x="256" y="480" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="normal" fill="#ffffff">COMPASS</text>
</svg>
