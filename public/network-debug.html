<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Network Debug</title>
</head>
<body>
    <div id="debug-info"></div>
    
    <script>
        
        const debugInfo = document.getElementById('debug-info');
        
        function addInfo(title, content) {
            const div = document.createElement('div');
            div.style.cssText = 'margin: 10px 0; padding: 10px; border: 1px solid #ccc; background: #f9f9f9;';
            div.innerHTML = `<strong>${title}:</strong><br><pre style="margin: 5px 0; font-family: monospace;">${content}</pre>`;
            debugInfo.appendChild(div);
        }
        
        // Check what files exist
        addInfo('Current URL', window.location.href);
        addInfo('Base URL', window.location.origin);
        
        // Test different script paths
        const testPaths = [
            '/src/main.tsx',
            '/assets/index.js',
            '/assets/main.js',
            '/dist/assets/index.js'
        ];
        
        addInfo('Testing Script Paths', 'Checking which paths return 200 vs 404...');
        
        testPaths.forEach(path => {
            fetch(path)
                .then(response => {
                    const status = response.status;
                    const statusText = response.statusText;
                    addInfo(`Path: ${path}`, `Status: ${status} ${statusText}`);
                    
                    if (status === 200) {
                        return response.text().then(text => {
                            addInfo(`Content of ${path}`, text.substring(0, 200) + '...');
                        });
                    }
                })
                .catch(error => {
                    addInfo(`Path: ${path}`, `Error: ${error.message}`);
                });
        });
        
        // Check if there are any assets in common locations
        const assetPaths = [
            '/assets/',
            '/dist/',
            '/build/'
        ];
        
        assetPaths.forEach(path => {
            fetch(path)
                .then(response => {
                    addInfo(`Directory: ${path}`, `Status: ${response.status} ${response.statusText}`);
                })
                .catch(error => {
                    addInfo(`Directory: ${path}`, `Error: ${error.message}`);
                });
        });
        
        // List all resources that have loaded
        setTimeout(() => {
            const resources = performance.getEntriesByType('resource');
            const resourceList = resources.map(r => `${r.name} (${r.duration.toFixed(2)}ms)`).join('\n');
            addInfo('Loaded Resources', resourceList || 'None');
        }, 2000);
    </script>
</body>
</html>
