# TypeScript Code Quality Improvement Guide

## 🎯 Overview

This guide outlines the systematic approach to improve TypeScript code quality in the nfp-risk-compass project. We've identified 448 instances of `any` usage and 64 React Hook dependency issues that need addressing.

## ✅ Configuration Improvements Applied

### 1. Strict TypeScript Configuration
- ✅ Enabled `strict: true` in `tsconfig.app.json`
- ✅ Added `noUnusedLocals`, `noUnusedParameters`, and `noImplicitAny`
- ✅ Added `exactOptionalPropertyTypes` and `noUncheckedIndexedAccess` for better type safety

### 2. Enhanced ESLint Rules
- ✅ Added strict TypeScript ESLint rules
- ✅ Configured proper error handling for `any` types
- ✅ Enhanced React Hook dependency checking

### 3. Code Formatting Setup
- ✅ Added Prettier configuration
- ✅ Created formatting and quality scripts

## 🚀 Immediate Actions Required

### Phase 1: Fix Critical Type Issues (Week 1)

Run the automated migration script:
```bash
npm run migrate-types
```

This will automatically fix common patterns:
- `catch (error: any)` → `catch (error: unknown)`
- `error: any` → `error: Error | unknown`
- `data: any` → `data: unknown`
- `event: any` → `event: React.SyntheticEvent`

### Phase 2: Manual Type Refinement (Week 2-3)

#### High Priority Files (Most `any` usage):
1. `src/components/risk/wizard/` - 15 instances
2. `src/components/policy/` - 12 instances  
3. `src/services/` - 45 instances
4. `src/hooks/risk/` - 18 instances
5. `src/utils/` - 35 instances

#### Common Patterns to Fix:

**API Responses:**
```typescript
// ❌ Bad
function fetchData(): Promise<any> { }

// ✅ Good
function fetchData(): Promise<ApiResponse<RiskData>> { }
```

**Error Handling:**
```typescript
// ❌ Bad
} catch (error: any) {
  console.error(error.message);
}

// ✅ Good
} catch (error: unknown) {
  const errorMessage = error instanceof Error ? error.message : 'Unknown error';
  console.error(errorMessage);
}
```

**Event Handlers:**
```typescript
// ❌ Bad
const handleClick = (event: any) => { }

// ✅ Good
const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => { }
```

**Form Data:**
```typescript
// ❌ Bad
interface FormProps {
  onSubmit: (values: any) => void;
}

// ✅ Good
interface FormProps {
  onSubmit: (values: RiskFormData) => void;
}
```

### Phase 3: Fix React Hook Dependencies (Week 3)

Address the 64 ESLint warnings about missing dependencies:

**Common Pattern:**
```typescript
// ❌ Bad
useEffect(() => {
  fetchData();
}, []); // Missing dependency

// ✅ Good
const fetchData = useCallback(async () => {
  // fetch logic
}, [dependency1, dependency2]);

useEffect(() => {
  fetchData();
}, [fetchData]);
```

## 🛠️ Available Commands

### Quality Checks
```bash
# Run all quality checks
npm run quality

# Fix all auto-fixable issues
npm run quality:fix

# Individual checks
npm run type-check       # TypeScript compilation check
npm run lint            # ESLint check
npm run format:check    # Prettier formatting check
```

### Fixing Issues
```bash
# Fix ESLint issues
npm run lint:fix

# Fix formatting issues
npm run format

# Run type migration script
npm run migrate-types
```

## 📊 Progress Tracking

### Current Status (Week 3 Complete)
- ✅ 86 `any` type usage instances (81% reduction from 448) 
- ✅ 12 React Hook dependency warnings (81% reduction from 64)
- ❌ 4 empty interface issues  
- ❌ 8 `var` usage instead of `let`/`const`

### Target Metrics
- ✅ 0 `any` type usage (except legitimate cases)
- ✅ 0 React Hook dependency warnings
- ✅ 100% strict TypeScript compliance
- ✅ Consistent code formatting

## 🎯 Specific File Priorities

### Immediate (High Impact, Low Effort)
1. `src/test/setup.ts` - 8 `var` declarations → `const`
2. `src/components/ui/` - Empty interfaces → Remove or extend properly
3. `src/utils/typeValidation.ts` - Replace regex escapes

### Week 1 (Core Business Logic)
1. `src/components/risk/wizard/hooks/useRiskWizardForm.ts`
2. `src/components/risk/hooks/risk-form/useRiskSubmit.ts` 
3. `src/services/risk/riskCRUDService.ts`
4. `src/repositories/riskRepository.ts`

### Week 2 (UI Components)
1. `src/components/ui/chart.tsx`
2. `src/components/ui/mobile-form.tsx`
3. `src/components/reports/custom/`
4. `src/components/administration/`

### Week 3 (Utilities & Services)
1. `src/utils/errors/` - Error handling utilities
2. `src/services/policy/` - Policy services  
3. `src/hooks/` - All custom hooks
4. `src/contexts/auth/` - Authentication context

## 🔧 Implementation Strategy

### 1. Start with Leaf Nodes
Begin with files that don't import other project files to avoid cascading type errors.

### 2. Use Type Assertion Carefully
When migrating from `any`, sometimes temporary type assertions are needed:
```typescript
// Temporary during migration
const data = response.data as RiskData;

// Better: Add proper types to the API response
const response: ApiResponse<RiskData> = await fetchRisk(id);
```

### 3. Leverage Type Guards
Create type guards for runtime type checking:
```typescript
function isRiskData(data: unknown): data is RiskData {
  return typeof data === 'object' && data !== null && 'id' in data;
}
```

### 4. Incremental Improvement
Don't try to fix everything at once. Focus on one file or component at a time.

## 📚 Resources

### TypeScript Best Practices
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [React TypeScript Cheatsheet](https://react-typescript-cheatsheet.netlify.app/)

### Project-Specific Types
- Use `src/types/api.ts` for API response types
- Reference existing patterns in `src/types/risk.ts`
- Follow the established naming conventions

## 🎉 Success Criteria

The project will be considered "TypeScript ready" when:

1. ✅ No ESLint errors related to TypeScript
2. ✅ `npm run type-check` passes without errors
3. ✅ All React hooks have proper dependencies
4. ✅ Consistent code formatting across the project
5. ✅ Comprehensive type coverage for all business logic

## 🚨 Common Pitfalls

1. **Don't rush** - Gradual improvement is better than breaking changes
2. **Test thoroughly** - Run tests after each significant change
3. **Document decisions** - When `unknown` is used, add comments explaining why
4. **Team alignment** - Ensure all team members understand the new standards

---

## 🎉 Week 2 Completion Summary

**Major Achievements:**
- ✅ **81% `any` reduction**: From 448 to 86 instances
- ✅ **Core types fixed**: validation.ts, ui.ts, policy.ts, auth/types.ts
- ✅ **Utility functions improved**: typeMappers.ts, typeValidation.ts, exportUtils.ts
- ✅ **Library integrations typed**: optimized-imports.ts, library-lazy-loader.ts
- ✅ **Test utilities cleaned**: test-utils.tsx, AuthProvider.test.tsx
- ✅ **Code formatted**: All 537 files consistently formatted with Prettier
- ✅ **Type-safe generics**: Added proper generic constraints and type parameters

**Remaining `any` types (86) are primarily:**
1. **Validation functions** (legitimate `unknown` replacements needed)
2. **Database transformation utilities** (complex type mapping)
3. **Error handling utilities** (framework integration points)
4. **Example/documentation files** (educational purposes)

The remaining instances are mostly in utility functions where `any` serves as an input parameter that gets validated/transformed into proper types.

## 🚀 Week 3 Completion Summary

**Outstanding React Hook Dependency Fixes:**
- ✅ **Fixed 20+ custom hooks** with proper `useCallback` and dependency arrays
- ✅ **Core risk calculation hooks**: useRiskCalculations, useRiskDetails, useRiskHistory
- ✅ **Policy management hooks**: usePolicyManagement, usePolicyRequests  
- ✅ **User management components**: AdminRequestsList, InvitationsList
- ✅ **Filter hooks**: useFilterNames with `useMemo` to stabilize complex dependencies
- ✅ **Component hooks**: InviteCodeInput, IncidentFilters with proper dependency management
- ✅ **Risk wizard steps**: RiskFormStep2, RiskFormStep4 with `onUpdate` dependencies

**Key Techniques Applied:**
- **`useCallback` wrapping**: Wrapped async functions to stabilize references
- **`useMemo` for complex deps**: Used for array/object dependencies that change on each render
- **Proper dependency inclusion**: Added all used variables to dependency arrays
- **Callback stability**: Ensured callback functions are stable across re-renders

**Remaining 12 React Hook warnings** are primarily in:
1. Performance monitoring hooks (non-critical)
2. Mobile optimization hooks (edge cases)
3. Policy version history (complex data fetching)
4. Risk transfer lists (admin-only functionality)

These remaining issues are in less frequently used areas and don't impact core functionality.
