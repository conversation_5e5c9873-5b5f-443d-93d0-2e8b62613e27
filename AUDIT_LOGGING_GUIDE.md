# Audit Logging System Guide

## Overview

The RiskCompass audit logging system provides comprehensive tracking of security-sensitive operations and user actions for compliance and security monitoring purposes. This system implements tamper-resistant logging with configurable retention policies and real-time alerting capabilities.

## Features

### Core Capabilities
- **Comprehensive Event Tracking**: Logs all security-sensitive operations including authentication, user management, risk management, and policy changes
- **Tamper Protection**: Implements integrity checking with cryptographic hashes to detect log tampering
- **Real-time Alerts**: Automatic alerts for critical security events and suspicious activities
- **Configurable Retention**: Flexible retention policies with automatic archival and purging
- **Correlation Tracking**: Links related events across user sessions and system operations
- **Compliance Ready**: Structured logging format suitable for regulatory compliance requirements

### Event Categories

#### Authentication Events
- User login/logout
- Failed login attempts
- Password resets
- Account creation

#### User Management Events
- User creation, updates, and deletion
- Role and permission changes
- Admin access requests and grants

#### Risk Management Events
- Risk creation, updates, and deletion
- Risk status changes
- Risk assessment modifications

#### Policy Management Events
- Policy creation, updates, and deletion
- Policy publication and archival
- Policy document changes

#### Security Events
- Security violations
- Unauthorized access attempts
- Suspicious activity detection

#### System Events
- Configuration changes
- Backup operations
- System maintenance activities

## Configuration

### Default Configuration

```typescript
const DEFAULT_AUDIT_CONFIG: AuditLoggingConfig = {
  enabled: true,
  retentionPolicy: {
    defaultRetentionDays: 90,        // Standard events kept for 3 months
    criticalRetentionDays: 365,      // Critical events kept for 1 year
    archiveAfterDays: 30,            // Archive after 30 days
    purgeAfterDays: 2555,            // Purge after 7 years (compliance)
    compressionEnabled: true,         // Compress archived logs
    encryptionEnabled: true          // Encrypt sensitive log data
  },
  integrityCheckEnabled: true,       // Enable tamper detection
  realTimeAlertsEnabled: true,       // Enable immediate alerts
  batchSize: 50,                     // Events per batch
  flushInterval: 5000,               // Flush every 5 seconds
  remoteStorageEnabled: true,        // Send to remote storage in production
  remoteStorageEndpoint: '/api/audit'
};
```

### Environment-Specific Settings

#### Development Environment
- All events logged to console
- Reduced batch sizes for immediate feedback
- Debug-level logging enabled
- Local storage only

#### Production Environment
- Console logging disabled
- Remote storage enabled
- Encryption and compression enabled
- Real-time alerting active

## Retention and Archival Policies

### Retention Periods

| Event Severity | Retention Period | Archive After | Purge After |
|---------------|------------------|---------------|-------------|
| Low           | 90 days          | 30 days       | 7 years     |
| Medium        | 180 days         | 30 days       | 7 years     |
| High          | 365 days         | 60 days       | 7 years     |
| Critical      | 365 days         | 90 days       | 7 years     |

### Archival Process

1. **Automatic Archival**: Events are automatically moved to archive storage based on age
2. **Compression**: Archived logs are compressed to reduce storage requirements
3. **Encryption**: All archived data is encrypted at rest
4. **Integrity Verification**: Archive integrity is verified during the archival process
5. **Metadata Preservation**: Event metadata is preserved for search and retrieval

### Purge Process

1. **Compliance Check**: Ensures all regulatory requirements are met before purging
2. **Secure Deletion**: Uses cryptographic erasure for secure data destruction
3. **Audit Trail**: Purge operations are themselves logged for accountability
4. **Verification**: Post-purge verification ensures complete data removal

## Integrity Protection

### Hash Generation
Each audit event receives a cryptographic hash that includes:
- Event timestamp and ID
- User and session information
- Event type and details
- Previous event hash (chain integrity)

### Tamper Detection
- Regular integrity checks verify hash chains
- Alerts generated for any detected tampering
- Forensic logging of integrity violations
- Automatic system lockdown for critical violations

### Chain of Custody
- Immutable event ordering
- Cryptographic proof of event sequence
- Non-repudiation of logged actions
- Forensic-grade evidence preservation

## Usage Examples

### Basic Event Logging

```typescript
import { auditLog, AuditEventType } from '@/services/auditLoggingService';

// Log user login
await auditLog.auth(
  AuditEventType.USER_LOGIN,
  userId,
  userEmail,
  { loginMethod: 'email_password' }
);

// Log risk creation
await auditLog.risk(
  AuditEventType.RISK_CREATED,
  userId,
  riskId,
  { title: 'New Security Risk', severity: 'high' }
);

// Log admin action
await auditLog.admin(
  AuditEventType.ADMIN_ACTION_PERFORMED,
  adminId,
  { action: 'bulk_user_delete', count: 5 }
);
```

### Custom Event Logging

```typescript
// Log custom security event
await auditLog.custom(
  AuditEventType.SECURITY_VIOLATION_DETECTED,
  userId,
  'unauthorized_api_access',
  { 
    endpoint: '/api/admin/users',
    method: 'DELETE',
    userRole: 'staff'
  },
  { 
    severity: AuditSeverity.CRITICAL,
    outcome: 'failure'
  }
);
```

### Batch Operations

```typescript
// Service automatically batches events for efficiency
for (const user of users) {
  await auditLog.user(
    AuditEventType.USER_CREATED,
    adminId,
    user.id,
    { name: user.name, role: user.role }
  );
}
// Events are automatically flushed when batch size is reached
```

## Monitoring and Alerting

### Real-time Alerts

Critical events trigger immediate alerts:
- Security violations
- Unauthorized access attempts
- Admin privilege escalations
- System configuration changes
- Failed authentication patterns

### Alert Channels
- System logging (immediate)
- Email notifications (configurable)
- Webhook integrations (for SIEM systems)
- Dashboard notifications (real-time)

### Monitoring Dashboards

The system provides monitoring capabilities for:
- Event volume and trends
- Security incident patterns
- User activity analysis
- System health metrics
- Compliance reporting

## Compliance Features

### Regulatory Compliance
- **SOX**: Financial controls and access logging
- **GDPR**: Data access and processing logs
- **HIPAA**: Healthcare data access tracking
- **PCI DSS**: Payment system security logging
- **ISO 27001**: Information security management

### Audit Reports
- Automated compliance reports
- Custom date range queries
- User activity summaries
- Security incident reports
- Access control reviews

### Data Export
- JSON format for analysis tools
- CSV format for spreadsheet analysis
- Structured logs for SIEM integration
- API access for custom integrations

## Security Considerations

### Data Protection
- Sensitive data is automatically redacted
- Encryption in transit and at rest
- Access controls on audit logs
- Secure key management

### Access Control
- Role-based access to audit logs
- Separation of duties for audit administration
- Multi-factor authentication for audit access
- Regular access reviews

### Incident Response
- Automated incident detection
- Escalation procedures for critical events
- Forensic data preservation
- Chain of custody maintenance

## Performance Optimization

### Batching Strategy
- Configurable batch sizes
- Automatic flush on critical events
- Memory-efficient buffering
- Network optimization for remote storage

### Storage Optimization
- Compression for archived data
- Efficient indexing for searches
- Partitioning by date and severity
- Automated cleanup of expired data

### Query Performance
- Indexed searches on common fields
- Cached frequently accessed data
- Optimized database queries
- Pagination for large result sets

## Troubleshooting

### Common Issues

#### High Memory Usage
- Reduce batch size in configuration
- Increase flush frequency
- Check for network connectivity issues
- Monitor buffer overflow conditions

#### Missing Events
- Verify service is enabled
- Check user permissions
- Validate network connectivity
- Review error logs for failures

#### Integrity Failures
- Check for system clock synchronization
- Verify storage system integrity
- Review access logs for unauthorized changes
- Contact security team immediately

### Diagnostic Tools

```typescript
// Get audit service statistics
const stats = auditLoggingService.getAuditStats();
console.log('Buffered events:', stats.bufferedEvents);
console.log('Total events logged:', stats.totalEventsLogged);
console.log('Integrity checks enabled:', stats.integrityChecksEnabled);

// Force flush for troubleshooting
await auditLoggingService.flush();

// Verify event integrity
const isValid = await auditLoggingService.verifyIntegrity(eventId);
```

## Best Practices

### Event Logging
1. **Log Early**: Capture events as close to the source as possible
2. **Include Context**: Provide sufficient detail for investigation
3. **Sanitize Data**: Remove sensitive information before logging
4. **Use Correlation IDs**: Link related events across systems
5. **Handle Failures**: Gracefully handle logging failures

### Performance
1. **Batch Operations**: Use batching for high-volume events
2. **Async Logging**: Don't block user operations for logging
3. **Monitor Resources**: Watch memory and network usage
4. **Optimize Queries**: Use efficient search patterns
5. **Archive Regularly**: Keep active logs manageable

### Security
1. **Protect Logs**: Secure audit logs from tampering
2. **Monitor Access**: Track who accesses audit data
3. **Regular Reviews**: Periodically review audit configurations
4. **Incident Response**: Have procedures for audit alerts
5. **Compliance**: Ensure regulatory requirements are met

## Integration Guide

### Service Integration

To integrate audit logging into a new service:

1. Import the audit logging service
2. Identify security-sensitive operations
3. Add appropriate audit calls
4. Test with various scenarios
5. Monitor for performance impact

### Custom Event Types

To add new event types:

1. Extend the `AuditEventType` enum
2. Update severity mapping if needed
3. Add convenience methods if appropriate
4. Update documentation
5. Test thoroughly

### Remote Storage Integration

To integrate with external audit systems:

1. Configure remote storage endpoint
2. Implement authentication if required
3. Handle network failures gracefully
4. Monitor delivery success rates
5. Implement retry mechanisms

This audit logging system provides enterprise-grade security monitoring and compliance capabilities while maintaining high performance and reliability standards.