#!/usr/bin/env node

/**
 * Security Audit Script
 * 
 * This script performs comprehensive security validation including:
 * - Console log detection in production builds
 * - XSS vulnerability scanning
 * - CSP header validation
 * - Dependency vulnerability checking
 * - Security best practices audit
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class SecurityAuditor {
  constructor() {
    this.results = {
      consoleLogCheck: { passed: false, issues: [] },
      xssProtection: { passed: false, issues: [] },
      cspValidation: { passed: false, issues: [] },
      dependencyAudit: { passed: false, issues: [] },
      codebaseAudit: { passed: false, issues: [] },
      overallScore: 0
    };
  }

  async runFullAudit() {
    console.log('🔒 Starting Security Audit...\n');

    try {
      await this.checkConsoleLogsInBuild();
      await this.validateXSSProtection();
      await this.validateCSPHeaders();
      await this.auditDependencies();
      await this.auditCodebase();
      
      this.calculateOverallScore();
      this.generateReport();
    } catch (error) {
      console.error('❌ Security audit failed:', error.message);
      process.exit(1);
    }
  }

  async checkConsoleLogsInBuild() {
    console.log('📋 Checking for console logs in production build...');
    
    try {
      // Build the project first
      console.log('Building project for production...');
      execSync('npm run build', { stdio: 'pipe' });
      
      const distPath = path.join(process.cwd(), 'dist');
      if (!fs.existsSync(distPath)) {
        throw new Error('Build directory not found. Run npm run build first.');
      }

      const consoleIssues = [];
      
      // Recursively scan all JS files in dist
      const scanDirectory = (dir) => {
        const files = fs.readdirSync(dir);
        
        files.forEach(file => {
          const filePath = path.join(dir, file);
          const stat = fs.statSync(filePath);
          
          if (stat.isDirectory()) {
            scanDirectory(filePath);
          } else if (file.endsWith('.js')) {
            const content = fs.readFileSync(filePath, 'utf8');
            
            // Check for console statements (excluding console.error for critical errors)
            const consoleRegex = /console\.(log|warn|debug|info|trace)/g;
            const matches = content.match(consoleRegex);
            
            if (matches) {
              consoleIssues.push({
                file: path.relative(process.cwd(), filePath),
                matches: matches
              });
            }
          }
        });
      };

      scanDirectory(distPath);

      if (consoleIssues.length === 0) {
        console.log('✅ No console logs found in production build');
        this.results.consoleLogCheck.passed = true;
      } else {
        console.log('❌ Console logs found in production build:');
        consoleIssues.forEach(issue => {
          console.log(`  - ${issue.file}: ${issue.matches.join(', ')}`);
        });
        this.results.consoleLogCheck.issues = consoleIssues;
      }
    } catch (error) {
      console.log('❌ Console log check failed:', error.message);
      this.results.consoleLogCheck.issues.push(error.message);
    }
  }

  async validateXSSProtection() {
    console.log('\n🛡️  Validating XSS protection...');
    
    const xssIssues = [];
    
    try {
      // Check for dangerouslySetInnerHTML usage
      const srcPath = path.join(process.cwd(), 'src');
      const dangerousHTMLUsage = this.scanForPattern(srcPath, /dangerouslySetInnerHTML/g);
      
      if (dangerousHTMLUsage.length > 0) {
        xssIssues.push({
          type: 'dangerouslySetInnerHTML',
          files: dangerousHTMLUsage
        });
      }

      // Check for unsafe HTML rendering patterns
      const unsafePatterns = [
        { pattern: /innerHTML\s*=\s*[^;]+/g, name: 'innerHTML assignment' },
        { pattern: /outerHTML\s*=\s*[^;]+/g, name: 'outerHTML assignment' },
        { pattern: /document\.write\(/g, name: 'document.write usage' }
      ];

      unsafePatterns.forEach(({ pattern, name }) => {
        const matches = this.scanForPattern(srcPath, pattern);
        if (matches.length > 0) {
          xssIssues.push({
            type: name,
            files: matches
          });
        }
      });

      // Check for SafeHTML component usage
      const safeHTMLUsage = this.scanForPattern(srcPath, /SafeHTML/g);
      
      if (xssIssues.length === 0 && safeHTMLUsage.length > 0) {
        console.log('✅ XSS protection validated - SafeHTML component in use');
        this.results.xssProtection.passed = true;
      } else {
        console.log('❌ XSS protection issues found:');
        xssIssues.forEach(issue => {
          console.log(`  - ${issue.type}: ${issue.files.length} files`);
        });
        this.results.xssProtection.issues = xssIssues;
      }
    } catch (error) {
      console.log('❌ XSS protection validation failed:', error.message);
      this.results.xssProtection.issues.push(error.message);
    }
  }

  async validateCSPHeaders() {
    console.log('\n🔐 Validating CSP headers...');
    
    const cspIssues = [];
    
    try {
      // Check netlify.toml for CSP headers
      const netlifyConfigPath = path.join(process.cwd(), 'netlify.toml');
      const headersPath = path.join(process.cwd(), 'public', '_headers');
      
      let cspFound = false;
      
      if (fs.existsSync(netlifyConfigPath)) {
        const netlifyConfig = fs.readFileSync(netlifyConfigPath, 'utf8');
        if (netlifyConfig.includes('Content-Security-Policy')) {
          cspFound = true;
          console.log('✅ CSP headers found in netlify.toml');
        }
      }
      
      if (fs.existsSync(headersPath)) {
        const headersConfig = fs.readFileSync(headersPath, 'utf8');
        if (headersConfig.includes('Content-Security-Policy')) {
          cspFound = true;
          console.log('✅ CSP headers found in _headers file');
        }
      }

      if (!cspFound) {
        cspIssues.push('No Content-Security-Policy headers found');
      }

      // Check for other security headers
      const requiredHeaders = [
        'X-Content-Type-Options',
        'X-Frame-Options',
        'X-XSS-Protection',
        'Strict-Transport-Security'
      ];

      const missingHeaders = [];
      requiredHeaders.forEach(header => {
        let found = false;
        
        if (fs.existsSync(netlifyConfigPath)) {
          const config = fs.readFileSync(netlifyConfigPath, 'utf8');
          if (config.includes(header)) found = true;
        }
        
        if (fs.existsSync(headersPath)) {
          const config = fs.readFileSync(headersPath, 'utf8');
          if (config.includes(header)) found = true;
        }
        
        if (!found) {
          missingHeaders.push(header);
        }
      });

      if (missingHeaders.length > 0) {
        cspIssues.push(`Missing security headers: ${missingHeaders.join(', ')}`);
      }

      if (cspIssues.length === 0) {
        console.log('✅ CSP and security headers validation passed');
        this.results.cspValidation.passed = true;
      } else {
        console.log('❌ CSP validation issues:');
        cspIssues.forEach(issue => console.log(`  - ${issue}`));
        this.results.cspValidation.issues = cspIssues;
      }
    } catch (error) {
      console.log('❌ CSP validation failed:', error.message);
      this.results.cspValidation.issues.push(error.message);
    }
  }

  async auditDependencies() {
    console.log('\n📦 Auditing dependencies for vulnerabilities...');
    
    try {
      // Run npm audit
      const auditResult = execSync('npm audit --json', { 
        stdio: 'pipe',
        encoding: 'utf8'
      });
      
      const audit = JSON.parse(auditResult);
      
      if (audit.metadata && audit.metadata.vulnerabilities) {
        const vulns = audit.metadata.vulnerabilities;
        const totalVulns = vulns.critical + vulns.high + vulns.moderate + vulns.low;
        
        if (totalVulns === 0) {
          console.log('✅ No dependency vulnerabilities found');
          this.results.dependencyAudit.passed = true;
        } else {
          console.log('❌ Dependency vulnerabilities found:');
          console.log(`  - Critical: ${vulns.critical}`);
          console.log(`  - High: ${vulns.high}`);
          console.log(`  - Moderate: ${vulns.moderate}`);
          console.log(`  - Low: ${vulns.low}`);
          
          this.results.dependencyAudit.issues.push({
            critical: vulns.critical,
            high: vulns.high,
            moderate: vulns.moderate,
            low: vulns.low,
            total: totalVulns
          });
        }
      }
    } catch (error) {
      // npm audit returns non-zero exit code when vulnerabilities are found
      if (error.stdout) {
        try {
          const audit = JSON.parse(error.stdout);
          if (audit.metadata && audit.metadata.vulnerabilities) {
            const vulns = audit.metadata.vulnerabilities;
            const totalVulns = vulns.critical + vulns.high + vulns.moderate + vulns.low;
            
            console.log('❌ Dependency vulnerabilities found:');
            console.log(`  - Critical: ${vulns.critical}`);
            console.log(`  - High: ${vulns.high}`);
            console.log(`  - Moderate: ${vulns.moderate}`);
            console.log(`  - Low: ${vulns.low}`);
            
            this.results.dependencyAudit.issues.push({
              critical: vulns.critical,
              high: vulns.high,
              moderate: vulns.moderate,
              low: vulns.low,
              total: totalVulns
            });
          }
        } catch (parseError) {
          console.log('❌ Dependency audit failed:', error.message);
          this.results.dependencyAudit.issues.push(error.message);
        }
      } else {
        console.log('❌ Dependency audit failed:', error.message);
        this.results.dependencyAudit.issues.push(error.message);
      }
    }
  }

  async auditCodebase() {
    console.log('\n🔍 Auditing codebase for security issues...');
    
    const codebaseIssues = [];
    const srcPath = path.join(process.cwd(), 'src');
    
    try {
      // Check for sensitive data exposure
      const sensitivePatterns = [
        { pattern: /password\s*[:=]\s*['"][^'"]+['"]/gi, name: 'Hardcoded passwords' },
        { pattern: /api[_-]?key\s*[:=]\s*['"][^'"]+['"]/gi, name: 'Hardcoded API keys' },
        { pattern: /secret\s*[:=]\s*['"][^'"]+['"]/gi, name: 'Hardcoded secrets' },
        { pattern: /token\s*[:=]\s*['"][^'"]+['"]/gi, name: 'Hardcoded tokens' }
      ];

      sensitivePatterns.forEach(({ pattern, name }) => {
        const matches = this.scanForPattern(srcPath, pattern);
        if (matches.length > 0) {
          codebaseIssues.push({
            type: name,
            files: matches
          });
        }
      });

      // Check for insecure random number generation
      const insecureRandom = this.scanForPattern(srcPath, /Math\.random\(\)/g);
      if (insecureRandom.length > 0) {
        codebaseIssues.push({
          type: 'Insecure random number generation',
          files: insecureRandom
        });
      }

      // Check for eval usage
      const evalUsage = this.scanForPattern(srcPath, /\beval\s*\(/g);
      if (evalUsage.length > 0) {
        codebaseIssues.push({
          type: 'eval() usage detected',
          files: evalUsage
        });
      }

      if (codebaseIssues.length === 0) {
        console.log('✅ Codebase security audit passed');
        this.results.codebaseAudit.passed = true;
      } else {
        console.log('❌ Codebase security issues found:');
        codebaseIssues.forEach(issue => {
          console.log(`  - ${issue.type}: ${issue.files.length} files`);
        });
        this.results.codebaseAudit.issues = codebaseIssues;
      }
    } catch (error) {
      console.log('❌ Codebase audit failed:', error.message);
      this.results.codebaseAudit.issues.push(error.message);
    }
  }

  scanForPattern(directory, pattern) {
    const matches = [];
    
    const scanDir = (dir) => {
      const files = fs.readdirSync(dir);
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules' && file !== 'test' && file !== '__tests__') {
          scanDir(filePath);
        } else if ((file.endsWith('.ts') || file.endsWith('.tsx') || file.endsWith('.js') || file.endsWith('.jsx')) && 
                   !file.includes('.test.') && !file.includes('.spec.')) {
          const content = fs.readFileSync(filePath, 'utf8');
          const fileMatches = content.match(pattern);
          
          if (fileMatches) {
            // Filter out safe innerHTML usage in SafeHTML component
            if (pattern.toString().includes('innerHTML') && filePath.includes('safe-html.tsx')) {
              return; // Skip safe innerHTML usage
            }
            
            matches.push({
              file: path.relative(process.cwd(), filePath),
              matches: fileMatches
            });
          }
        }
      });
    };

    scanDir(directory);
    return matches;
  }

  calculateOverallScore() {
    const checks = Object.values(this.results).filter(result => 
      typeof result === 'object' && result.hasOwnProperty('passed')
    );
    
    const passedChecks = checks.filter(check => check.passed).length;
    this.results.overallScore = Math.round((passedChecks / checks.length) * 100);
  }

  generateReport() {
    console.log('\n📊 Security Audit Report');
    console.log('========================');
    
    const checks = [
      { name: 'Console Log Removal', result: this.results.consoleLogCheck },
      { name: 'XSS Protection', result: this.results.xssProtection },
      { name: 'CSP Headers', result: this.results.cspValidation },
      { name: 'Dependency Security', result: this.results.dependencyAudit },
      { name: 'Codebase Security', result: this.results.codebaseAudit }
    ];

    checks.forEach(({ name, result }) => {
      const status = result.passed ? '✅ PASS' : '❌ FAIL';
      const issueCount = result.issues.length;
      console.log(`${status} ${name} ${issueCount > 0 ? `(${issueCount} issues)` : ''}`);
    });

    console.log(`\nOverall Security Score: ${this.results.overallScore}%`);
    
    if (this.results.overallScore < 100) {
      console.log('\n⚠️  Security issues detected. Please review and fix the issues above.');
      process.exit(1);
    } else {
      console.log('\n🎉 All security checks passed!');
    }

    // Save detailed report
    const reportPath = path.join(process.cwd(), 'security-audit-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    console.log(`\nDetailed report saved to: ${reportPath}`);
  }
}

// Run the audit if this script is executed directly
if (require.main === module) {
  const auditor = new SecurityAuditor();
  auditor.runFullAudit().catch(error => {
    console.error('Security audit failed:', error);
    process.exit(1);
  });
}

module.exports = SecurityAuditor;