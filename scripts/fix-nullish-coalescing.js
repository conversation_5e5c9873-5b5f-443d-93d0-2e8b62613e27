#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

// Function to recursively get all TypeScript files
function getAllTsFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach((file) => {
    const fullPath = path.join(dirPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      // Skip node_modules and other irrelevant directories
      if (file !== 'node_modules' && file !== '.git' && file !== 'dist' && file !== 'build') {
        arrayOfFiles = getAllTsFiles(fullPath, arrayOfFiles);
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      arrayOfFiles.push(fullPath);
    }
  });

  return arrayOfFiles;
}

// Patterns to fix - more specific patterns that are commonly safe
const patterns = [
  // String or empty string patterns
  {
    pattern: /(\w+|\w+\.\w+|\w+\[\w+\])\s*\|\|\s*('|"|`)[^'"`]*\2/g,
    replacement: '$1 ?? $2$3'
  },
  // Number or default number patterns
  {
    pattern: /(\w+|\w+\.\w+|\w+\[\w+\])\s*\|\|\s*(\d+)/g,
    replacement: '$1 ?? $2'
  },
  // Array or empty array patterns
  {
    pattern: /(\w+|\w+\.\w+|\w+\[\w+\])\s*\|\|\s*\[\]/g,
    replacement: '$1 ?? []'
  },
  // Object or empty object patterns
  {
    pattern: /(\w+|\w+\.\w+|\w+\[\w+\])\s*\|\|\s*\{\}/g,
    replacement: '$1 ?? {}'
  }
];

function fixNullishCoalescing(content) {
  let fixedContent = content;
  let changes = 0;

  patterns.forEach(({ pattern, replacement }) => {
    const newContent = fixedContent.replace(pattern, replacement);
    if (newContent !== fixedContent) {
      changes++;
      fixedContent = newContent;
    }
  });

  return { content: fixedContent, changes };
}

// Main execution
const srcPath = process.argv[2] || './src';
const files = getAllTsFiles(srcPath);

console.log(`Found ${files.length} TypeScript files`);

let totalChanges = 0;
let filesChanged = 0;

files.forEach((filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const { content: fixedContent, changes } = fixNullishCoalescing(content);
    
    if (changes > 0) {
      fs.writeFileSync(filePath, fixedContent);
      filesChanged++;
      totalChanges += changes;
      console.log(`Fixed ${changes} issues in ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
});

console.log(`\nSummary:`);
console.log(`Files changed: ${filesChanged}`);
console.log(`Total changes: ${totalChanges}`);
