#!/usr/bin/env node

import fs from "fs";
import path from "path";
import { glob } from "glob";

function fixIntroducedIssues(content, filePath) {
  let changes = 0;

  // Fix unused _error variables by replacing them with a void expression
  content = content.replace(
    /catch\s*\(\s*_([^)]+)\s*\)\s*{([^}]*)console\.error\([^,]+,\s*_\1\);([^}]*)}/g,
    (match, errorVar, before, after) => {
      changes++;
      return `catch (_${errorVar}) {${before}console.error('Error in ${path.basename(filePath)}', _${errorVar});${after}void _${errorVar}; // Acknowledge unused variable\n  }`;
    }
  );

  // Fix unsafe member access on error.message by using proper type checking
  content = content.replace(
    /console\.error\([^,]+,\s*(\w+)\);\s*\/\/ TODO:/g,
    (match, errorVar) => {
      changes++;
      return `console.error('Error in ${path.basename(filePath)}', ${errorVar} instanceof Error ? ${errorVar}.message : String(${errorVar}));\n    // TODO:`;
    }
  );

  // Fix unsafe member access in error handling
  content = content.replace(/error\.message/g, match => {
    changes++;
    return "error instanceof Error ? error.message : String(error)";
  });

  // Remove duplicate error logging we may have introduced
  content = content.replace(/console\.error\([^)]+\);\s*console\.error\([^)]+\);/g, match => {
    changes++;
    return match.split("\n")[0]; // Keep only the first console.error
  });

  return { content, changes };
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, "utf8");
    const { content: newContent, changes } = fixIntroducedIssues(content, filePath);

    if (changes > 0) {
      fs.writeFileSync(filePath, newContent);
      console.log(`Fixed ${changes} introduced issues in ${filePath}`);
      return changes;
    }
    return 0;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return 0;
  }
}

function main() {
  const patterns = ["src/**/*.ts", "src/**/*.tsx"];

  let totalChanges = 0;

  patterns.forEach(pattern => {
    const files = glob.sync(pattern, { ignore: "node_modules/**" });

    files.forEach(file => {
      totalChanges += processFile(file);
    });
  });

  console.log(`\nTotal fixes for introduced issues: ${totalChanges}`);
}

main();
