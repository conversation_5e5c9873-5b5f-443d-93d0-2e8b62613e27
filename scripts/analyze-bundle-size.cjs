#!/usr/bin/env node

/**
 * Bundle size analysis script for Vite React application
 * Analyzes build output and provides optimization recommendations
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const zlib = require('zlib');

console.log('📊 Bundle Size Analysis\n');

const DIST_PATH = 'dist';
const ASSETS_PATH = path.join(DIST_PATH, 'assets');

// Size thresholds (in KB)
const THRESHOLDS = {
  CHUNK_WARNING: 500,
  CHUNK_ERROR: 1000,
  TOTAL_WARNING: 2000,
  TOTAL_ERROR: 5000,
};

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function getFileSize(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return stats.size;
  } catch (error) {
    return 0;
  }
}

function analyzeAssets() {
  if (!fs.existsSync(ASSETS_PATH)) {
    console.error('❌ Build output not found. Run "npm run build" first.');
    process.exit(1);
  }

  const files = fs.readdirSync(ASSETS_PATH, { recursive: true });
  const jsFiles = [];
  const cssFiles = [];
  const otherFiles = [];

  files.forEach(file => {
    const filePath = path.join(ASSETS_PATH, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isFile()) {
      const size = stats.size;
      const fileInfo = { name: file, size, path: filePath };
      
      if (file.endsWith('.js')) {
        jsFiles.push(fileInfo);
      } else if (file.endsWith('.css')) {
        cssFiles.push(fileInfo);
      } else {
        otherFiles.push(fileInfo);
      }
    }
  });

  return { jsFiles, cssFiles, otherFiles };
}

function categorizeChunks(jsFiles) {
  const categories = {
    vendor: [],
    routes: [],
    components: [],
    utilities: [],
    unknown: []
  };

  jsFiles.forEach(file => {
    const name = file.name.toLowerCase();
    
    if (name.includes('vendor') || name.includes('node_modules')) {
      categories.vendor.push(file);
    } else if (name.includes('page') || name.includes('route')) {
      categories.routes.push(file);
    } else if (name.includes('component')) {
      categories.components.push(file);
    } else if (name.includes('util') || name.includes('lib')) {
      categories.utilities.push(file);
    } else {
      categories.unknown.push(file);
    }
  });

  return categories;
}

function generateReport(assets) {
  const { jsFiles, cssFiles, otherFiles } = assets;
  
  // Sort by size (largest first)
  jsFiles.sort((a, b) => b.size - a.size);
  cssFiles.sort((a, b) => b.size - a.size);
  
  const totalJSSize = jsFiles.reduce((sum, file) => sum + file.size, 0);
  const totalCSSSize = cssFiles.reduce((sum, file) => sum + file.size, 0);
  const totalOtherSize = otherFiles.reduce((sum, file) => sum + file.size, 0);
  const totalSize = totalJSSize + totalCSSSize + totalOtherSize;

  console.log('📦 Bundle Size Summary');
  console.log('='.repeat(50));
  console.log(`Total Bundle Size: ${formatBytes(totalSize)}`);
  console.log(`JavaScript: ${formatBytes(totalJSSize)} (${((totalJSSize/totalSize)*100).toFixed(1)}%)`);
  console.log(`CSS: ${formatBytes(totalCSSSize)} (${((totalCSSSize/totalSize)*100).toFixed(1)}%)`);
  console.log(`Other Assets: ${formatBytes(totalOtherSize)} (${((totalOtherSize/totalSize)*100).toFixed(1)}%)`);
  console.log('');

  // JavaScript files analysis
  console.log('📄 JavaScript Files (Top 10)');
  console.log('-'.repeat(50));
  jsFiles.slice(0, 10).forEach((file, index) => {
    const sizeKB = file.size / 1024;
    const status = sizeKB > THRESHOLDS.CHUNK_ERROR ? '🔴' : 
                   sizeKB > THRESHOLDS.CHUNK_WARNING ? '🟡' : '🟢';
    console.log(`${index + 1}. ${status} ${file.name} - ${formatBytes(file.size)}`);
  });
  console.log('');

  // CSS files analysis
  if (cssFiles.length > 0) {
    console.log('🎨 CSS Files');
    console.log('-'.repeat(50));
    cssFiles.forEach((file, index) => {
      console.log(`${index + 1}. ${file.name} - ${formatBytes(file.size)}`);
    });
    console.log('');
  }

  // Chunk categorization
  const categories = categorizeChunks(jsFiles);
  console.log('📊 Chunk Categories');
  console.log('-'.repeat(50));
  Object.entries(categories).forEach(([category, files]) => {
    if (files.length > 0) {
      const totalSize = files.reduce((sum, file) => sum + file.size, 0);
      console.log(`${category.toUpperCase()}: ${files.length} files, ${formatBytes(totalSize)}`);
      files.forEach(file => {
        console.log(`  - ${file.name} (${formatBytes(file.size)})`);
      });
    }
  });
  console.log('');

  return { totalSize, totalJSSize, jsFiles, categories };
}

function generateOptimizationRecommendations(analysis) {
  const { totalSize, totalJSSize, jsFiles, categories } = analysis;
  
  console.log('💡 Optimization Recommendations');
  console.log('='.repeat(50));

  const recommendations = [];

  // Large bundle warning
  if (totalSize > THRESHOLDS.TOTAL_ERROR * 1024) {
    recommendations.push('🔴 CRITICAL: Total bundle size exceeds 5MB. Consider aggressive code splitting.');
  } else if (totalSize > THRESHOLDS.TOTAL_WARNING * 1024) {
    recommendations.push('🟡 WARNING: Total bundle size exceeds 2MB. Consider optimization.');
  }

  // Large chunk warnings
  const largeChunks = jsFiles.filter(file => file.size > THRESHOLDS.CHUNK_ERROR * 1024);
  if (largeChunks.length > 0) {
    recommendations.push(`🔴 ${largeChunks.length} chunks exceed 1MB. Consider splitting these chunks:`);
    largeChunks.forEach(chunk => {
      recommendations.push(`   - ${chunk.name} (${formatBytes(chunk.size)})`);
    });
  }

  // Vendor chunk analysis
  const vendorSize = categories.vendor.reduce((sum, file) => sum + file.size, 0);
  if (vendorSize > 800 * 1024) {
    recommendations.push('🟡 Vendor chunks are large. Consider splitting by library type.');
  }

  // General recommendations
  recommendations.push('');
  recommendations.push('📋 General Optimization Strategies:');
  recommendations.push('1. Implement dynamic imports for heavy libraries');
  recommendations.push('2. Use tree shaking to eliminate unused code');
  recommendations.push('3. Consider lazy loading for non-critical components');
  recommendations.push('4. Optimize images and use modern formats (WebP, AVIF)');
  recommendations.push('5. Enable gzip/brotli compression on your server');
  recommendations.push('6. Consider using a CDN for vendor libraries');

  recommendations.forEach(rec => console.log(rec));
  console.log('');
}

/**
 * Cross-platform gzip size analysis with proper error handling
 */
function checkGzipSizes() {
  console.log('🗜️  Gzip Size Analysis');
  console.log('-'.repeat(50));

  const platform = process.platform;
  const isWindows = platform === 'win32';
  const gzipInfo = hasSystemGzip();

  // Show which compression method will be used
  console.log(`Platform: ${platform}`);
  console.log(`Compression method: ${gzipInfo.method}`);
  if (!gzipInfo.available) {
    console.log('ℹ️  Using Node.js zlib for cross-platform compatibility');
  }
  console.log('');

  try {
    const { jsFiles } = analyzeAssets();
    const largestFiles = jsFiles.slice(0, 5);

    if (largestFiles.length === 0) {
      console.log('No JavaScript files found for analysis');
      console.log('');
      return;
    }

    console.log('Analyzing compression for largest JavaScript files...');
    console.log('');

    largestFiles.forEach((file, index) => {
      try {
        const gzipSize = calculateGzipSize(file.path, isWindows);
        if (gzipSize !== null) {
          const compressionRatio = ((1 - gzipSize / file.size) * 100).toFixed(1);

          console.log(`${index + 1}. ${file.name}:`);
          console.log(`   Original: ${formatBytes(file.size)}`);
          console.log(`   Gzipped:  ${formatBytes(gzipSize)} (${compressionRatio}% reduction)`);
          console.log('');
        } else {
          console.log(`${index + 1}. ${file.name}: Unable to calculate gzip size`);
          console.log('');
        }
      } catch (error) {
        console.log(`${index + 1}. ${file.name}: Error during compression analysis`);
        if (process.env.DEBUG) {
          console.log(`   Debug: ${error.message}`);
        }
        console.log('');
      }
    });
  } catch (error) {
    console.log('❌ Failed to perform gzip analysis');
    if (process.env.DEBUG) {
      console.log(`Debug: ${error.message}`);
    }
    console.log('');
  }
}

/**
 * Check if gzip analysis can be performed on the current platform
 * Now always returns true since we have Node.js zlib fallback
 */
function canPerformGzipAnalysis() {
  // Always return true since we have Node.js zlib as fallback
  return true;
}

/**
 * Check if system gzip is available (for informational purposes)
 */
function hasSystemGzip() {
  const platform = process.platform;

  try {
    if (platform === 'win32') {
      // On Windows, check for common Unix-like environments
      try {
        // Check for WSL
        execSync('wsl --version', { stdio: 'pipe' });
        return { available: true, method: 'WSL' };
      } catch {
        try {
          // Check for Git Bash or MSYS2
          execSync('where gzip', { stdio: 'pipe' });
          return { available: true, method: 'Git Bash/MSYS2' };
        } catch {
          return { available: false, method: 'Node.js zlib fallback' };
        }
      }
    } else {
      // On Unix-like systems, check for gzip
      execSync('which gzip', { stdio: 'pipe' });
      return { available: true, method: 'system gzip' };
    }
  } catch (error) {
    return { available: false, method: 'Node.js zlib fallback' };
  }
}

/**
 * Calculate gzip size for a file with cross-platform support
 * Falls back to Node.js zlib if system gzip is not available
 */
function calculateGzipSize(filePath, isWindows = false) {
  // First try system gzip commands
  try {
    let gzipCommand;

    if (isWindows) {
      // Try different Windows approaches
      try {
        // Try WSL first
        gzipCommand = `wsl gzip -c "${filePath.replace(/\\/g, '/')}" | wsl wc -c`;
        const result = execSync(gzipCommand, { encoding: 'utf8', stdio: 'pipe' });
        return parseInt(result.trim());
      } catch {
        try {
          // Try Git Bash/MSYS2 style
          gzipCommand = `gzip -c "${filePath}" | wc -c`;
          const result = execSync(gzipCommand, { encoding: 'utf8', stdio: 'pipe' });
          return parseInt(result.trim());
        } catch {
          // Fall back to Node.js zlib
          return calculateGzipSizeWithNodejs(filePath);
        }
      }
    } else {
      // Unix-like systems
      gzipCommand = `gzip -c "${filePath}" | wc -c`;
      const result = execSync(gzipCommand, { encoding: 'utf8', stdio: 'pipe' });
      return parseInt(result.trim());
    }
  } catch (error) {
    // Fall back to Node.js zlib
    return calculateGzipSizeWithNodejs(filePath);
  }
}

/**
 * Calculate gzip size using Node.js zlib (cross-platform fallback)
 */
function calculateGzipSizeWithNodejs(filePath) {
  try {
    const fileContent = fs.readFileSync(filePath);
    const gzippedContent = zlib.gzipSync(fileContent);
    return gzippedContent.length;
  } catch (error) {
    return null;
  }
}

// Main execution
function main() {
  try {
    const assets = analyzeAssets();
    const analysis = generateReport(assets);
    generateOptimizationRecommendations(analysis);
    checkGzipSizes();
    
    console.log('✅ Bundle analysis complete!');
    console.log('💡 Run "npm run build:analyze" to open visual bundle analyzer');
    
  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main, analyzeAssets, generateReport };
