#!/usr/bin/env node

import fs from "fs";
import path from "path";
import { glob } from "glob";

function fixEmptyBlocks(content, filePath) {
  let changes = 0;

  // Pattern to match empty catch blocks
  const emptyCatchPattern = /catch\s*\(\s*([^)]+)\s*\)\s*{\s*}/g;
  const emptyCatchWithCommentPattern = /catch\s*\(\s*([^)]+)\s*\)\s*{\s*(\/\/[^\n]*)\s*}/g;
  content = content.replace(emptyCatchPattern, (match, errorVar) => {
    changes++;
    return `catch (${errorVar}) {\n    console.error('Error in ${path.basename(filePath)}', ${errorVar});\n    // TODO: Handle this error\n  }`;
  });

  // Fix catch blocks with comments only
  content = content.replace(emptyCatchWithCommentPattern, (match, errorVar, comment) => {
    changes++;
    return `catch (${errorVar}) {\n  console.error('Error in ${path.basename(filePath)}', ${errorVar});\n  ${comment}\n  // TODO: Implement proper error handling\n}`;
  });

  // Pattern to match catch blocks with only comments
  const commentOnlyCatchPattern = /catch\s*\(\s*([^)]+)\s*\)\s*{\s*(\/\/[^\n]*\n\s*)*\s*}/g;
  content = content.replace(commentOnlyCatchPattern, (match, errorVar, comments) => {
    changes++;
    const commentLines = comments ? comments.trim() : "";
    return `catch (${errorVar}) {\n    console.error('Error in ${path.basename(filePath)}', ${errorVar});\n    ${commentLines}\n  }`;
  });

  // Pattern to match unused error variables that are defined but never referenced
  const lines = content.split("\n");
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    const catchMatch = line.match(/catch\s*\(\s*([^)]+)\s*\)/);
    if (catchMatch) {
      const errorVar = catchMatch[1].trim();
      // Find the end of this catch block
      let braceCount = 0;
      let blockStart = i;
      let blockEnd = i;

      // Find opening brace
      while (blockStart < lines.length && !lines[blockStart].includes("{")) {
        blockStart++;
      }

      if (blockStart < lines.length) {
        // Count braces to find block end
        for (let j = blockStart; j < lines.length; j++) {
          const currentLine = lines[j];
          braceCount += (currentLine.match(/{/g) || []).length;
          braceCount -= (currentLine.match(/}/g) || []).length;

          if (braceCount === 0) {
            blockEnd = j;
            break;
          }
        }

        // Check if error variable is used in the catch block
        const blockContent = lines.slice(blockStart + 1, blockEnd).join("\n");
        const errorVarUsed = blockContent.includes(errorVar);

        // If error variable is not used, prefix it with underscore
        if (!errorVarUsed && !errorVar.startsWith("_")) {
          lines[i] = line.replace(catchMatch[0], `catch (_${errorVar})`);
          changes++;
        }
      }
    }
  }

  return { content: lines.join("\n"), changes };
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, "utf8");
    const { content: newContent, changes } = fixEmptyBlocks(content, filePath);

    if (changes > 0) {
      fs.writeFileSync(filePath, newContent);
      console.log(`Fixed ${changes} empty blocks in ${filePath}`);
      return changes;
    }
    return 0;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return 0;
  }
}

function main() {
  const patterns = ["src/**/*.ts", "src/**/*.tsx"];

  let totalChanges = 0;

  patterns.forEach(pattern => {
    const files = glob.sync(pattern, { ignore: "node_modules/**" });

    files.forEach(file => {
      totalChanges += processFile(file);
    });
  });

  console.log(`\nTotal changes made: ${totalChanges}`);
}

main();
