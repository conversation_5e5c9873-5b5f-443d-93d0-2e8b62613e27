#!/usr/bin/env node

/**
 * Production Service Worker Build Script
 * 
 * This script processes the service worker file to remove console logs
 * and other development-only code for production builds.
 */

const fs = require('fs');
const path = require('path');

function processServiceWorkerForProduction() {
  const swPath = path.join(process.cwd(), 'public', 'sw.js');
  const distSwPath = path.join(process.cwd(), 'dist', 'sw.js');
  
  if (!fs.existsSync(swPath)) {
    console.error('Service worker not found at:', swPath);
    return;
  }

  let swContent = fs.readFileSync(swPath, 'utf8');
  
  // Remove all console statements except console.error
  swContent = swContent.replace(/console\.(log|warn|debug|info|trace)\([^)]*\);?/g, '');
  
  // Remove development-only conditional blocks
  swContent = swContent.replace(/if \(typeof importScripts[^}]+}/g, '');
  
  // Remove empty lines and clean up formatting
  swContent = swContent.replace(/\n\s*\n\s*\n/g, '\n\n');
  
  // Ensure dist directory exists
  const distDir = path.dirname(distSwPath);
  if (!fs.existsSync(distDir)) {
    fs.mkdirSync(distDir, { recursive: true });
  }
  
  // Write the processed service worker to dist
  fs.writeFileSync(distSwPath, swContent);
  
  console.log('✅ Service worker processed for production');
  console.log(`   Input: ${swPath}`);
  console.log(`   Output: ${distSwPath}`);
}

// Run if this script is executed directly
if (require.main === module) {
  processServiceWorkerForProduction();
}

module.exports = { processServiceWorkerForProduction };