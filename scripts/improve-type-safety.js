#!/usr/bin/env node

/**
 * Automated type safety improvement script
 * Addresses Requirements Document - Requirement 1,2,3: Type safety improvements
 */

import fs from "fs";
import path from "path";
import { promisify } from "util";

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

class TypeSafetyImprover {
  constructor() {
    this.processedFiles = 0;
    this.fixedAnyTypes = 0;
    this.addedTypeGuards = 0;
    this.fixedNullishCoalescing = 0;
    this.dryRun = process.argv.includes("--dry-run");
    this.verbose = process.argv.includes("--verbose");
  }

  /**
   * Replace logical OR with nullish coalescing where appropriate
   */
  fixNullishCoalescing(code) {
    let modifiedCode = code;
    let fixedCount = 0;

    // Pattern for || that should be ?? (avoiding boolean logic)
    const patterns = [
      // Simple variable || default
      /(\w+)\s*\|\|\s*(['"][^'"]*['"]|[\w.]+|null|undefined)/g,
      // Object property || default
      /(\w+\.\w+|\w+\[['"][^'"]*['"]\])\s*\|\|\s*(['"][^'"]*['"]|[\w.]+|null|undefined)/g,
      // Function call || default
      /(\w+\([^)]*\))\s*\|\|\s*(['"][^'"]*['"]|[\w.]+|null|undefined)/g,
    ];

    patterns.forEach(pattern => {
      const matches = modifiedCode.match(pattern);
      if (matches) {
        // Replace || with ?? but be conservative
        const newCode = modifiedCode.replace(pattern, (match, left, right) => {
          // Don't replace if it looks like boolean logic
          if (right === "true" || right === "false" || left.includes("&&") || left.includes("||")) {
            return match;
          }
          fixedCount++;
          return `${left} ?? ${right}`;
        });
        modifiedCode = newCode;
      }
    });

    return { code: modifiedCode, fixedCount };
  }

  /**
   * Add type guards for unsafe member access
   */
  addTypeGuards(code) {
    let modifiedCode = code;
    let addedCount = 0;

    // Pattern for potentially unsafe member access
    const unsafePatterns = [
      // error.message, error.stack, etc.
      /(error|err)\.(\w+)/g,
      // data.property when data might be any
      /(\w*data\w*)\.(\w+)/g,
      // response.field when response might be any
      /(\w*response\w*|\w*result\w*)\.(\w+)/g,
    ];

    unsafePatterns.forEach(pattern => {
      modifiedCode = modifiedCode.replace(pattern, (match, object, property) => {
        // Skip if already has type guard
        if (
          modifiedCode.includes(`${object} instanceof`) ||
          modifiedCode.includes(`typeof ${object}`) ||
          modifiedCode.includes(`${object}?.${property}`)
        ) {
          return match;
        }

        // Add type guard for error objects
        if (object === "error" || object === "err") {
          addedCount++;
          return `${object} instanceof Error ? ${object}.${property} : 'Unknown ${property}'`;
        }

        // Use optional chaining for other cases
        if (property !== "length" && property !== "toString") {
          addedCount++;
          return `${object}?.${property}`;
        }

        return match;
      });
    });

    return { code: modifiedCode, addedCount };
  }

  /**
   * Replace some basic any types with better alternatives
   */
  replaceAnyTypes(code) {
    let modifiedCode = code;
    let replacedCount = 0;

    const replacements = [
      // any[] -> unknown[]
      { pattern: /:\s*any\[\]/g, replacement: ": unknown[]", description: "any[] → unknown[]" },
      // : any -> : unknown (in function parameters)
      {
        pattern: /\(\s*\w+\s*:\s*any\s*\)/g,
        replacement: match => match.replace("any", "unknown"),
        description: "parameter any → unknown",
      },
      // Record<string, any> -> Record<string, unknown>
      {
        pattern: /Record<([^,>]+),\s*any>/g,
        replacement: "Record<$1, unknown>",
        description: "Record<string, any> → Record<string, unknown>",
      },
      // Promise<any> -> Promise<unknown>
      {
        pattern: /Promise<any>/g,
        replacement: "Promise<unknown>",
        description: "Promise<any> → Promise<unknown>",
      },
    ];

    replacements.forEach(({ pattern, replacement, description }) => {
      const matches = modifiedCode.match(pattern);
      if (matches) {
        replacedCount += matches.length;
        modifiedCode = modifiedCode.replace(pattern, replacement);
        if (this.verbose) {
          console.log(`  Applied ${description}: ${matches.length} replacements`);
        }
      }
    });

    return { code: modifiedCode, replacedCount };
  }

  /**
   * Fix empty object types
   */
  fixEmptyObjectTypes(code) {
    let modifiedCode = code;
    let fixedCount = 0;

    // Replace {} with Record<string, unknown> or object
    const emptyObjectPattern = /:\s*\{\s*\}/g;
    const matches = modifiedCode.match(emptyObjectPattern);

    if (matches) {
      fixedCount = matches.length;
      modifiedCode = modifiedCode.replace(emptyObjectPattern, ": Record<string, unknown>");
    }

    return { code: modifiedCode, fixedCount };
  }

  /**
   * Process a single file
   */
  async processFile(filePath) {
    try {
      const originalCode = await readFile(filePath, "utf8");
      let modifiedCode = originalCode;
      let totalChanges = 0;

      // Apply fixes
      const nullishResult = this.fixNullishCoalescing(modifiedCode);
      modifiedCode = nullishResult.code;
      this.fixedNullishCoalescing += nullishResult.fixedCount;
      totalChanges += nullishResult.fixedCount;

      const typeGuardResult = this.addTypeGuards(modifiedCode);
      modifiedCode = typeGuardResult.code;
      this.addedTypeGuards += typeGuardResult.addedCount;
      totalChanges += typeGuardResult.addedCount;

      const anyTypeResult = this.replaceAnyTypes(modifiedCode);
      modifiedCode = anyTypeResult.code;
      this.fixedAnyTypes += anyTypeResult.replacedCount;
      totalChanges += anyTypeResult.replacedCount;

      const emptyObjectResult = this.fixEmptyObjectTypes(modifiedCode);
      modifiedCode = emptyObjectResult.code;
      totalChanges += emptyObjectResult.fixedCount;

      if (totalChanges > 0) {
        if (this.verbose) {
          console.log(`📝 ${filePath}: ${totalChanges} type safety improvement(s)`);
        }

        if (!this.dryRun && modifiedCode !== originalCode) {
          await writeFile(filePath, modifiedCode, "utf8");
        }
      }

      this.processedFiles++;
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  /**
   * Recursively process directory
   */
  async processDirectory(dirPath) {
    try {
      const entries = await readdir(dirPath);

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry);
        const stats = await stat(fullPath);

        if (stats.isDirectory()) {
          // Skip node_modules, .git, and other non-source directories
          if (!["node_modules", ".git", "dist", "build", ".next"].includes(entry)) {
            await this.processDirectory(fullPath);
          }
        } else if (stats.isFile()) {
          // Process TypeScript files
          if (/\.(ts|tsx)$/.test(entry) && !entry.includes(".test.") && !entry.includes(".spec.")) {
            await this.processFile(fullPath);
          }
        }
      }
    } catch (error) {
      console.error(`❌ Error processing directory ${dirPath}:`, error.message);
    }
  }

  /**
   * Run the type safety improvement process
   */
  async run() {
    const startTime = Date.now();

    console.log("🛡️  Starting type safety improvements...");
    console.log(`Mode: ${this.dryRun ? "DRY RUN" : "LIVE"}`);
    console.log("────────────────────────────────────────");

    // Process src directory
    await this.processDirectory("./src");

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log("────────────────────────────────────────");
    console.log(`✅ Type safety improvements complete!`);
    console.log(`📊 Files processed: ${this.processedFiles}`);
    console.log(`🔄 Nullish coalescing fixes: ${this.fixedNullishCoalescing}`);
    console.log(`🛡️  Type guards added: ${this.addedTypeGuards}`);
    console.log(`📝 Any types replaced: ${this.fixedAnyTypes}`);
    console.log(`⏱️  Duration: ${duration}s`);

    const totalFixes = this.fixedNullishCoalescing + this.addedTypeGuards + this.fixedAnyTypes;

    if (this.dryRun && totalFixes > 0) {
      console.log("\n💡 Run without --dry-run to apply changes");
    }

    if (totalFixes > 0) {
      console.log("\n🔧 Next steps:");
      console.log("   1. Run TypeScript compilation: npm run type-check");
      console.log("   2. Run tests: npm test");
      console.log("   3. Run linter: npm run lint");
    }
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  const improver = new TypeSafetyImprover();
  improver.run().catch(console.error);
}

export default TypeSafetyImprover;
