#!/usr/bin/env node

/**
 * TypeScript Error Fix Script
 * Systematically addresses TypeScript errors in batches to prevent circular fixes
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';

// Priority levels for different error types
const ERROR_PRIORITIES = {
  // High Priority - Type Safety
  '@typescript-eslint/no-explicit-any': 1,
  '@typescript-eslint/no-unsafe-assignment': 1,
  '@typescript-eslint/no-unsafe-member-access': 1,
  '@typescript-eslint/no-unsafe-call': 1,
  '@typescript-eslint/no-unsafe-return': 1,
  '@typescript-eslint/no-unused-vars': 1,
  
  // Medium Priority - Code Quality
  '@typescript-eslint/prefer-nullish-coalescing': 2,
  '@typescript-eslint/prefer-optional-chain': 2,
  'react-hooks/exhaustive-deps': 2,
  '@typescript-eslint/no-empty-object-type': 2,
  
  // Low Priority - Style
  'react-refresh/only-export-components': 3,
  '@typescript-eslint/no-non-null-assertion': 3,
};

function runLintAndParseErrors() {
  try {
    // Run ESLint and capture output
    execSync('npm run lint', { stdio: 'pipe' });
    return [];
  } catch (error) {
    const output = error.stdout.toString();
    return parseESLintOutput(output);
  }
}

function parseESLintOutput(output) {
  const errors = [];
  const lines = output.split('\n');
  let currentFile = '';
  
  for (const line of lines) {
    // Detect file path
    if (line.startsWith('/Users/') && line.endsWith('.tsx') || line.endsWith('.ts')) {
      currentFile = line;
    }
    
    // Parse error line
    const errorMatch = line.match(/^\s*(\d+):(\d+)\s+(error|warning)\s+(.+?)\s+(.+)$/);
    if (errorMatch && currentFile) {
      const [, lineNum, colNum, severity, message, ruleId] = errorMatch;
      errors.push({
        file: currentFile,
        line: parseInt(lineNum),
        column: parseInt(colNum),
        severity,
        message: message.trim(),
        ruleId: ruleId.trim(),
        priority: ERROR_PRIORITIES[ruleId.trim()] || 4
      });
    }
  }
  
  return errors;
}

function categorizeErrors(errors) {
  const categories = {
    high: errors.filter(e => e.priority === 1),
    medium: errors.filter(e => e.priority === 2),
    low: errors.filter(e => e.priority === 3),
    other: errors.filter(e => e.priority === 4)
  };
  
  return categories;
}

function generateReport(categories) {
  console.log('\\n=== TypeScript Error Analysis ===\\n');
  
  console.log(`🔴 High Priority (Type Safety): ${categories.high.length} errors`);
  console.log(`🟡 Medium Priority (Code Quality): ${categories.medium.length} errors`);
  console.log(`🟢 Low Priority (Style): ${categories.low.length} errors`);
  console.log(`⚪ Other: ${categories.other.length} errors`);
  
  console.log('\\n=== High Priority Breakdown ===');
  const highPriorityByRule = {};
  categories.high.forEach(error => {
    highPriorityByRule[error.ruleId] = (highPriorityByRule[error.ruleId] || 0) + 1;
  });
  
  Object.entries(highPriorityByRule)
    .sort(([,a], [,b]) => b - a)
    .forEach(([rule, count]) => {
      console.log(`  ${rule}: ${count} errors`);
    });
    
  console.log('\\n=== Recommended Next Steps ===');
  console.log('1. Fix high priority errors first (type safety)');
  console.log('2. Address medium priority errors (code quality)');
  console.log('3. Clean up low priority issues (style)');
  console.log('\\nStart with the most frequent high priority errors.');
}

function main() {
  console.log('Analyzing TypeScript errors...');
  const errors = runLintAndParseErrors();
  const categories = categorizeErrors(errors);
  generateReport(categories);
  
  // Save detailed report to file
  fs.writeFileSync(
    'typescript-error-report.json',
    JSON.stringify({ categories, summary: {
      total: errors.length,
      high: categories.high.length,
      medium: categories.medium.length,
      low: categories.low.length,
      other: categories.other.length
    }}, null, 2)
  );
  
  console.log('\\nDetailed report saved to typescript-error-report.json');
}

// Run if this is the main module
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { runLintAndParseErrors, categorizeErrors, generateReport };
