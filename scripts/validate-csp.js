#!/usr/bin/env node

/**
 * CSP Configuration Validation Script
 * 
 * This script validates the CSP configuration in netlify.toml and public/_headers
 * to ensure they are properly configured and consistent.
 */

import fs from 'fs';
import path from 'path';

const REQUIRED_DIRECTIVES = [
  'default-src',
  'script-src',
  'style-src',
  'img-src',
  'connect-src',
  'font-src',
  'object-src',
  'base-uri',
  'form-action',
  'frame-ancestors'
];

const SECURITY_IMPROVEMENTS = [
  { directive: 'unsafe-eval', shouldBeAbsent: true, message: 'unsafe-eval should be removed for better security' },
  { directive: 'frame-ancestors \'none\'', shouldBePresent: true, message: 'frame-ancestors should be set to none for clickjacking protection' },
  { directive: 'object-src \'none\'', shouldBePresent: true, message: 'object-src should be set to none to block plugins' },
  { directive: 'upgrade-insecure-requests', shouldBePresent: true, message: 'upgrade-insecure-requests should be present' },
  { directive: 'block-all-mixed-content', shouldBePresent: true, message: 'block-all-mixed-content should be present' }
];

function parseCSPFromFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    // For netlify.toml format
    if (filePath.includes('netlify.toml')) {
      const cspMatch = content.match(/Content-Security-Policy\s*=\s*"([^"]+)"/);
      return cspMatch ? cspMatch[1].trim() : null;
    }
    
    // For _headers format
    if (filePath.includes('_headers')) {
      const cspMatch = content.match(/Content-Security-Policy:\s*(.+)/);
      return cspMatch ? cspMatch[1].trim() : null;
    }
    
    return null;
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error.message);
    return null;
  }
}

function validateCSP(csp, source) {
  const results = [];
  
  console.log(`\n🔍 Validating CSP from ${source}:`);
  console.log(`CSP: ${csp.substring(0, 100)}...`);
  
  // Check required directives
  REQUIRED_DIRECTIVES.forEach(directive => {
    const hasDirective = csp.includes(directive);
    results.push({
      test: `Has ${directive} directive`,
      passed: hasDirective,
      message: hasDirective ? `✅ ${directive} directive present` : `❌ Missing ${directive} directive`
    });
  });
  
  // Check security improvements
  SECURITY_IMPROVEMENTS.forEach(({ directive, shouldBePresent, shouldBeAbsent, message }) => {
    const hasDirective = csp.includes(directive);
    
    if (shouldBePresent) {
      results.push({
        test: `Security: ${directive}`,
        passed: hasDirective,
        message: hasDirective ? `✅ ${message}` : `❌ ${message}`
      });
    }
    
    if (shouldBeAbsent) {
      results.push({
        test: `Security: No ${directive}`,
        passed: !hasDirective,
        message: !hasDirective ? `✅ ${message}` : `❌ ${message}`
      });
    }
  });
  
  return results;
}

function compareCSPs(csp1, csp2) {
  const normalized1 = csp1.replace(/\s+/g, ' ').trim();
  const normalized2 = csp2.replace(/\s+/g, ' ').trim();
  
  return {
    identical: normalized1 === normalized2,
    csp1: normalized1,
    csp2: normalized2
  };
}

function main() {
  console.log('🔒 CSP Configuration Validation');
  console.log('================================');
  
  const netlifyPath = path.join(process.cwd(), 'netlify.toml');
  const headersPath = path.join(process.cwd(), 'public/_headers');
  
  // Parse CSP from both files
  const netlifyCSP = parseCSPFromFile(netlifyPath);
  const headersCSP = parseCSPFromFile(headersPath);
  
  if (!netlifyCSP) {
    console.error('❌ Could not parse CSP from netlify.toml');
    process.exit(1);
  }
  
  if (!headersCSP) {
    console.error('❌ Could not parse CSP from public/_headers');
    process.exit(1);
  }
  
  // Validate each CSP
  const netlifyResults = validateCSP(netlifyCSP, 'netlify.toml');
  const headersResults = validateCSP(headersCSP, 'public/_headers');
  
  // Check consistency
  const comparison = compareCSPs(netlifyCSP, headersCSP);
  
  console.log('\n🔄 Consistency Check:');
  if (comparison.identical) {
    console.log('✅ CSP configurations are identical');
  } else {
    console.log('❌ CSP configurations differ');
    console.log('Netlify CSP:', comparison.csp1.substring(0, 200) + '...');
    console.log('Headers CSP:', comparison.csp2.substring(0, 200) + '...');
  }
  
  // Summary
  const allResults = [...netlifyResults, ...headersResults];
  const passedTests = allResults.filter(r => r.passed).length;
  const totalTests = allResults.length;
  
  console.log('\n📊 Summary:');
  console.log(`Tests passed: ${passedTests}/${totalTests}`);
  console.log(`Consistency: ${comparison.identical ? 'PASS' : 'FAIL'}`);
  
  // Print failed tests
  const failedTests = allResults.filter(r => !r.passed);
  if (failedTests.length > 0) {
    console.log('\n❌ Failed Tests:');
    failedTests.forEach(test => {
      console.log(`  - ${test.message}`);
    });
  }
  
  // Overall result
  const overallPassed = failedTests.length === 0 && comparison.identical;
  console.log(`\n🎯 Overall Result: ${overallPassed ? '✅ PASS' : '❌ FAIL'}`);
  
  if (!overallPassed) {
    process.exit(1);
  }
}

main();