#!/usr/bin/env python3

import os
import re
import glob
import subprocess

def get_unused_vars():
    """Get unused variables from ESLint output."""
    try:
        result = subprocess.run(['npm', 'run', 'lint'], capture_output=True, text=True, cwd='.')
        output = result.stdout + result.stderr
        
        unused_vars = []
        lines = output.split('\n')
        
        for i, line in enumerate(lines):
            if '@typescript-eslint/no-unused-vars' in line:
                # Extract variable name from the error message
                if "''" in line:
                    # Pattern: 'variableName' is defined but never used
                    match = re.search(r"'([^']+)'.*is.*but never used", line)
                    if match:
                        var_name = match.group(1)
                        # Get file path from the line above or context
                        file_path = None
                        for j in range(max(0, i-5), i):
                            if lines[j].startswith('/Users/') and ('.tsx' in lines[j] or '.ts' in lines[j]):
                                file_path = lines[j].strip()
                                break
                        
                        if file_path and var_name:
                            # Extract line number
                            line_match = re.search(r'(\d+):\d+\s+error', line)
                            line_num = int(line_match.group(1)) if line_match else None
                            
                            unused_vars.append({
                                'file': file_path,
                                'var': var_name,
                                'line': line_num
                            })
        
        return unused_vars
    except Exception as e:
        print(f"Error getting unused vars: {e}")
        return []

def fix_unused_variable(file_path, var_name, line_num=None):
    """Fix an unused variable by prefixing with underscore."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern 1: Import that's unused - remove from import
        content = re.sub(
            rf'\b{re.escape(var_name)},?\s*',
            '',
            content
        )
        
        # Pattern 2: Variable declaration - prefix with underscore
        content = re.sub(
            rf'\b(const|let|var)\s+{re.escape(var_name)}\b',
            rf'\1 _{var_name}',
            content
        )
        
        # Pattern 3: Function parameter - prefix with underscore
        content = re.sub(
            rf'\b{re.escape(var_name)}\s*:',
            f'_{var_name}:',
            content
        )
        
        # Pattern 4: Destructuring assignment
        content = re.sub(
            rf'\{([^}]*\b){re.escape(var_name)}\b([^}]*)\}',
            rf'{{\1_{var_name}\2}}',
            content
        )
        
        # Clean up any double commas or trailing commas in imports
        content = re.sub(r',\s*,', ',', content)
        content = re.sub(r'{\s*,', '{', content)
        content = re.sub(r',\s*}', '}', content)
        content = re.sub(r'import\s*{\s*}\s*from[^;]*;?\s*\n', '', content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
    
    except Exception as e:
        print(f"Error fixing {var_name} in {file_path}: {e}")
        return False

def main():
    """Fix unused variables."""
    print("Getting unused variables from ESLint...")
    unused_vars = get_unused_vars()
    
    if not unused_vars:
        print("No unused variables found or error getting them.")
        return
    
    print(f"Found {len(unused_vars)} unused variables to fix...")
    
    fixed_count = 0
    files_changed = set()
    
    for var_info in unused_vars:
        file_path = var_info['file']
        var_name = var_info['var']
        
        if fix_unused_variable(file_path, var_name):
            fixed_count += 1
            files_changed.add(file_path)
            print(f"Fixed unused variable '{var_name}' in {os.path.basename(file_path)}")
    
    print(f"\nSummary:")
    print(f"- Unused variables fixed: {fixed_count}")
    print(f"- Files modified: {len(files_changed)}")

if __name__ == "__main__":
    main()
