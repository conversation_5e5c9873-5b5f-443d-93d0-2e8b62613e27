#!/usr/bin/env python3

import os
import re
import glob

def fix_complex_nullish_patterns(content):
    """Fix more complex nullish coalescing patterns."""
    
    # Pattern 1: Complex property access with ||
    # someObject?.property || 'default' -> someObject?.property ?? 'default'
    content = re.sub(
        r'(\w+\?\.\w+(?:\?\.\w+)*)\s*\|\|\s*(\'[^\']*\'|\"[^\"]*\"|\d+|\w+)',
        r'\1 ?? \2',
        content
    )
    
    # Pattern 2: Array access with ||
    # array?.[0] || 'default' -> array?.[0] ?? 'default'
    content = re.sub(
        r'(\w+\?\.\[\d+\])\s*\|\|\s*(\'[^\']*\'|\"[^\"]*\"|\d+|\w+)',
        r'\1 ?? \2',
        content
    )
    
    # Pattern 3: Function calls with ||
    # someFunc() || 'default' -> someFunc() ?? 'default'
    content = re.sub(
        r'(\w+\(\))\s*\|\|\s*(\'[^\']*\'|\"[^\"]*\"|\d+|\w+)',
        r'\1 ?? \2',
        content
    )
    
    # Pattern 4: Nested property access
    # data?.user?.name || 'Unknown' -> data?.user?.name ?? 'Unknown'
    content = re.sub(
        r'(\w+(?:\?\.\w+)+)\s*\|\|\s*(\'[^\']*\'|\"[^\"]*\"|\d+|\w+)',
        r'\1 ?? \2',
        content
    )
    
    # Pattern 5: Variable with || followed by literal
    # variable || 'default' -> variable ?? 'default'
    # But be careful not to match boolean expressions
    content = re.sub(
        r'\b(\w+)\s*\|\|\s*(\'[^\']*\'|\"[^\"]*\"|\d+|null|undefined)\b',
        r'\1 ?? \2',
        content
    )
    
    # Pattern 6: Array methods with ||
    # array?.length || 0 -> array?.length ?? 0
    content = re.sub(
        r'(\w+\?\.\w+)\s*\|\|\s*(\d+)',
        r'\1 ?? \2',
        content
    )
    
    return content

def fix_optional_chaining_patterns(content):
    """Fix optional chaining patterns."""
    
    # Pattern: obj && obj.prop -> obj?.prop
    # Only for simple cases to avoid breaking logic
    content = re.sub(
        r'\b(\w+)\s*&&\s*\1\.(\w+)\b',
        r'\1?.\2',
        content
    )
    
    return content

def fix_react_hook_deps(content, file_path):
    """Fix React hook dependency issues."""
    
    # This is complex and file-specific, so we'll handle manually
    # For now, just document patterns that need manual fixing
    return content

def fix_typescript_file(file_path):
    """Apply TypeScript fixes to a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Apply fixes
        content = fix_complex_nullish_patterns(content)
        content = fix_optional_chaining_patterns(content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed: {file_path}")
            return True
        
        return False
    
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Apply fixes to all TypeScript files."""
    # Find all TypeScript files
    ts_files = glob.glob('src/**/*.ts', recursive=True) + glob.glob('src/**/*.tsx', recursive=True)
    
    # Filter to files that likely have remaining issues
    target_files = []
    for file_path in ts_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if '||' in content:  # Files that might have remaining nullish coalescing issues
                    target_files.append(file_path)
        except Exception:
            continue
    
    fixed_count = 0
    total_count = len(target_files)
    
    print(f"Processing {total_count} TypeScript files with potential nullish coalescing issues...")
    
    for file_path in target_files:
        if fix_typescript_file(file_path):
            fixed_count += 1
    
    print(f"\nSummary:")
    print(f"- Files processed: {total_count}")
    print(f"- Files modified: {fixed_count}")
    print(f"- Files unchanged: {total_count - fixed_count}")

if __name__ == "__main__":
    main()
