#!/usr/bin/env node

/**
 * Performance Validation and Benchmarking Script
 * Validates 30% bundle size reduction, FCP under 1.5s, memory usage, and network conditions
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🎯 Performance Validation and Benchmarking\n');

const DIST_PATH = 'dist';
const PERFORMANCE_HISTORY_FILE = 'performance-history.json';
const BUNDLE_HISTORY_FILE = 'bundle-size-history.json';
const VALIDATION_REPORT_FILE = 'performance-validation-report.json';

// Performance targets from requirements
const PERFORMANCE_TARGETS = {
  bundleSizeReduction: 0.30, // 30% reduction target
  firstContentfulPaint: 1500, // 1.5 seconds
  largestContentfulPaint: 2500, // 2.5 seconds
  timeToInteractive: 3500, // 3.5 seconds
  cumulativeLayoutShift: 0.1, // <PERSON><PERSON> score
  memoryUsageThreshold: 50 * 1024 * 1024, // 50MB
  networkConditions: [
    { name: 'Fast 3G', rtt: 150, throughput: 1600 },
    { name: 'Slow 3G', rtt: 300, throughput: 400 },
    { name: 'Regular 4G', rtt: 40, throughput: 10000 },
  ]
};

// Baseline bundle sizes (before optimization)
const BASELINE_BUNDLE_SIZES = {
  // These would be the original sizes before optimization
  totalJS: 2.5 * 1024 * 1024, // 2.5MB baseline
  totalCSS: 300 * 1024, // 300KB baseline
  total: 2.8 * 1024 * 1024, // 2.8MB total baseline
};

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatTime(ms) {
  if (ms < 1000) return `${ms.toFixed(0)}ms`;
  return `${(ms / 1000).toFixed(2)}s`;
}

function formatPercentage(value) {
  return `${(value * 100).toFixed(1)}%`;
}

function getCurrentBundleMetrics() {
  if (!fs.existsSync(DIST_PATH)) {
    throw new Error('Build output not found. Run "npm run build" first.');
  }

  const assetsPath = path.join(DIST_PATH, 'assets');
  if (!fs.existsSync(assetsPath)) {
    throw new Error('Assets directory not found in build output.');
  }

  const files = fs.readdirSync(assetsPath, { recursive: true });
  const jsFiles = [];
  const cssFiles = [];

  files.forEach(file => {
    const filePath = path.join(assetsPath, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isFile()) {
      const size = stats.size;
      const fileInfo = { name: file, size, path: filePath };
      
      if (file.endsWith('.js')) {
        jsFiles.push(fileInfo);
      } else if (file.endsWith('.css')) {
        cssFiles.push(fileInfo);
      }
    }
  });

  const totalJSSize = jsFiles.reduce((sum, file) => sum + file.size, 0);
  const totalCSSSize = cssFiles.reduce((sum, file) => sum + file.size, 0);
  const totalBundleSize = totalJSSize + totalCSSSize;

  return {
    jsFiles: jsFiles.sort((a, b) => b.size - a.size),
    cssFiles: cssFiles.sort((a, b) => b.size - a.size),
    totalJSSize,
    totalCSSSize,
    totalBundleSize,
    largestChunk: jsFiles[0]?.size || 0,
  };
}

function validateBundleSizeReduction(currentMetrics) {
  console.log('📦 Bundle Size Reduction Validation');
  console.log('='.repeat(50));

  // Get baseline from bundle history or use default
  let baseline = BASELINE_BUNDLE_SIZES;
  
  try {
    if (fs.existsSync(BUNDLE_HISTORY_FILE)) {
      const history = JSON.parse(fs.readFileSync(BUNDLE_HISTORY_FILE, 'utf8'));
      if (history.length > 0) {
        // Use the first entry as baseline (oldest)
        const firstEntry = history[0];
        baseline = {
          totalJS: firstEntry.totalJS || BASELINE_BUNDLE_SIZES.totalJS,
          totalCSS: firstEntry.totalCSS || BASELINE_BUNDLE_SIZES.totalCSS,
          total: firstEntry.total || BASELINE_BUNDLE_SIZES.total,
        };
      }
    }
  } catch (error) {
    console.warn('⚠️ Could not load bundle history, using default baseline');
  }

  const jsReduction = (baseline.totalJS - currentMetrics.totalJSSize) / baseline.totalJS;
  const cssReduction = (baseline.totalCSS - currentMetrics.totalCSSSize) / baseline.totalCSS;
  const totalReduction = (baseline.total - currentMetrics.totalBundleSize) / baseline.total;

  console.log(`Baseline Bundle Size: ${formatBytes(baseline.total)}`);
  console.log(`Current Bundle Size: ${formatBytes(currentMetrics.totalBundleSize)}`);
  console.log(`Total Reduction: ${formatBytes(baseline.total - currentMetrics.totalBundleSize)} (${formatPercentage(totalReduction)})`);
  console.log('');

  console.log('Breakdown:');
  console.log(`  JavaScript: ${formatBytes(baseline.totalJS)} → ${formatBytes(currentMetrics.totalJSSize)} (${formatPercentage(jsReduction)} reduction)`);
  console.log(`  CSS: ${formatBytes(baseline.totalCSS)} → ${formatBytes(currentMetrics.totalCSSSize)} (${formatPercentage(cssReduction)} reduction)`);
  console.log('');

  const targetReduction = PERFORMANCE_TARGETS.bundleSizeReduction;
  const reductionMet = totalReduction >= targetReduction;

  if (reductionMet) {
    console.log(`✅ Bundle size reduction target MET: ${formatPercentage(totalReduction)} >= ${formatPercentage(targetReduction)}`);
  } else {
    console.log(`❌ Bundle size reduction target NOT MET: ${formatPercentage(totalReduction)} < ${formatPercentage(targetReduction)}`);
    console.log(`   Need additional ${formatBytes((targetReduction - totalReduction) * baseline.total)} reduction`);
  }

  console.log('');

  return {
    baseline,
    current: currentMetrics.totalBundleSize,
    reduction: totalReduction,
    targetMet: reductionMet,
    details: {
      jsReduction,
      cssReduction,
      totalReduction,
    }
  };
}

async function runLighthousePerformanceTest(networkCondition = null) {
  console.log(`🔍 Running Lighthouse Performance Test${networkCondition ? ` (${networkCondition.name})` : ''}...`);
  
  try {
    // Create Lighthouse config
    const configPath = createLighthouseConfig(networkCondition);
    
    // Check if lighthouse is available
    try {
      execSync('npx lighthouse --version', { stdio: 'pipe' });
    } catch (error) {
      console.warn('⚠️ Lighthouse not available, skipping web vitals analysis');
      return null;
    }

    // Start a local server for testing
    const serverProcess = startLocalServer();
    
    try {
      // Wait for server to start
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Run Lighthouse
      const outputFile = `lighthouse-report${networkCondition ? `-${networkCondition.name.replace(/\s+/g, '-').toLowerCase()}` : ''}.json`;
      const lighthouseCommand = `npx lighthouse http://localhost:8080 --config-path=${configPath} --output=json --output-path=${outputFile} --chrome-flags="--headless --no-sandbox --disable-dev-shm-usage"`;
      
      execSync(lighthouseCommand, { stdio: 'pipe' });
      
      if (fs.existsSync(outputFile)) {
        const report = JSON.parse(fs.readFileSync(outputFile, 'utf8'));
        
        return {
          networkCondition: networkCondition?.name || 'Default',
          firstContentfulPaint: report.audits['first-contentful-paint']?.numericValue || null,
          largestContentfulPaint: report.audits['largest-contentful-paint']?.numericValue || null,
          cumulativeLayoutShift: report.audits['cumulative-layout-shift']?.numericValue || null,
          timeToInteractive: report.audits['interactive']?.numericValue || null,
          performanceScore: report.categories.performance?.score * 100 || null,
          totalBlockingTime: report.audits['total-blocking-time']?.numericValue || null,
          speedIndex: report.audits['speed-index']?.numericValue || null,
        };
      }
    } finally {
      // Clean up server
      if (serverProcess) {
        try {
          process.kill(serverProcess.pid);
        } catch (error) {
          // Server might already be stopped
        }
      }
    }
  } catch (error) {
    console.warn(`⚠️ Lighthouse analysis failed for ${networkCondition?.name || 'default'}: ${error.message}`);
    return null;
  }
  
  return null;
}

function createLighthouseConfig(networkCondition = null) {
  const throttling = networkCondition ? {
    rttMs: networkCondition.rtt,
    throughputKbps: networkCondition.throughput,
    cpuSlowdownMultiplier: 1,
    requestLatencyMs: 0,
    downloadThroughputKbps: 0,
    uploadThroughputKbps: 0,
  } : {
    rttMs: 40,
    throughputKbps: 10240,
    cpuSlowdownMultiplier: 1,
    requestLatencyMs: 0,
    downloadThroughputKbps: 0,
    uploadThroughputKbps: 0,
  };

  const config = `
module.exports = {
  extends: 'lighthouse:default',
  settings: {
    onlyAudits: [
      'first-contentful-paint',
      'largest-contentful-paint',
      'cumulative-layout-shift',
      'interactive',
      'total-blocking-time',
      'speed-index',
    ],
    throttling: ${JSON.stringify(throttling, null, 6)},
    screenEmulation: {
      mobile: false,
      width: 1350,
      height: 940,
      deviceScaleFactor: 1,
      disabled: false,
    },
    emulatedUserAgent: false,
  },
};
`;
  
  const configFile = `lighthouse-config${networkCondition ? `-${networkCondition.name.replace(/\s+/g, '-').toLowerCase()}` : ''}.js`;
  fs.writeFileSync(configFile, config);
  return configFile;
}

function startLocalServer() {
  try {
    // Try to start a simple HTTP server
    const serverCommand = 'npx serve dist -l 8080 -s';
    const serverProcess = execSync(serverCommand, { 
      stdio: 'pipe',
      detached: true,
    });
    
    return serverProcess;
  } catch (error) {
    console.warn('⚠️ Could not start local server for Lighthouse testing');
    return null;
  }
}

function validateWebVitals(performanceResults) {
  console.log('🌐 Web Vitals Validation');
  console.log('='.repeat(50));

  const results = {
    firstContentfulPaint: { target: PERFORMANCE_TARGETS.firstContentfulPaint, results: [] },
    largestContentfulPaint: { target: PERFORMANCE_TARGETS.largestContentfulPaint, results: [] },
    timeToInteractive: { target: PERFORMANCE_TARGETS.timeToInteractive, results: [] },
    cumulativeLayoutShift: { target: PERFORMANCE_TARGETS.cumulativeLayoutShift, results: [] },
  };

  performanceResults.forEach(result => {
    if (!result) return;

    console.log(`\n📊 ${result.networkCondition} Network Conditions:`);
    
    if (result.firstContentfulPaint) {
      const fcp = result.firstContentfulPaint;
      const fcpMet = fcp <= PERFORMANCE_TARGETS.firstContentfulPaint;
      results.firstContentfulPaint.results.push({ condition: result.networkCondition, value: fcp, met: fcpMet });
      
      const emoji = fcpMet ? '✅' : '❌';
      console.log(`  ${emoji} First Contentful Paint: ${formatTime(fcp)} (target: ${formatTime(PERFORMANCE_TARGETS.firstContentfulPaint)})`);
    }

    if (result.largestContentfulPaint) {
      const lcp = result.largestContentfulPaint;
      const lcpMet = lcp <= PERFORMANCE_TARGETS.largestContentfulPaint;
      results.largestContentfulPaint.results.push({ condition: result.networkCondition, value: lcp, met: lcpMet });
      
      const emoji = lcpMet ? '✅' : '❌';
      console.log(`  ${emoji} Largest Contentful Paint: ${formatTime(lcp)} (target: ${formatTime(PERFORMANCE_TARGETS.largestContentfulPaint)})`);
    }

    if (result.timeToInteractive) {
      const tti = result.timeToInteractive;
      const ttiMet = tti <= PERFORMANCE_TARGETS.timeToInteractive;
      results.timeToInteractive.results.push({ condition: result.networkCondition, value: tti, met: ttiMet });
      
      const emoji = ttiMet ? '✅' : '❌';
      console.log(`  ${emoji} Time to Interactive: ${formatTime(tti)} (target: ${formatTime(PERFORMANCE_TARGETS.timeToInteractive)})`);
    }

    if (result.cumulativeLayoutShift !== null && result.cumulativeLayoutShift !== undefined) {
      const cls = result.cumulativeLayoutShift;
      const clsMet = cls <= PERFORMANCE_TARGETS.cumulativeLayoutShift;
      results.cumulativeLayoutShift.results.push({ condition: result.networkCondition, value: cls, met: clsMet });
      
      const emoji = clsMet ? '✅' : '❌';
      console.log(`  ${emoji} Cumulative Layout Shift: ${cls.toFixed(3)} (target: ${PERFORMANCE_TARGETS.cumulativeLayoutShift})`);
    }

    if (result.performanceScore) {
      console.log(`  📈 Performance Score: ${result.performanceScore.toFixed(0)}/100`);
    }
  });

  console.log('\n📋 Web Vitals Summary:');
  Object.entries(results).forEach(([metric, data]) => {
    if (data.results.length > 0) {
      const allMet = data.results.every(r => r.met);
      const emoji = allMet ? '✅' : '❌';
      const avgValue = data.results.reduce((sum, r) => sum + r.value, 0) / data.results.length;
      
      console.log(`  ${emoji} ${metric}: ${metric.includes('Paint') || metric.includes('Interactive') ? formatTime(avgValue) : avgValue.toFixed(3)} avg (${data.results.filter(r => r.met).length}/${data.results.length} conditions passed)`);
    }
  });

  console.log('');

  return results;
}

function validateMemoryUsage() {
  console.log('🧠 Memory Usage Validation');
  console.log('='.repeat(50));

  // This is a simplified memory validation
  // In a real implementation, you would use tools like:
  // - Chrome DevTools Protocol
  // - Puppeteer with memory profiling
  // - Custom memory monitoring scripts

  try {
    // Estimate memory usage based on bundle size and complexity
    const bundleMetrics = getCurrentBundleMetrics();
    const estimatedMemoryUsage = bundleMetrics.totalBundleSize * 2; // Rough estimate
    
    console.log(`Estimated Memory Usage: ${formatBytes(estimatedMemoryUsage)}`);
    console.log(`Memory Threshold: ${formatBytes(PERFORMANCE_TARGETS.memoryUsageThreshold)}`);
    
    const memoryMet = estimatedMemoryUsage <= PERFORMANCE_TARGETS.memoryUsageThreshold;
    const emoji = memoryMet ? '✅' : '❌';
    
    console.log(`${emoji} Memory usage ${memoryMet ? 'within' : 'exceeds'} threshold`);
    console.log('');

    // Memory leak prevention checks
    console.log('🔍 Memory Leak Prevention Checks:');
    
    // Check for common memory leak patterns in the codebase
    const memoryLeakChecks = performMemoryLeakChecks();
    memoryLeakChecks.forEach(check => {
      const emoji = check.passed ? '✅' : '⚠️';
      console.log(`  ${emoji} ${check.name}: ${check.message}`);
    });
    
    console.log('');

    return {
      estimatedUsage: estimatedMemoryUsage,
      threshold: PERFORMANCE_TARGETS.memoryUsageThreshold,
      withinThreshold: memoryMet,
      leakChecks: memoryLeakChecks,
    };
  } catch (error) {
    console.warn(`⚠️ Memory validation failed: ${error.message}`);
    return null;
  }
}

function performMemoryLeakChecks() {
  const checks = [];
  
  try {
    // Check for proper cleanup in useEffect hooks
    const useEffectCleanupCheck = checkUseEffectCleanup();
    checks.push({
      name: 'useEffect Cleanup',
      passed: useEffectCleanupCheck.cleanupCount > 0,
      message: `Found ${useEffectCleanupCheck.cleanupCount} cleanup functions in ${useEffectCleanupCheck.totalEffects} useEffect hooks`,
    });

    // Check for event listener cleanup
    const eventListenerCheck = checkEventListenerCleanup();
    checks.push({
      name: 'Event Listener Cleanup',
      passed: eventListenerCheck.cleanupCount > 0,
      message: `Found ${eventListenerCheck.cleanupCount} event listener cleanup patterns`,
    });

    // Check for timer cleanup
    const timerCheck = checkTimerCleanup();
    checks.push({
      name: 'Timer Cleanup',
      passed: timerCheck.cleanupCount > 0 || timerCheck.timerCount === 0,
      message: `Found ${timerCheck.timerCount} timers with ${timerCheck.cleanupCount} cleanup patterns`,
    });

  } catch (error) {
    checks.push({
      name: 'Memory Leak Analysis',
      passed: false,
      message: `Analysis failed: ${error.message}`,
    });
  }

  return checks;
}

function checkUseEffectCleanup() {
  // Simplified check - in reality, you'd use AST parsing
  try {
    const srcFiles = getAllSourceFiles();
    let totalEffects = 0;
    let cleanupCount = 0;

    srcFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      const useEffectMatches = content.match(/useEffect\s*\(/g) || [];
      totalEffects += useEffectMatches.length;
      
      // Look for return statements in useEffect (cleanup functions)
      const cleanupMatches = content.match(/useEffect\s*\([^}]*return\s*\(\s*\)\s*=>/g) || [];
      cleanupCount += cleanupMatches.length;
    });

    return { totalEffects, cleanupCount };
  } catch (error) {
    return { totalEffects: 0, cleanupCount: 0 };
  }
}

function checkEventListenerCleanup() {
  try {
    const srcFiles = getAllSourceFiles();
    let cleanupCount = 0;

    srcFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      // Look for removeEventListener patterns
      const cleanupMatches = content.match(/removeEventListener/g) || [];
      cleanupCount += cleanupMatches.length;
    });

    return { cleanupCount };
  } catch (error) {
    return { cleanupCount: 0 };
  }
}

function checkTimerCleanup() {
  try {
    const srcFiles = getAllSourceFiles();
    let timerCount = 0;
    let cleanupCount = 0;

    srcFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      // Count timers
      const timerMatches = content.match(/(setTimeout|setInterval)/g) || [];
      timerCount += timerMatches.length;
      
      // Count cleanup
      const cleanupMatches = content.match(/(clearTimeout|clearInterval)/g) || [];
      cleanupCount += cleanupMatches.length;
    });

    return { timerCount, cleanupCount };
  } catch (error) {
    return { timerCount: 0, cleanupCount: 0 };
  }
}

function getAllSourceFiles() {
  const files = [];
  
  function walkDir(dir) {
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        walkDir(fullPath);
      } else if (stat.isFile() && (item.endsWith('.ts') || item.endsWith('.tsx'))) {
        files.push(fullPath);
      }
    });
  }
  
  walkDir('src');
  return files;
}

function generateValidationReport(bundleValidation, webVitalsValidation, memoryValidation, performanceResults) {
  console.log('📊 Performance Validation Report');
  console.log('='.repeat(50));

  const report = {
    timestamp: new Date().toISOString(),
    commit: getGitCommit(),
    bundleSizeReduction: bundleValidation,
    webVitals: webVitalsValidation,
    memoryUsage: memoryValidation,
    performanceResults,
    summary: {
      bundleSizeTargetMet: bundleValidation.targetMet,
      fcpTargetMet: webVitalsValidation.firstContentfulPaint.results.every(r => r.met),
      lcpTargetMet: webVitalsValidation.largestContentfulPaint.results.every(r => r.met),
      memoryTargetMet: memoryValidation?.withinThreshold || false,
      overallScore: 0,
    }
  };

  // Calculate overall score
  let score = 0;
  let maxScore = 0;

  // Bundle size reduction (25 points)
  maxScore += 25;
  if (report.summary.bundleSizeTargetMet) score += 25;
  else score += Math.max(0, (bundleValidation.reduction / PERFORMANCE_TARGETS.bundleSizeReduction) * 25);

  // FCP (25 points)
  maxScore += 25;
  if (report.summary.fcpTargetMet) score += 25;
  else {
    const fcpResults = webVitalsValidation.firstContentfulPaint.results;
    const fcpScore = fcpResults.filter(r => r.met).length / fcpResults.length * 25;
    score += fcpScore;
  }

  // LCP (25 points)
  maxScore += 25;
  if (report.summary.lcpTargetMet) score += 25;
  else {
    const lcpResults = webVitalsValidation.largestContentfulPaint.results;
    const lcpScore = lcpResults.filter(r => r.met).length / lcpResults.length * 25;
    score += lcpScore;
  }

  // Memory (25 points)
  maxScore += 25;
  if (report.summary.memoryTargetMet) score += 25;

  report.summary.overallScore = Math.round((score / maxScore) * 100);

  // Display summary
  console.log('🎯 Validation Summary:');
  console.log(`  Overall Score: ${report.summary.overallScore}/100`);
  console.log(`  Bundle Size Reduction: ${bundleValidation.targetMet ? '✅' : '❌'} (${formatPercentage(bundleValidation.reduction)})`);
  console.log(`  First Contentful Paint: ${report.summary.fcpTargetMet ? '✅' : '❌'}`);
  console.log(`  Largest Contentful Paint: ${report.summary.lcpTargetMet ? '✅' : '❌'}`);
  console.log(`  Memory Usage: ${report.summary.memoryTargetMet ? '✅' : '❌'}`);
  console.log('');

  // Save report
  try {
    fs.writeFileSync(VALIDATION_REPORT_FILE, JSON.stringify(report, null, 2));
    console.log(`💾 Validation report saved: ${VALIDATION_REPORT_FILE}`);
  } catch (error) {
    console.warn(`⚠️ Could not save validation report: ${error.message}`);
  }

  return report;
}

function getGitCommit() {
  try {
    return execSync('git rev-parse --short HEAD', { encoding: 'utf8' }).trim();
  } catch (error) {
    return 'unknown';
  }
}

async function main() {
  const args = process.argv.slice(2);
  const verbose = args.includes('--verbose') || args.includes('-v');
  const skipLighthouse = args.includes('--skip-lighthouse');

  try {
    console.log('Starting Performance Validation and Benchmarking...\n');

    // 1. Validate bundle size reduction (30% target)
    const bundleMetrics = getCurrentBundleMetrics();
    const bundleValidation = validateBundleSizeReduction(bundleMetrics);

    // 2. Validate web vitals under various network conditions
    const performanceResults = [];
    
    if (!skipLighthouse) {
      // Test under default conditions
      const defaultResult = await runLighthousePerformanceTest();
      if (defaultResult) performanceResults.push(defaultResult);

      // Test under various network conditions
      for (const networkCondition of PERFORMANCE_TARGETS.networkConditions) {
        const result = await runLighthousePerformanceTest(networkCondition);
        if (result) performanceResults.push(result);
      }
    } else {
      console.log('⏭️ Skipping Lighthouse tests (--skip-lighthouse flag)');
    }

    const webVitalsValidation = validateWebVitals(performanceResults);

    // 3. Validate memory usage and leak prevention
    const memoryValidation = validateMemoryUsage();

    // 4. Generate comprehensive validation report
    const report = generateValidationReport(
      bundleValidation,
      webVitalsValidation,
      memoryValidation,
      performanceResults
    );

    // Final recommendations
    console.log('💡 Recommendations:');
    if (!bundleValidation.targetMet) {
      console.log('  • Implement additional bundle optimization techniques');
      console.log('  • Consider removing unused dependencies');
      console.log('  • Implement more aggressive code splitting');
    }
    
    if (!report.summary.fcpTargetMet) {
      console.log('  • Optimize critical rendering path');
      console.log('  • Reduce render-blocking resources');
    }
    
    if (!report.summary.memoryTargetMet) {
      console.log('  • Review memory usage patterns');
      console.log('  • Implement proper cleanup in components');
    }

    console.log('\n✅ Performance validation complete!');
    console.log(`📊 Overall Score: ${report.summary.overallScore}/100`);

    // Exit with appropriate code
    if (report.summary.overallScore < 80) {
      console.log('\n⚠️ Performance validation score below 80% - consider improvements');
      process.exit(1);
    }

  } catch (error) {
    console.error('❌ Performance validation failed:', error.message);
    if (verbose) {
      console.error(error.stack);
    }
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  main,
  getCurrentBundleMetrics,
  validateBundleSizeReduction,
  validateWebVitals,
  validateMemoryUsage,
  generateValidationReport,
};