#!/bin/bash

# Script to set up nvm and Node.js for pre-commit testing

echo "Setting up nvm and Node.js for pre-commit testing..."

# Install nvm if not already installed
if [ ! -d "$HOME/.nvm" ]; then
    echo "Installing nvm..."
    curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.3/install.sh | bash
    
    # Source nvm
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
else
    echo "nvm is already installed"
    # Source nvm
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
fi

# Install and use Node.js 18
echo "Installing Node.js 18..."
nvm install 18
nvm use 18

# Verify installation
node -v
npm -v

echo "nvm and Node.js setup complete!"