#!/usr/bin/env python3

import os
import re
import glob

def fix_common_any_types(content, file_path):
    """Fix common any type patterns."""
    original_content = content
    
    # Pattern 1: React.ComponentType<any> -> React.ComponentType<{}>
    content = re.sub(
        r'React\.ComponentType<any>',
        'React.ComponentType<{}>',
        content
    )
    
    # Pattern 2: (props: any) -> (props: Record<string, unknown>)
    content = re.sub(
        r'\(([^)]*)\s*:\s*any\s*\)',
        r'(\1: Record<string, unknown>)',
        content
    )
    
    # Pattern 3: useState<any> -> useState<unknown>
    content = re.sub(
        r'useState<any>',
        'useState<unknown>',
        content
    )
    
    # Pattern 4: function(...args: any[]) -> function(...args: unknown[])
    content = re.sub(
        r'\.\.\.(\w+)\s*:\s*any\[\]',
        r'...\1: unknown[]',
        content
    )
    
    # Pattern 5: : any[] -> : unknown[]
    content = re.sub(
        r':\s*any\[\]',
        ': unknown[]',
        content
    )
    
    # Pattern 6: Promise<any> -> Promise<unknown>
    content = re.sub(
        r'Promise<any>',
        'Promise<unknown>',
        content
    )
    
    # Pattern 7: type/interface with any -> unknown
    content = re.sub(
        r'(\w+):\s*any(\s*[;}])',
        r'\1: unknown\2',
        content
    )
    
    return content

def fix_unused_variables(content):
    """Remove unused imports and variables that are clearly unused."""
    lines = content.split('\n')
    new_lines = []
    
    for line in lines:
        # Remove unused imports that are clearly not used
        if (line.strip().startswith('import') and 
            any(unused in line for unused in ['RiskStatus', 'RiskSeverity', 'MatrixCell', 'Badge', 'Table']) and
            'from' in line):
            
            # Extract what's being imported
            if 'RiskStatus' in line and 'RiskStatus' not in content.replace(line, ''):
                line = re.sub(r'RiskStatus,?\s*', '', line)
            if 'RiskSeverity' in line and 'RiskSeverity' not in content.replace(line, ''):
                line = re.sub(r'RiskSeverity,?\s*', '', line)
            if 'MatrixCell' in line and 'MatrixCell' not in content.replace(line, ''):
                line = re.sub(r'MatrixCell,?\s*', '', line)
            
            # Clean up empty imports
            if re.match(r'import\s*{\s*}\s*from', line):
                continue
                
        new_lines.append(line)
    
    return '\n'.join(new_lines)

def fix_typescript_file(file_path):
    """Apply TypeScript fixes to a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Apply fixes
        content = fix_common_any_types(content, file_path)
        content = fix_unused_variables(content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed: {file_path}")
            return True
        
        return False
    
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Apply fixes to all TypeScript files."""
    # Find all TypeScript files
    ts_files = glob.glob('src/**/*.ts', recursive=True) + glob.glob('src/**/*.tsx', recursive=True)
    
    # Filter to files that likely have 'any' types
    any_files = []
    for file_path in ts_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if ': any' in content or '<any>' in content or 'any[]' in content:
                    any_files.append(file_path)
        except Exception:
            continue
    
    fixed_count = 0
    total_count = len(any_files)
    
    print(f"Processing {total_count} TypeScript files with 'any' types...")
    
    for file_path in any_files:
        if fix_typescript_file(file_path):
            fixed_count += 1
    
    print(f"\nSummary:")
    print(f"- Files with 'any' types: {total_count}")
    print(f"- Files modified: {fixed_count}")
    print(f"- Files unchanged: {total_count - fixed_count}")

if __name__ == "__main__":
    main()
