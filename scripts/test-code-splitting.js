#!/usr/bin/env node

/**
 * Automated test runner for code splitting implementation
 * Run with: node scripts/test-code-splitting.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🧪 Code Splitting Test Runner\n');

// Test configuration
const tests = [
  {
    name: 'Unit Tests',
    command: 'npm run test src/test/lazy-routes.test.tsx',
    description: 'Testing lazy route creation and preloading utilities',
  },
  {
    name: 'Route Preloader Tests',
    command: 'npm run test src/test/route-preloader.test.tsx',
    description: 'Testing intelligent route preloading hooks',
  },
  {
    name: 'Bundle Analyzer Tests',
    command: 'npm run test src/test/bundle-analyzer.test.ts',
    description: 'Testing performance monitoring and bundle analysis',
  },
  {
    name: 'App Integration Tests',
    command: 'npm run test src/test/app-lazy-loading.test.tsx',
    description: 'Testing App component with lazy loading integration',
  },
];

// File structure validation
const requiredFiles = [
  'src/components/ui/loading.tsx',
  'src/components/error-boundaries/LazyLoadErrorBoundary.tsx',
  'src/utils/lazy-routes.tsx',
  'src/routes/lazy-routes.tsx',
  'src/hooks/useRoutePreloader.ts',
  'src/components/navigation/PreloadLink.tsx',
  'src/utils/bundle-analyzer.ts',
  'src/utils/performance-validator.ts',
];

function validateFileStructure() {
  console.log('📁 Validating file structure...\n');
  
  const missing = [];
  const existing = [];
  
  for (const file of requiredFiles) {
    if (fs.existsSync(file)) {
      existing.push(file);
      console.log(`✅ ${file}`);
    } else {
      missing.push(file);
      console.log(`❌ ${file} - MISSING`);
    }
  }
  
  console.log(`\n📊 File Structure: ${existing.length}/${requiredFiles.length} files present\n`);
  
  if (missing.length > 0) {
    console.log('❌ Missing files detected. Please ensure all required files are created.\n');
    return false;
  }
  
  return true;
}

function runTest(test) {
  console.log(`🧪 Running: ${test.name}`);
  console.log(`📝 ${test.description}`);
  
  try {
    const output = execSync(test.command, { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    console.log('✅ PASSED\n');
    return { name: test.name, status: 'PASSED', output };
  } catch (error) {
    console.log('❌ FAILED');
    console.log(`Error: ${error.message}\n`);
    return { name: test.name, status: 'FAILED', error: error.message };
  }
}

function validateBuildOutput() {
  console.log('🏗️  Validating build output...\n');
  
  try {
    // Run build
    console.log('Building project...');
    execSync('npm run build', { stdio: 'pipe' });
    
    // Check for chunk files in dist
    const distPath = 'dist/assets';
    if (!fs.existsSync(distPath)) {
      console.log('❌ Build output directory not found');
      return false;
    }
    
    const files = fs.readdirSync(distPath);
    const jsFiles = files.filter(f => f.endsWith('.js'));
    const chunkFiles = jsFiles.filter(f => 
      /\.[a-f0-9]{8,}\.js$/.test(f) || 
      /chunk\.[a-f0-9]+\.js$/.test(f)
    );
    
    console.log(`📦 Total JS files: ${jsFiles.length}`);
    console.log(`🧩 Chunk files: ${chunkFiles.length}`);
    
    if (chunkFiles.length > 0) {
      console.log('✅ Code splitting detected in build output');
      chunkFiles.forEach(file => console.log(`   - ${file}`));
    } else {
      console.log('❌ No chunk files found - code splitting may not be working');
      return false;
    }
    
    console.log('');
    return true;
  } catch (error) {
    console.log(`❌ Build failed: ${error.message}\n`);
    return false;
  }
}

function generateReport(results) {
  console.log('📊 Test Results Summary\n');
  console.log('='.repeat(50));
  
  const passed = results.filter(r => r.status === 'PASSED').length;
  const failed = results.filter(r => r.status === 'FAILED').length;
  
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${results.length}`);
  
  if (failed > 0) {
    console.log('\n❌ Failed Tests:');
    results
      .filter(r => r.status === 'FAILED')
      .forEach(r => {
        console.log(`   - ${r.name}: ${r.error}`);
      });
  }
  
  console.log('\n' + '='.repeat(50));
  
  if (failed === 0) {
    console.log('🎉 All tests passed! Code splitting implementation is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please review the errors above.');
  }
}

// Main execution
async function main() {
  console.log('Starting code splitting validation...\n');
  
  // Step 1: Validate file structure
  if (!validateFileStructure()) {
    process.exit(1);
  }
  
  // Step 2: Run unit tests
  console.log('🧪 Running unit tests...\n');
  const results = [];
  
  for (const test of tests) {
    const result = runTest(test);
    results.push(result);
  }
  
  // Step 3: Validate build output
  const buildValid = validateBuildOutput();
  results.push({
    name: 'Build Validation',
    status: buildValid ? 'PASSED' : 'FAILED',
  });
  
  // Step 4: Generate report
  generateReport(results);
  
  // Exit with appropriate code
  const hasFailures = results.some(r => r.status === 'FAILED');
  process.exit(hasFailures ? 1 : 0);
}

// Handle errors
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught exception:', error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason) => {
  console.error('❌ Unhandled rejection:', reason);
  process.exit(1);
});

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Test runner failed:', error.message);
    process.exit(1);
  });
}

module.exports = { main, validateFileStructure, runTest, validateBuildOutput };
