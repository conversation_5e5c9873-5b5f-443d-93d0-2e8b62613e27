#!/usr/bin/env node

/**
 * Automated console statement removal script for production builds
 * Addresses Requirements Document #1 - Production Security Hardening
 */

import fs from "fs";
import path from "path";
import { promisify } from "util";

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

class ConsoleRemover {
  constructor() {
    this.processedFiles = 0;
    this.removedStatements = 0;
    this.dryRun = process.argv.includes("--dry-run");
    this.verbose = process.argv.includes("--verbose");
  }

  /**
   * Remove console statements from code
   */
  removeConsoleStatements(code) {
    let modifiedCode = code;
    let removedCount = 0;

    // Patterns to match console statements
    const patterns = [
      // console.log, console.warn, console.error, etc.
      /console\.(log|warn|error|info|debug|trace|dir|table|time|timeEnd|group|groupEnd|count|assert)\s*\([^;]*\);?\s*\n?/g,
      // Multi-line console statements
      /console\.(log|warn|error|info|debug|trace|dir|table|time|timeEnd|group|groupEnd|count|assert)\s*\(\s*[\s\S]*?\)\s*;?\s*\n?/g,
    ];

    patterns.forEach(pattern => {
      const matches = modifiedCode.match(pattern);
      if (matches) {
        removedCount += matches.length;
        modifiedCode = modifiedCode.replace(pattern, "");
      }
    });

    // Clean up empty lines left by removed console statements
    modifiedCode = modifiedCode.replace(/^\s*\n/gm, "");

    return { code: modifiedCode, removedCount };
  }

  /**
   * Process a single file
   */
  async processFile(filePath) {
    try {
      const code = await readFile(filePath, "utf8");
      const { code: modifiedCode, removedCount } = this.removeConsoleStatements(code);

      if (removedCount > 0) {
        if (this.verbose) {
          console.log(
            `📝 ${filePath}: ${removedCount} console statement(s) ${this.dryRun ? "would be " : ""}removed`
          );
        }

        if (!this.dryRun && modifiedCode !== code) {
          await writeFile(filePath, modifiedCode, "utf8");
        }

        this.removedStatements += removedCount;
      }

      this.processedFiles++;
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  /**
   * Recursively process directory
   */
  async processDirectory(dirPath) {
    try {
      const entries = await readdir(dirPath);

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry);
        const stats = await stat(fullPath);

        if (stats.isDirectory()) {
          // Skip node_modules, .git, and other non-source directories
          if (!["node_modules", ".git", "dist", "build", ".next"].includes(entry)) {
            await this.processDirectory(fullPath);
          }
        } else if (stats.isFile()) {
          // Process TypeScript and JavaScript files
          if (
            /\.(ts|tsx|js|jsx)$/.test(entry) &&
            !entry.includes(".test.") &&
            !entry.includes(".spec.")
          ) {
            await this.processFile(fullPath);
          }
        }
      }
    } catch (error) {
      console.error(`❌ Error processing directory ${dirPath}:`, error.message);
    }
  }

  /**
   * Run the console removal process
   */
  async run() {
    const startTime = Date.now();

    console.log("🧹 Starting console statement removal...");
    console.log(`Mode: ${this.dryRun ? "DRY RUN" : "LIVE"}`);
    console.log("────────────────────────────────────────");

    // Process src directory
    await this.processDirectory("./src");

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log("────────────────────────────────────────");
    console.log(`✅ Processing complete!`);
    console.log(`📊 Files processed: ${this.processedFiles}`);
    console.log(
      `🗑️  Console statements ${this.dryRun ? "found" : "removed"}: ${this.removedStatements}`
    );
    console.log(`⏱️  Duration: ${duration}s`);

    if (this.dryRun && this.removedStatements > 0) {
      console.log("\n💡 Run without --dry-run to apply changes");
    }
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  const remover = new ConsoleRemover();
  remover.run().catch(console.error);
}

export default ConsoleRemover;
