#!/usr/bin/env node

import fs from "fs";
import { glob } from "glob";

function fixCatchVariableConsistency(content) {
  let changes = 0;

  // Fix inconsistent variable usage in catch blocks
  // Pattern: catch (_error) { ... use error instead of _error ... }
  const catchBlockPattern = /catch\s*\(\s*(_\w+)\s*\)\s*{([^}]*)}/g;

  content = content.replace(catchBlockPattern, (match, varName, blockContent) => {
    const originalVarName = varName.substring(1); // Remove underscore

    // Check if the block content uses the original name instead of underscore version
    if (blockContent.includes(originalVarName) && !blockContent.includes(varName)) {
      changes++;
      // Replace all instances of original variable name with underscore version
      const fixedBlockContent = blockContent.replace(
        new RegExp(`\\b${originalVarName}\\b`, "g"),
        varName
      );
      return `catch (${varName}) {${fixedBlockContent}}`;
    }

    return match;
  });

  // Fix mixed usage where both versions are used
  content = content.replace(
    /catch\s*\(\s*(_\w+)\s*\)\s*{([^}]*)console\.error\([^,]+,\s*(\w+)\)([^}]*)}/g,
    (match, underscoreVar, before, usedVar, after) => {
      if (usedVar !== underscoreVar && usedVar === underscoreVar.substring(1)) {
        changes++;
        return `catch (${underscoreVar}) {${before}console.error('Error caught', ${underscoreVar})${after}}`;
      }
      return match;
    }
  );

  return { content, changes };
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, "utf8");
    const { content: newContent, changes } = fixCatchVariableConsistency(content);

    if (changes > 0) {
      fs.writeFileSync(filePath, newContent);
      console.log(`Fixed ${changes} catch variable inconsistencies in ${filePath}`);
      return changes;
    }
    return 0;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return 0;
  }
}

function main() {
  const patterns = ["src/**/*.ts", "src/**/*.tsx"];

  let totalChanges = 0;

  patterns.forEach(pattern => {
    const files = glob.sync(pattern, { ignore: "node_modules/**" });

    files.forEach(file => {
      totalChanges += processFile(file);
    });
  });

  console.log(`\nTotal catch variable consistency fixes: ${totalChanges}`);
}

main();
