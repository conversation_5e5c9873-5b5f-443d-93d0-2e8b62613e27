#!/usr/bin/env node

/**
 * Automated unused code removal script
 * Addresses Requirements Document - Requirement 4: Remove unused variables and imports
 */

import fs from "fs";
import path from "path";
import { promisify } from "util";
import { execSync } from "child_process";

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

class UnusedCodeRemover {
  constructor() {
    this.processedFiles = 0;
    this.removedImports = 0;
    this.removedVariables = 0;
    this.dryRun = process.argv.includes("--dry-run");
    this.verbose = process.argv.includes("--verbose");
  }

  /**
   * Remove unused imports from code
   */
  removeUnusedImports(code) {
    let modifiedCode = code;
    let removedCount = 0;

    // Simple patterns for common unused import scenarios
    const patterns = [
      // Unused named imports (single line)
      /import\s+{\s*[^}]*?\s*}\s+from\s+['"][^'"]+['"];\s*\n/g,
      // Unused default imports
      /import\s+\w+\s+from\s+['"][^'"]+['"];\s*\n/g,
      // Unused import * as patterns
      /import\s+\*\s+as\s+\w+\s+from\s+['"][^'"]+['"];\s*\n/g,
    ];

    // This is a basic implementation - for production use, we'd want
    // a more sophisticated AST-based approach
    const lines = modifiedCode.split("\n");
    const modifiedLines = [];

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];

      // Skip import lines that match common unused patterns
      if (
        line.trim().startsWith("import ") &&
        (line.includes("// eslint-disable-next-line") ||
          line.includes("@typescript-eslint/no-unused-vars"))
      ) {
        // This import is marked as unused by ESLint
        if (this.verbose) {
          console.log(`  Removing unused import: ${line.trim()}`);
        }
        removedCount++;
        continue;
      }

      modifiedLines.push(line);
    }

    return {
      code: modifiedLines.join("\n"),
      removedCount,
    };
  }

  /**
   * Process a single file
   */
  async processFile(filePath) {
    try {
      const code = await readFile(filePath, "utf8");
      const { code: modifiedCode, removedCount } = this.removeUnusedImports(code);

      if (removedCount > 0) {
        if (this.verbose) {
          console.log(
            `📝 ${filePath}: ${removedCount} unused import(s) ${this.dryRun ? "would be " : ""}removed`
          );
        }

        if (!this.dryRun && modifiedCode !== code) {
          await writeFile(filePath, modifiedCode, "utf8");
        }

        this.removedImports += removedCount;
      }

      this.processedFiles++;
    } catch (error) {
      console.error(`❌ Error processing ${filePath}:`, error.message);
    }
  }

  /**
   * Recursively process directory
   */
  async processDirectory(dirPath) {
    try {
      const entries = await readdir(dirPath);

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry);
        const stats = await stat(fullPath);

        if (stats.isDirectory()) {
          // Skip node_modules, .git, and other non-source directories
          if (!["node_modules", ".git", "dist", "build", ".next"].includes(entry)) {
            await this.processDirectory(fullPath);
          }
        } else if (stats.isFile()) {
          // Process TypeScript and JavaScript files
          if (
            /\.(ts|tsx|js|jsx)$/.test(entry) &&
            !entry.includes(".test.") &&
            !entry.includes(".spec.")
          ) {
            await this.processFile(fullPath);
          }
        }
      }
    } catch (error) {
      console.error(`❌ Error processing directory ${dirPath}:`, error.message);
    }
  }

  /**
   * Use ESLint to identify unused variables
   */
  getUnusedVariables() {
    try {
      console.log("🔍 Analyzing unused variables with ESLint...");

      const eslintOutput = execSync("npx eslint . --format=json --quiet", {
        encoding: "utf8",
        maxBuffer: 10 * 1024 * 1024,
      });

      const results = JSON.parse(eslintOutput);
      const unusedVars = [];

      results.forEach(result => {
        result.messages.forEach(message => {
          if (
            message.ruleId === "@typescript-eslint/no-unused-vars" ||
            message.ruleId === "no-unused-vars"
          ) {
            unusedVars.push({
              file: result.filePath,
              line: message.line,
              column: message.column,
              message: message.message,
              variable: message.message.match(/'([^']+)'/)?.[1],
            });
          }
        });
      });

      return unusedVars;
    } catch (error) {
      console.error("⚠️  Could not analyze unused variables:", error.message);
      return [];
    }
  }

  /**
   * Run the unused code removal process
   */
  async run() {
    const startTime = Date.now();

    console.log("🧹 Starting unused code removal...");
    console.log(`Mode: ${this.dryRun ? "DRY RUN" : "LIVE"}`);
    console.log("────────────────────────────────────────");

    // Get unused variables from ESLint
    const unusedVars = this.getUnusedVariables();

    if (unusedVars.length > 0) {
      console.log(`📊 Found ${unusedVars.length} unused variables/imports`);

      if (this.verbose) {
        unusedVars.slice(0, 10).forEach(item => {
          console.log(
            `  - ${item.variable} in ${path.relative(process.cwd(), item.file)}:${item.line}`
          );
        });
        if (unusedVars.length > 10) {
          console.log(`  ... and ${unusedVars.length - 10} more`);
        }
      }
    }

    // Process src directory for import cleanup
    await this.processDirectory("./src");

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);

    console.log("────────────────────────────────────────");
    console.log(`✅ Processing complete!`);
    console.log(`📊 Files processed: ${this.processedFiles}`);
    console.log(`📦 Unused imports ${this.dryRun ? "found" : "removed"}: ${this.removedImports}`);
    console.log(`🗑️  Unused variables found: ${unusedVars.length}`);
    console.log(`⏱️  Duration: ${duration}s`);

    if (this.dryRun && (this.removedImports > 0 || unusedVars.length > 0)) {
      console.log("\n💡 Run without --dry-run to apply changes");
    }

    if (!this.dryRun && unusedVars.length > 0) {
      console.log("\n🔧 For unused variables, consider:");
      console.log("   1. Prefixing with underscore: _unusedVar");
      console.log("   2. Adding eslint-disable comments");
      console.log("   3. Removing if truly unused");
    }
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  const remover = new UnusedCodeRemover();
  remover.run().catch(console.error);
}

export default UnusedCodeRemover;
