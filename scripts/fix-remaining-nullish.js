#!/usr/bin/env node

import fs from 'fs';
import path from 'path';

// Function to recursively get all TypeScript files
function getAllTsFiles(dirPath, arrayOfFiles = []) {
  const files = fs.readdirSync(dirPath);

  files.forEach((file) => {
    const fullPath = path.join(dirPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      // Skip node_modules and other irrelevant directories
      if (file !== 'node_modules' && file !== '.git' && file !== 'dist' && file !== 'build') {
        arrayOfFiles = getAllTsFiles(fullPath, arrayOfFiles);
      }
    } else if (file.endsWith('.ts') || file.endsWith('.tsx')) {
      arrayOfFiles.push(fullPath);
    }
  });

  return arrayOfFiles;
}

// More sophisticated patterns to fix nullish coalescing
const patterns = [
  // Property access with || fallback
  {
    pattern: /(\w+\.\w+(?:\.\w+)*)\s*\|\|\s*('|"|`)[^'"`]*\2/g,
    replacement: '$1 ?? $2$3',
    description: 'Property access with string fallback'
  },
  // Array access with || fallback
  {
    pattern: /(\w+\[\w+\](?:\.\w+)*)\s*\|\|\s*('|"|`)[^'"`]*\2/g,
    replacement: '$1 ?? $2$3',
    description: 'Array access with string fallback'
  },
  // Optional chaining with || fallback
  {
    pattern: /(\w+(?:\?\.\w+)+)\s*\|\|\s*('|"|`)[^'"`]*\2/g,
    replacement: '$1 ?? $2$3',
    description: 'Optional chaining with string fallback'
  },
  // Function call with || fallback
  {
    pattern: /(\w+\([^)]*\))\s*\|\|\s*('|"|`)[^'"`]*\2/g,
    replacement: '$1 ?? $2$3',
    description: 'Function call with string fallback'
  },
  // Variable with || number fallback
  {
    pattern: /(\w+(?:\.\w+)*)\s*\|\|\s*(\d+(?:\.\d+)?)/g,
    replacement: '$1 ?? $2',
    description: 'Variable with number fallback'
  },
  // Variable with || boolean fallback
  {
    pattern: /(\w+(?:\.\w+)*)\s*\|\|\s*(true|false)/g,
    replacement: '$1 ?? $2',
    description: 'Variable with boolean fallback'
  },
  // Complex expression with || fallback
  {
    pattern: /(\([^)]+\))\s*\|\|\s*('|"|`)[^'"`]*\2/g,
    replacement: '$1 ?? $2$3',
    description: 'Complex expression with string fallback'
  }
];

function fixNullishCoalescing(content) {
  let fixedContent = content;
  let changes = 0;

  // Apply patterns in order
  patterns.forEach(({ pattern, replacement, description }) => {
    const matches = fixedContent.match(pattern);
    if (matches) {
      const newContent = fixedContent.replace(pattern, replacement);
      if (newContent !== fixedContent) {
        console.log(`  - Applied: ${description} (${matches.length} matches)`);
        changes++;
        fixedContent = newContent;
      }
    }
  });

  return { content: fixedContent, changes };
}

// Main execution
const srcPath = process.argv[2] || './src';
const files = getAllTsFiles(srcPath);

console.log(`Found ${files.length} TypeScript files`);

let totalChanges = 0;
let filesChanged = 0;

files.forEach((filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const { content: fixedContent, changes } = fixNullishCoalescing(content);
    
    if (changes > 0) {
      fs.writeFileSync(filePath, fixedContent);
      filesChanged++;
      totalChanges += changes;
      console.log(`Fixed ${changes} pattern(s) in ${filePath}`);
    }
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
  }
});

console.log(`\nSummary:`);
console.log(`Files changed: ${filesChanged}`);
console.log(`Total changes: ${totalChanges}`);
