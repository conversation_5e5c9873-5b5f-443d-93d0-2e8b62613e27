#!/usr/bin/env node

/**
 * TypeScript Migration Script
 * Systematically replaces 'any' types with proper TypeScript types
 */

import fs from 'fs';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const MIGRATIONS = [
  // API Response patterns
  {
    pattern: /catch\s*\(\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:\s*any\s*\)/g,
    replacement: 'catch ($1: unknown)',
    description: 'Replace any in catch blocks with unknown'
  },
  {
    pattern: /error\s*:\s*any/g,
    replacement: 'error: Error | unknown',
    description: 'Replace any error types with Error | unknown'
  },
  {
    pattern: /data\s*:\s*any/g,
    replacement: 'data: unknown',
    description: 'Replace any data types with unknown'
  },
  // Event handler patterns
  {
    pattern: /event\s*:\s*any/g,
    replacement: 'event: React.SyntheticEvent',
    description: 'Replace any event types with React.SyntheticEvent'
  },
  // Form patterns
  {
    pattern: /values\s*:\s*any/g,
    replacement: 'values: Record<string, unknown>',
    description: 'Replace any form values with Record<string, unknown>'
  },
  // Props patterns
  {
    pattern: /props\s*:\s*any/g,
    replacement: 'props: Record<string, unknown>',
    description: 'Replace any props with Record<string, unknown>'
  }
];

class TypeMigrator {
  constructor() {
    this.stats = {
      filesProcessed: 0,
      replacements: 0,
      errors: 0
    };
  }

  async migrateFile(filePath) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let hasChanges = false;

      MIGRATIONS.forEach(migration => {
        if (migration.pattern.test(content)) {
          content = content.replace(migration.pattern, migration.replacement);
          hasChanges = true;
          this.stats.replacements++;
          console.log(`  ✓ ${migration.description}`);
        }
      });

      if (hasChanges) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`✓ Updated: ${filePath}`);
      }

      this.stats.filesProcessed++;
    } catch (error) {
      console.error(`✗ Error processing ${filePath}:`, error.message);
      this.stats.errors++;
    }
  }

  async findTypeScriptFiles(dir) {
    const files = [];
    
    function traverse(currentDir) {
      const entries = fs.readdirSync(currentDir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(currentDir, entry.name);
        
        if (entry.isDirectory() && !entry.name.startsWith('.') && entry.name !== 'node_modules') {
          traverse(fullPath);
        } else if (entry.isFile() && (entry.name.endsWith('.ts') || entry.name.endsWith('.tsx'))) {
          files.push(fullPath);
        }
      }
    }
    
    traverse(dir);
    return files;
  }

  async run() {
    console.log('🚀 Starting TypeScript migration...\n');

    const files = await this.findTypeScriptFiles('./src');
    console.log(`Found ${files.length} TypeScript files\n`);

    for (const file of files) {
      await this.migrateFile(file);
    }

    console.log('\n📊 Migration Summary:');
    console.log(`Files processed: ${this.stats.filesProcessed}`);
    console.log(`Replacements made: ${this.stats.replacements}`);
    console.log(`Errors: ${this.stats.errors}`);

    if (this.stats.replacements > 0) {
      console.log('\n🔧 Running TypeScript check...');
      try {
        execSync('npx tsc --noEmit', { stdio: 'inherit' });
        console.log('✓ TypeScript check passed');
      } catch (error) {
        console.log('⚠️  TypeScript errors found - manual review needed');
      }
    }

    console.log('\n✅ Migration complete!');
  }
}

// Run the migration
if (import.meta.url === `file://${process.argv[1]}`) {
  const migrator = new TypeMigrator();
  migrator.run().catch(console.error);
}

export default TypeMigrator;
