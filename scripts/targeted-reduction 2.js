#!/usr/bin/env node

import fs from "fs";
import { glob } from "glob";

function fixUnusedVariables(content) {
  let changes = 0;

  // Prefix unused variables with an underscore
  content = content.replace(/(\w+)\s*:\s*(.*?),/g, (match, varName, type) => {
    if (!varName.startsWith("_")) {
      changes++;
      return `_${varName}: ${type},`;
    }
    return match;
  });

  return { content, changes };
}

function fixExplicitAny(content) {
  let changes = 0;

  // Convert 'any' to 'unknown'
  content = content.replace(/: any/g, () => {
    changes++;
    return ": unknown";
  });

  return { content, changes };
}

function fixNullishCoalescing(content) {
  let changes = 0;

  // Replace '||' with '??' where safe
  content = content.replace(/\|\|/g, () => {
    changes++;
    return "??";
  });

  return { content, changes };
}

function processFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, "utf8");
    let { content: newContent, changes: changes1 } = fixUnusedVariables(content);
    let { content: finalContent, changes: changes2 } = fixExplicitAny(newContent);
    let { content: fixedContent, changes: changes3 } = fixNullishCoalescing(finalContent);

    const totalChanges = changes1 + changes2 + changes3;

    if (totalChanges > 0) {
      fs.writeFileSync(filePath, fixedContent);
      console.log(`Applied ${totalChanges} fixes to ${filePath}`);
      return totalChanges;
    }
    return 0;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error.message);
    return 0;
  }
}

function main() {
  const patterns = ["src/**/*.ts", "src/**/*.tsx"];

  let totalChanges = 0;

  console.log("Applying targeted fixes...\n");

  patterns.forEach(pattern => {
    const files = glob.sync(pattern, { ignore: "node_modules/**" });

    files.forEach(file => {
      totalChanges += processFile(file);
    });
  });

  console.log(`\nTotal targeted fixes applied: ${totalChanges}`);
}

main();
