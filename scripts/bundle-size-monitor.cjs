#!/usr/bin/env node

/**
 * Bundle size monitoring script for CI/CD integration
 * Monitors bundle size changes and enforces size budgets
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('📊 Bundle Size Monitor\n');

const DIST_PATH = 'dist';
const ASSETS_PATH = path.join(DIST_PATH, 'assets');
const BUDGET_FILE = 'bundle-budget.json';

// Default bundle size budgets (in bytes)
const DEFAULT_BUDGETS = {
  totalJS: 2 * 1024 * 1024,      // 2MB total JS
  totalCSS: 500 * 1024,          // 500KB total CSS
  maxChunk: 1 * 1024 * 1024,     // 1MB max chunk size
  vendorChunk: 800 * 1024,       // 800KB vendor chunk
  initialBundle: 500 * 1024      // 500KB initial bundle
};

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function loadBudgets() {
  try {
    if (fs.existsSync(BUDGET_FILE)) {
      const budgets = JSON.parse(fs.readFileSync(BUDGET_FILE, 'utf8'));
      return { ...DEFAULT_BUDGETS, ...budgets };
    }
  } catch (error) {
    console.warn(`⚠️ Could not load budget file: ${error.message}`);
  }
  return DEFAULT_BUDGETS;
}

function saveBudgets(budgets) {
  try {
    fs.writeFileSync(BUDGET_FILE, JSON.stringify(budgets, null, 2));
    console.log(`💾 Budget file saved: ${BUDGET_FILE}`);
  } catch (error) {
    console.error(`❌ Could not save budget file: ${error.message}`);
  }
}

function analyzeBundleSize() {
  if (!fs.existsSync(ASSETS_PATH)) {
    console.error('❌ Build output not found. Run "npm run build" first.');
    process.exit(1);
  }

  const files = fs.readdirSync(ASSETS_PATH, { recursive: true });
  const jsFiles = [];
  const cssFiles = [];

  files.forEach(file => {
    const filePath = path.join(ASSETS_PATH, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isFile()) {
      const size = stats.size;
      const fileInfo = { name: file, size, path: filePath };
      
      if (file.endsWith('.js')) {
        jsFiles.push(fileInfo);
      } else if (file.endsWith('.css')) {
        cssFiles.push(fileInfo);
      }
    }
  });

  // Sort by size (largest first)
  jsFiles.sort((a, b) => b.size - a.size);
  cssFiles.sort((a, b) => b.size - a.size);

  const totalJSSize = jsFiles.reduce((sum, file) => sum + file.size, 0);
  const totalCSSSize = cssFiles.reduce((sum, file) => sum + file.size, 0);

  return {
    jsFiles,
    cssFiles,
    totalJSSize,
    totalCSSSize,
    totalSize: totalJSSize + totalCSSSize
  };
}

function categorizeChunks(jsFiles) {
  const categories = {
    vendor: jsFiles.filter(f => f.name.includes('vendor')),
    main: jsFiles.filter(f => f.name.includes('main') || f.name.includes('index')),
    chunks: jsFiles.filter(f => f.name.includes('chunk') && !f.name.includes('vendor')),
    other: jsFiles.filter(f => 
      !f.name.includes('vendor') && 
      !f.name.includes('main') && 
      !f.name.includes('index') && 
      !f.name.includes('chunk')
    )
  };

  return categories;
}

function checkBudgets(analysis, budgets) {
  const { jsFiles, cssFiles, totalJSSize, totalCSSSize } = analysis;
  const categories = categorizeChunks(jsFiles);
  
  const results = {
    passed: 0,
    failed: 0,
    warnings: [],
    errors: []
  };

  console.log('🎯 Budget Validation');
  console.log('='.repeat(50));

  // Check total JS size
  if (totalJSSize <= budgets.totalJS) {
    console.log(`✅ Total JS size: ${formatBytes(totalJSSize)} (under ${formatBytes(budgets.totalJS)})`);
    results.passed++;
  } else {
    const message = `❌ Total JS size: ${formatBytes(totalJSSize)} (exceeds ${formatBytes(budgets.totalJS)})`;
    console.log(message);
    results.errors.push(message);
    results.failed++;
  }

  // Check total CSS size
  if (totalCSSSize <= budgets.totalCSS) {
    console.log(`✅ Total CSS size: ${formatBytes(totalCSSSize)} (under ${formatBytes(budgets.totalCSS)})`);
    results.passed++;
  } else {
    const message = `❌ Total CSS size: ${formatBytes(totalCSSSize)} (exceeds ${formatBytes(budgets.totalCSS)})`;
    console.log(message);
    results.errors.push(message);
    results.failed++;
  }

  // Check largest chunk
  const largestChunk = jsFiles[0];
  if (largestChunk && largestChunk.size <= budgets.maxChunk) {
    console.log(`✅ Largest chunk: ${formatBytes(largestChunk.size)} (under ${formatBytes(budgets.maxChunk)})`);
    results.passed++;
  } else if (largestChunk) {
    const message = `❌ Largest chunk: ${formatBytes(largestChunk.size)} (exceeds ${formatBytes(budgets.maxChunk)})`;
    console.log(message);
    results.errors.push(message);
    results.failed++;
  }

  // Check vendor chunk size
  const vendorChunk = categories.vendor[0];
  if (vendorChunk) {
    if (vendorChunk.size <= budgets.vendorChunk) {
      console.log(`✅ Vendor chunk: ${formatBytes(vendorChunk.size)} (under ${formatBytes(budgets.vendorChunk)})`);
      results.passed++;
    } else {
      const message = `❌ Vendor chunk: ${formatBytes(vendorChunk.size)} (exceeds ${formatBytes(budgets.vendorChunk)})`;
      console.log(message);
      results.errors.push(message);
      results.failed++;
    }
  } else {
    const message = `⚠️ No vendor chunk found - consider separating vendor libraries`;
    console.log(message);
    results.warnings.push(message);
  }

  // Check initial bundle size (main + vendor)
  const mainChunk = categories.main[0];
  const initialSize = (mainChunk?.size || 0) + (vendorChunk?.size || 0);
  if (initialSize <= budgets.initialBundle) {
    console.log(`✅ Initial bundle: ${formatBytes(initialSize)} (under ${formatBytes(budgets.initialBundle)})`);
    results.passed++;
  } else {
    const message = `❌ Initial bundle: ${formatBytes(initialSize)} (exceeds ${formatBytes(budgets.initialBundle)})`;
    console.log(message);
    results.errors.push(message);
    results.failed++;
  }

  console.log('');
  return results;
}

function generateOptimizationReport(analysis, budgetResults) {
  const { jsFiles, totalJSSize } = analysis;
  const categories = categorizeChunks(jsFiles);

  console.log('📈 Optimization Report');
  console.log('='.repeat(50));

  // Bundle composition
  console.log('📊 Bundle Composition:');
  console.log(`  Vendor chunks: ${categories.vendor.length} files, ${formatBytes(categories.vendor.reduce((sum, f) => sum + f.size, 0))}`);
  console.log(`  Main chunks: ${categories.main.length} files, ${formatBytes(categories.main.reduce((sum, f) => sum + f.size, 0))}`);
  console.log(`  Dynamic chunks: ${categories.chunks.length} files, ${formatBytes(categories.chunks.reduce((sum, f) => sum + f.size, 0))}`);
  console.log(`  Other files: ${categories.other.length} files, ${formatBytes(categories.other.reduce((sum, f) => sum + f.size, 0))}`);
  console.log('');

  // Optimization recommendations
  console.log('💡 Optimization Recommendations:');
  
  if (categories.vendor.length === 0) {
    console.log('  • Implement vendor chunk separation for better caching');
  }
  
  if (categories.chunks.length < 3) {
    console.log('  • Consider more aggressive code splitting for routes and features');
  }
  
  const largeChunks = jsFiles.filter(f => f.size > 500 * 1024);
  if (largeChunks.length > 0) {
    console.log(`  • Split ${largeChunks.length} large chunks (>500KB):`);
    largeChunks.forEach(chunk => {
      console.log(`    - ${chunk.name} (${formatBytes(chunk.size)})`);
    });
  }

  if (totalJSSize > 1.5 * 1024 * 1024) {
    console.log('  • Consider dynamic imports for heavy libraries (PDF, Excel, Charts)');
    console.log('  • Implement tree shaking for unused code elimination');
    console.log('  • Use CDN for common vendor libraries');
  }

  console.log('');
}

function trackSizeHistory(analysis) {
  const HISTORY_FILE = 'bundle-size-history.json';
  const { totalJSSize, totalCSSSize, totalSize } = analysis;
  
  let history = [];
  try {
    if (fs.existsSync(HISTORY_FILE)) {
      history = JSON.parse(fs.readFileSync(HISTORY_FILE, 'utf8'));
    }
  } catch (error) {
    console.warn(`⚠️ Could not load history file: ${error.message}`);
  }

  const entry = {
    timestamp: new Date().toISOString(),
    totalJS: totalJSSize,
    totalCSS: totalCSSSize,
    total: totalSize,
    commit: getGitCommit()
  };

  history.push(entry);

  // Keep only last 50 entries
  if (history.length > 50) {
    history = history.slice(-50);
  }

  try {
    fs.writeFileSync(HISTORY_FILE, JSON.stringify(history, null, 2));
  } catch (error) {
    console.warn(`⚠️ Could not save history file: ${error.message}`);
  }

  // Show trend if we have previous data
  if (history.length > 1) {
    const previous = history[history.length - 2];
    const change = totalSize - previous.total;
    const changePercent = ((change / previous.total) * 100).toFixed(1);
    
    if (Math.abs(change) > 10 * 1024) { // Only show if change > 10KB
      const trend = change > 0 ? '📈' : '📉';
      console.log(`${trend} Size change from previous build: ${formatBytes(Math.abs(change))} (${changePercent}%)`);
      console.log('');
    }
  }
}

function getGitCommit() {
  try {
    return execSync('git rev-parse --short HEAD', { encoding: 'utf8' }).trim();
  } catch (error) {
    return 'unknown';
  }
}

function main() {
  const args = process.argv.slice(2);
  const isCI = process.env.CI === 'true' || args.includes('--ci');
  const shouldFail = args.includes('--fail-on-budget') || isCI;

  try {
    console.log(`Running in ${isCI ? 'CI' : 'local'} mode\n`);

    const budgets = loadBudgets();
    const analysis = analyzeBundleSize();
    const budgetResults = checkBudgets(analysis, budgets);
    
    generateOptimizationReport(analysis, budgetResults);
    trackSizeHistory(analysis);

    // Summary
    console.log('📋 Summary');
    console.log('='.repeat(50));
    console.log(`✅ Passed: ${budgetResults.passed}`);
    console.log(`❌ Failed: ${budgetResults.failed}`);
    console.log(`⚠️ Warnings: ${budgetResults.warnings.length}`);

    if (budgetResults.warnings.length > 0) {
      console.log('\n⚠️ Warnings:');
      budgetResults.warnings.forEach(warning => console.log(`  ${warning}`));
    }

    if (budgetResults.errors.length > 0) {
      console.log('\n❌ Errors:');
      budgetResults.errors.forEach(error => console.log(`  ${error}`));
    }

    console.log('\n💡 Next steps:');
    console.log('  • Run "npm run build:analyze" for visual bundle analysis');
    console.log('  • Check bundle-size-history.json for size trends');
    console.log('  • Update bundle-budget.json to adjust size limits');

    // Exit with error code if budgets failed and we should fail
    if (shouldFail && budgetResults.failed > 0) {
      console.log('\n💥 Bundle size budget exceeded - failing build');
      process.exit(1);
    }

    console.log('\n✅ Bundle size monitoring complete!');
    
  } catch (error) {
    console.error('❌ Bundle size monitoring failed:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main, analyzeBundleSize, checkBudgets };