#!/usr/bin/env python3

import os
import re
import glob

def apply_nullish_coalescing_fixes(content):
    """Apply nullish coalescing operator fixes."""
    # Pattern: something || 'default' -> something ?? 'default'
    # Only apply to safe cases where || is used with string/number literals
    patterns = [
        (r'(\w+\??\.\w+\??)\s*\|\|\s*(\'[^\']*\')', r'\1 ?? \2'),
        (r'(\w+\??\.\w+\??)\s*\|\|\s*(\"[^\"]*\")', r'\1 ?? \2'),
        (r'(\w+\??\.\w+\??)\s*\|\|\s*(\d+)', r'\1 ?? \2'),
        (r'(\w+\??)\s*\|\|\s*(\'[^\']*\')', r'\1 ?? \2'),
        (r'(\w+\??)\s*\|\|\s*(\"[^\"]*\")', r'\1 ?? \2'),
        (r'(\w+\??)\s*\|\|\s*(\d+)', r'\1 ?? \2'),
        (r'(\w+\??)\s*\|\|\s*(\[\])', r'\1 ?? \2'),
        (r'(\w+\??)\s*\|\|\s*(\{\})', r'\1 ?? \2'),
    ]
    
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)
    
    return content

def apply_optional_chaining_fixes(content):
    """Apply optional chaining fixes for simple cases."""
    # Pattern: something && something.property -> something?.property
    patterns = [
        (r'(\w+)\s*&&\s*\1\.(\w+)', r'\1?.\2'),
    ]
    
    for pattern, replacement in patterns:
        content = re.sub(pattern, replacement, content)
    
    return content

def remove_unused_imports(content):
    """Remove obviously unused imports."""
    lines = content.split('\n')
    new_lines = []
    
    for line in lines:
        # Skip lines that import something that's clearly not used
        # This is a very conservative approach
        if (line.strip().startswith('import') and 
            ('RiskStatus' in line and 'RiskStatus' not in content.replace(line, '')) or
            ('RiskSeverity' in line and 'RiskSeverity' not in content.replace(line, ''))):
            continue
        new_lines.append(line)
    
    return '\n'.join(new_lines)

def fix_typescript_file(file_path):
    """Apply common TypeScript fixes to a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Apply fixes
        content = apply_nullish_coalescing_fixes(content)
        content = apply_optional_chaining_fixes(content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed: {file_path}")
            return True
        
        return False
    
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return False

def main():
    """Apply fixes to all TypeScript files."""
    # Find all TypeScript files
    ts_files = glob.glob('src/**/*.ts', recursive=True) + glob.glob('src/**/*.tsx', recursive=True)
    
    fixed_count = 0
    total_count = len(ts_files)
    
    print(f"Processing {total_count} TypeScript files...")
    
    for file_path in ts_files:
        if fix_typescript_file(file_path):
            fixed_count += 1
    
    print(f"\nSummary:")
    print(f"- Total files: {total_count}")
    print(f"- Files modified: {fixed_count}")
    print(f"- Files unchanged: {total_count - fixed_count}")

if __name__ == "__main__":
    main()
