#!/usr/bin/env node

/**
 * Performance Monitoring Script for CI/CD Integration
 * Monitors performance metrics, enforces budgets, and generates alerts
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Performance Monitor\n');

const DIST_PATH = 'dist';
const PERFORMANCE_HISTORY_FILE = 'performance-history.json';
const PERFORMANCE_BUDGET_FILE = 'performance-budget.json';
const LIGHTHOUSE_CONFIG_FILE = 'lighthouse.config.js';

// Default performance budgets
const DEFAULT_BUDGETS = {
  firstContentfulPaint: 1500,
  largestContentfulPaint: 2500,
  cumulativeLayoutShift: 0.1,
  totalBundleSize: 2 * 1024 * 1024, // 2MB
  maxChunkSize: 1 * 1024 * 1024,    // 1MB
  timeToInteractive: 3500,
};

// Performance thresholds for alerts
const ALERT_THRESHOLDS = {
  CRITICAL: 2.0,  // 100% over budget
  ERROR: 1.5,     // 50% over budget
  WARNING: 1.2,   // 20% over budget
};

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function formatTime(ms) {
  if (ms < 1000) return `${ms.toFixed(0)}ms`;
  return `${(ms / 1000).toFixed(2)}s`;
}

function loadBudgets() {
  try {
    if (fs.existsSync(PERFORMANCE_BUDGET_FILE)) {
      const budgets = JSON.parse(fs.readFileSync(PERFORMANCE_BUDGET_FILE, 'utf8'));
      return { ...DEFAULT_BUDGETS, ...budgets };
    }
  } catch (error) {
    console.warn(`⚠️ Could not load performance budget file: ${error.message}`);
  }
  return DEFAULT_BUDGETS;
}

function saveBudgets(budgets) {
  try {
    const budgetData = {
      ...budgets,
      lastUpdated: new Date().toISOString(),
      version: '1.0.0',
      description: {
        firstContentfulPaint: 'First Contentful Paint budget in milliseconds',
        largestContentfulPaint: 'Largest Contentful Paint budget in milliseconds',
        cumulativeLayoutShift: 'Cumulative Layout Shift budget (score)',
        totalBundleSize: 'Total bundle size budget in bytes',
        maxChunkSize: 'Maximum chunk size budget in bytes',
        timeToInteractive: 'Time to Interactive budget in milliseconds',
      },
    };
    
    fs.writeFileSync(PERFORMANCE_BUDGET_FILE, JSON.stringify(budgetData, null, 2));
    console.log(`💾 Performance budget saved: ${PERFORMANCE_BUDGET_FILE}`);
  } catch (error) {
    console.error(`❌ Could not save performance budget file: ${error.message}`);
  }
}

function analyzeBundlePerformance() {
  if (!fs.existsSync(DIST_PATH)) {
    console.error('❌ Build output not found. Run "npm run build" first.');
    process.exit(1);
  }

  const assetsPath = path.join(DIST_PATH, 'assets');
  if (!fs.existsSync(assetsPath)) {
    console.error('❌ Assets directory not found in build output.');
    process.exit(1);
  }

  const files = fs.readdirSync(assetsPath, { recursive: true });
  const jsFiles = [];
  const cssFiles = [];

  files.forEach(file => {
    const filePath = path.join(assetsPath, file);
    const stats = fs.statSync(filePath);
    
    if (stats.isFile()) {
      const size = stats.size;
      const fileInfo = { name: file, size, path: filePath };
      
      if (file.endsWith('.js')) {
        jsFiles.push(fileInfo);
      } else if (file.endsWith('.css')) {
        cssFiles.push(fileInfo);
      }
    }
  });

  jsFiles.sort((a, b) => b.size - a.size);
  cssFiles.sort((a, b) => b.size - a.size);

  const totalJSSize = jsFiles.reduce((sum, file) => sum + file.size, 0);
  const totalCSSSize = cssFiles.reduce((sum, file) => sum + file.size, 0);
  const totalBundleSize = totalJSSize + totalCSSSize;

  return {
    jsFiles,
    cssFiles,
    totalJSSize,
    totalCSSSize,
    totalBundleSize,
    largestChunk: jsFiles[0]?.size || 0,
  };
}

function runLighthouseAnalysis() {
  console.log('🔍 Running Lighthouse Performance Analysis...');
  
  try {
    // Create Lighthouse config if it doesn't exist
    if (!fs.existsSync(LIGHTHOUSE_CONFIG_FILE)) {
      createLighthouseConfig();
    }

    // Check if lighthouse is available
    try {
      execSync('npx lighthouse --version', { stdio: 'pipe' });
    } catch (error) {
      console.warn('⚠️ Lighthouse not available, skipping web vitals analysis');
      return null;
    }

    // Run Lighthouse on built application
    const lighthouseCommand = `npx lighthouse http://localhost:8080 --config-path=${LIGHTHOUSE_CONFIG_FILE} --output=json --output-path=lighthouse-report.json --chrome-flags="--headless --no-sandbox"`;
    
    try {
      execSync(lighthouseCommand, { stdio: 'pipe' });
      
      if (fs.existsSync('lighthouse-report.json')) {
        const report = JSON.parse(fs.readFileSync('lighthouse-report.json', 'utf8'));
        
        return {
          firstContentfulPaint: report.audits['first-contentful-paint']?.numericValue || null,
          largestContentfulPaint: report.audits['largest-contentful-paint']?.numericValue || null,
          cumulativeLayoutShift: report.audits['cumulative-layout-shift']?.numericValue || null,
          timeToInteractive: report.audits['interactive']?.numericValue || null,
          performanceScore: report.categories.performance?.score * 100 || null,
        };
      }
    } catch (error) {
      console.warn('⚠️ Lighthouse analysis failed, using bundle analysis only');
      return null;
    }
  } catch (error) {
    console.warn('⚠️ Could not run Lighthouse analysis:', error.message);
    return null;
  }
  
  return null;
}

function createLighthouseConfig() {
  const config = `
module.exports = {
  extends: 'lighthouse:default',
  settings: {
    onlyAudits: [
      'first-contentful-paint',
      'largest-contentful-paint',
      'cumulative-layout-shift',
      'interactive',
      'total-blocking-time',
      'speed-index',
    ],
    throttling: {
      rttMs: 40,
      throughputKbps: 10240,
      cpuSlowdownMultiplier: 1,
      requestLatencyMs: 0,
      downloadThroughputKbps: 0,
      uploadThroughputKbps: 0,
    },
    screenEmulation: {
      mobile: false,
      width: 1350,
      height: 940,
      deviceScaleFactor: 1,
      disabled: false,
    },
    emulatedUserAgent: false,
  },
};
`;
  
  fs.writeFileSync(LIGHTHOUSE_CONFIG_FILE, config);
  console.log(`📝 Created Lighthouse config: ${LIGHTHOUSE_CONFIG_FILE}`);
}

function checkPerformanceBudgets(metrics, budgets) {
  const violations = [];
  const warnings = [];
  
  console.log('🎯 Performance Budget Validation');
  console.log('='.repeat(50));

  // Check bundle size budgets
  if (metrics.totalBundleSize > budgets.totalBundleSize) {
    const ratio = metrics.totalBundleSize / budgets.totalBundleSize;
    const violation = {
      metric: 'totalBundleSize',
      actual: metrics.totalBundleSize,
      budget: budgets.totalBundleSize,
      ratio,
      severity: ratio > ALERT_THRESHOLDS.CRITICAL ? 'critical' : 
               ratio > ALERT_THRESHOLDS.ERROR ? 'error' : 'warning',
    };
    
    if (violation.severity === 'critical' || violation.severity === 'error') {
      violations.push(violation);
    } else {
      warnings.push(violation);
    }
    
    const emoji = violation.severity === 'critical' ? '🔴' : 
                  violation.severity === 'error' ? '🟠' : '🟡';
    console.log(`${emoji} Total bundle size: ${formatBytes(metrics.totalBundleSize)} (budget: ${formatBytes(budgets.totalBundleSize)})`);
  } else {
    console.log(`✅ Total bundle size: ${formatBytes(metrics.totalBundleSize)} (under ${formatBytes(budgets.totalBundleSize)})`);
  }

  // Check largest chunk size
  if (metrics.largestChunk > budgets.maxChunkSize) {
    const ratio = metrics.largestChunk / budgets.maxChunkSize;
    const violation = {
      metric: 'maxChunkSize',
      actual: metrics.largestChunk,
      budget: budgets.maxChunkSize,
      ratio,
      severity: ratio > ALERT_THRESHOLDS.CRITICAL ? 'critical' : 
               ratio > ALERT_THRESHOLDS.ERROR ? 'error' : 'warning',
    };
    
    if (violation.severity === 'critical' || violation.severity === 'error') {
      violations.push(violation);
    } else {
      warnings.push(violation);
    }
    
    const emoji = violation.severity === 'critical' ? '🔴' : 
                  violation.severity === 'error' ? '🟠' : '🟡';
    console.log(`${emoji} Largest chunk: ${formatBytes(metrics.largestChunk)} (budget: ${formatBytes(budgets.maxChunkSize)})`);
  } else {
    console.log(`✅ Largest chunk: ${formatBytes(metrics.largestChunk)} (under ${formatBytes(budgets.maxChunkSize)})`);
  }

  // Check web vitals if available
  if (metrics.webVitals) {
    const vitals = metrics.webVitals;
    
    if (vitals.firstContentfulPaint && vitals.firstContentfulPaint > budgets.firstContentfulPaint) {
      const ratio = vitals.firstContentfulPaint / budgets.firstContentfulPaint;
      const violation = {
        metric: 'firstContentfulPaint',
        actual: vitals.firstContentfulPaint,
        budget: budgets.firstContentfulPaint,
        ratio,
        severity: ratio > ALERT_THRESHOLDS.CRITICAL ? 'critical' : 
                 ratio > ALERT_THRESHOLDS.ERROR ? 'error' : 'warning',
      };
      
      if (violation.severity === 'critical' || violation.severity === 'error') {
        violations.push(violation);
      } else {
        warnings.push(violation);
      }
      
      const emoji = violation.severity === 'critical' ? '🔴' : 
                    violation.severity === 'error' ? '🟠' : '🟡';
      console.log(`${emoji} First Contentful Paint: ${formatTime(vitals.firstContentfulPaint)} (budget: ${formatTime(budgets.firstContentfulPaint)})`);
    } else if (vitals.firstContentfulPaint) {
      console.log(`✅ First Contentful Paint: ${formatTime(vitals.firstContentfulPaint)} (under ${formatTime(budgets.firstContentfulPaint)})`);
    }

    if (vitals.largestContentfulPaint && vitals.largestContentfulPaint > budgets.largestContentfulPaint) {
      const ratio = vitals.largestContentfulPaint / budgets.largestContentfulPaint;
      const violation = {
        metric: 'largestContentfulPaint',
        actual: vitals.largestContentfulPaint,
        budget: budgets.largestContentfulPaint,
        ratio,
        severity: ratio > ALERT_THRESHOLDS.CRITICAL ? 'critical' : 
                 ratio > ALERT_THRESHOLDS.ERROR ? 'error' : 'warning',
      };
      
      if (violation.severity === 'critical' || violation.severity === 'error') {
        violations.push(violation);
      } else {
        warnings.push(violation);
      }
      
      const emoji = violation.severity === 'critical' ? '🔴' : 
                    violation.severity === 'error' ? '🟠' : '🟡';
      console.log(`${emoji} Largest Contentful Paint: ${formatTime(vitals.largestContentfulPaint)} (budget: ${formatTime(budgets.largestContentfulPaint)})`);
    } else if (vitals.largestContentfulPaint) {
      console.log(`✅ Largest Contentful Paint: ${formatTime(vitals.largestContentfulPaint)} (under ${formatTime(budgets.largestContentfulPaint)})`);
    }

    if (vitals.cumulativeLayoutShift && vitals.cumulativeLayoutShift > budgets.cumulativeLayoutShift) {
      const ratio = vitals.cumulativeLayoutShift / budgets.cumulativeLayoutShift;
      const violation = {
        metric: 'cumulativeLayoutShift',
        actual: vitals.cumulativeLayoutShift,
        budget: budgets.cumulativeLayoutShift,
        ratio,
        severity: ratio > ALERT_THRESHOLDS.CRITICAL ? 'critical' : 
                 ratio > ALERT_THRESHOLDS.ERROR ? 'error' : 'warning',
      };
      
      if (violation.severity === 'critical' || violation.severity === 'error') {
        violations.push(violation);
      } else {
        warnings.push(violation);
      }
      
      const emoji = violation.severity === 'critical' ? '🔴' : 
                    violation.severity === 'error' ? '🟠' : '🟡';
      console.log(`${emoji} Cumulative Layout Shift: ${vitals.cumulativeLayoutShift.toFixed(3)} (budget: ${budgets.cumulativeLayoutShift})`);
    } else if (vitals.cumulativeLayoutShift) {
      console.log(`✅ Cumulative Layout Shift: ${vitals.cumulativeLayoutShift.toFixed(3)} (under ${budgets.cumulativeLayoutShift})`);
    }

    if (vitals.timeToInteractive && vitals.timeToInteractive > budgets.timeToInteractive) {
      const ratio = vitals.timeToInteractive / budgets.timeToInteractive;
      const violation = {
        metric: 'timeToInteractive',
        actual: vitals.timeToInteractive,
        budget: budgets.timeToInteractive,
        ratio,
        severity: ratio > ALERT_THRESHOLDS.CRITICAL ? 'critical' : 
                 ratio > ALERT_THRESHOLDS.ERROR ? 'error' : 'warning',
      };
      
      if (violation.severity === 'critical' || violation.severity === 'error') {
        violations.push(violation);
      } else {
        warnings.push(violation);
      }
      
      const emoji = violation.severity === 'critical' ? '🔴' : 
                    violation.severity === 'error' ? '🟠' : '🟡';
      console.log(`${emoji} Time to Interactive: ${formatTime(vitals.timeToInteractive)} (budget: ${formatTime(budgets.timeToInteractive)})`);
    } else if (vitals.timeToInteractive) {
      console.log(`✅ Time to Interactive: ${formatTime(vitals.timeToInteractive)} (under ${formatTime(budgets.timeToInteractive)})`);
    }
  }

  console.log('');
  return { violations, warnings };
}

function generatePerformanceReport(metrics, budgetResults, budgets) {
  console.log('📊 Performance Report');
  console.log('='.repeat(50));

  // Bundle composition
  console.log('📦 Bundle Analysis:');
  console.log(`  Total Bundle Size: ${formatBytes(metrics.totalBundleSize)}`);
  console.log(`  JavaScript: ${formatBytes(metrics.totalJSSize)} (${((metrics.totalJSSize/metrics.totalBundleSize)*100).toFixed(1)}%)`);
  console.log(`  CSS: ${formatBytes(metrics.totalCSSSize)} (${((metrics.totalCSSSize/metrics.totalBundleSize)*100).toFixed(1)}%)`);
  console.log(`  Largest Chunk: ${formatBytes(metrics.largestChunk)}`);
  console.log(`  Total Chunks: ${metrics.jsFiles.length}`);
  console.log('');

  // Web Vitals (if available)
  if (metrics.webVitals) {
    console.log('🌐 Web Vitals:');
    if (metrics.webVitals.performanceScore) {
      console.log(`  Performance Score: ${metrics.webVitals.performanceScore.toFixed(0)}/100`);
    }
    if (metrics.webVitals.firstContentfulPaint) {
      console.log(`  First Contentful Paint: ${formatTime(metrics.webVitals.firstContentfulPaint)}`);
    }
    if (metrics.webVitals.largestContentfulPaint) {
      console.log(`  Largest Contentful Paint: ${formatTime(metrics.webVitals.largestContentfulPaint)}`);
    }
    if (metrics.webVitals.cumulativeLayoutShift) {
      console.log(`  Cumulative Layout Shift: ${metrics.webVitals.cumulativeLayoutShift.toFixed(3)}`);
    }
    if (metrics.webVitals.timeToInteractive) {
      console.log(`  Time to Interactive: ${formatTime(metrics.webVitals.timeToInteractive)}`);
    }
    console.log('');
  }

  // Budget violations summary
  const totalViolations = budgetResults.violations.length + budgetResults.warnings.length;
  console.log('🚨 Budget Violations:');
  console.log(`  Critical/Errors: ${budgetResults.violations.length}`);
  console.log(`  Warnings: ${budgetResults.warnings.length}`);
  console.log(`  Total: ${totalViolations}`);
  console.log('');

  // Recommendations
  console.log('💡 Performance Recommendations:');
  const recommendations = generateRecommendations(metrics, budgetResults);
  recommendations.forEach(rec => console.log(`  • ${rec}`));
  console.log('');

  return {
    summary: {
      bundleSize: metrics.totalBundleSize,
      violations: budgetResults.violations.length,
      warnings: budgetResults.warnings.length,
      score: metrics.webVitals?.performanceScore || null,
    },
    recommendations,
  };
}

function generateRecommendations(metrics, budgetResults) {
  const recommendations = [];
  
  // Bundle size recommendations
  if (metrics.totalBundleSize > 1.5 * 1024 * 1024) { // 1.5MB
    recommendations.push('Implement more aggressive code splitting');
    recommendations.push('Consider using CDN for vendor libraries');
    recommendations.push('Enable tree shaking to remove unused code');
  }
  
  if (metrics.largestChunk > 800 * 1024) { // 800KB
    recommendations.push('Split large chunks into smaller pieces');
    recommendations.push('Implement dynamic imports for heavy features');
  }
  
  if (metrics.jsFiles.length < 5) {
    recommendations.push('Implement route-based code splitting');
    recommendations.push('Separate vendor libraries into dedicated chunks');
  }

  // Web vitals recommendations
  if (metrics.webVitals) {
    if (metrics.webVitals.firstContentfulPaint > 1500) {
      recommendations.push('Optimize critical rendering path');
      recommendations.push('Reduce render-blocking resources');
    }
    
    if (metrics.webVitals.largestContentfulPaint > 2500) {
      recommendations.push('Optimize largest content elements');
      recommendations.push('Implement image lazy loading and optimization');
    }
    
    if (metrics.webVitals.cumulativeLayoutShift > 0.1) {
      recommendations.push('Set explicit dimensions for images and ads');
      recommendations.push('Avoid inserting content above existing content');
    }
    
    if (metrics.webVitals.timeToInteractive > 3500) {
      recommendations.push('Reduce JavaScript execution time');
      recommendations.push('Implement progressive loading strategies');
    }
  }

  // Violation-specific recommendations
  budgetResults.violations.forEach(violation => {
    if (violation.severity === 'critical') {
      recommendations.push(`URGENT: Address ${violation.metric} - ${((violation.ratio - 1) * 100).toFixed(0)}% over budget`);
    }
  });

  return [...new Set(recommendations)]; // Remove duplicates
}

function trackPerformanceHistory(metrics, budgetResults) {
  let history = [];
  try {
    if (fs.existsSync(PERFORMANCE_HISTORY_FILE)) {
      history = JSON.parse(fs.readFileSync(PERFORMANCE_HISTORY_FILE, 'utf8'));
    }
  } catch (error) {
    console.warn(`⚠️ Could not load performance history: ${error.message}`);
  }

  const entry = {
    timestamp: new Date().toISOString(),
    commit: getGitCommit(),
    bundleSize: metrics.totalBundleSize,
    largestChunk: metrics.largestChunk,
    violations: budgetResults.violations.length,
    warnings: budgetResults.warnings.length,
    webVitals: metrics.webVitals || null,
  };

  history.push(entry);

  // Keep only last 100 entries
  if (history.length > 100) {
    history = history.slice(-100);
  }

  try {
    fs.writeFileSync(PERFORMANCE_HISTORY_FILE, JSON.stringify(history, null, 2));
  } catch (error) {
    console.warn(`⚠️ Could not save performance history: ${error.message}`);
  }

  // Show trend if we have previous data
  if (history.length > 1) {
    const previous = history[history.length - 2];
    const bundleChange = metrics.totalBundleSize - previous.bundleSize;
    const bundleChangePercent = ((bundleChange / previous.bundleSize) * 100).toFixed(1);
    
    if (Math.abs(bundleChange) > 50 * 1024) { // Only show if change > 50KB
      const trend = bundleChange > 0 ? '📈' : '📉';
      console.log(`${trend} Bundle size change: ${formatBytes(Math.abs(bundleChange))} (${bundleChangePercent}%)`);
    }

    const violationChange = budgetResults.violations.length - previous.violations;
    if (violationChange !== 0) {
      const trend = violationChange > 0 ? '📈' : '📉';
      console.log(`${trend} Violations change: ${violationChange > 0 ? '+' : ''}${violationChange}`);
    }
    
    console.log('');
  }
}

function getGitCommit() {
  try {
    return execSync('git rev-parse --short HEAD', { encoding: 'utf8' }).trim();
  } catch (error) {
    return 'unknown';
  }
}

function sendPerformanceAlerts(metrics, budgetResults) {
  const criticalViolations = budgetResults.violations.filter(v => v.severity === 'critical');
  const errorViolations = budgetResults.violations.filter(v => v.severity === 'error');
  
  if (criticalViolations.length > 0 || errorViolations.length > 0) {
    console.log('🚨 Performance Alerts');
    console.log('='.repeat(50));
    
    criticalViolations.forEach(violation => {
      console.log(`🔴 CRITICAL: ${violation.metric} is ${((violation.ratio - 1) * 100).toFixed(0)}% over budget`);
    });
    
    errorViolations.forEach(violation => {
      console.log(`🟠 ERROR: ${violation.metric} is ${((violation.ratio - 1) * 100).toFixed(0)}% over budget`);
    });
    
    console.log('');
    
    // In a real implementation, this would send alerts via:
    // - Slack webhook
    // - Email notifications
    // - GitHub status checks
    // - Custom webhook endpoints
  }
}

function main() {
  const args = process.argv.slice(2);
  const isCI = process.env.CI === 'true' || args.includes('--ci');
  const shouldFail = args.includes('--fail-on-budget') || isCI;
  const verbose = args.includes('--verbose') || args.includes('-v');

  try {
    console.log(`Running performance monitoring in ${isCI ? 'CI' : 'local'} mode\n`);

    // Load performance budgets
    const budgets = loadBudgets();
    
    // Analyze bundle performance
    const bundleMetrics = analyzeBundlePerformance();
    
    // Run Lighthouse analysis (optional)
    const webVitals = runLighthouseAnalysis();
    
    // Combine metrics
    const metrics = {
      ...bundleMetrics,
      webVitals,
    };
    
    // Check performance budgets
    const budgetResults = checkPerformanceBudgets(metrics, budgets);
    
    // Generate performance report
    const report = generatePerformanceReport(metrics, budgetResults, budgets);
    
    // Track performance history
    trackPerformanceHistory(metrics, budgetResults);
    
    // Send alerts if needed
    sendPerformanceAlerts(metrics, budgetResults);
    
    // Summary
    console.log('📋 Summary');
    console.log('='.repeat(50));
    console.log(`Bundle Size: ${formatBytes(metrics.totalBundleSize)}`);
    console.log(`Violations: ${budgetResults.violations.length} critical/error, ${budgetResults.warnings.length} warnings`);
    if (metrics.webVitals?.performanceScore) {
      console.log(`Performance Score: ${metrics.webVitals.performanceScore.toFixed(0)}/100`);
    }
    
    console.log('\n💡 Next steps:');
    console.log('  • Check performance-history.json for trends');
    console.log('  • Update performance-budget.json to adjust budgets');
    console.log('  • Run "npm run build:analyze" for detailed bundle analysis');
    
    // Exit with error code if budgets failed and we should fail
    if (shouldFail && budgetResults.violations.length > 0) {
      console.log('\n💥 Performance budget exceeded - failing build');
      process.exit(1);
    }
    
    console.log('\n✅ Performance monitoring complete!');
    
  } catch (error) {
    console.error('❌ Performance monitoring failed:', error.message);
    if (verbose) {
      console.error(error.stack);
    }
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { 
  main, 
  analyzeBundlePerformance, 
  checkPerformanceBudgets,
  runLighthouseAnalysis,
};