#!/usr/bin/env node

/**
 * Comprehensive Quality Gates Validation Script
 * This script runs all quality checks and provides detailed reporting
 */

import { execSync } from 'child_process';
import { readFileSync, existsSync } from 'fs';
import { join } from 'path';

const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  bold: '\x1b[1m'
};

class QualityGates {
  constructor() {
    this.results = [];
    this.startTime = Date.now();
  }

  log(message, color = colors.reset) {
    console.log(`${color}${message}${colors.reset}`);
  }

  logResult(name, passed, details = '') {
    const status = passed ? '✅' : '❌';
    const color = passed ? colors.green : colors.red;
    this.log(`${status} ${name}`, color);
    if (details) {
      this.log(`   ${details}`, colors.cyan);
    }
    this.results.push({ name, passed, details });
  }

  async runCommand(command, description) {
    try {
      this.log(`🔍 ${description}...`, colors.blue);
      const output = execSync(command, { 
        encoding: 'utf8', 
        stdio: 'pipe',
        timeout: 60000 
      });
      return { success: true, output };
    } catch (error) {
      return { 
        success: false, 
        output: error.stdout || error.stderr || error.message 
      };
    }
  }

  async checkTypeScript() {
    const result = await this.runCommand('npm run type-check', 'TypeScript type checking');
    this.logResult(
      'TypeScript Compilation', 
      result.success,
      result.success ? 'No type errors found' : 'Type errors detected'
    );
    return result.success;
  }

  async checkESLint() {
    const result = await this.runCommand('npm run lint -- --max-warnings=0', 'ESLint validation');
    this.logResult(
      'ESLint Validation', 
      result.success,
      result.success ? 'No linting errors or warnings' : 'Linting issues found'
    );
    return result.success;
  }

  async checkPrettier() {
    const result = await this.runCommand('npm run format:check', 'Prettier formatting check');
    this.logResult(
      'Code Formatting', 
      result.success,
      result.success ? 'All files properly formatted' : 'Formatting issues found'
    );
    return result.success;
  }

  async checkTests() {
    const result = await this.runCommand('npm run test:run', 'Running tests');
    this.logResult(
      'Test Suite', 
      result.success,
      result.success ? 'All tests passing' : 'Test failures detected'
    );
    return result.success;
  }

  async checkConsoleStatements() {
    try {
      const command = 'grep -r "console\\." src/ --include="*.ts" --include="*.tsx" --exclude-dir=test --exclude="*test*" --exclude="*spec*"';
      execSync(command, { stdio: 'pipe' });
      // If grep finds matches, it returns 0, which means console statements were found
      this.logResult(
        'Console Statement Check', 
        false,
        'Console statements found in production code'
      );
      return false;
    } catch (error) {
      // If grep returns non-zero (no matches found), that's what we want
      this.logResult(
        'Console Statement Check', 
        true,
        'No console statements in production code'
      );
      return true;
    }
  }

  async checkSecurityValidation() {
    const result = await this.runCommand('npm run validate:csp', 'CSP validation');
    this.logResult(
      'Security Validation', 
      result.success,
      result.success ? 'CSP configuration valid' : 'Security issues detected'
    );
    return result.success;
  }

  async checkDependencies() {
    const result = await this.runCommand('npm run deps:check', 'Dependency validation');
    this.logResult(
      'Dependency Check', 
      result.success,
      result.success ? 'No unused dependencies' : 'Unused dependencies found'
    );
    return result.success;
  }

  async checkPackageVulnerabilities() {
    const result = await this.runCommand('npm audit --audit-level=high', 'Security audit');
    this.logResult(
      'Security Audit', 
      result.success,
      result.success ? 'No high-severity vulnerabilities' : 'Security vulnerabilities found'
    );
    return result.success;
  }

  async checkBuildSuccess() {
    const result = await this.runCommand('npm run build', 'Production build');
    this.logResult(
      'Production Build', 
      result.success,
      result.success ? 'Build completed successfully' : 'Build failed'
    );
    return result.success;
  }

  generateReport() {
    const endTime = Date.now();
    const duration = ((endTime - this.startTime) / 1000).toFixed(2);
    
    this.log('\n' + '='.repeat(60), colors.bold);
    this.log('QUALITY GATES REPORT', colors.bold + colors.cyan);
    this.log('='.repeat(60), colors.bold);
    
    const passed = this.results.filter(r => r.passed).length;
    const total = this.results.length;
    const percentage = ((passed / total) * 100).toFixed(1);
    
    this.log(`\nResults: ${passed}/${total} checks passed (${percentage}%)`, colors.bold);
    this.log(`Duration: ${duration}s`, colors.blue);
    
    if (passed === total) {
      this.log('\n🎉 All quality gates passed!', colors.green + colors.bold);
      return true;
    } else {
      this.log('\n❌ Some quality gates failed!', colors.red + colors.bold);
      this.log('\nFailed checks:', colors.red);
      this.results
        .filter(r => !r.passed)
        .forEach(r => this.log(`  • ${r.name}: ${r.details}`, colors.red));
      return false;
    }
  }

  async runAll() {
    this.log('🚀 Running comprehensive quality gates...', colors.bold + colors.cyan);
    this.log('');

    // Run all quality checks
    await this.checkTypeScript();
    await this.checkESLint();
    await this.checkPrettier();
    await this.checkConsoleStatements();
    await this.checkSecurityValidation();
    await this.checkTests();
    await this.checkDependencies();
    await this.checkPackageVulnerabilities();
    await this.checkBuildSuccess();

    return this.generateReport();
  }

  async runPreCommit() {
    this.log('🔍 Running pre-commit quality gates...', colors.bold + colors.cyan);
    this.log('');

    // Run essential pre-commit checks
    await this.checkTypeScript();
    await this.checkESLint();
    await this.checkPrettier();
    await this.checkConsoleStatements();
    await this.checkTests();

    return this.generateReport();
  }
}

// CLI interface
const args = process.argv.slice(2);
const mode = args[0] || 'all';

const qualityGates = new QualityGates();

(async () => {
  let success;
  
  switch (mode) {
    case 'pre-commit':
      success = await qualityGates.runPreCommit();
      break;
    case 'all':
    default:
      success = await qualityGates.runAll();
      break;
  }
  
  process.exit(success ? 0 : 1);
})().catch(error => {
  console.error('Quality gates script failed:', error);
  process.exit(1);
});