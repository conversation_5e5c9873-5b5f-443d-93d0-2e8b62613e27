# Quality Gates and Development Standards

This document outlines the comprehensive quality gates and development standards implemented for the RiskCompass application.

## Overview

Our quality gates ensure that all code meets enterprise-grade standards before being committed or deployed. The system includes automated checks for TypeScript quality, code formatting, security, testing, and more.

## Pre-commit Quality Gates

Every commit must pass the following quality gates:

### 1. TypeScript Type Checking
- **Command**: `npm run type-check`
- **Requirement**: Zero TypeScript compilation errors
- **Purpose**: Ensures type safety and prevents runtime type errors

### 2. ESLint Validation
- **Command**: `npm run lint --max-warnings=0`
- **Requirement**: Zero ESLint errors or warnings
- **Purpose**: Enforces code quality standards and best practices

### 3. Code Formatting
- **Command**: `npm run format:check`
- **Requirement**: All files must be properly formatted
- **Purpose**: Maintains consistent code style across the project

### 4. Console Statement Check
- **Process**: Automated scan for console statements in production code
- **Requirement**: No console.* statements in src/ (excluding test files)
- **Purpose**: Prevents debug information leakage in production

### 5. Security Validation
- **Command**: `npm run validate:csp`
- **Requirement**: CSP configuration must be valid
- **Purpose**: Ensures security headers are properly configured

### 6. Test Validation
- **Command**: `npm run test:run`
- **Requirement**: All tests must pass
- **Purpose**: Prevents broken functionality from being committed

## Commit Message Standards

All commit messages must follow the conventional commit format:

### Format
```
type(scope): description

[optional body]

[optional footer]
```

### Types
- **feat**: A new feature
- **fix**: A bug fix
- **docs**: Documentation only changes
- **style**: Changes that do not affect the meaning of the code
- **refactor**: A code change that neither fixes a bug nor adds a feature
- **test**: Adding missing tests or correcting existing tests
- **chore**: Changes to the build process or auxiliary tools
- **perf**: A code change that improves performance
- **ci**: Changes to CI configuration files and scripts
- **build**: Changes that affect the build system or external dependencies

### Examples
```bash
feat(auth): add user authentication system
fix(ui): resolve button alignment issue
docs: update API documentation
refactor(utils): simplify date formatting function
test(components): add unit tests for risk form
chore(deps): update dependencies to latest versions
```

### Rules
- Description must be 1-50 characters
- Use present tense ("add" not "added")
- Don't capitalize the first letter of description
- No period at the end of description
- Total message length should not exceed 72 characters

## Quality Gate Scripts

### Available Commands

#### Basic Quality Checks
```bash
# Run all quality checks
npm run quality

# Run quality checks and fix issues
npm run quality:fix

# Run comprehensive quality validation
npm run quality:full

# Run pre-commit specific checks
npm run quality:pre-commit

# Run strict quality validation with coverage
npm run quality:strict
```

#### Advanced Quality Gates
```bash
# Run comprehensive quality gates
npm run quality:gates

# Run pre-commit quality gates only
npm run quality:gates:pre-commit
```

### Quality Gates Script Features

The `scripts/quality-gates.js` script provides:

1. **Comprehensive Validation**
   - TypeScript compilation
   - ESLint validation (zero warnings)
   - Prettier formatting check
   - Console statement detection
   - Security validation
   - Test execution
   - Dependency validation
   - Security audit
   - Production build validation

2. **Detailed Reporting**
   - Pass/fail status for each check
   - Execution time tracking
   - Percentage of checks passed
   - Detailed failure information

3. **Multiple Modes**
   - `all`: Run all quality checks
   - `pre-commit`: Run essential pre-commit checks only

## Lint-Staged Configuration

Files are automatically processed on commit:

### TypeScript/JavaScript Files
- ESLint with zero warnings tolerance
- Prettier formatting
- TypeScript compilation check
- Console statement validation

### JSON/CSS/Markdown Files
- Prettier formatting

### Package.json
- Dependency validation check

## Branch Protection Rules

To set up branch protection rules in your repository:

1. **Required Status Checks**
   - TypeScript compilation
   - ESLint validation
   - Test suite execution
   - Security validation

2. **Required Reviews**
   - At least 1 reviewer required
   - Dismiss stale reviews when new commits are pushed

3. **Restrictions**
   - Require branches to be up to date before merging
   - Require status checks to pass before merging
   - Include administrators in restrictions

## Development Workflow

### Before Committing
1. Make your changes
2. Run `npm run quality:fix` to automatically fix issues
3. Run `npm run test` to ensure tests pass
4. Commit with proper message format
5. Pre-commit hooks will automatically validate

### If Pre-commit Fails
1. Review the error messages
2. Fix the identified issues
3. Run `npm run quality:gates:pre-commit` to test locally
4. Commit again

### Manual Quality Check
```bash
# Run comprehensive quality validation
npm run quality:gates

# Check specific areas
npm run type-check
npm run lint
npm run format:check
npm run test:run
npm run validate:csp
```

## Troubleshooting

### Common Issues

#### TypeScript Errors
```bash
# Check specific errors
npm run type-check

# Watch mode for development
npm run type-check:watch
```

#### Linting Issues
```bash
# Auto-fix linting issues
npm run lint:fix

# Check specific files
npx eslint src/path/to/file.ts --fix
```

#### Formatting Issues
```bash
# Auto-format all files
npm run format

# Check formatting without fixing
npm run format:check
```

#### Console Statements
- Remove all `console.log`, `console.error`, etc. from production code
- Use the centralized logging service instead
- Console statements are allowed in test files

#### Test Failures
```bash
# Run tests with detailed output
npm run test:run

# Run specific test files
npx vitest run src/path/to/test.test.ts
```

### Getting Help

1. Check the error messages carefully
2. Run individual quality checks to isolate issues
3. Use the `--help` flag with npm scripts when available
4. Review this documentation for standards and requirements

## Continuous Improvement

This quality gate system is designed to evolve with the project. Regular reviews and updates ensure that:

- Standards remain relevant and effective
- New quality checks are added as needed
- Performance of quality gates is optimized
- Developer experience is continuously improved

## Integration with CI/CD

These quality gates integrate with CI/CD pipelines to ensure:

- All commits meet quality standards
- Pull requests are automatically validated
- Production deployments only include quality-validated code
- Quality metrics are tracked over time

For CI/CD integration, use:
```bash
npm run quality:gates
```

This command returns appropriate exit codes for automated systems.