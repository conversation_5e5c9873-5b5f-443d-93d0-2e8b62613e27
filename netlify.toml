[build]
  publish = "dist"
  command = "npm run build:dev"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    # Prevent clickjacking attacks
    X-Frame-Options = "DENY"
    # Prevent MIME type sniffing
    X-Content-Type-Options = "nosniff"
    # Enable XSS protection (legacy browsers)
    X-XSS-Protection = "1; mode=block"
    # Control referrer information
    Referrer-Policy = "strict-origin-when-cross-origin"
    # Restrict permissions for sensitive APIs
    Permissions-Policy = "camera=(), microphone=(), geolocation=(), payment=(), usb=(), bluetooth=(), magnetometer=(), gyroscope=(), accelerometer=(), fullscreen=(self), picture-in-picture=()"
    # Strict Transport Security (HTTPS enforcement)
    Strict-Transport-Security = "max-age=31536000; includeSubDomains; preload"
    # Cross-Origin policies
    Cross-Origin-Embedder-Policy = "require-corp"
    Cross-Origin-Opener-Policy = "same-origin"
    Cross-Origin-Resource-Policy = "same-origin"
    # Enhanced Content Security Policy with stricter rules
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' https://alieiaxzqyxjceitkoqx.supabase.co; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com data:; img-src 'self' data: https://alieiaxzqyxjceitkoqx.supabase.co https://*.supabase.co blob:; connect-src 'self' https://alieiaxzqyxjceitkoqx.supabase.co wss://alieiaxzqyxjceitkoqx.supabase.co https://fonts.googleapis.com https://fonts.gstatic.com; manifest-src 'self'; worker-src 'self' blob:; child-src 'self' blob:; frame-src 'self' blob:; media-src 'self' data: blob:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests; block-all-mixed-content;"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

[[headers]]
  for = "*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
