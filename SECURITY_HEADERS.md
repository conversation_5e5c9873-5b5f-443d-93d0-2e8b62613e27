# Security Headers Configuration

This document outlines the security headers implemented in the RiskCompass application to protect against various web security threats.

## Overview

Security headers are HTTP response headers that help protect web applications from common attacks such as XSS, clickjacking, MIME sniffing, and other security vulnerabilities.

## Implemented Headers

### 1. Content Security Policy (CSP)

**Purpose**: Prevents XSS attacks by controlling which resources can be loaded and executed.

**Configuration**:
```
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://alieiaxzqyxjceitkoqx.supabase.co; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com data:; img-src 'self' data: https://alieiaxzqyxjceitkoqx.supabase.co https://*.supabase.co blob:; connect-src 'self' https://alieiaxzqyxjceitkoqx.supabase.co wss://alieiaxzqyxjceitkoqx.supabase.co https://fonts.googleapis.com https://fonts.gstatic.com; manifest-src 'self'; worker-src 'self' blob:; child-src 'self' blob:; frame-src 'self' blob:; media-src 'self' data: blob:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests; block-all-mixed-content;
```

**Breakdown**:
- `default-src 'self'`: Only allow resources from the same origin by default
- `script-src`: Allow scripts from self, inline scripts (for React), and Supabase
- `style-src`: Allow styles from self, inline styles, and Google Fonts
- `font-src`: Allow fonts from self, Google Fonts, and data URLs
- `img-src`: Allow images from self, data URLs, Supabase domains, and blob URLs
- `connect-src`: Allow connections to self, Supabase API/WebSocket, and Google Fonts
- `manifest-src 'self'`: Only allow manifest from same origin
- `worker-src`: Allow service workers from self and blob URLs
- `child-src`: Allow child contexts from self and blob URLs
- `frame-src`: Allow frames from self and blob URLs
- `media-src`: Allow media from self, data URLs, and blob URLs
- `object-src 'none'`: Block all plugins (Flash, etc.)
- `base-uri 'self'`: Restrict base tag to same origin
- `form-action 'self'`: Only allow form submissions to same origin
- `frame-ancestors 'none'`: Prevent the page from being embedded in frames (additional clickjacking protection)
- `upgrade-insecure-requests`: Automatically upgrade HTTP requests to HTTPS
- `block-all-mixed-content`: Block mixed content (HTTP resources on HTTPS pages)

### 2. X-Frame-Options

**Purpose**: Prevents clickjacking attacks by controlling whether the page can be embedded in frames.

**Configuration**: `DENY`

**Effect**: Completely prevents the page from being embedded in any frame or iframe.

### 3. X-Content-Type-Options

**Purpose**: Prevents MIME type sniffing attacks.

**Configuration**: `nosniff`

**Effect**: Forces browsers to respect the declared Content-Type header.

### 4. X-XSS-Protection

**Purpose**: Enables XSS filtering in legacy browsers.

**Configuration**: `1; mode=block`

**Effect**: Enables XSS filtering and blocks the page if an attack is detected.

### 5. Referrer-Policy

**Purpose**: Controls how much referrer information is sent with requests.

**Configuration**: `strict-origin-when-cross-origin`

**Effect**: Sends full URL for same-origin requests, only origin for cross-origin HTTPS requests, and no referrer for HTTP requests.

### 6. Permissions-Policy

**Purpose**: Controls which browser features and APIs can be used.

**Configuration**: `camera=(), microphone=(), geolocation=(), payment=(), usb=(), bluetooth=(), magnetometer=(), gyroscope=(), accelerometer=(), fullscreen=(self), picture-in-picture=()`

**Effect**: Disables access to sensitive device APIs that the application doesn't need, while allowing fullscreen for the application itself.

### 7. Strict-Transport-Security (HSTS)

**Purpose**: Enforces HTTPS connections and prevents protocol downgrade attacks.

**Configuration**: `max-age=31536000; includeSubDomains; preload`

**Effect**: Forces HTTPS for one year, includes all subdomains, and enables HSTS preloading.

### 8. Cross-Origin-Embedder-Policy (COEP)

**Purpose**: Controls how the document can be embedded by cross-origin resources.

**Configuration**: `require-corp`

**Effect**: Requires cross-origin resources to explicitly opt-in to being embedded.

### 9. Cross-Origin-Opener-Policy (COOP)

**Purpose**: Controls how the document can be opened by cross-origin resources.

**Configuration**: `same-origin`

**Effect**: Isolates the browsing context to same-origin documents only.

### 10. Cross-Origin-Resource-Policy (CORP)

**Purpose**: Controls how the document's resources can be accessed by cross-origin requests.

**Configuration**: `same-origin`

**Effect**: Only allows same-origin access to the document's resources.

## Configuration Files

### Netlify Configuration (`netlify.toml`)

Primary configuration for Netlify deployments:

```toml
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-Content-Type-Options = "nosniff"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Permissions-Policy = "camera=(), microphone=(), geolocation=(), payment=(), usb=(), bluetooth=(), magnetometer=(), gyroscope=(), accelerometer=(), fullscreen=(self), picture-in-picture=()"
    Strict-Transport-Security = "max-age=31536000; includeSubDomains; preload"
    Cross-Origin-Embedder-Policy = "require-corp"
    Cross-Origin-Opener-Policy = "same-origin"
    Cross-Origin-Resource-Policy = "same-origin"
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' https://alieiaxzqyxjceitkoqx.supabase.co; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com data:; img-src 'self' data: https://alieiaxzqyxjceitkoqx.supabase.co https://*.supabase.co blob:; connect-src 'self' https://alieiaxzqyxjceitkoqx.supabase.co wss://alieiaxzqyxjceitkoqx.supabase.co https://fonts.googleapis.com https://fonts.gstatic.com; manifest-src 'self'; worker-src 'self' blob:; child-src 'self' blob:; frame-src 'self' blob:; media-src 'self' data: blob:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests; block-all-mixed-content;"
```

### Static Headers (`public/_headers`)

Fallback configuration for static hosting:

```
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=(), payment=(), usb=(), bluetooth=(), magnetometer=(), gyroscope=(), accelerometer=(), fullscreen=(self), picture-in-picture=()
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
  Cross-Origin-Embedder-Policy: require-corp
  Cross-Origin-Opener-Policy: same-origin
  Cross-Origin-Resource-Policy: same-origin
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://alieiaxzqyxjceitkoqx.supabase.co; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com data:; img-src 'self' data: https://alieiaxzqyxjceitkoqx.supabase.co https://*.supabase.co blob:; connect-src 'self' https://alieiaxzqyxjceitkoqx.supabase.co wss://alieiaxzqyxjceitkoqx.supabase.co https://fonts.googleapis.com https://fonts.gstatic.com; manifest-src 'self'; worker-src 'self' blob:; child-src 'self' blob:; frame-src 'self' blob:; media-src 'self' data: blob:; object-src 'none'; base-uri 'self'; form-action 'self'; frame-ancestors 'none'; upgrade-insecure-requests; block-all-mixed-content;
```

## Testing and Validation

### Automated Testing

The application includes a CSP validator utility (`src/utils/csp-validator.ts`) that performs the following tests:

1. **CSP Headers Present**: Verifies that CSP headers are configured
2. **Inline Script Blocking**: Tests that inline scripts are properly blocked
3. **External Resource Compliance**: Validates that allowed external resources load correctly
4. **Form Action Compliance**: Ensures form submissions work within CSP constraints

### Manual Testing

You can test the security headers using browser developer tools:

1. **Network Tab**: Check response headers for security headers
2. **Console**: Look for CSP violation warnings
3. **Security Tab**: Review security state and certificate information

### Online Tools

Use these online tools to validate security headers:

- [Security Headers](https://securityheaders.com/)
- [Mozilla Observatory](https://observatory.mozilla.org/)
- [CSP Evaluator](https://csp-evaluator.withgoogle.com/)

## Troubleshooting

### Common Issues

1. **Resources Not Loading**
   - Check if the resource domain is allowed in CSP
   - Verify the resource uses HTTPS
   - Check browser console for CSP violations

2. **Inline Styles/Scripts Blocked**
   - Use external files instead of inline code
   - Add nonce or hash to CSP if inline code is necessary
   - Consider using CSS-in-JS solutions that generate external stylesheets

3. **Third-Party Integrations Failing**
   - Add the third-party domain to appropriate CSP directives
   - Check if the integration requires specific permissions

### CSP Violation Reporting

To enable CSP violation reporting, add a `report-uri` or `report-to` directive:

```
Content-Security-Policy: ...; report-uri /csp-violation-report-endpoint/
```

## Security Considerations

### Current Limitations

1. **'unsafe-inline' for Scripts**: Required for React and inline event handlers in error boundaries
2. **'unsafe-inline' for Styles**: Required for dynamic styling in UI components (charts, animations, etc.)
3. **Broad HTTPS Sources**: `https:` allows any HTTPS source for images

### Recent Improvements

1. **Removed 'unsafe-eval'**: Eliminated the unsafe-eval directive as the application doesn't use dynamic code execution
2. **Added frame-ancestors 'none'**: Enhanced clickjacking protection beyond X-Frame-Options
3. **Stricter Script Policy**: Removed unnecessary eval permissions while maintaining React compatibility

### Future Improvements

1. **Nonce-based CSP**: Implement nonce-based CSP for better inline script security
2. **Stricter Image Sources**: Limit image sources to specific trusted domains
3. **CSP Reporting**: Implement violation reporting for monitoring
4. **Subresource Integrity**: Add SRI hashes for external resources

## Maintenance

### Regular Updates

1. **Review CSP Violations**: Monitor and address any CSP violations
2. **Update Allowed Domains**: Add new trusted domains as needed
3. **Remove Unused Permissions**: Regularly audit and remove unnecessary CSP directives
4. **Test After Changes**: Always test the application after modifying security headers

### Environment-Specific Configuration

Consider different CSP configurations for different environments:

- **Development**: More permissive for debugging
- **Staging**: Production-like but with additional monitoring
- **Production**: Strictest security configuration

## References

- [MDN Web Docs - Content Security Policy](https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP)
- [OWASP - Content Security Policy](https://owasp.org/www-community/controls/Content_Security_Policy)
- [Google Web Fundamentals - CSP](https://developers.google.com/web/fundamentals/security/csp)
- [CSP Quick Reference](https://content-security-policy.com/)