name: Code Quality Metrics

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run daily at 2 AM UTC
    - cron: '0 2 * * *'

permissions:
  contents: read
  pull-requests: write
  issues: write

jobs:
  quality-metrics:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run TypeScript check
      run: npm run type-check
      continue-on-error: true
      
    - name: Run ESLint
      run: npm run lint
      continue-on-error: true
      
    - name: Run tests with coverage
      run: npm run test:coverage
      continue-on-error: true
      
    - name: Build application
      run: npm run build
      continue-on-error: true
      
    - name: Collect quality metrics
      run: npm run quality:metrics
      
    - name: Upload quality metrics
      uses: actions/upload-artifact@v4
      with:
        name: quality-metrics-${{ github.sha }}
        path: quality-metrics.json
        retention-days: 30
        
    - name: Comment PR with quality metrics
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v7
      with:
        script: |
          const fs = require('fs');
          
          try {
            const metrics = JSON.parse(fs.readFileSync('quality-metrics.json', 'utf8'));
            
            const comment = `## 📊 Code Quality Metrics
            
            | Metric | Value | Status |
            |--------|-------|--------|
            | Quality Score | ${metrics.qualityScore.toFixed(1)} | ${metrics.qualityScore >= 85 ? '✅' : metrics.qualityScore >= 70 ? '⚠️' : '❌'} |
            | TypeScript Errors | ${metrics.typescript.errors} | ${metrics.typescript.errors === 0 ? '✅' : '❌'} |
            | ESLint Errors | ${metrics.eslint.errors} | ${metrics.eslint.errors === 0 ? '✅' : '❌'} |
            | ESLint Warnings | ${metrics.eslint.warnings} | ${metrics.eslint.warnings <= 5 ? '✅' : '⚠️'} |
            | Test Coverage | ${metrics.coverage.lines.toFixed(1)}% | ${metrics.coverage.lines >= 80 ? '✅' : '❌'} |
            | Bundle Size | ${Math.round(metrics.bundle.totalSize / 1024)}KB | ${metrics.bundle.totalSize < 500 * 1024 ? '✅' : '❌'} |
            | Maintainability | ${metrics.complexity.maintainabilityIndex.toFixed(1)} | ${metrics.complexity.maintainabilityIndex >= 70 ? '✅' : '❌'} |
            
            ### 📈 Details
            - **Lines of Code:** ${metrics.complexity.linesOfCode.toLocaleString()}
            - **Cyclomatic Complexity:** ${metrics.complexity.cyclomaticComplexity.toFixed(1)}
            - **Technical Debt:** ${metrics.complexity.technicalDebt.toFixed(1)}h
            - **Bundle Chunks:** ${metrics.bundle.chunkCount}
            - **Gzipped Size:** ${Math.round(metrics.bundle.gzippedSize / 1024)}KB
            
            ${metrics.qualityScore < 85 ? '⚠️ **Quality score is below recommended threshold of 85**' : ''}
            ${metrics.typescript.errors > 0 ? '❌ **TypeScript errors must be fixed before merge**' : ''}
            ${metrics.eslint.errors > 0 ? '❌ **ESLint errors must be fixed before merge**' : ''}
            ${metrics.coverage.lines < 80 ? '❌ **Test coverage is below 80% threshold**' : ''}
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
          } catch (error) {
            console.error('Failed to post quality metrics comment:', error);
          }
          
    - name: Check quality gates
      run: |
        node -e "
        const fs = require('fs');
        const metrics = JSON.parse(fs.readFileSync('quality-metrics.json', 'utf8'));
        
        let failed = false;
        const failures = [];
        
        if (metrics.typescript.errors > 0) {
          failures.push(\`TypeScript errors: \${metrics.typescript.errors}\`);
          failed = true;
        }
        
        if (metrics.eslint.errors > 0) {
          failures.push(\`ESLint errors: \${metrics.eslint.errors}\`);
          failed = true;
        }
        
        if (metrics.coverage.lines < 80) {
          failures.push(\`Test coverage: \${metrics.coverage.lines.toFixed(1)}% (minimum: 80%)\`);
          failed = true;
        }
        
        if (metrics.bundle.totalSize > 500 * 1024) {
          failures.push(\`Bundle size: \${Math.round(metrics.bundle.totalSize / 1024)}KB (maximum: 500KB)\`);
          failed = true;
        }
        
        if (failed) {
          console.error('❌ Quality gates failed:');
          failures.forEach(failure => console.error(\`  - \${failure}\`));
          process.exit(1);
        } else {
          console.log('✅ All quality gates passed');
        }
        "
        
  quality-trend-analysis:
    runs-on: ubuntu-latest
    needs: quality-metrics
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Download quality metrics
      uses: actions/download-artifact@v4
      with:
        name: quality-metrics-${{ github.sha }}
        
    - name: Store metrics in repository
      run: |
        mkdir -p .quality-history
        cp quality-metrics.json .quality-history/$(date +%Y-%m-%d-%H-%M-%S)-${{ github.sha }}.json
        
        # Keep only last 30 days of history
        find .quality-history -name "*.json" -mtime +30 -delete
        
    - name: Commit quality history
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
        git add .quality-history/
        git diff --staged --quiet || git commit -m "Add quality metrics for ${{ github.sha }}"
        git push
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}