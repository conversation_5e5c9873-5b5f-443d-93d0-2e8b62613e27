<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <title>Risk Compass - Non-Profit Risk Management</title>
    <meta name="description" content="Comprehensive risk management platform for non-profit organizations. Manage risks, track incidents, and ensure compliance." />
    <meta name="author" content="Risk Compass Team" />
    <meta name="keywords" content="risk management, non-profit, compliance, incident tracking, governance" />

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json" />

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#000000" />
    <meta name="background-color" content="#ffffff" />
    <meta name="display" content="standalone" />
    <meta name="orientation" content="portrait-primary" />

    <!-- iOS PWA Support -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="default" />
    <meta name="apple-mobile-web-app-title" content="Risk Compass" />
    <link rel="apple-touch-icon" href="/favicon.ico" />
    <link rel="apple-touch-startup-image" href="/favicon.ico" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon.ico" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon.ico" />

    <!-- Microsoft Tiles -->
    <meta name="msapplication-TileColor" content="#000000" />
    <meta name="msapplication-TileImage" content="/favicon.ico" />
    <meta name="msapplication-config" content="/browserconfig.xml" />

    <!-- Open Graph -->
    <meta property="og:title" content="Risk Compass - Non-Profit Risk Management" />
    <meta property="og:description" content="Comprehensive risk management platform for non-profit organizations" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="/favicon.ico" />
    <meta property="og:url" content="https://riskcompass.netlify.app" />
    <meta property="og:site_name" content="Risk Compass" />

    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Risk Compass - Non-Profit Risk Management" />
    <meta name="twitter:description" content="Comprehensive risk management platform for non-profit organizations" />
    <meta name="twitter:image" content="/favicon.ico" />

    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff" />
    <meta http-equiv="X-XSS-Protection" content="1; mode=block" />

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  </head>

  <body>
    <div id="root"></div>

    <!-- Debug info -->
    <script>

      // Check if main script loads
      window.addEventListener('error', function(e) {
      });
    </script>

    <script type="module" src="/src/main.tsx"></script>

  </body>
</html>
