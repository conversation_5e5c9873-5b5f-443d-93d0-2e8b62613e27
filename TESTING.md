# RiskCompass Testing Guide

This document provides comprehensive information about the testing strategy, setup, and best practices for the RiskCompass application.

## 📋 Table of Contents

- [Testing Strategy](#testing-strategy)
- [Test Structure](#test-structure)
- [Running Tests](#running-tests)
- [Writing Tests](#writing-tests)
- [Test Coverage](#test-coverage)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## 🎯 Testing Strategy

RiskCompass uses a comprehensive testing approach with multiple layers:

### Test Types

1. **Unit Tests** - Individual functions, utilities, and services
2. **Component Tests** - React components with user interactions
3. **Integration Tests** - Service interactions and data flow
4. **Hook Tests** - Custom React hooks
5. **End-to-End Tests** - Complete user workflows (future)

### Testing Stack

- **Test Runner**: Vitest
- **Testing Library**: React Testing Library
- **Mocking**: Vitest mocks
- **Coverage**: c8 (built into Vitest)
- **Environment**: jsdom

## 📁 Test Structure

```
src/
├── test/
│   ├── setup.ts              # Global test setup
│   ├── test-utils.tsx        # Testing utilities and helpers
│   └── test-runner.ts        # Custom test runner script
├── components/
│   └── __tests__/            # Component tests
├── services/
│   └── __tests__/            # Service tests
├── hooks/
│   └── __tests__/            # Hook tests
├── utils/
│   └── __tests__/            # Utility function tests
└── contexts/
    └── __tests__/            # Context tests
```

## 🚀 Running Tests

### Basic Commands

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage
```

### Advanced Commands

```bash
# Run specific test categories
npm run test:unit          # Unit tests only
npm run test:component     # Component tests only
npm run test:integration   # Integration tests only

# Run tests matching pattern
npm test -- --pattern="*Risk*"

# Run tests with custom reporter
npm test -- --reporter=verbose

# Run tests in CI mode
npm run test:ci
```

### Custom Test Runner

Use the custom test runner for more control:

```bash
# Run with coverage and watch mode
npm run test:runner -- --coverage --watch

# Run unit tests with UI
npm run test:runner -- --unit --ui

# Run specific pattern with bail on first failure
npm run test:runner -- --pattern="**/*Service*.test.*" --bail
```

## ✍️ Writing Tests

### Test File Naming

- Unit tests: `*.test.ts`
- Component tests: `*.test.tsx`
- Integration tests: `*.integration.test.ts`
- Hook tests: `*.test.ts` (in hooks directory)

### Basic Test Structure

```typescript
import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@/test/test-utils'

describe('ComponentName', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render correctly', () => {
    // Test implementation
  })

  it('should handle user interactions', async () => {
    // Test implementation
  })
})
```

### Testing Components

```typescript
import { render, screen, fireEvent, waitFor } from '@/test/test-utils'
import { MyComponent } from '../MyComponent'

describe('MyComponent', () => {
  it('should render with props', () => {
    render(<MyComponent title="Test Title" />)
    
    expect(screen.getByText('Test Title')).toBeInTheDocument()
  })

  it('should handle click events', async () => {
    const handleClick = vi.fn()
    render(<MyComponent onClick={handleClick} />)
    
    fireEvent.click(screen.getByRole('button'))
    
    await waitFor(() => {
      expect(handleClick).toHaveBeenCalledTimes(1)
    })
  })
})
```

### Testing Hooks

```typescript
import { renderHook, act } from '@testing-library/react'
import { useMyHook } from '../useMyHook'

describe('useMyHook', () => {
  it('should initialize with default values', () => {
    const { result } = renderHook(() => useMyHook())
    
    expect(result.current.value).toBe(defaultValue)
  })

  it('should update value when called', () => {
    const { result } = renderHook(() => useMyHook())
    
    act(() => {
      result.current.setValue('new value')
    })
    
    expect(result.current.value).toBe('new value')
  })
})
```

### Testing Services

```typescript
import { vi } from 'vitest'
import { myService } from '../myService'
import { supabase } from '@/integrations/supabase/client'

vi.mock('@/integrations/supabase/client')

describe('myService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should call supabase with correct parameters', async () => {
    const mockResponse = { data: { id: '1' }, error: null }
    vi.mocked(supabase.from).mockReturnValue({
      insert: vi.fn().mockReturnThis(),
      select: vi.fn().mockResolvedValue(mockResponse),
    } as any)

    const result = await myService.create({ name: 'test' })
    
    expect(result).toEqual({ id: '1' })
  })
})
```

## 📊 Test Coverage

### Coverage Targets

- **Overall Coverage**: > 80%
- **Critical Business Logic**: > 95%
- **Utility Functions**: > 90%
- **Components**: > 85%
- **Services**: > 90%

### Viewing Coverage

```bash
# Generate coverage report
npm run test:coverage

# Generate HTML coverage report
npm run test:coverage -- --reporter=html

# Open coverage report (cross-platform)
npx open-cli coverage/index.html
```

### Coverage Configuration

Coverage is configured in `vitest.config.ts`:

```typescript
export default defineConfig({
  test: {
    coverage: {
      provider: 'c8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'src/test/',
        '**/*.d.ts',
        '**/*.config.*',
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80,
        },
      },
    },
  },
})
```

## 🎯 Best Practices

### General Guidelines

1. **Write tests first** - Follow TDD when possible
2. **Test behavior, not implementation** - Focus on what the code does
3. **Use descriptive test names** - Clearly state what is being tested
4. **Keep tests simple** - One assertion per test when possible
5. **Mock external dependencies** - Isolate the code under test

### Component Testing

1. **Test user interactions** - Click, type, submit, etc.
2. **Test accessibility** - Screen readers, keyboard navigation
3. **Test error states** - Loading, error, empty states
4. **Use semantic queries** - getByRole, getByLabelText, etc.
5. **Avoid testing implementation details** - Don't test internal state

### Service Testing

1. **Mock external APIs** - Use vi.mock for Supabase calls
2. **Test error handling** - Network errors, validation errors
3. **Test data transformation** - Input/output mapping
4. **Test edge cases** - Empty data, invalid inputs
5. **Verify API calls** - Correct parameters, headers, etc.

### Hook Testing

1. **Test initial state** - Default values and setup
2. **Test state updates** - Actions and side effects
3. **Test cleanup** - useEffect cleanup functions
4. **Test dependencies** - Effect dependencies and re-runs
5. **Use act() for updates** - Wrap state updates in act()

## 🔧 Troubleshooting

### Common Issues

#### Tests failing with "Cannot read property of undefined"
- Check if all required props are provided
- Verify mock data structure matches expected format
- Ensure components are wrapped with necessary providers

#### Async tests timing out
- Use `waitFor` for async operations
- Increase timeout in test configuration
- Check for unresolved promises

#### Mock not working
- Ensure mock is called before import
- Use `vi.clearAllMocks()` in beforeEach
- Check mock implementation matches expected interface

#### Coverage not accurate
- Exclude test files from coverage
- Check for untested branches in conditional logic
- Verify all files are included in test runs

### Debug Commands

```bash
# Run tests with debug output
npm test -- --reporter=verbose

# Run single test file
npm test -- src/components/MyComponent.test.tsx

# Run tests with Node debugger
node --inspect-brk node_modules/.bin/vitest

# Run tests with specific timeout
npm test -- --testTimeout=10000
```

### Environment Issues

If tests fail due to environment setup:

1. Check Node.js version (requires 16+)
2. Clear node_modules and reinstall
3. Verify vitest.config.ts setup
4. Check for conflicting global packages

## 📚 Additional Resources

- [Vitest Documentation](https://vitest.dev/)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
- [Mock Service Worker](https://mswjs.io/) - For API mocking

## 🤝 Contributing

When adding new features:

1. Write tests for new functionality
2. Maintain or improve coverage percentage
3. Follow existing test patterns
4. Update this documentation if needed
5. Run full test suite before submitting PR

For questions about testing, please refer to the team's testing guidelines or reach out to the development team.
