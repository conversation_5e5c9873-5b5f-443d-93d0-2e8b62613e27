{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build && node scripts/build-production-sw.cjs", "build:secure": "node scripts/secure-build.js", "build:dev": "vite build --mode development", "build:analyze": "vite build && open-cli dist/stats.html", "build:size": "npm run build && node scripts/analyze-bundle-size.cjs", "build:monitor": "npm run build && node scripts/bundle-size-monitor.cjs", "build:budget": "npm run build && node scripts/bundle-size-monitor.cjs --fail-on-budget", "build:performance": "npm run build && node scripts/performance-monitor.cjs", "build:performance:ci": "npm run build && node scripts/performance-monitor.cjs --ci --fail-on-budget", "validate:performance": "npm run build && node scripts/performance-validation.cjs", "validate:performance:ci": "npm run build && node scripts/performance-validation.cjs --ci", "validate:performance:skip-lighthouse": "npm run build && node scripts/performance-validation.cjs --skip-lighthouse", "analyze": "npm run build:analyze", "size-check": "npm run build:size", "size-monitor": "npm run build:monitor", "lint": "eslint .", "lint:fix": "eslint . --fix", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "quality": "npm run type-check && npm run lint && npm run format:check", "quality:fix": "npm run type-check && npm run lint:fix && npm run format", "quality:full": "npm run quality && npm run test:run && npm run build", "quality:pre-commit": "npm run type-check && npm run lint --max-warnings=0 && npm run format:check && npm run test:run", "quality:strict": "npm run type-check && npm run lint --max-warnings=0 && npm run format:check && npm run test:coverage && npm run validate:csp", "quality:gates": "node scripts/quality-gates.js", "quality:gates:pre-commit": "node scripts/quality-gates.js pre-commit", "quality:metrics": "node scripts/collect-quality-metrics.cjs", "quality:metrics:watch": "node scripts/collect-quality-metrics.cjs --watch", "quality:check": "npm run quality:full", "quality:dashboard": "npm run quality:metrics && echo 'Quality dashboard available in application'", "quality:daily-check": "npm run quality:check && npm run security:audit && npm run performance:test", "quality:weekly-report": "npm run quality:metrics && npm run test:coverage && npm run analyze", "quality:complexity": "npm run quality:metrics", "deps:check": "depcheck", "deps:update": "ncu -u", "deps:unused": "depcheck --json", "deps:duplicates": "npm ls --depth=0 --json | node -e 'console.log(\"Checking for duplicates...\")'", "security:audit": "npm audit", "security:fix": "npm audit fix", "security:test": "node scripts/security-audit.cjs", "security:console-check": "grep -r 'console\\.' src/ || echo 'No console statements found'", "security:headers-check": "node scripts/validate-csp.js", "security:penetration-test": "node scripts/penetration-test.cjs", "security:daily-scan": "npm run security:audit && npm run security:test", "csp:validate": "npm run validate:csp", "migrate-types": "node scripts/migrate-types.js", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:watch": "vitest --watch", "test:coverage": "vitest run --coverage", "test:coverage:ui": "vitest --ui --coverage", "test:unit": "vitest run src/utils src/services src/hooks src/repositories", "test:component": "vitest run src/components src/pages", "test:integration": "vitest run src/integrations src/contexts", "test:ci": "vitest run --coverage --reporter=json --reporter=default", "test:runner": "tsx src/test/test-runner.ts", "test:validate": "tsx src/test/test-runner.ts --help", "test:code-splitting": "node scripts/test-code-splitting.js", "test:lazy-routes": "vitest run src/test/lazy-routes.test.tsx", "test:preloader": "vitest run src/test/route-preloader.test.tsx", "test:bundle": "vitest run src/test/bundle-analyzer.test.ts", "test:app-lazy": "vitest run src/test/app-lazy-loading.test.tsx", "validate:csp": "node scripts/validate-csp.js", "performance:test": "npm run validate:performance:skip-lighthouse", "performance:monitor": "npm run build:performance", "performance:memory": "node -e 'console.log(\"Memory usage:\", process.memoryUsage())'", "performance:profile": "npm run build:analyze", "performance:daily-check": "npm run performance:test", "performance:trend-analysis": "node scripts/performance-validation.cjs --trend", "performance:optimization-analysis": "npm run analyze", "performance:budget-review": "npm run build:budget", "performance:load-test": "npm run performance:test", "bundle:monitor": "npm run build:monitor", "type-check:strict": "tsc --noEmit --strict", "type-check:any-usage": "grep -r ': any' src/ || echo 'No any types found'", "lint:hooks": "eslint --rule 'react-hooks/exhaustive-deps: error' src/", "prepare": "husky", "clean:console": "node scripts/remove-console-statements.js", "clean:console:dry-run": "node scripts/remove-console-statements.js --dry-run --verbose", "clean:unused": "node scripts/remove-unused-code.js", "clean:unused:dry-run": "node scripts/remove-unused-code.js --dry-run --verbose", "fix:types": "node scripts/improve-type-safety.js", "fix:types:dry-run": "node scripts/improve-type-safety.js --dry-run --verbose", "clean:all": "npm run clean:console && npm run fix:types && npm run lint:fix"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@radix-ui/react-visually-hidden": "^1.2.3", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.56.2", "@types/dompurify": "^3.0.5", "@types/html2canvas": "^1.0.0", "@types/react-beautiful-dnd": "^13.1.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dompurify": "^3.2.6", "embla-carousel-react": "^8.3.0", "file-saver": "^2.0.5", "framer-motion": "^10.16.5", "html2canvas": "^1.4.1", "input-otp": "^1.2.4", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "xlsx": "^0.18.5", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/file-saver": "^2.0.7", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^8.37.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^3.2.4", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "depcheck": "^1.4.7", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.9.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^15.5.2", "lovable-tagger": "^1.1.7", "npm-check-updates": "^18.0.1", "open-cli": "^8.0.0", "postcss": "^8.5.6", "prettier": "^3.6.0", "rollup-plugin-visualizer": "^6.0.3", "tailwindcss": "^3.4.11", "terser": "^5.36.0", "ts-unused-exports": "^11.0.1", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "vitest": "^3.2.4"}}