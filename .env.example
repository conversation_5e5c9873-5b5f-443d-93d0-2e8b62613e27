# Environment Variables Example
# Copy this file to .env.local and configure as needed

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================

# Supabase URL - Optional (defaults to production URL if not set)
# Use this to point to different Supabase projects for different environments
# VITE_SUPABASE_URL=https://your-project-id.supabase.co

# Note: Supabase anon key is hardcoded in the client as it's safe to expose
# It's protected by Row Level Security (RLS) policies in the database

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Error Reporting (Optional)
# VITE_ERROR_REPORTING_ENDPOINT=https://your-error-service.com/api/errors
# VITE_ERROR_REPORTING_API_KEY=your-api-key

# Logging Endpoint (Optional)
# VITE_LOG_ENDPOINT=https://your-logging-service.com/api/logs

# Build Version (Optional - automatically set during CI/CD)
# VITE_BUILD_VERSION=1.0.0

# =============================================================================
# DEVELOPMENT NOTES
# =============================================================================

# 1. This file (.env.example) should be committed to version control
# 2. Your actual .env.local file should NOT be committed (it's in .gitignore)
# 3. The VITE_ prefix makes variables available to the client-side code
# 4. Environment variables are embedded at build time, not runtime
# 5. The Supabase anon key is intentionally hardcoded as it's designed to be public
