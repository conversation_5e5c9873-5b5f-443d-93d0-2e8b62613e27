
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.4';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

interface AdminNotificationRequest {
  type: 'organization_request' | 'admin_request';
  requester_email: string;
  requester_name: string;
  organization_name?: string;
  message: string;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { 
      type, 
      requester_email, 
      requester_name, 
      organization_name, 
      message 
    }: AdminNotificationRequest = await req.json();

    console.log('Admin notification request:', {
      type,
      requester_email,
      requester_name,
      organization_name
    });

    // Get all admin users from the profiles table
    const { data: admins, error: adminsError } = await supabase
      .from('profiles')
      .select('id, name, email')
      .eq('role', 'admin');

    if (adminsError) {
      console.error('Error fetching admin users:', adminsError);
      throw adminsError;
    }

    if (!admins || admins.length === 0) {
      console.log('No admin users found');
      // Still return success but log that no admins were found
      return new Response(
        JSON.stringify({ 
          success: true, 
          message: 'Request logged but no administrators found to notify' 
        }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    console.log(`Found ${admins.length} admin users to notify`);

    // Create notifications in the database for each admin
    const notifications = admins.map(admin => ({
      user_id: admin.id,
      type: 'admin_request',
      title: type === 'organization_request' 
        ? `Organization Access Request` 
        : 'Admin Access Request',
      message: `${message}\n\nRequester: ${requester_name} (${requester_email})${organization_name ? `\nOrganization: ${organization_name}` : ''}`,
      metadata: {
        requester_email,
        requester_name,
        organization_name,
        request_type: type
      },
      read: false
    }));

    const { error: notificationError } = await supabase
      .from('notifications')
      .insert(notifications);

    if (notificationError) {
      console.error('Error creating notifications:', notificationError);
      throw notificationError;
    }

    console.log(`Successfully created ${notifications.length} notifications`);

    // TODO: If you have email service configured (like Resend), you can send actual emails here
    // For now, we're just creating database notifications
    
    // Example of how to send emails if you have Resend configured:
    /*
    const resendApiKey = Deno.env.get('RESEND_API_KEY');
    if (resendApiKey) {
      const { Resend } = await import('npm:resend@2.0.0');
      const resend = new Resend(resendApiKey);
      
      for (const admin of admins) {
        await resend.emails.send({
          from: '<EMAIL>',
          to: admin.email,
          subject: `New ${type === 'organization_request' ? 'Organization Access' : 'Admin Access'} Request`,
          html: `
            <h2>New Access Request</h2>
            <p><strong>From:</strong> ${requester_name} (${requester_email})</p>
            ${organization_name ? `<p><strong>Organization:</strong> ${organization_name}</p>` : ''}
            <p><strong>Message:</strong> ${message}</p>
            <p>Please log into the admin panel to review this request.</p>
          `
        });
      }
    }
    */

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Admin notification sent successfully',
        notified_admins: admins.length
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );

  } catch (error: any) {
    console.error('Error in send-admin-notification function:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Internal server error' 
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  }
};

serve(handler);
