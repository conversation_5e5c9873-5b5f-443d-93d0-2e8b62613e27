import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";
import { visualizer } from "rollup-plugin-visualizer";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "::",
    port: 8080,
  },
  plugins: [
    react(),
    mode === "development" && componentTagger(),
    // Bundle analyzer - generates stats.html after build
    mode === "production" &&
      visualizer({
        filename: "dist/stats.html",
        open: false,
        gzipSize: true,
        brotliSize: true,
        template: "treemap", // 'treemap', 'sunburst', 'network'
      }),
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  // Only expose VITE_ prefixed environment variables for security
  envPrefix: ["VITE_"],

  // Production-optimized build configuration
  build: {
    // Generate source maps only in development
    sourcemap: mode === "development",

    // Enable minification in production for security and performance
    minify: mode === "production" ? "terser" : false,

    // Terser options for production builds with enhanced security
    terserOptions:
      mode === "production"
        ? {
            compress: {
              // Remove console logs in production
              drop_console: true,
              drop_debugger: true,
              // Remove unused code
              dead_code: true,
              // Remove unreachable code
              conditionals: true,
              // Evaluate constant expressions
              evaluate: true,
              // Remove unused variables
              unused: true,
              // Inline functions when beneficial
              inline: 2,
            },
            mangle: {
              // Mangle variable names for obfuscation
              toplevel: true,
            },
            format: {
              // Remove comments in production
              comments: false,
            },
          }
        : undefined,

    // ESBuild options for development builds
    esbuild:
      mode === "development"
        ? {
            // Keep console logs in development
            drop: [],
          }
        : {
            // Remove console logs and debugger in production (backup to Terser)
            drop: ["console", "debugger"],
            // Remove comments
            legalComments: "none",
          },

    // Set chunk size warning limit
    chunkSizeWarningLimit: 1000,

    // Simplified rollup options
    rollupOptions: {
      output: {
        // Ensure consistent chunk naming
        chunkFileNames: "assets/[name]-[hash].js",
        entryFileNames: "assets/[name]-[hash].js",
        assetFileNames: "assets/[name]-[hash].[ext]",
      },
    },
  },
}));
