{"timestamp": "2025-07-20T03:40:22.999Z", "commit": "unknown", "bundleSizeReduction": {"baseline": {"totalJS": 711, "totalCSS": 105039, "total": 105750}, "current": 107132, "reduction": -0.01306855791962175, "targetMet": false, "details": {"jsReduction": -0.026722925457102673, "cssReduction": -0.012976132674530412, "totalReduction": -0.01306855791962175}}, "webVitals": {"firstContentfulPaint": {"target": 1500, "results": []}, "largestContentfulPaint": {"target": 2500, "results": []}, "timeToInteractive": {"target": 3500, "results": []}, "cumulativeLayoutShift": {"target": 0.1, "results": []}}, "memoryUsage": {"estimatedUsage": 214264, "threshold": 52428800, "withinThreshold": true, "leakChecks": [{"name": "useEffect Cleanup", "passed": false, "message": "Found 0 cleanup functions in 0 useEffect hooks"}, {"name": "Event Listener Cleanup", "passed": false, "message": "Found 0 event listener cleanup patterns"}, {"name": "Timer Cleanup", "passed": true, "message": "Found 0 timers with 0 cleanup patterns"}]}, "performanceResults": [], "summary": {"bundleSizeTargetMet": false, "fcpTargetMet": true, "lcpTargetMet": true, "memoryTargetMet": true, "overallScore": 75}}