# Branch Protection Configuration

This document provides the configuration for setting up branch protection rules that enforce quality gates before code can be merged.

## GitHub Branch Protection Rules

### Main Branch Protection

Configure the following settings for the `main` branch:

#### General Settings
- **Restrict pushes that create files larger than 100MB**: ✅ Enabled
- **Restrict pushes that create files larger than specified limit**: 50MB

#### Branch Protection Rules

##### Protect matching branches
- **Branch name pattern**: `main`
- **Restrict deletions**: ✅ Enabled
- **Restrict force pushes**: ✅ Enabled

##### Require a pull request before merging
- ✅ **Require a pull request before merging**
- **Required number of reviewers before merging**: 1
- ✅ **Dismiss stale pull request approvals when new commits are pushed**
- ✅ **Require review from code owners**
- ✅ **Restrict pushes that create files larger than specified limit**

##### Require status checks before merging
- ✅ **Require status checks to pass before merging**
- ✅ **Require branches to be up to date before merging**

**Required Status Checks:**
- `TypeScript Compilation` (npm run type-check)
- `ESLint Validation` (npm run lint)
- `Code Formatting` (npm run format:check)
- `Test Suite` (npm run test:run)
- `Security Validation` (npm run validate:csp)
- `Quality Gates` (npm run quality:gates)

##### Require conversation resolution before merging
- ✅ **Require conversation resolution before merging**

##### Require signed commits
- ✅ **Require signed commits** (recommended for enterprise)

##### Require linear history
- ✅ **Require linear history** (prevents merge commits)

##### Include administrators
- ✅ **Include administrators** (applies rules to admins too)

##### Allow specified actors to bypass required pull requests
- Leave empty (no bypasses recommended)

## Development Branch Protection

### Feature Branch Pattern: `feature/*`

#### Branch Protection Rules
- **Branch name pattern**: `feature/*`
- **Restrict deletions**: ❌ Disabled (allow cleanup)
- **Restrict force pushes**: ✅ Enabled

##### Require status checks before merging
- ✅ **Require status checks to pass before merging**
- ❌ **Require branches to be up to date before merging** (optional for feature branches)

**Required Status Checks:**
- `TypeScript Compilation`
- `ESLint Validation`
- `Test Suite`

## CI/CD Integration

### GitHub Actions Workflow

Create `.github/workflows/quality-gates.yml`:

```yaml
name: Quality Gates

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  quality-gates:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run TypeScript compilation
      run: npm run type-check
      
    - name: Run ESLint validation
      run: npm run lint -- --max-warnings=0
      
    - name: Check code formatting
      run: npm run format:check
      
    - name: Run tests
      run: npm run test:run
      
    - name: Run security validation
      run: npm run validate:csp
      
    - name: Run comprehensive quality gates
      run: npm run quality:gates
      
    - name: Build production bundle
      run: npm run build
      
    - name: Upload coverage reports
      if: matrix.node-version == '20.x'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
```

### Status Check Configuration

The following status checks should be configured in your repository settings:

1. **TypeScript Compilation**
   - Context: `ci/typescript`
   - Description: "TypeScript type checking"
   - Target URL: Link to CI job

2. **ESLint Validation**
   - Context: `ci/eslint`
   - Description: "Code quality validation"
   - Target URL: Link to CI job

3. **Code Formatting**
   - Context: `ci/prettier`
   - Description: "Code formatting validation"
   - Target URL: Link to CI job

4. **Test Suite**
   - Context: `ci/tests`
   - Description: "Unit and integration tests"
   - Target URL: Link to CI job

5. **Security Validation**
   - Context: `ci/security`
   - Description: "Security and CSP validation"
   - Target URL: Link to CI job

6. **Quality Gates**
   - Context: `ci/quality-gates`
   - Description: "Comprehensive quality validation"
   - Target URL: Link to CI job

## Local Development Integration

### Pre-commit Hook Validation

The pre-commit hooks automatically enforce:
- Code formatting (Prettier)
- Linting (ESLint)
- Type checking (TypeScript)
- Console statement detection
- Basic test validation

### Manual Quality Validation

Developers can run quality checks manually:

```bash
# Quick pre-commit validation
npm run quality:gates:pre-commit

# Comprehensive validation
npm run quality:gates

# Individual checks
npm run type-check
npm run lint
npm run format:check
npm run test:run
npm run validate:csp
```

## Enforcement Levels

### Level 1: Pre-commit (Local)
- Code formatting
- Basic linting
- Type checking
- Console statement detection

### Level 2: Pull Request (CI/CD)
- Comprehensive linting (zero warnings)
- Full test suite
- Security validation
- Build validation
- Coverage requirements

### Level 3: Main Branch (Production)
- All Level 2 checks
- Performance validation
- Bundle size validation
- Security audit
- Documentation updates

## Bypass Procedures

### Emergency Hotfixes

For critical production issues:

1. Create hotfix branch from main
2. Apply minimal fix
3. Run `npm run quality:gates:pre-commit`
4. Create PR with `[HOTFIX]` prefix
5. Require 2 reviewers instead of 1
6. Merge after approval and CI validation

### Quality Gate Failures

If quality gates fail:

1. **DO NOT** bypass without fixing
2. Identify root cause
3. Fix issues locally
4. Re-run validation
5. Only proceed when all gates pass

## Monitoring and Metrics

### Quality Metrics Dashboard

Track the following metrics:
- Pre-commit hook success rate
- CI/CD pipeline success rate
- Time to fix quality issues
- Number of quality gate bypasses
- Code coverage trends
- Security vulnerability trends

### Alerting

Set up alerts for:
- Quality gate failure rate > 10%
- Security vulnerabilities detected
- Coverage dropping below 80%
- Build time exceeding 10 minutes

## Maintenance

### Regular Reviews

Monthly review of:
- Quality gate effectiveness
- Developer feedback
- Performance impact
- Rule adjustments needed

### Updates

Keep the following updated:
- ESLint rules and plugins
- TypeScript configuration
- Test coverage requirements
- Security validation rules
- CI/CD pipeline configuration

This configuration ensures that all code meets enterprise-grade quality standards before being merged into the main branch.